{"id": "v1dev-environment-uuid", "name": "V1 Dev Environment", "values": [{"key": "baseUrl", "value": "https://your-production-domain.com", "enabled": true, "type": "default", "description": "Base URL for V1 Dev API - Update this to your actual domain"}, {"key": "localUrl", "value": "http://localhost:8787", "enabled": false, "type": "default", "description": "Local development URL (Wrangler dev server)"}, {"key": "csrf_token", "value": "", "enabled": true, "type": "secret", "description": "CSRF token - automatically populated by requests"}, {"key": "user_id", "value": "", "enabled": true, "type": "default", "description": "Current user ID - automatically populated after login"}, {"key": "session_id", "value": "", "enabled": true, "type": "secret", "description": "Current session ID - automatically populated after login"}, {"key": "agent_id", "value": "", "enabled": true, "type": "default", "description": "Current agent/app ID - automatically populated when creating apps"}, {"key": "app_id", "value": "", "enabled": true, "type": "default", "description": "Current app ID - automatically populated when working with specific apps"}, {"key": "provider_id", "value": "", "enabled": true, "type": "default", "description": "Model provider ID - set this when testing provider endpoints"}, {"key": "secret_id", "value": "", "enabled": true, "type": "default", "description": "Secret ID - set this when testing secret endpoints"}], "_postman_variable_scope": "environment"}