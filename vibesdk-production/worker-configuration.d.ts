/* eslint-disable */
// Generated by Wrangler by running `wrangler types --include-runtime false` (hash: a434f433ba4653af25e418e89f62a09b)
declare namespace Cloudflare {
	interface Env {
		VibecoderStore: KVNamespace;
		TEMPLATES_REPOSITORY: "https://github.com/cloudflare/vibesdk-templates";
		ALLOWED_EMAIL: "";
		DISPATCH_NAMESPACE: "vibesdk-default-namespace";
		CLOUDFLARE_AI_GATEWAY: "vibesdk-gateway";
		ENABLE_READ_REPLICAS: "true";
		ANTHROPIC_API_KEY: string;
		OPENAI_API_KEY: string;
		GOOGLE_AI_STUDIO_API_KEY: string;
		OPENROUTER_API_KEY: string;
		CEREBRAS_API_KEY: string;
		GROQ_API_KEY: string;
		SANDBOX_SERVICE_API_KEY: string;
		SANDBOX_SERVICE_TYPE: string;
		SANDBOX_SERVICE_URL: string;
		CLOUDFLARE_API_TOKEN: string;
		CLOUDFLARE_ACCOUNT_ID: string;
		CLOUDFLARE_AI_GATEWAY_URL: string;
		CLOUDFLARE_AI_GATEWAY_TOKEN: string;
		SERPAPI_KEY: string;
		GOOGLE_CLIENT_SECRET: string;
		GOOGLE_CLIENT_ID: string;
		GITHUB_CLIENT_ID: string;
		GITHUB_CLIENT_SECRET: string;
		JWT_SECRET: string;
		ENTROPY_KEY: string;
		ENVIRONMENT: string;
		SECRETS_ENCRYPTION_KEY: string;
		MAX_SANDBOX_INSTANCES: string;
		SANDBOX_INSTANCE_TYPE: string;
		CUSTOM_DOMAIN: string;
		CUSTOM_PREVIEW_DOMAIN: string;
		ALLOCATION_STRATEGY: string;
		GITHUB_EXPORTER_CLIENT_ID: string;
		GITHUB_EXPORTER_CLIENT_SECRET: string;
		CF_ACCESS_ID: string;
		CF_ACCESS_SECRET: string;
		SENTRY_DSN: string;
		CodeGenObject: DurableObjectNamespace<import("./worker/index").CodeGeneratorAgent>;
		Sandbox: DurableObjectNamespace<import("./worker/index").UserAppSandboxService>;
		DORateLimitStore: DurableObjectNamespace<import("./worker/index").DORateLimitStore>;
		TEMPLATES_BUCKET: R2Bucket;
		DB: D1Database;
		DISPATCHER: DispatchNamespace;
		API_RATE_LIMITER: RateLimit;
		AUTH_RATE_LIMITER: RateLimit;
		AI: Ai;
		IMAGES: ImagesBinding;
		CF_VERSION_METADATA: WorkerVersionMetadata;
		ASSETS: Fetcher;
	}
}
interface Env extends Cloudflare.Env {}
type StringifyValues<EnvType extends Record<string, unknown>> = {
	[Binding in keyof EnvType]: EnvType[Binding] extends string ? EnvType[Binding] : string;
};
declare namespace NodeJS {
	interface ProcessEnv extends StringifyValues<Pick<Cloudflare.Env, "TEMPLATES_REPOSITORY" | "ALLOWED_EMAIL" | "DISPATCH_NAMESPACE" | "CLOUDFLARE_AI_GATEWAY" | "ENABLE_READ_REPLICAS" | "ANTHROPIC_API_KEY" | "OPENAI_API_KEY" | "GOOGLE_AI_STUDIO_API_KEY" | "OPENROUTER_API_KEY" | "CEREBRAS_API_KEY" | "GROQ_API_KEY" | "SANDBOX_SERVICE_API_KEY" | "SANDBOX_SERVICE_TYPE" | "SANDBOX_SERVICE_URL" | "CLOUDFLARE_API_TOKEN" | "CLOUDFLARE_ACCOUNT_ID" | "CLOUDFLARE_AI_GATEWAY_URL" | "CLOUDFLARE_AI_GATEWAY_TOKEN" | "SERPAPI_KEY" | "GOOGLE_CLIENT_SECRET" | "GOOGLE_CLIENT_ID" | "GITHUB_CLIENT_ID" | "GITHUB_CLIENT_SECRET" | "JWT_SECRET" | "ENTROPY_KEY" | "ENVIRONMENT" | "SECRETS_ENCRYPTION_KEY" | "MAX_SANDBOX_INSTANCES" | "SANDBOX_INSTANCE_TYPE" | "CUSTOM_DOMAIN" | "CUSTOM_PREVIEW_DOMAIN" | "ALLOCATION_STRATEGY" | "GITHUB_EXPORTER_CLIENT_ID" | "GITHUB_EXPORTER_CLIENT_SECRET" | "CF_ACCESS_ID" | "CF_ACCESS_SECRET" | "SENTRY_DSN">> {}
}
