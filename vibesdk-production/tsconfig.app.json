{
	"compilerOptions": {
		"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
		"target": "ES2022",
		"useDefineForClassFields": true,
		"lib": ["ES2022", "DOM", "DOM.Iterable"],
		"module": "ESNext",
		"skipLibCheck": true,
		// "types": ["./worker-configuration.d.ts", "vite/client", "jest"],

		/* Bundler mode */
		"moduleResolution": "bundler",
		"allowImportingTsExtensions": true,
		"isolatedModules": true,
		"verbatimModuleSyntax": false,
		"moduleDetection": "force",
		"noEmit": true,
		"jsx": "react-jsx",

		/* Linting */
		"strict": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noFallthroughCasesInSwitch": true,
		"noUncheckedSideEffectImports": true,
		"baseUrl": ".",
		"paths": {
		  "@/*": [
			"./src/*"
		  ],
          "shared/*": [
            "./shared/*"
          ],
          "worker/*": [
            "./worker/*"
          ]
		}
	},
	"include": ["src", "shared"],
}
