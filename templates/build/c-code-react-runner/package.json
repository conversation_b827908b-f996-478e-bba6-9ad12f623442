{"name": "c-code-react-runner", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port ${PORT:-3000}", "build": "vite build", "lint": "eslint --cache -f json --quiet .", "preview": "bun run build && vite preview --host 0.0.0.0 --port ${PORT:-4173}", "deploy": "bun run build && wrangler deploy", "cf-typegen": "wrangler types"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@typescript-eslint/typescript-estree": "^8.39.0", "chalk": "^5.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "framer-motion": "^12.23.0", "hono": "^4.9.8", "immer": "^10.1.1", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^9.8.0", "react-dom": "^18.3.1", "react-flow": "^1.0.3", "react-hook-form": "^7.60.0", "react-hotkeys-hook": "^5.1.0", "react-resizable-panels": "^3.0.3", "react-router-dom": "6.30.0", "react-select": "^5.10.2", "react-swipeable": "^7.0.2", "react-use": "^17.6.0", "recharts": "2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.5", "uuid": "^11.1.0", "vaul": "^1.1.2", "wrangler": "^4.39.0", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.9.4", "@cloudflare/workers-types": "^4.20250424.0", "@eslint/js": "^9.22.0", "@types/node": "^22.15.3", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "5.8", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}