@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Enhanced Color Palette */
    --background: 220 13% 98%;
    --foreground: 224 15% 15%;
    --card: 220 13% 98%;
    --card-foreground: 224 15% 15%;
    --popover: 220 13% 98%;
    --popover-foreground: 224 15% 15%;
    --primary: 262 83% 58%;  /* Enhanced purple */
    --primary-foreground: 210 40% 98%;
    --secondary: 220 14% 96%;
    --secondary-foreground: 224 15% 15%;
    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 45%;
    --accent: 262 83% 58%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 262 83% 58%;
    --radius: 0.75rem;

    /* Chart colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    
    /* Sidebar colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    
    /* Enhanced design tokens */
    --surface-1: 220 13% 98%;
    --surface-2: 220 14% 96%;
    --surface-3: 220 15% 94%;
    --shadow-color: 220 3% 15%;
    --gradient-1: linear-gradient(135deg, #F38020 0%, #E55A1B 100%);
    --gradient-2: linear-gradient(135deg, #F38020 0%, #E55A1B 50%, #D14615 100%);
    --gradient-3: linear-gradient(135deg, #F38020 0%, #E55A1B 25%, #D14615 50%, #ff6b35 100%);
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --border: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --input: 0 0% 14.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    
    /* Dark sidebar colors */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    
    /* Enhanced dark design tokens */
    --surface-1: 224 15% 8%;
    --surface-2: 224 15% 12%;
    --surface-3: 224 15% 16%;
    --shadow-color: 0 0% 0%;
  }
  
  /* Enhanced Base Styles */
  * {
    @apply border-border;
  }
  
  html {
    scroll-behavior: smooth;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }
  
  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: 'rlig' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Enhanced Typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-semibold tracking-tight;
  }
  
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }
  
  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }
  
  h3 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }
  
  h4 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }
  
  p {
    @apply leading-7 text-muted-foreground;
  }
  
  /* Enhanced Focus States */
  :focus-visible {
    @apply outline-2 outline-offset-2 outline-ring;
  }
  
  /* Selection Styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }
}

@layer components {
  /* Button Component Styles */
  .btn {
    @apply inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-border;
  }
  
  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }
  
  .btn-gradient {
    background: linear-gradient(135deg, #F38020 0%, #E55A1B 100%);
    @apply text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 bg-size-200 hover:bg-pos-100;
  }
  
  /* Card Component Styles */
  .card {
    @apply rounded-2xl border bg-card text-card-foreground shadow-sm transition-all duration-300;
  }
  
  .card-hover {
    @apply hover:shadow-lg hover:-translate-y-1;
  }
  
  .card-glass {
    @apply backdrop-blur-xl bg-white/10 border-white/20 shadow-2xl;
  }
  
  /* Input Component Styles */
  .input {
    @apply flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }
  
  .input-floating {
    @apply peer w-full border-0 border-b-2 border-border bg-transparent pt-4 pb-1.5 text-foreground placeholder-transparent focus:border-primary focus:outline-none;
  }
  
  /* Layout Components */
  .container {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section {
    @apply py-16 md:py-24 lg:py-32;
  }
  
  /* Animation Helper Classes */
  .animate-in {
    @apply animate-fade-in-up;
  }
  
  .stagger-1 {
    animation-delay: 0.1s;
  }
  
  .stagger-2 {
    animation-delay: 0.2s;
  }
  
  .stagger-3 {
    animation-delay: 0.3s;
  }
  
  .stagger-4 {
    animation-delay: 0.4s;
  }
}

@layer utilities {
  /* Background Utilities */
  .bg-gradient-primary {
    background: var(--gradient-1);
  }
  
  .bg-gradient-secondary {
    background: var(--gradient-2);
  }
  
  .bg-gradient-rainbow {
    background: var(--gradient-3);
  }
  
  .bg-size-200 {
    background-size: 200% 200%;
  }
  
  .bg-pos-0 {
    background-position: 0% 50%;
  }
  
  .bg-pos-100 {
    background-position: 100% 50%;
  }
  
  /* Text Utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  .text-pretty {
    text-wrap: pretty;
  }
  
  /* Spacing Utilities */
  .space-y-fluid > * + * {
    @apply mt-4 md:mt-6 lg:mt-8;
  }
  
  /* Shadow Utilities */
  .shadow-colored {
    box-shadow: 0 10px 25px -3px hsl(var(--shadow-color) / 0.1), 0 4px 6px -4px hsl(var(--shadow-color) / 0.1);
  }
  
  .shadow-primary {
    box-shadow: 0 10px 25px -3px hsl(var(--primary) / 0.3), 0 4px 6px -4px hsl(var(--primary) / 0.3);
  }
  
  /* Scrollbar Utilities */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-muted rounded-full;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
  
  /* Responsive Design Helpers */
  .responsive-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }
  
  .responsive-flex {
    @apply flex flex-col md:flex-row gap-6;
  }
  
  /* Animation Fill Modes */
  .animate-fill-both {
    animation-fill-mode: both;
  }
  
  .animate-fill-forwards {
    animation-fill-mode: forwards;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}