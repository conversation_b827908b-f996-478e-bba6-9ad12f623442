{"name": "c-code-dashboard-next-runner", "version": "0.1.0", "private": true, "type": "commonjs", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint -f json", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@headlessui/react": "^2.2.7", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-query": "^5.85.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "critters": "^0.0.25", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "immer": "^10.1.3", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "next": "15.5.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "next.js": "^1.0.3", "react": "^18.3.1", "react-day-picker": "^9.9.0", "react-dom": "^18.3.1", "react-flow": "^1.0.3", "react-hook-form": "^7.62.0", "react-resizable-panels": "^3.0.5", "react-select": "^5.10.2", "recharts": "2.15.4", "sonner": "^2.0.7", "swc-loader": "^0.2.6", "swr": "^2.3.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^4.1.5", "zustand": "^5.0.8"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250903.0", "@opennextjs/cloudflare": "^1.7.1", "@types/node": "^20.19.11", "@types/react": "^18.3.24", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-config-next": "14.2.18", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.9.2", "wrangler": "^4.33.2"}}