# Template Selection Guidelines

This template offers a streamlined and performant foundation for building beautiful, responsive landing pages with modern animations and iconography.

* Use this template when you need:
  * High-performance server-side heavy projects and dashboards
  * Pages optimized for SEO with server-rendered content
  * Responsive design with smooth scrolling and page transitions
  * Easily customizable layouts for product launches, waitlists, or portfolios
  * Design-first experiences with animation and interactivity

* Do not use it for:
  * Lightweight, mostly client side heavy projects
  * Static pages

* Built with:
  * **Next.js (Page Router)** for hybrid static & server rendering, built-in SEO, and routing
  * **Tailwind CSS** for rapid UI development with utility-first styling
  * **Lucide Icons** for sleek, consistent iconography
  * **Framer Motion** for intuitive, production-ready animations
  * **TypeScript** and **ESLint** for type safety and code quality