/**
 * Wrang<PERSON> config for the Cloudflare Durable Object and KV template
 * STRICTLY DO NOT MODIFY THIS FILE - Hidden from AI to prevent breaking core functionality
 */
 {
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "test-project",
	"main": "worker/index.ts",
	"compatibility_date": "2025-04-24",
	"assets": {
		"not_found_handling": "single-page-application",
		"run_worker_first": ["/api/*", "!/api/docs/*"]
	},
	"observability": {
		"enabled": true
	},
    "durable_objects": {
      "bindings": [
        {
          "name": "GlobalDurableObject",
          "class_name": "GlobalDurableObject"
        }
      ]
    },
    "migrations": [
      {
        "tag": "v1",
        "new_sqlite_classes": [
          "GlobalDurableObject"
        ]
      }
    ],
    "kv_namespaces": [
      {
        "binding": "KVStore",
        "id": "{{KV_ID}}"
      }
    ]
}
