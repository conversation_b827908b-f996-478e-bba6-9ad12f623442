[{"/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/eslint.config.js": "1", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/postcss.config.js": "2", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ErrorBoundary.tsx": "3", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ErrorFallback.tsx": "4", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/RouteErrorBoundary.tsx": "5", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ThemeToggle.tsx": "6", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/accordion.tsx": "7", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/alert-dialog.tsx": "8", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/alert.tsx": "9", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/aspect-ratio.tsx": "10", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/avatar.tsx": "11", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/badge.tsx": "12", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/breadcrumb.tsx": "13", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/button.tsx": "14", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/calendar.tsx": "15", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/card.tsx": "16", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/carousel.tsx": "17", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/chart.tsx": "18", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/checkbox.tsx": "19", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/collapsible.tsx": "20", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/command.tsx": "21", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/context-menu.tsx": "22", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/dialog.tsx": "23", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/drawer.tsx": "24", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/dropdown-menu.tsx": "25", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/form.tsx": "26", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/hover-card.tsx": "27", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/input-otp.tsx": "28", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/input.tsx": "29", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/label.tsx": "30", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/menubar.tsx": "31", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/navigation-menu.tsx": "32", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/pagination.tsx": "33", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/popover.tsx": "34", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/progress.tsx": "35", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/radio-group.tsx": "36", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/resizable.tsx": "37", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/scroll-area.tsx": "38", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/select.tsx": "39", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/separator.tsx": "40", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/sheet.tsx": "41", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/sidebar.tsx": "42", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/skeleton.tsx": "43", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/slider.tsx": "44", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/sonner.tsx": "45", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/switch.tsx": "46", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/table.tsx": "47", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/tabs.tsx": "48", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/textarea.tsx": "49", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/toggle-group.tsx": "50", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/toggle.tsx": "51", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/tooltip.tsx": "52", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/hooks/use-mobile.tsx": "53", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/hooks/use-theme.ts": "54", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/lib/errorReporter.ts": "55", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/lib/utils.ts": "56", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/main.tsx": "57", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/pages/HomePage.tsx": "58", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/tailwind.config.js": "59", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/vite.config.ts": "60", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/worker/core-utils.ts": "61", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/worker/index.ts": "62", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/worker/userRoutes.ts": "63", "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/vite-env.d.ts": "64"}, {"size": 2989, "mtime": 1757483665339, "results": "65", "hashOfConfig": "66"}, {"size": 80, "mtime": 1757483665338, "results": "67", "hashOfConfig": "66"}, {"size": 1975, "mtime": 1757483607918, "results": "68", "hashOfConfig": "69"}, {"size": 3549, "mtime": 1757483607916, "results": "70", "hashOfConfig": "69"}, {"size": 1841, "mtime": 1757483607913, "results": "71", "hashOfConfig": "69"}, {"size": 565, "mtime": 1757483607917, "results": "72", "hashOfConfig": "69"}, {"size": 2001, "mtime": 1757483599185, "results": "73", "hashOfConfig": "74"}, {"size": 4419, "mtime": 1757483599145, "results": "75", "hashOfConfig": "74"}, {"size": 1598, "mtime": 1757483599190, "results": "76", "hashOfConfig": "74"}, {"size": 140, "mtime": 1757483599144, "results": "77", "hashOfConfig": "74"}, {"size": 1419, "mtime": 1757483599209, "results": "78", "hashOfConfig": "74"}, {"size": 1140, "mtime": 1757483599212, "results": "79", "hashOfConfig": "74"}, {"size": 2712, "mtime": 1757483599201, "results": "80", "hashOfConfig": "74"}, {"size": 1902, "mtime": 1757483599217, "results": "81", "hashOfConfig": "74"}, {"size": 7555, "mtime": 1757483599200, "results": "82", "hashOfConfig": "74"}, {"size": 1828, "mtime": 1757483599152, "results": "83", "hashOfConfig": "74"}, {"size": 6210, "mtime": 1757483599229, "results": "84", "hashOfConfig": "74"}, {"size": 10481, "mtime": 1757483599164, "results": "85", "hashOfConfig": "74"}, {"size": 1012, "mtime": 1757483599218, "results": "86", "hashOfConfig": "74"}, {"size": 329, "mtime": 1757483599219, "results": "87", "hashOfConfig": "74"}, {"size": 4873, "mtime": 1757483599205, "results": "88", "hashOfConfig": "74"}, {"size": 7406, "mtime": 1757483599226, "results": "89", "hashOfConfig": "74"}, {"size": 3849, "mtime": 1757483599211, "results": "90", "hashOfConfig": "74"}, {"size": 3021, "mtime": 1757483599185, "results": "91", "hashOfConfig": "74"}, {"size": 7592, "mtime": 1757483599219, "results": "92", "hashOfConfig": "74"}, {"size": 4132, "mtime": 1757483599227, "results": "93", "hashOfConfig": "74"}, {"size": 1251, "mtime": 1757483599164, "results": "94", "hashOfConfig": "74"}, {"size": 2143, "mtime": 1757483599162, "results": "95", "hashOfConfig": "74"}, {"size": 768, "mtime": 1757483599223, "results": "96", "hashOfConfig": "74"}, {"size": 710, "mtime": 1757483599178, "results": "97", "hashOfConfig": "74"}, {"size": 8622, "mtime": 1757483599210, "results": "98", "hashOfConfig": "74"}, {"size": 5124, "mtime": 1757483599181, "results": "99", "hashOfConfig": "74"}, {"size": 2751, "mtime": 1757483599148, "results": "100", "hashOfConfig": "74"}, {"size": 1342, "mtime": 1757483599157, "results": "101", "hashOfConfig": "74"}, {"size": 792, "mtime": 1757483599159, "results": "102", "hashOfConfig": "74"}, {"size": 1410, "mtime": 1757483599204, "results": "103", "hashOfConfig": "74"}, {"size": 1723, "mtime": 1757483599174, "results": "104", "hashOfConfig": "74"}, {"size": 1642, "mtime": 1757483599169, "results": "105", "hashOfConfig": "74"}, {"size": 5745, "mtime": 1757483599222, "results": "106", "hashOfConfig": "74"}, {"size": 756, "mtime": 1757483599214, "results": "107", "hashOfConfig": "74"}, {"size": 4280, "mtime": 1757483599165, "results": "108", "hashOfConfig": "74"}, {"size": 23572, "mtime": 1757483607912, "results": "109", "hashOfConfig": "74"}, {"size": 266, "mtime": 1757483599226, "results": "110", "hashOfConfig": "74"}, {"size": 1037, "mtime": 1757483599155, "results": "111", "hashOfConfig": "74"}, {"size": 908, "mtime": 1757483607908, "results": "112", "hashOfConfig": "74"}, {"size": 1148, "mtime": 1757483599199, "results": "113", "hashOfConfig": "74"}, {"size": 2859, "mtime": 1757483599213, "results": "114", "hashOfConfig": "74"}, {"size": 1877, "mtime": 1757483599151, "results": "115", "hashOfConfig": "74"}, {"size": 649, "mtime": 1757483599223, "results": "116", "hashOfConfig": "74"}, {"size": 1753, "mtime": 1757483599206, "results": "117", "hashOfConfig": "74"}, {"size": 1486, "mtime": 1757483599217, "results": "118", "hashOfConfig": "74"}, {"size": 1267, "mtime": 1757483599188, "results": "119", "hashOfConfig": "74"}, {"size": 565, "mtime": 1757483599231, "results": "120", "hashOfConfig": "69"}, {"size": 662, "mtime": 1757522396572, "results": "121", "hashOfConfig": "69"}, {"size": 22970, "mtime": 1757483614324, "results": "122", "hashOfConfig": "69"}, {"size": 166, "mtime": 1757483614324, "results": "123", "hashOfConfig": "69"}, {"size": 741, "mtime": 1757626561299, "results": "124", "hashOfConfig": "69"}, {"size": 5083, "mtime": 1757631725679, "results": "125", "hashOfConfig": "69"}, {"size": 5962, "mtime": 1757483665338, "results": "126", "hashOfConfig": "66"}, {"size": 1249, "mtime": 1757626915915, "results": "127", "hashOfConfig": "69"}, {"size": 214, "mtime": 1757627853930, "results": "128", "hashOfConfig": "69"}, {"size": 2243, "mtime": 1757627443065, "results": "129", "hashOfConfig": "69"}, {"size": 300, "mtime": 1757627479879, "results": "130", "hashOfConfig": "69"}, {"size": 37, "mtime": 1757627938528, "results": "131", "hashOfConfig": "69"}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "oqoahq", {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bxv0e7", {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "174sxev", {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/eslint.config.js", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/postcss.config.js", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ErrorFallback.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/RouteErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ThemeToggle.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/accordion.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/alert-dialog.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/alert.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/aspect-ratio.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/avatar.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/breadcrumb.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/button.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/calendar.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/card.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/carousel.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/chart.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/checkbox.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/collapsible.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/command.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/context-menu.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/dialog.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/drawer.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/dropdown-menu.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/form.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/hover-card.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/input-otp.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/input.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/label.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/menubar.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/navigation-menu.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/pagination.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/popover.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/progress.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/radio-group.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/resizable.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/scroll-area.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/select.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/separator.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/sheet.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/sidebar.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/skeleton.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/slider.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/sonner.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/switch.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/table.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/tabs.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/textarea.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/toggle-group.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/toggle.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/components/ui/tooltip.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/hooks/use-mobile.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/hooks/use-theme.ts", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/lib/errorReporter.ts", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/lib/utils.ts", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/main.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/pages/HomePage.tsx", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/tailwind.config.js", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/vite.config.ts", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/worker/core-utils.ts", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/worker/index.ts", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/worker/userRoutes.ts", [], [], "/Users/<USER>/Desktop/cloudflare coder templates/reference/vite-reference/src/vite-env.d.ts", [], []]