@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Core Colors */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --border: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --input: 0 0% 89.8%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --border: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --input: 0 0% 14.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  
  * {
    @apply border-border;
  }
  
  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    text-rendering: optimizeLegibility;
  }
}

@layer components {
  /* Button System */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-gradient {
    @apply btn text-white shadow-primary hover:shadow-glow hover:scale-105 active:scale-95;
    background: linear-gradient(135deg, #F38020 0%, #E55A1B 100%);
  }
  
  /* Gradient System */
  .bg-gradient-primary {
    background: linear-gradient(135deg, #F38020 0%, #E55A1B 50%, #D14615 100%);
  }
  
  .bg-gradient-rainbow {
    background: linear-gradient(135deg, #F38020 0%, #E55A1B 25%, #D14615 50%, #ff6b35 100%);
  }
  
  /* Text System */
  .text-gradient {
    background: linear-gradient(90deg, #F38020 0%, #E55A1B 50%, #D14615 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  .text-pretty {
    text-wrap: pretty;
  }
  
  /* Glass Morphism */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20 shadow-glass;
  }
  
  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-white/10 shadow-glass;
  }
  
  /* Interactive Effects */
  .floating {
    animation: float 2s ease-in-out infinite;
  }
  
  .rotating {
    animation: rotate 2s ease-in-out infinite;
  }
  
  @keyframes float {
    0%, 100% { 
      transform: translateY(-4px);
    }
    50% { 
      transform: translateY(4px);
    }
  }
  
  @keyframes rotate {
    0% { 
      transform: rotate(0deg);
    }
    100% { 
      transform: rotate(360deg);
    }
  }
  
  .shimmer-bg {
    @apply relative overflow-hidden;
  }
  
  .shimmer-bg::before {
    @apply absolute inset-0 -translate-x-full animate-shimmer bg-gradient-to-r from-transparent via-white/20 to-transparent;
    content: '';
  }
  
  /* Layout Utilities */
  .center {
    @apply flex items-center justify-center;
  }
  
  .center-col {
    @apply flex flex-col items-center justify-center;
  }
  
  /* Responsive Typography */
  .text-display {
    @apply text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-display font-bold leading-tight;
  }
  
  .text-body {
    @apply text-base sm:text-lg text-muted-foreground leading-relaxed;
  }
}