{"_chunk-NNGBXDMY-DzOFPLdZ.js": {"file": "assets/chunk-NNGBXDMY-DzOFPLdZ.js", "name": "chunk-NNGBXDMY"}, "_utils-DHKSaTNJ.js": {"file": "assets/utils-DHKSaTNJ.js", "name": "utils"}, "node_modules/@cloudflare/sandbox/dist/sse-parser.js": {"file": "assets/sse-parser-CDiCl5f3.js", "name": "sse-parser", "src": "node_modules/@cloudflare/sandbox/dist/sse-parser.js", "isDynamicEntry": true, "imports": ["_chunk-NNGBXDMY-DzOFPLdZ.js"]}, "node_modules/mimetext/dist/mimetext.node.es.js": {"file": "assets/mimetext.node.es-C7m2cC2Z.js", "name": "mimetext.node.es", "src": "node_modules/mimetext/dist/mimetext.node.es.js", "isDynamicEntry": true, "imports": ["_utils-DHKSaTNJ.js"]}, "virtual:cloudflare/worker-entry": {"file": "index.js", "name": "index", "src": "virtual:cloudflare/worker-entry", "isEntry": true, "imports": ["_chunk-NNGBXDMY-DzOFPLdZ.js", "_utils-DHKSaTNJ.js"], "dynamicImports": ["node_modules/mimetext/dist/mimetext.node.es.js", "node_modules/@cloudflare/sandbox/dist/sse-parser.js"]}}