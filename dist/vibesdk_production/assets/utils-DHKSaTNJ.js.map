{"version": 3, "file": "utils-DHKSaTNJ.js", "names": [], "sources": ["../../../node_modules/unenv/dist/runtime/_internal/utils.mjs"], "sourcesContent": ["/* @__NO_SIDE_EFFECTS__ */\nexport function rawHeaders(headers) {\n\tconst rawHeaders = [];\n\tfor (const key in headers) {\n\t\tif (Array.isArray(headers[key])) {\n\t\t\tfor (const h of headers[key]) {\n\t\t\t\trawHeaders.push(key, h);\n\t\t\t}\n\t\t} else {\n\t\t\trawHeaders.push(key, headers[key]);\n\t\t}\n\t}\n\treturn rawHeaders;\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function mergeFns(...functions) {\n\treturn function(...args) {\n\t\tfor (const fn of functions) {\n\t\t\tfn(...args);\n\t\t}\n\t};\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function createNotImplementedError(name) {\n\treturn new Error(`[unenv] ${name} is not implemented yet!`);\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function notImplemented(name) {\n\tconst fn = () => {\n\t\tthrow createNotImplementedError(name);\n\t};\n\treturn Object.assign(fn, { __unenv__: true });\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function notImplementedAsync(name) {\n\tconst fn = notImplemented(name);\n\tfn.__promisify__ = () => notImplemented(name + \".__promisify__\");\n\tfn.native = fn;\n\treturn fn;\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function notImplementedClass(name) {\n\treturn class {\n\t\t__unenv__ = true;\n\t\tconstructor() {\n\t\t\tthrow new Error(`[unenv] ${name} is not implemented yet!`);\n\t\t}\n\t};\n}\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,SAAgB,0BAA0B,MAAM;AAC/C,wBAAO,IAAI,MAAM,WAAW,KAAK,0BAA0B;;;AAG5D,SAAgB,eAAe,MAAM;CACpC,MAAM,WAAW;AAChB,QAAM,0CAA0B,KAAK;;AAEtC,QAAO,OAAO,OAAO,IAAI,EAAE,WAAW,MAAM,CAAC;;;AAU9C,SAAgB,oBAAoB,MAAM;AACzC,QAAO,MAAM;EACZ,YAAY;EACZ,cAAc;AACb,SAAM,IAAI,MAAM,WAAW,KAAK,0BAA0B"}