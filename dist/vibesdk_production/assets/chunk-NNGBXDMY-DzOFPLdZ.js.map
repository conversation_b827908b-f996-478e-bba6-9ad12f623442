{"version": 3, "file": "chunk-NNGBXDMY-DzOFPLdZ.js", "names": [], "sources": ["../../../node_modules/@cloudflare/sandbox/dist/chunk-NNGBXDMY.js"], "sourcesContent": ["// src/sse-parser.ts\nasync function* parseSSEStream(stream, signal) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  let buffer = \"\";\n  try {\n    while (true) {\n      if (signal?.aborted) {\n        throw new Error(\"Operation was aborted\");\n      }\n      const { done, value } = await reader.read();\n      if (done) break;\n      buffer += decoder.decode(value, { stream: true });\n      const lines = buffer.split(\"\\n\");\n      buffer = lines.pop() || \"\";\n      for (const line of lines) {\n        if (line.trim() === \"\") continue;\n        if (line.startsWith(\"data: \")) {\n          const data = line.substring(6);\n          if (data === \"[DONE]\" || data.trim() === \"\") continue;\n          try {\n            const event = JSON.parse(data);\n            yield event;\n          } catch (error) {\n            console.error(\"Failed to parse SSE event:\", data, error);\n          }\n        }\n      }\n    }\n    if (buffer.trim() && buffer.startsWith(\"data: \")) {\n      const data = buffer.substring(6);\n      if (data !== \"[DONE]\" && data.trim()) {\n        try {\n          const event = JSON.parse(data);\n          yield event;\n        } catch (error) {\n          console.error(\"Failed to parse final SSE event:\", data, error);\n        }\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n}\nasync function* responseToAsyncIterable(response, signal) {\n  if (!response.ok) {\n    throw new Error(`Response not ok: ${response.status} ${response.statusText}`);\n  }\n  if (!response.body) {\n    throw new Error(\"No response body\");\n  }\n  yield* parseSSEStream(response.body, signal);\n}\nfunction asyncIterableToSSEStream(events, options) {\n  const encoder = new TextEncoder();\n  const serialize = options?.serialize || JSON.stringify;\n  return new ReadableStream({\n    async start(controller) {\n      try {\n        for await (const event of events) {\n          if (options?.signal?.aborted) {\n            controller.error(new Error(\"Operation was aborted\"));\n            break;\n          }\n          const data = serialize(event);\n          const sseEvent = `data: ${data}\n\n`;\n          controller.enqueue(encoder.encode(sseEvent));\n        }\n        controller.enqueue(encoder.encode(\"data: [DONE]\\n\\n\"));\n      } catch (error) {\n        controller.error(error);\n      } finally {\n        controller.close();\n      }\n    },\n    cancel() {\n      console.log(\"SSE stream cancelled\");\n    }\n  });\n}\n\nexport {\n  parseSSEStream,\n  responseToAsyncIterable,\n  asyncIterableToSSEStream\n};\n//# sourceMappingURL=chunk-NNGBXDMY.js.map"], "x_google_ignoreList": [0], "mappings": "AACA,gBAAgB,eAAe,QAAQ,QAAQ;CAC7C,MAAM,SAAS,OAAO,WAAW;CACjC,MAAM,UAAU,IAAI,aAAa;CACjC,IAAI,SAAS;AACb,KAAI;AACF,SAAO,MAAM;AACX,OAAI,QAAQ,QACV,OAAM,IAAI,MAAM,wBAAwB;GAE1C,MAAM,EAAE,MAAM,UAAU,MAAM,OAAO,MAAM;AAC3C,OAAI,KAAM;AACV,aAAU,QAAQ,OAAO,OAAO,EAAE,QAAQ,MAAM,CAAC;GACjD,MAAM,QAAQ,OAAO,MAAM,KAAK;AAChC,YAAS,MAAM,KAAK,IAAI;AACxB,QAAK,MAAM,QAAQ,OAAO;AACxB,QAAI,KAAK,MAAM,KAAK,GAAI;AACxB,QAAI,KAAK,WAAW,SAAS,EAAE;KAC7B,MAAM,OAAO,KAAK,UAAU,EAAE;AAC9B,SAAI,SAAS,YAAY,KAAK,MAAM,KAAK,GAAI;AAC7C,SAAI;AAEF,YADc,KAAK,MAAM,KAAK;cAEvB,OAAO;AACd,cAAQ,MAAM,8BAA8B,MAAM,MAAM;;;;;AAKhE,MAAI,OAAO,MAAM,IAAI,OAAO,WAAW,SAAS,EAAE;GAChD,MAAM,OAAO,OAAO,UAAU,EAAE;AAChC,OAAI,SAAS,YAAY,KAAK,MAAM,CAClC,KAAI;AAEF,UADc,KAAK,MAAM,KAAK;YAEvB,OAAO;AACd,YAAQ,MAAM,oCAAoC,MAAM,MAAM;;;WAI5D;AACR,SAAO,aAAa"}