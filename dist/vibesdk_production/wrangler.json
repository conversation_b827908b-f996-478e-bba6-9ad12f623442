{"configPath": "/Users/<USER>/Desktop/vibe/wrangler.jsonc", "userConfigPath": "/Users/<USER>/Desktop/vibe/wrangler.jsonc", "topLevelName": "vibesdk-production", "definedEnvironments": [], "legacy_env": true, "compatibility_date": "2025-08-10", "compatibility_flags": ["nodejs_compat"], "jsx_factory": "React.createElement", "jsx_fragment": "React.Fragment", "rules": [{"type": "ESModule", "globs": ["**/*.js", "**/*.mjs"]}], "name": "vibesdk-production", "main": "index.js", "routes": [{"pattern": "vibe.stokeleads.com", "custom_domain": true}, {"pattern": "*vibe.stokeleads.com/*", "custom_domain": false, "zone_id": "beae95447dc85f1334a30bf0ba4f6ff7"}], "triggers": {}, "assets": {"directory": "../client", "not_found_handling": "single-page-application", "run_worker_first": true, "binding": "ASSETS"}, "workers_dev": false, "preview_urls": false, "vars": {"TEMPLATES_REPOSITORY": "https://github.com/cloudflare/vibesdk-templates", "ALLOWED_EMAIL": "<EMAIL>", "ENABLE_READ_REPLICAS": "true", "CLOUDFLARE_AI_GATEWAY": "vibesdk-gateway", "CUSTOM_DOMAIN": "vibe.stokeleads.com", "MAX_SANDBOX_INSTANCES": "10", "SANDBOX_INSTANCE_TYPE": "standard"}, "durable_objects": {"bindings": [{"class_name": "CodeGeneratorAgent", "name": "CodeGenObject"}, {"class_name": "UserAppSandboxService", "name": "Sandbox"}, {"class_name": "DORateLimitStore", "name": "DORateLimitStore"}]}, "workflows": [], "migrations": [{"new_sqlite_classes": ["CodeGeneratorAgent", "UserAppSandboxService"], "tag": "v1"}, {"new_sqlite_classes": ["DORateLimitStore"], "tag": "v2"}], "kv_namespaces": [{"binding": "VibecoderStore", "id": "3c361d46d85445cdafab20aaaa248c21", "remote": true}], "cloudchamber": {}, "containers": [{"class_name": "UserAppSandboxService", "image": "/Users/<USER>/Desktop/vibe/SandboxDockerfile", "max_instances": 10, "instance_type": "standard", "rollout_step_percentage": 100, "name": "vibesdk-production-userappsandboxservice", "image_build_context": "/Users/<USER>/Desktop/vibe"}], "send_email": [], "queues": {"producers": [], "consumers": []}, "r2_buckets": [{"binding": "TEMPLATES_BUCKET", "bucket_name": "vibesdk-templates", "remote": true}], "d1_databases": [{"binding": "DB", "database_name": "vibesdk-db", "database_id": "f0fdead0-ff73-4fd5-ac74-6ca9b646f50b", "migrations_dir": "migrations", "remote": true}], "vectorize": [], "hyperdrive": [], "services": [], "analytics_engine_datasets": [], "dispatch_namespaces": [], "mtls_certificates": [], "unsafe": {"bindings": [{"name": "API_RATE_LIMITER", "type": "ratelimit", "namespace_id": "2101", "simple": {"limit": 10000, "period": 60}}, {"name": "AUTH_RATE_LIMITER", "type": "ratelimit", "namespace_id": "2102", "simple": {"limit": 1000, "period": 60}}]}, "ai": {"binding": "AI", "remote": true}, "images": {"binding": "IMAGES"}, "pipelines": [], "secrets_store_secrets": [], "unsafe_hello_world": [], "ratelimits": [], "version_metadata": {"binding": "CF_VERSION_METADATA"}, "logfwdr": {"bindings": []}, "observability": {"enabled": true, "head_sampling_rate": 1}, "dev": {"ip": "localhost", "local_protocol": "http", "upstream_protocol": "http", "enable_containers": true}, "no_bundle": true}