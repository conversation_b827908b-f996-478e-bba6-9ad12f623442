/*!-----------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation. All rights reserved.
* Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
* Released under the MIT license
* https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
*-----------------------------------------------------------------------------*/
var e={comments:{lineComment:`#`}},t={defaultToken:`keyword`,ignoreCase:!0,tokenPostfix:`.azcli`,str:/[^#\s]/,tokenizer:{root:[{include:`@comment`},[/\s-+@str*\s*/,{cases:{"@eos":{token:`key.identifier`,next:`@popall`},"@default":{token:`key.identifier`,next:`@type`}}}],[/^-+@str*\s*/,{cases:{"@eos":{token:`key.identifier`,next:`@popall`},"@default":{token:`key.identifier`,next:`@type`}}}]],type:[{include:`@comment`},[/-+@str*\s*/,{cases:{"@eos":{token:`key.identifier`,next:`@popall`},"@default":`key.identifier`}}],[/@str+\s*/,{cases:{"@eos":{token:`string`,next:`@popall`},"@default":`string`}}]],comment:[[/#.*$/,{cases:{"@eos":{token:`comment`,next:`@popall`}}}]]}};export{e as conf,t as language};
//# sourceMappingURL=azcli-C-qqIyZJ.js.map