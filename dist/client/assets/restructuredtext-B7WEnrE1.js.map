{"version": 3, "file": "restructuredtext-B7WEnrE1.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/restructuredtext/restructuredtext.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"(\", close: \")\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*<!--\\\\s*#?region\\\\b.*-->\"),\n      end: new RegExp(\"^\\\\s*<!--\\\\s*#?endregion\\\\b.*-->\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".rst\",\n  control: /[\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  escapes: /\\\\(?:@control)/,\n  empty: [\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"link\",\n    \"meta\",\n    \"param\"\n  ],\n  alphanumerics: /[A-Za-z0-9]/,\n  simpleRefNameWithoutBq: /(?:@alphanumerics[-_+:.]*@alphanumerics)+|(?:@alphanumerics+)/,\n  simpleRefName: /(?:`@phrase`|@simpleRefNameWithoutBq)/,\n  phrase: /@simpleRefNameWithoutBq(?:\\s@simpleRefNameWithoutBq)*/,\n  citationName: /[A-Za-z][A-Za-z0-9-_.]*/,\n  blockLiteralStart: /(?:[!\"#$%&'()*+,-./:;<=>?@\\[\\]^_`{|}~]|[\\s])/,\n  precedingChars: /(?:[ -:/'\"<([{])/,\n  followingChars: /(?:[ -.,:;!?/'\")\\]}>]|$)/,\n  punctuation: /(=|-|~|`|#|\"|\\^|\\+|\\*|:|\\.|'|_|\\+)/,\n  tokenizer: {\n    root: [\n      //sections\n      [/^(@punctuation{3,}$){1,1}?/, \"keyword\"],\n      //line-blocks\n      //No rules on it\n      //bullet-lists\n      [/^\\s*([\\*\\-+‣•]|[a-zA-Z0-9]+\\.|\\([a-zA-Z0-9]+\\)|[a-zA-Z0-9]+\\))\\s/, \"keyword\"],\n      //literal-blocks\n      [/([ ]::)\\s*$/, \"keyword\", \"@blankLineOfLiteralBlocks\"],\n      [/(::)\\s*$/, \"keyword\", \"@blankLineOfLiteralBlocks\"],\n      { include: \"@tables\" },\n      { include: \"@explicitMarkupBlocks\" },\n      { include: \"@inlineMarkup\" }\n    ],\n    explicitMarkupBlocks: [\n      //citations\n      { include: \"@citations\" },\n      //footnotes\n      { include: \"@footnotes\" },\n      //directives\n      [\n        /^(\\.\\.\\s)(@simpleRefName)(::\\s)(.*)$/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"keyword\", \"\", \"\"]\n      ],\n      //hyperlink-targets\n      [\n        /^(\\.\\.)(\\s+)(_)(@simpleRefName)(:)(\\s+)(.*)/,\n        [{ token: \"\", next: \"hyperlinks\" }, \"\", \"\", \"string.link\", \"\", \"\", \"string.link\"]\n      ],\n      //anonymous-hyperlinks\n      [\n        /^((?:(?:\\.\\.)(?:\\s+))?)(__)(:)(\\s+)(.*)/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"\", \"\", \"\", \"string.link\"]\n      ],\n      [/^(__\\s+)(.+)/, [\"\", \"string.link\"]],\n      //substitution-definitions\n      [\n        /^(\\.\\.)( \\|)([^| ]+[^|]*[^| ]*)(\\| )(@simpleRefName)(:: .*)/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"\", \"string.link\", \"\", \"keyword\", \"\"],\n        \"@rawBlocks\"\n      ],\n      [/(\\|)([^| ]+[^|]*[^| ]*)(\\|_{0,2})/, [\"\", \"string.link\", \"\"]],\n      //comments\n      [/^(\\.\\.)([ ].*)$/, [{ token: \"\", next: \"@comments\" }, \"comment\"]]\n    ],\n    inlineMarkup: [\n      { include: \"@citationsReference\" },\n      { include: \"@footnotesReference\" },\n      //hyperlink-references\n      [/(@simpleRefName)(_{1,2})/, [\"string.link\", \"\"]],\n      //embedded-uris-and-aliases\n      [/(`)([^<`]+\\s+)(<)(.*)(>)(`)(_)/, [\"\", \"string.link\", \"\", \"string.link\", \"\", \"\", \"\"]],\n      //emphasis\n      [/\\*\\*([^\\\\*]|\\*(?!\\*))+\\*\\*/, \"strong\"],\n      [/\\*[^*]+\\*/, \"emphasis\"],\n      //inline-literals\n      [/(``)((?:[^`]|\\`(?!`))+)(``)/, [\"\", \"keyword\", \"\"]],\n      [/(__\\s+)(.+)/, [\"\", \"keyword\"]],\n      //interpreted-text\n      [/(:)((?:@simpleRefNameWithoutBq)?)(:`)([^`]+)(`)/, [\"\", \"keyword\", \"\", \"\", \"\"]],\n      [/(`)([^`]+)(`:)((?:@simpleRefNameWithoutBq)?)(:)/, [\"\", \"\", \"\", \"keyword\", \"\"]],\n      [/(`)([^`]+)(`)/, \"\"],\n      //inline-internal-targets\n      [/(_`)(@phrase)(`)/, [\"\", \"string.link\", \"\"]]\n    ],\n    citations: [\n      [\n        /^(\\.\\.\\s+\\[)((?:@citationName))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ]\n    ],\n    citationsReference: [[/(\\[)(@citationName)(\\]_)/, [\"\", \"string.link\", \"\"]]],\n    footnotes: [\n      [\n        /^(\\.\\.\\s+\\[)((?:[0-9]+))(\\]\\s+.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\"]\n      ],\n      [\n        /^(\\.\\.\\s+\\[)((?:#@simpleRefName?))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ],\n      [\n        /^(\\.\\.\\s+\\[)((?:\\*))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ]\n    ],\n    footnotesReference: [\n      [/(\\[)([0-9]+)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]],\n      [/(\\[)(#@simpleRefName?)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]],\n      [/(\\[)(\\*)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]]\n    ],\n    blankLineOfLiteralBlocks: [\n      [/^$/, \"\", \"@subsequentLinesOfLiteralBlocks\"],\n      [/^.*$/, \"\", \"@pop\"]\n    ],\n    subsequentLinesOfLiteralBlocks: [\n      [/(@blockLiteralStart+)(.*)/, [\"keyword\", \"\"]],\n      [/^(?!blockLiteralStart)/, \"\", \"@popall\"]\n    ],\n    subsequentLines: [\n      [/^[\\s]+.*/, \"\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    hyperlinks: [\n      [/^[\\s]+.*/, \"string.link\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    comments: [\n      [/^[\\s]+.*/, \"comment\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    tables: [\n      [/\\+-[+-]+/, \"keyword\"],\n      [/\\+=[+=]+/, \"keyword\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC7C,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,gCAAgC,CAClD,IAAS,OAAO,mCAAmC,CACpD,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,OACd,QAAS,yBACT,QAAS,iBACT,MAAO,CACL,OACA,OACA,WACA,KACA,MACA,QACA,KACA,MACA,QACA,UACA,OACA,OACA,QACD,CACD,cAAe,cACf,uBAAwB,gEACxB,cAAe,wCACf,OAAQ,wDACR,aAAc,0BACd,kBAAmB,+CACnB,eAAgB,mBAChB,eAAgB,2BAChB,YAAa,qCACb,UAAW,CACT,KAAM,CAEJ,CAAC,6BAA8B,UAAU,CAIzC,CAAC,mEAAoE,UAAU,CAE/E,CAAC,cAAe,UAAW,4BAA4B,CACvD,CAAC,WAAY,UAAW,4BAA4B,CACpD,CAAE,QAAS,UAAW,CACtB,CAAE,QAAS,wBAAyB,CACpC,CAAE,QAAS,gBAAiB,CAC7B,CACD,qBAAsB,CAEpB,CAAE,QAAS,aAAc,CAEzB,CAAE,QAAS,aAAc,CAEzB,CACE,uCACA,CAAC,CAAE,MAAO,GAAI,KAAM,kBAAmB,CAAE,UAAW,GAAI,GAAG,CAC5D,CAED,CACE,8CACA,CAAC,CAAE,MAAO,GAAI,KAAM,aAAc,CAAE,GAAI,GAAI,cAAe,GAAI,GAAI,cAAc,CAClF,CAED,CACE,0CACA,CAAC,CAAE,MAAO,GAAI,KAAM,kBAAmB,CAAE,GAAI,GAAI,GAAI,cAAc,CACpE,CACD,CAAC,eAAgB,CAAC,GAAI,cAAc,CAAC,CAErC,CACE,8DACA,CAAC,CAAE,MAAO,GAAI,KAAM,kBAAmB,CAAE,GAAI,cAAe,GAAI,UAAW,GAAG,CAC9E,aACD,CACD,CAAC,oCAAqC,CAAC,GAAI,cAAe,GAAG,CAAC,CAE9D,CAAC,kBAAmB,CAAC,CAAE,MAAO,GAAI,KAAM,YAAa,CAAE,UAAU,CAAC,CACnE,CACD,aAAc,CACZ,CAAE,QAAS,sBAAuB,CAClC,CAAE,QAAS,sBAAuB,CAElC,CAAC,2BAA4B,CAAC,cAAe,GAAG,CAAC,CAEjD,CAAC,iCAAkC,CAAC,GAAI,cAAe,GAAI,cAAe,GAAI,GAAI,GAAG,CAAC,CAEtF,CAAC,6BAA8B,SAAS,CACxC,CAAC,YAAa,WAAW,CAEzB,CAAC,8BAA+B,CAAC,GAAI,UAAW,GAAG,CAAC,CACpD,CAAC,cAAe,CAAC,GAAI,UAAU,CAAC,CAEhC,CAAC,kDAAmD,CAAC,GAAI,UAAW,GAAI,GAAI,GAAG,CAAC,CAChF,CAAC,kDAAmD,CAAC,GAAI,GAAI,GAAI,UAAW,GAAG,CAAC,CAChF,CAAC,gBAAiB,GAAG,CAErB,CAAC,mBAAoB,CAAC,GAAI,cAAe,GAAG,CAAC,CAC9C,CACD,UAAW,CACT,CACE,6CACA,CAAC,CAAE,MAAO,GAAI,KAAM,mBAAoB,CAAE,cAAe,GAAI,GAAG,CACjE,CACF,CACD,mBAAoB,CAAC,CAAC,2BAA4B,CAAC,GAAI,cAAe,GAAG,CAAC,CAAC,CAC3E,UAAW,CACT,CACE,oCACA,CAAC,CAAE,MAAO,GAAI,KAAM,mBAAoB,CAAE,cAAe,GAAG,CAC7D,CACD,CACE,gDACA,CAAC,CAAE,MAAO,GAAI,KAAM,mBAAoB,CAAE,cAAe,GAAI,GAAG,CACjE,CACD,CACE,kCACA,CAAC,CAAE,MAAO,GAAI,KAAM,mBAAoB,CAAE,cAAe,GAAI,GAAG,CACjE,CACF,CACD,mBAAoB,CAClB,CAAC,sBAAuB,CAAC,GAAI,cAAe,GAAI,GAAG,CAAC,CACpD,CAAC,gCAAiC,CAAC,GAAI,cAAe,GAAI,GAAG,CAAC,CAC9D,CAAC,kBAAmB,CAAC,GAAI,cAAe,GAAI,GAAG,CAAC,CACjD,CACD,yBAA0B,CACxB,CAAC,KAAM,GAAI,kCAAkC,CAC7C,CAAC,OAAQ,GAAI,OAAO,CACrB,CACD,+BAAgC,CAC9B,CAAC,4BAA6B,CAAC,UAAW,GAAG,CAAC,CAC9C,CAAC,yBAA0B,GAAI,UAAU,CAC1C,CACD,gBAAiB,CACf,CAAC,WAAY,GAAG,CAChB,CAAC,UAAW,GAAI,OAAO,CACxB,CACD,WAAY,CACV,CAAC,WAAY,cAAc,CAC3B,CAAC,UAAW,GAAI,OAAO,CACxB,CACD,SAAU,CACR,CAAC,WAAY,UAAU,CACvB,CAAC,UAAW,GAAI,OAAO,CACxB,CACD,OAAQ,CACN,CAAC,WAAY,UAAU,CACvB,CAAC,WAAY,UAAU,CACxB,CACF,CACF"}