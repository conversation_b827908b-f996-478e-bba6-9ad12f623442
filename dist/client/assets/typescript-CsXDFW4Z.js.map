{"version": 3, "file": "typescript-CsXDFW4Z.js", "names": ["monaco_editor_core_star"], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/typescript/typescript.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/typescript/typescript.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  onEnterRules: [\n    {\n      // e.g. /** | */\n      beforeText: /^\\s*\\/\\*\\*(?!\\/)([^\\*]|\\*(?!\\/))*$/,\n      afterText: /^\\s*\\*\\/$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent,\n        appendText: \" * \"\n      }\n    },\n    {\n      // e.g. /** ...|\n      beforeText: /^\\s*\\/\\*\\*(?!\\/)([^\\*]|\\*(?!\\/))*$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.None,\n        appendText: \" * \"\n      }\n    },\n    {\n      // e.g.  * ...|\n      beforeText: /^(\\t|(\\ \\ ))*\\ \\*(\\ ([^\\*]|\\*(?!\\/))*)?$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.None,\n        appendText: \"* \"\n      }\n    },\n    {\n      // e.g.  */|\n      beforeText: /^(\\t|(\\ \\ ))*\\ \\*\\/\\s*$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.None,\n        removeText: 1\n      }\n    }\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"/**\", close: \" */\", notIn: [\"string\"] }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*#?region\\\\b\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*#?endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  defaultToken: \"invalid\",\n  tokenPostfix: \".ts\",\n  keywords: [\n    // Should match the keys of textToKeywordObj in\n    // https://github.com/microsoft/TypeScript/blob/master/src/compiler/scanner.ts\n    \"abstract\",\n    \"any\",\n    \"as\",\n    \"asserts\",\n    \"bigint\",\n    \"boolean\",\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"continue\",\n    \"const\",\n    \"constructor\",\n    \"debugger\",\n    \"declare\",\n    \"default\",\n    \"delete\",\n    \"do\",\n    \"else\",\n    \"enum\",\n    \"export\",\n    \"extends\",\n    \"false\",\n    \"finally\",\n    \"for\",\n    \"from\",\n    \"function\",\n    \"get\",\n    \"if\",\n    \"implements\",\n    \"import\",\n    \"in\",\n    \"infer\",\n    \"instanceof\",\n    \"interface\",\n    \"is\",\n    \"keyof\",\n    \"let\",\n    \"module\",\n    \"namespace\",\n    \"never\",\n    \"new\",\n    \"null\",\n    \"number\",\n    \"object\",\n    \"out\",\n    \"package\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"override\",\n    \"readonly\",\n    \"require\",\n    \"global\",\n    \"return\",\n    \"satisfies\",\n    \"set\",\n    \"static\",\n    \"string\",\n    \"super\",\n    \"switch\",\n    \"symbol\",\n    \"this\",\n    \"throw\",\n    \"true\",\n    \"try\",\n    \"type\",\n    \"typeof\",\n    \"undefined\",\n    \"unique\",\n    \"unknown\",\n    \"var\",\n    \"void\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    \"async\",\n    \"await\",\n    \"of\"\n  ],\n  operators: [\n    \"<=\",\n    \">=\",\n    \"==\",\n    \"!=\",\n    \"===\",\n    \"!==\",\n    \"=>\",\n    \"+\",\n    \"-\",\n    \"**\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"++\",\n    \"--\",\n    \"<<\",\n    \"</\",\n    \">>\",\n    \">>>\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"!\",\n    \"~\",\n    \"&&\",\n    \"||\",\n    \"??\",\n    \"?\",\n    \":\",\n    \"=\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"**=\",\n    \"/=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\",\n    \">>>=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"@\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+(_+\\d+)*/,\n  octaldigits: /[0-7]+(_+[0-7]+)*/,\n  binarydigits: /[0-1]+(_+[0-1]+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[bBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [[/[{}]/, \"delimiter.bracket\"], { include: \"common\" }],\n    common: [\n      // identifiers and keywords\n      [\n        /#?[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z][\\w\\$]*/, \"type.identifier\"],\n      // to show class names nicely\n      // [/[A-Z][\\w\\$]*/, 'identifier'],\n      // whitespace\n      { include: \"@whitespace\" },\n      // regular expression: ensure it is terminated before beginning (otherwise it is an opeator)\n      [\n        /\\/(?=([^\\\\\\/]|\\\\.)+\\/([dgimsuy]*)(\\s*)(\\.|;|,|\\)|\\]|\\}|$))/,\n        { token: \"regexp\", bracket: \"@open\", next: \"@regexp\" }\n      ],\n      // delimiters and operators\n      [/[()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/!(?=([^=]|$))/, \"delimiter\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?/, \"number.float\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?/, \"number.float\"],\n      [/0[xX](@hexdigits)n?/, \"number.hex\"],\n      [/0[oO]?(@octaldigits)n?/, \"number.octal\"],\n      [/0[bB](@binarydigits)n?/, \"number.binary\"],\n      [/(@digits)n?/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string_double\"],\n      [/'/, \"string\", \"@string_single\"],\n      [/`/, \"string\", \"@string_backtick\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@jsdoc\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    jsdoc: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    // We match regular expression quite precisely\n    regexp: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\"regexp.escape.control\", \"regexp.escape.control\", \"regexp.escape.control\"]\n      ],\n      [\n        /(\\[)(\\^?)(?=(?:[^\\]\\\\\\/]|\\\\.)+)/,\n        [\"regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?:|\\?=|\\?!)/, [\"regexp.escape.control\", \"regexp.escape.control\"]],\n      [/[()]/, \"regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/[^\\\\\\/]/, \"regexp\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/(\\/)([dgimsuy]*)/, [{ token: \"regexp\", bracket: \"@close\", next: \"@pop\" }, \"keyword.other\"]]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [\n        /\\]/,\n        {\n          token: \"regexp.escape.control\",\n          next: \"@pop\",\n          bracket: \"@close\"\n        }\n      ]\n    ],\n    string_double: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    string_single: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, \"string\", \"@pop\"]\n    ],\n    string_backtick: [\n      [/\\$\\{/, { token: \"delimiter.bracket\", next: \"@bracketCounting\" }],\n      [/[^\\\\`$]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/`/, \"string\", \"@pop\"]\n    ],\n    bracketCounting: [\n      [/\\{/, \"delimiter.bracket\", \"@bracketCounting\"],\n      [/\\}/, \"delimiter.bracket\", \"@pop\"],\n      { include: \"common\" }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;;AAOA,IAAI,EAAY,OAAO,eACnB,EAAmB,OAAO,yBAC1B,EAAoB,OAAO,oBAC3B,EAAe,OAAO,UAAU,eAChC,GAAe,EAAI,EAAM,EAAQ,IAAS,CAC5C,GAAI,GAAQ,OAAO,GAAS,UAAY,OAAO,GAAS,eACjD,IAAI,KAAO,EAAkB,EAAK,CACjC,CAAC,EAAa,KAAK,EAAI,EAAI,EAAI,IAAQ,GACzC,EAAU,EAAI,EAAK,CAAE,QAAW,EAAK,GAAM,WAAY,EAAE,EAAO,EAAiB,EAAM,EAAI,GAAK,EAAK,WAAY,CAAC,CAExH,OAAO,GAEL,GAAc,EAAQ,EAAK,KAAkB,EAAY,EAAQ,EAAK,UAAU,CAAE,GAAgB,EAAY,EAAc,EAAK,UAAU,EAG3I,EAA6B,EAAE,CACnC,EAAW,EAA4BA,EAAwB,CAI/D,IAAI,EAAO,CACT,YAAa,uFACb,SAAU,CACR,YAAa,KACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,aAAc,CACZ,CAEE,WAAY,qCACZ,UAAW,YACX,OAAQ,CACN,aAAc,EAA2B,UAAU,aAAa,cAChE,WAAY,MACb,CACF,CACD,CAEE,WAAY,qCACZ,OAAQ,CACN,aAAc,EAA2B,UAAU,aAAa,KAChE,WAAY,MACb,CACF,CACD,CAEE,WAAY,2CACZ,OAAQ,CACN,aAAc,EAA2B,UAAU,aAAa,KAChE,WAAY,KACb,CACF,CACD,CAEE,WAAY,0BACZ,OAAQ,CACN,aAAc,EAA2B,UAAU,aAAa,KAChE,WAAY,EACb,CACF,CACF,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC5C,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,MAAO,MAAO,MAAO,MAAO,CAAC,SAAS,CAAE,CACjD,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,yBAAyB,CAC3C,IAAS,OAAO,4BAA4B,CAC7C,CACF,CACF,CACG,EAAW,CAEb,aAAc,UACd,aAAc,MACd,SAAU,khBAkFT,CACD,UAAW,uIA4CV,CAED,QAAS,wBACT,QAAS,wEACT,OAAQ,cACR,YAAa,oBACb,aAAc,oBACd,UAAW,iCACX,UAAW,yBACX,UAAW,+EAEX,UAAW,CACT,KAAM,CAAC,CAAC,OAAQ,oBAAoB,CAAE,CAAE,QAAS,SAAU,CAAC,CAC5D,OAAQ,CAEN,CACE,kBACA,CACE,MAAO,CACL,YAAa,UACb,WAAY,aACb,CACF,CACF,CACD,CAAC,eAAgB,kBAAkB,CAInC,CAAE,QAAS,cAAe,CAE1B,CACE,6DACA,CAAE,MAAO,SAAU,QAAS,QAAS,KAAM,UAAW,CACvD,CAED,CAAC,WAAY,YAAY,CACzB,CAAC,mBAAoB,YAAY,CACjC,CAAC,gBAAiB,YAAY,CAC9B,CACE,WACA,CACE,MAAO,CACL,aAAc,YACd,WAAY,GACb,CACF,CACF,CAED,CAAC,kCAAmC,eAAe,CACnD,CAAC,6CAA8C,eAAe,CAC9D,CAAC,sBAAuB,aAAa,CACrC,CAAC,yBAA0B,eAAe,CAC1C,CAAC,yBAA0B,gBAAgB,CAC3C,CAAC,cAAe,SAAS,CAEzB,CAAC,QAAS,YAAY,CAEtB,CAAC,kBAAmB,iBAAiB,CAErC,CAAC,kBAAmB,iBAAiB,CAErC,CAAC,IAAK,SAAU,iBAAiB,CACjC,CAAC,IAAK,SAAU,iBAAiB,CACjC,CAAC,IAAK,SAAU,mBAAmB,CACpC,CACD,WAAY,CACV,CAAC,aAAc,GAAG,CAClB,CAAC,eAAgB,cAAe,SAAS,CACzC,CAAC,OAAQ,UAAW,WAAW,CAC/B,CAAC,UAAW,UAAU,CACvB,CACD,QAAS,CACP,CAAC,UAAW,UAAU,CACtB,CAAC,OAAQ,UAAW,OAAO,CAC3B,CAAC,QAAS,UAAU,CACrB,CACD,MAAO,CACL,CAAC,UAAW,cAAc,CAC1B,CAAC,OAAQ,cAAe,OAAO,CAC/B,CAAC,QAAS,cAAc,CACzB,CAED,OAAQ,CACN,CACE,yBACA,CAAC,wBAAyB,wBAAyB,wBAAwB,CAC5E,CACD,CACE,kCACA,CAAC,wBAAyB,CAAE,MAAO,wBAAyB,KAAM,cAAe,CAAC,CACnF,CACD,CAAC,oBAAqB,CAAC,wBAAyB,wBAAwB,CAAC,CACzE,CAAC,OAAQ,wBAAwB,CACjC,CAAC,aAAc,wBAAwB,CACvC,CAAC,UAAW,SAAS,CACrB,CAAC,aAAc,gBAAgB,CAC/B,CAAC,OAAQ,iBAAiB,CAC1B,CAAC,mBAAoB,CAAC,CAAE,MAAO,SAAU,QAAS,SAAU,KAAM,OAAQ,CAAE,gBAAgB,CAAC,CAC9F,CACD,WAAY,CACV,CAAC,IAAK,wBAAwB,CAC9B,CAAC,KAAM,iBAAiB,CACxB,CAAC,aAAc,gBAAgB,CAC/B,CAAC,QAAS,SAAS,CACnB,CACE,KACA,CACE,MAAO,wBACP,KAAM,OACN,QAAS,SACV,CACF,CACF,CACD,cAAe,CACb,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,SAAU,OAAO,CACxB,CACD,cAAe,CACb,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,SAAU,OAAO,CACxB,CACD,gBAAiB,CACf,CAAC,OAAQ,CAAE,MAAO,oBAAqB,KAAM,mBAAoB,CAAC,CAClE,CAAC,WAAY,SAAS,CACtB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,SAAU,OAAO,CACxB,CACD,gBAAiB,CACf,CAAC,KAAM,oBAAqB,mBAAmB,CAC/C,CAAC,KAAM,oBAAqB,OAAO,CACnC,CAAE,QAAS,SAAU,CACtB,CACF,CACF"}