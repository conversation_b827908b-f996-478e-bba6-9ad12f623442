{"version": 3, "file": "csp-CwgGPK5o.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/csp/csp.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/csp/csp.ts\nvar conf = {\n  brackets: [],\n  autoClosingPairs: [],\n  surroundingPairs: []\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  // defaultToken: 'invalid',\n  keywords: [],\n  typeKeywords: [],\n  tokenPostfix: \".csp\",\n  operators: [],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      [/child-src/, \"string.quote\"],\n      [/connect-src/, \"string.quote\"],\n      [/default-src/, \"string.quote\"],\n      [/font-src/, \"string.quote\"],\n      [/frame-src/, \"string.quote\"],\n      [/img-src/, \"string.quote\"],\n      [/manifest-src/, \"string.quote\"],\n      [/media-src/, \"string.quote\"],\n      [/object-src/, \"string.quote\"],\n      [/script-src/, \"string.quote\"],\n      [/style-src/, \"string.quote\"],\n      [/worker-src/, \"string.quote\"],\n      [/base-uri/, \"string.quote\"],\n      [/plugin-types/, \"string.quote\"],\n      [/sandbox/, \"string.quote\"],\n      [/disown-opener/, \"string.quote\"],\n      [/form-action/, \"string.quote\"],\n      [/frame-ancestors/, \"string.quote\"],\n      [/report-uri/, \"string.quote\"],\n      [/report-to/, \"string.quote\"],\n      [/upgrade-insecure-requests/, \"string.quote\"],\n      [/block-all-mixed-content/, \"string.quote\"],\n      [/require-sri-for/, \"string.quote\"],\n      [/reflected-xss/, \"string.quote\"],\n      [/referrer/, \"string.quote\"],\n      [/policy-uri/, \"string.quote\"],\n      [/'self'/, \"string.quote\"],\n      [/'unsafe-inline'/, \"string.quote\"],\n      [/'unsafe-eval'/, \"string.quote\"],\n      [/'strict-dynamic'/, \"string.quote\"],\n      [/'unsafe-hashed-attributes'/, \"string.quote\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,EAAE,CACZ,iBAAkB,EAAE,CACpB,iBAAkB,EAAE,CACrB,CACG,EAAW,CAGb,SAAU,EAAE,CACZ,aAAc,EAAE,CAChB,aAAc,OACd,UAAW,EAAE,CACb,QAAS,wBACT,QAAS,wEACT,UAAW,CACT,KAAM,CACJ,CAAC,YAAa,eAAe,CAC7B,CAAC,cAAe,eAAe,CAC/B,CAAC,cAAe,eAAe,CAC/B,CAAC,WAAY,eAAe,CAC5B,CAAC,YAAa,eAAe,CAC7B,CAAC,UAAW,eAAe,CAC3B,CAAC,eAAgB,eAAe,CAChC,CAAC,YAAa,eAAe,CAC7B,CAAC,aAAc,eAAe,CAC9B,CAAC,aAAc,eAAe,CAC9B,CAAC,YAAa,eAAe,CAC7B,CAAC,aAAc,eAAe,CAC9B,CAAC,WAAY,eAAe,CAC5B,CAAC,eAAgB,eAAe,CAChC,CAAC,UAAW,eAAe,CAC3B,CAAC,gBAAiB,eAAe,CACjC,CAAC,cAAe,eAAe,CAC/B,CAAC,kBAAmB,eAAe,CACnC,CAAC,aAAc,eAAe,CAC9B,CAAC,YAAa,eAAe,CAC7B,CAAC,4BAA6B,eAAe,CAC7C,CAAC,0BAA2B,eAAe,CAC3C,CAAC,kBAAmB,eAAe,CACnC,CAAC,gBAAiB,eAAe,CACjC,CAAC,WAAY,eAAe,CAC5B,CAAC,aAAc,eAAe,CAC9B,CAAC,SAAU,eAAe,CAC1B,CAAC,kBAAmB,eAAe,CACnC,CAAC,gBAAiB,eAAe,CACjC,CAAC,mBAAoB,eAAe,CACpC,CAAC,6BAA8B,eAAe,CAC/C,CACF,CACF"}