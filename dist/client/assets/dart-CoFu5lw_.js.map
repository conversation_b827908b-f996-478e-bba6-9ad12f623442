{"version": 3, "file": "dart-CoFu5lw_.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/dart/dart.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/dart/dart.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"/**\", close: \" */\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: /^\\s*\\s*#?region\\b/,\n      end: /^\\s*\\s*#?endregion\\b/\n    }\n  }\n};\nvar language = {\n  defaultToken: \"invalid\",\n  tokenPostfix: \".dart\",\n  keywords: [\n    \"abstract\",\n    \"dynamic\",\n    \"implements\",\n    \"show\",\n    \"as\",\n    \"else\",\n    \"import\",\n    \"static\",\n    \"assert\",\n    \"enum\",\n    \"in\",\n    \"super\",\n    \"async\",\n    \"export\",\n    \"interface\",\n    \"switch\",\n    \"await\",\n    \"extends\",\n    \"is\",\n    \"sync\",\n    \"break\",\n    \"external\",\n    \"library\",\n    \"this\",\n    \"case\",\n    \"factory\",\n    \"mixin\",\n    \"throw\",\n    \"catch\",\n    \"false\",\n    \"new\",\n    \"true\",\n    \"class\",\n    \"final\",\n    \"null\",\n    \"try\",\n    \"const\",\n    \"finally\",\n    \"on\",\n    \"typedef\",\n    \"continue\",\n    \"for\",\n    \"operator\",\n    \"var\",\n    \"covariant\",\n    \"Function\",\n    \"part\",\n    \"void\",\n    \"default\",\n    \"get\",\n    \"rethrow\",\n    \"while\",\n    \"deferred\",\n    \"hide\",\n    \"return\",\n    \"with\",\n    \"do\",\n    \"if\",\n    \"set\",\n    \"yield\"\n  ],\n  typeKeywords: [\"int\", \"double\", \"String\", \"bool\"],\n  operators: [\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"~/\",\n    \"%\",\n    \"++\",\n    \"--\",\n    \"==\",\n    \"!=\",\n    \">\",\n    \"<\",\n    \">=\",\n    \"<=\",\n    \"=\",\n    \"-=\",\n    \"/=\",\n    \"%=\",\n    \">>=\",\n    \"^=\",\n    \"+=\",\n    \"*=\",\n    \"~/=\",\n    \"<<=\",\n    \"&=\",\n    \"!=\",\n    \"||\",\n    \"&&\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"~\",\n    \"<<\",\n    \">>\",\n    \"!\",\n    \">>>\",\n    \"??\",\n    \"?\",\n    \":\",\n    \"|=\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+(_+\\d+)*/,\n  octaldigits: /[0-7]+(_+[0-7]+)*/,\n  binarydigits: /[0-1]+(_+[0-1]+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[bBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [[/[{}]/, \"delimiter.bracket\"], { include: \"common\" }],\n    common: [\n      // identifiers and keywords\n      [\n        /[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@typeKeywords\": \"type.identifier\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z_$][\\w\\$]*/, \"type.identifier\"],\n      // show class names\n      // [/[A-Z][\\w\\$]*/, 'identifier'],\n      // whitespace\n      { include: \"@whitespace\" },\n      // regular expression: ensure it is terminated before beginning (otherwise it is an opeator)\n      [\n        /\\/(?=([^\\\\\\/]|\\\\.)+\\/([gimsuy]*)(\\s*)(\\.|;|,|\\)|\\]|\\}|$))/,\n        { token: \"regexp\", bracket: \"@open\", next: \"@regexp\" }\n      ],\n      // @ annotations.\n      [/@[a-zA-Z]+/, \"annotation\"],\n      // variable\n      // delimiters and operators\n      [/[()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/!(?=([^=]|$))/, \"delimiter\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?/, \"number.float\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?/, \"number.float\"],\n      [/0[xX](@hexdigits)n?/, \"number.hex\"],\n      [/0[oO]?(@octaldigits)n?/, \"number.octal\"],\n      [/0[bB](@binarydigits)n?/, \"number.binary\"],\n      [/(@digits)n?/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string_double\"],\n      [/'/, \"string\", \"@string_single\"]\n      //   [/[a-zA-Z]+/, \"variable\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@jsdoc\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/\\/.*$/, \"comment.doc\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    jsdoc: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    // We match regular expression quite precisely\n    regexp: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\"regexp.escape.control\", \"regexp.escape.control\", \"regexp.escape.control\"]\n      ],\n      [\n        /(\\[)(\\^?)(?=(?:[^\\]\\\\\\/]|\\\\.)+)/,\n        [\"regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?:|\\?=|\\?!)/, [\"regexp.escape.control\", \"regexp.escape.control\"]],\n      [/[()]/, \"regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/[^\\\\\\/]/, \"regexp\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/(\\/)([gimsuy]*)/, [{ token: \"regexp\", bracket: \"@close\", next: \"@pop\" }, \"keyword.other\"]]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [\n        /\\]/,\n        {\n          token: \"regexp.escape.control\",\n          next: \"@pop\",\n          bracket: \"@close\"\n        }\n      ]\n    ],\n    string_double: [\n      [/[^\\\\\"\\$]+/, \"string\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"],\n      [/\\$\\w+/, \"identifier\"]\n    ],\n    string_single: [\n      [/[^\\\\'\\$]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, \"string\", \"@pop\"],\n      [/\\$\\w+/, \"identifier\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,KACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC5C,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,MAAO,MAAO,MAAO,MAAO,CAAC,SAAS,CAAE,CACjD,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAO,oBACP,IAAK,uBACN,CACF,CACF,CACG,EAAW,CACb,aAAc,UACd,aAAc,QACd,SAAU,+XA6DT,CACD,aAAc,CAAC,MAAO,SAAU,SAAU,OAAO,CACjD,UAAW,yHAyCV,CAED,QAAS,wBACT,QAAS,wEACT,OAAQ,cACR,YAAa,oBACb,aAAc,oBACd,UAAW,iCACX,UAAW,yBACX,UAAW,+EAEX,UAAW,CACT,KAAM,CAAC,CAAC,OAAQ,oBAAoB,CAAE,CAAE,QAAS,SAAU,CAAC,CAC5D,OAAQ,CAEN,CACE,gBACA,CACE,MAAO,CACL,gBAAiB,kBACjB,YAAa,UACb,WAAY,aACb,CACF,CACF,CACD,CAAC,iBAAkB,kBAAkB,CAIrC,CAAE,QAAS,cAAe,CAE1B,CACE,4DACA,CAAE,MAAO,SAAU,QAAS,QAAS,KAAM,UAAW,CACvD,CAED,CAAC,aAAc,aAAa,CAG5B,CAAC,WAAY,YAAY,CACzB,CAAC,mBAAoB,YAAY,CACjC,CAAC,gBAAiB,YAAY,CAC9B,CACE,WACA,CACE,MAAO,CACL,aAAc,YACd,WAAY,GACb,CACF,CACF,CAED,CAAC,kCAAmC,eAAe,CACnD,CAAC,6CAA8C,eAAe,CAC9D,CAAC,sBAAuB,aAAa,CACrC,CAAC,yBAA0B,eAAe,CAC1C,CAAC,yBAA0B,gBAAgB,CAC3C,CAAC,cAAe,SAAS,CAEzB,CAAC,QAAS,YAAY,CAEtB,CAAC,kBAAmB,iBAAiB,CAErC,CAAC,kBAAmB,iBAAiB,CAErC,CAAC,IAAK,SAAU,iBAAiB,CACjC,CAAC,IAAK,SAAU,iBAAiB,CAElC,CACD,WAAY,CACV,CAAC,aAAc,GAAG,CAClB,CAAC,eAAgB,cAAe,SAAS,CACzC,CAAC,OAAQ,UAAW,WAAW,CAC/B,CAAC,YAAa,cAAc,CAC5B,CAAC,UAAW,UAAU,CACvB,CACD,QAAS,CACP,CAAC,UAAW,UAAU,CACtB,CAAC,OAAQ,UAAW,OAAO,CAC3B,CAAC,QAAS,UAAU,CACrB,CACD,MAAO,CACL,CAAC,UAAW,cAAc,CAC1B,CAAC,OAAQ,cAAe,OAAO,CAC/B,CAAC,QAAS,cAAc,CACzB,CAED,OAAQ,CACN,CACE,yBACA,CAAC,wBAAyB,wBAAyB,wBAAwB,CAC5E,CACD,CACE,kCACA,CAAC,wBAAyB,CAAE,MAAO,wBAAyB,KAAM,cAAe,CAAC,CACnF,CACD,CAAC,oBAAqB,CAAC,wBAAyB,wBAAwB,CAAC,CACzE,CAAC,OAAQ,wBAAwB,CACjC,CAAC,aAAc,wBAAwB,CACvC,CAAC,UAAW,SAAS,CACrB,CAAC,aAAc,gBAAgB,CAC/B,CAAC,OAAQ,iBAAiB,CAC1B,CAAC,kBAAmB,CAAC,CAAE,MAAO,SAAU,QAAS,SAAU,KAAM,OAAQ,CAAE,gBAAgB,CAAC,CAC7F,CACD,WAAY,CACV,CAAC,IAAK,wBAAwB,CAC9B,CAAC,KAAM,iBAAiB,CACxB,CAAC,aAAc,gBAAgB,CAC/B,CAAC,QAAS,SAAS,CACnB,CACE,KACA,CACE,MAAO,wBACP,KAAM,OACN,QAAS,SACV,CACF,CACF,CACD,cAAe,CACb,CAAC,YAAa,SAAS,CACvB,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,SAAU,OAAO,CACvB,CAAC,QAAS,aAAa,CACxB,CACD,cAAe,CACb,CAAC,YAAa,SAAS,CACvB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,SAAU,OAAO,CACvB,CAAC,QAAS,aAAa,CACxB,CACF,CACF"}