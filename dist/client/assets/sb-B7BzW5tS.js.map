{"version": 3, "file": "sb-B7BzW5tS.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/sb/sb.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/sb/sb.ts\nvar conf = {\n  comments: {\n    lineComment: \"'\"\n  },\n  brackets: [\n    [\"(\", \")\"],\n    [\"[\", \"]\"],\n    [\"If\", \"EndIf\"],\n    [\"While\", \"EndWhile\"],\n    [\"For\", \"EndFor\"],\n    [\"Sub\", \"EndSub\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sb\",\n  ignoreCase: true,\n  brackets: [\n    { token: \"delimiter.array\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    // Special bracket statement pairs\n    { token: \"keyword.tag-if\", open: \"If\", close: \"EndIf\" },\n    { token: \"keyword.tag-while\", open: \"While\", close: \"EndWhile\" },\n    { token: \"keyword.tag-for\", open: \"For\", close: \"EndFor\" },\n    { token: \"keyword.tag-sub\", open: \"Sub\", close: \"EndSub\" }\n  ],\n  keywords: [\n    \"Else\",\n    \"ElseIf\",\n    \"EndFor\",\n    \"EndIf\",\n    \"EndSub\",\n    \"EndWhile\",\n    \"For\",\n    \"Goto\",\n    \"If\",\n    \"Step\",\n    \"Sub\",\n    \"Then\",\n    \"To\",\n    \"While\"\n  ],\n  tagwords: [\"If\", \"Sub\", \"While\", \"For\"],\n  operators: [\">\", \"<\", \"<>\", \"<=\", \">=\", \"And\", \"Or\", \"+\", \"-\", \"*\", \"/\", \"=\"],\n  // we include these common regular expressions\n  identifier: /[a-zA-Z_][\\w]*/,\n  symbols: /[=><:+\\-*\\/%\\.,]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // whitespace\n      { include: \"@whitespace\" },\n      // classes\n      [/(@identifier)(?=[.])/, \"type\"],\n      // identifiers, tagwords, and keywords\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@operators\": \"operator\",\n            \"@default\": \"variable.name\"\n          }\n        }\n      ],\n      // methods, properties, and events\n      [\n        /([.])(@identifier)/,\n        {\n          cases: {\n            $2: [\"delimiter\", \"type.member\"],\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d*\\.\\d+/, \"number.float\"],\n      [/\\d+/, \"number\"],\n      // delimiters and operators\n      [/[()\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@default\": \"delimiter\"\n          }\n        }\n      ],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/(\\').*$/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"C?/, \"string\", \"@pop\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,IACd,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,KAAM,QAAQ,CACf,CAAC,QAAS,WAAW,CACrB,CAAC,MAAO,SAAS,CACjB,CAAC,MAAO,SAAS,CAClB,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACxD,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,MACd,WAAY,GACZ,SAAU,CACR,CAAE,MAAO,kBAAmB,KAAM,IAAK,MAAO,IAAK,CACnD,CAAE,MAAO,wBAAyB,KAAM,IAAK,MAAO,IAAK,CAEzD,CAAE,MAAO,iBAAkB,KAAM,KAAM,MAAO,QAAS,CACvD,CAAE,MAAO,oBAAqB,KAAM,QAAS,MAAO,WAAY,CAChE,CAAE,MAAO,kBAAmB,KAAM,MAAO,MAAO,SAAU,CAC1D,CAAE,MAAO,kBAAmB,KAAM,MAAO,MAAO,SAAU,CAC3D,CACD,SAAU,CACR,OACA,SACA,SACA,QACA,SACA,WACA,MACA,OACA,KACA,OACA,MACA,OACA,KACA,QACD,CACD,SAAU,CAAC,KAAM,MAAO,QAAS,MAAM,CACvC,UAAW,CAAC,IAAK,IAAK,KAAM,KAAM,KAAM,MAAO,KAAM,IAAK,IAAK,IAAK,IAAK,IAAI,CAE7E,WAAY,iBACZ,QAAS,oBACT,QAAS,wEAET,UAAW,CACT,KAAM,CAEJ,CAAE,QAAS,cAAe,CAE1B,CAAC,uBAAwB,OAAO,CAEhC,CACE,cACA,CACE,MAAO,CACL,YAAa,CAAE,MAAO,aAAc,CACpC,aAAc,WACd,WAAY,gBACb,CACF,CACF,CAED,CACE,qBACA,CACE,MAAO,CACL,GAAI,CAAC,YAAa,cAAc,CAChC,WAAY,GACb,CACF,CACF,CAED,CAAC,WAAY,eAAe,CAC5B,CAAC,MAAO,SAAS,CAEjB,CAAC,WAAY,YAAY,CACzB,CACE,WACA,CACE,MAAO,CACL,aAAc,WACd,WAAY,YACb,CACF,CACF,CAED,CAAC,kBAAmB,iBAAiB,CAErC,CAAC,IAAK,SAAU,UAAU,CAC3B,CACD,WAAY,CACV,CAAC,aAAc,GAAG,CAClB,CAAC,UAAW,UAAU,CACvB,CACD,OAAQ,CACN,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,MAAO,SAAU,OAAO,CAC1B,CACF,CACF"}