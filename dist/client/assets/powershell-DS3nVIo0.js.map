{"version": 3, "file": "powershell-DS3nVIo0.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/powershell/powershell.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/powershell/powershell.ts\nvar conf = {\n  // the default separators except `$-`\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#%\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"<#\", \"#>\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: true,\n  tokenPostfix: \".ps1\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" }\n  ],\n  keywords: [\n    \"begin\",\n    \"break\",\n    \"catch\",\n    \"class\",\n    \"continue\",\n    \"data\",\n    \"define\",\n    \"do\",\n    \"dynamicparam\",\n    \"else\",\n    \"elseif\",\n    \"end\",\n    \"exit\",\n    \"filter\",\n    \"finally\",\n    \"for\",\n    \"foreach\",\n    \"from\",\n    \"function\",\n    \"if\",\n    \"in\",\n    \"param\",\n    \"process\",\n    \"return\",\n    \"switch\",\n    \"throw\",\n    \"trap\",\n    \"try\",\n    \"until\",\n    \"using\",\n    \"var\",\n    \"while\",\n    \"workflow\",\n    \"parallel\",\n    \"sequence\",\n    \"inlinescript\",\n    \"configuration\"\n  ],\n  helpKeywords: /SYNOPSIS|DESCRIPTION|PARAMETER|EXAMPLE|INPUTS|OUTPUTS|NOTES|LINK|COMPONENT|ROLE|FUNCTIONALITY|FORWARDHELPTARGETNAME|FORWARDHELPCATEGORY|REMOTEHELPRUNSPACE|EXTERNALHELP/,\n  // we include these common regular expressions\n  symbols: /[=><!~?&%|+\\-*\\/\\^;\\.,]+/,\n  escapes: /`(?:[abfnrtv\\\\\"'$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // commands and keywords\n      [\n        /[a-zA-Z_][\\w-]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // whitespace\n      [/[ \\t\\r\\n]+/, \"\"],\n      // labels\n      [/^:\\w*/, \"metatag\"],\n      // variables\n      [\n        /\\$(\\{((global|local|private|script|using):)?[\\w]+\\}|((global|local|private|script|using):)?[\\w]+)/,\n        \"variable\"\n      ],\n      // Comments\n      [/<#/, \"comment\", \"@comment\"],\n      [/#.*$/, \"comment\"],\n      // delimiters\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]*[0-9a-fA-F]/, \"number.hex\"],\n      [/\\d+?/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings:\n      [/\\@\"/, \"string\", '@herestring.\"'],\n      [/\\@'/, \"string\", \"@herestring.'\"],\n      [\n        /\"/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: '@string.\"' }\n          }\n        }\n      ],\n      [\n        /'/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: \"@string.'\" }\n          }\n        }\n      ]\n    ],\n    string: [\n      [\n        /[^\"'\\$`]+/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /@escapes/,\n        {\n          cases: {\n            \"@eos\": { token: \"string.escape\", next: \"@popall\" },\n            \"@default\": \"string.escape\"\n          }\n        }\n      ],\n      [\n        /`./,\n        {\n          cases: {\n            \"@eos\": {\n              token: \"string.escape.invalid\",\n              next: \"@popall\"\n            },\n            \"@default\": \"string.escape.invalid\"\n          }\n        }\n      ],\n      [\n        /\\$[\\w]+$/,\n        {\n          cases: {\n            '$S2==\"': { token: \"variable\", next: \"@popall\" },\n            \"@default\": { token: \"string\", next: \"@popall\" }\n          }\n        }\n      ],\n      [\n        /\\$[\\w]+/,\n        {\n          cases: {\n            '$S2==\"': \"variable\",\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": {\n              cases: {\n                \"@eos\": { token: \"string\", next: \"@popall\" },\n                \"@default\": \"string\"\n              }\n            }\n          }\n        }\n      ]\n    ],\n    herestring: [\n      [\n        /^\\s*([\"'])@/,\n        {\n          cases: {\n            \"$1==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/[^\\$`]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/`./, \"string.escape.invalid\"],\n      [\n        /\\$[\\w]+/,\n        {\n          cases: {\n            '$S2==\"': \"variable\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    comment: [\n      [/[^#\\.]+/, \"comment\"],\n      [/#>/, \"comment\", \"@pop\"],\n      [/(\\.)(@helpKeywords)(?!\\w)/, { token: \"comment.keyword.$2\" }],\n      [/[\\.#]/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CAET,YAAa,oFACb,SAAU,CACR,YAAa,IACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC5C,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACxD,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,kBAAkB,CACpC,IAAS,OAAO,qBAAqB,CACtC,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,WAAY,GACZ,aAAc,OACd,SAAU,CACR,CAAE,MAAO,kBAAmB,KAAM,IAAK,MAAO,IAAK,CACnD,CAAE,MAAO,mBAAoB,KAAM,IAAK,MAAO,IAAK,CACpD,CAAE,MAAO,wBAAyB,KAAM,IAAK,MAAO,IAAK,CAC1D,CACD,SAAU,oQAsCT,CACD,aAAc,0KAEd,QAAS,2BACT,QAAS,wEAET,UAAW,CACT,KAAM,CAEJ,CACE,kBACA,CACE,MAAO,CACL,YAAa,CAAE,MAAO,aAAc,CACpC,WAAY,GACb,CACF,CACF,CAED,CAAC,aAAc,GAAG,CAElB,CAAC,QAAS,UAAU,CAEpB,CACE,oGACA,WACD,CAED,CAAC,KAAM,UAAW,WAAW,CAC7B,CAAC,OAAQ,UAAU,CAEnB,CAAC,aAAc,YAAY,CAC3B,CAAC,WAAY,YAAY,CAEzB,CAAC,2BAA4B,eAAe,CAC5C,CAAC,gCAAiC,aAAa,CAC/C,CAAC,OAAQ,SAAS,CAElB,CAAC,QAAS,YAAY,CAEtB,CAAC,MAAO,SAAU,gBAAgB,CAClC,CAAC,MAAO,SAAU,gBAAgB,CAClC,CACE,IACA,CACE,MAAO,CACL,OAAQ,SACR,WAAY,CAAE,MAAO,SAAU,KAAM,YAAa,CACnD,CACF,CACF,CACD,CACE,IACA,CACE,MAAO,CACL,OAAQ,SACR,WAAY,CAAE,MAAO,SAAU,KAAM,YAAa,CACnD,CACF,CACF,CACF,CACD,OAAQ,CACN,CACE,YACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,SAAU,KAAM,UAAW,CAC5C,WAAY,SACb,CACF,CACF,CACD,CACE,WACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,gBAAiB,KAAM,UAAW,CACnD,WAAY,gBACb,CACF,CACF,CACD,CACE,KACA,CACE,MAAO,CACL,OAAQ,CACN,MAAO,wBACP,KAAM,UACP,CACD,WAAY,wBACb,CACF,CACF,CACD,CACE,WACA,CACE,MAAO,CACL,SAAU,CAAE,MAAO,WAAY,KAAM,UAAW,CAChD,WAAY,CAAE,MAAO,SAAU,KAAM,UAAW,CACjD,CACF,CACF,CACD,CACE,UACA,CACE,MAAO,CACL,SAAU,WACV,WAAY,SACb,CACF,CACF,CACD,CACE,OACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,SAAU,KAAM,OAAQ,CAC5C,WAAY,CACV,MAAO,CACL,OAAQ,CAAE,MAAO,SAAU,KAAM,UAAW,CAC5C,WAAY,SACb,CACF,CACF,CACF,CACF,CACF,CACD,WAAY,CACV,CACE,cACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,SAAU,KAAM,OAAQ,CAC5C,WAAY,SACb,CACF,CACF,CACD,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,KAAM,wBAAwB,CAC/B,CACE,UACA,CACE,MAAO,CACL,SAAU,WACV,WAAY,SACb,CACF,CACF,CACF,CACD,QAAS,CACP,CAAC,UAAW,UAAU,CACtB,CAAC,KAAM,UAAW,OAAO,CACzB,CAAC,4BAA6B,CAAE,MAAO,qBAAsB,CAAC,CAC9D,CAAC,QAAS,UAAU,CACrB,CACF,CACF"}