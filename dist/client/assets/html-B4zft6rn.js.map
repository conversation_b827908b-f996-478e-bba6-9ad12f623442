{"version": 3, "file": "html-B4zft6rn.js", "names": ["monaco_editor_core_star"], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/html/html.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/html/html.ts\nvar EMPTY_ELEMENTS = [\n  \"area\",\n  \"base\",\n  \"br\",\n  \"col\",\n  \"embed\",\n  \"hr\",\n  \"img\",\n  \"input\",\n  \"keygen\",\n  \"link\",\n  \"menuitem\",\n  \"meta\",\n  \"param\",\n  \"source\",\n  \"track\",\n  \"wbr\"\n];\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  comments: {\n    blockComment: [\"<!--\", \"-->\"]\n  },\n  brackets: [\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"],\n    [\"{\", \"}\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([_:\\\\w][_:\\\\w-.\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      afterText: /^<\\/([_:\\w][_:\\w-.\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*<!--\\\\s*#region\\\\b.*-->\"),\n      end: new RegExp(\"^\\\\s*<!--\\\\s*#endregion\\\\b.*-->\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".html\",\n  ignoreCase: true,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/<!DOCTYPE/, \"metatag\", \"@doctype\"],\n      [/<!--/, \"comment\", \"@comment\"],\n      [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)(\\s*)(\\/>)/, [\"delimiter\", \"tag\", \"\", \"delimiter\"]],\n      [/(<)(script)/, [\"delimiter\", { token: \"tag\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter\", { token: \"tag\", next: \"@style\" }]],\n      [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)/, [\"delimiter\", { token: \"tag\", next: \"@otherTag\" }]],\n      [/(<\\/)((?:[\\w\\-]+:)?[\\w\\-]+)/, [\"delimiter\", { token: \"tag\", next: \"@otherTag\" }]],\n      [/</, \"delimiter\"],\n      [/[^<]+/]\n      // text\n    ],\n    doctype: [\n      [/[^>]+/, \"metatag.content\"],\n      [/>/, \"metatag\", \"@pop\"]\n    ],\n    comment: [\n      [/-->/, \"comment\", \"@pop\"],\n      [/[^-]+/, \"comment.content\"],\n      [/./, \"comment.content\"]\n    ],\n    otherTag: [\n      [/\\/?>/, \"delimiter\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [/type/, \"attribute.name\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/(<\\/)(script\\s*)(>)/, [\"delimiter\", \"tag\", { token: \"delimiter\", next: \"@pop\" }]]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [/=/, \"delimiter\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /\"module\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.text/javascript\"\n        }\n      ],\n      [\n        /'module'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.text/javascript\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        />/,\n        {\n          token: \"delimiter\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^<]+/, \"\"]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [/type/, \"attribute.name\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/(<\\/)(style\\s*)(>)/, [\"delimiter\", \"tag\", { token: \"delimiter\", next: \"@pop\" }]]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [/=/, \"delimiter\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        />/,\n        {\n          token: \"delimiter\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^<]+/, \"\"]\n    ]\n    // -- END <style> tags handling\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;;AAOA,IAAI,EAAY,OAAO,eACnB,EAAmB,OAAO,yBAC1B,EAAoB,OAAO,oBAC3B,EAAe,OAAO,UAAU,eAChC,GAAe,EAAI,EAAM,EAAQ,IAAS,CAC5C,GAAI,GAAQ,OAAO,GAAS,UAAY,OAAO,GAAS,eACjD,IAAI,KAAO,EAAkB,EAAK,CACjC,CAAC,EAAa,KAAK,EAAI,EAAI,EAAI,IAAQ,GACzC,EAAU,EAAI,EAAK,CAAE,QAAW,EAAK,GAAM,WAAY,EAAE,EAAO,EAAiB,EAAM,EAAI,GAAK,EAAK,WAAY,CAAC,CAExH,OAAO,GAEL,GAAc,EAAQ,EAAK,KAAkB,EAAY,EAAQ,EAAK,UAAU,CAAE,GAAgB,EAAY,EAAc,EAAK,UAAU,EAG3I,EAA6B,EAAE,CACnC,EAAW,EAA4BA,EAAwB,CAI/D,IAAI,EAAiB,CACnB,OACA,OACA,KACA,MACA,QACA,KACA,MACA,QACA,SACA,OACA,WACA,OACA,QACA,SACA,QACA,MACD,CACG,EAAO,CACT,YAAa,iFACb,SAAU,CACR,aAAc,CAAC,OAAQ,MAAM,CAC9B,CACD,SAAU,CACR,CAAC,OAAQ,MAAM,CACf,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,aAAc,CACZ,CACE,WAAgB,OACd,UAAU,EAAe,KAAK,IAAI,CAAC,8CACnC,IACD,CACD,UAAW,gCACX,OAAQ,CACN,aAAc,EAA2B,UAAU,aAAa,cACjE,CACF,CACD,CACE,WAAgB,OACd,UAAU,EAAe,KAAK,IAAI,CAAC,sCACnC,IACD,CACD,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,OAAQ,CACnF,CACF,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,+BAA+B,CACjD,IAAS,OAAO,kCAAkC,CACnD,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,QACd,WAAY,GAEZ,UAAW,CACT,KAAM,CACJ,CAAC,YAAa,UAAW,WAAW,CACpC,CAAC,OAAQ,UAAW,WAAW,CAC/B,CAAC,sCAAuC,CAAC,YAAa,MAAO,GAAI,YAAY,CAAC,CAC9E,CAAC,cAAe,CAAC,YAAa,CAAE,MAAO,MAAO,KAAM,UAAW,CAAC,CAAC,CACjE,CAAC,aAAc,CAAC,YAAa,CAAE,MAAO,MAAO,KAAM,SAAU,CAAC,CAAC,CAC/D,CAAC,4BAA6B,CAAC,YAAa,CAAE,MAAO,MAAO,KAAM,YAAa,CAAC,CAAC,CACjF,CAAC,8BAA+B,CAAC,YAAa,CAAE,MAAO,MAAO,KAAM,YAAa,CAAC,CAAC,CACnF,CAAC,IAAK,YAAY,CAClB,CAAC,QAAQ,CAEV,CACD,QAAS,CACP,CAAC,QAAS,kBAAkB,CAC5B,CAAC,IAAK,UAAW,OAAO,CACzB,CACD,QAAS,CACP,CAAC,MAAO,UAAW,OAAO,CAC1B,CAAC,QAAS,kBAAkB,CAC5B,CAAC,IAAK,kBAAkB,CACzB,CACD,SAAU,CACR,CAAC,OAAQ,YAAa,OAAO,CAC7B,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CAAC,aAAa,CAEf,CAGD,OAAQ,CACN,CAAC,OAAQ,iBAAkB,mBAAmB,CAC9C,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CACE,IACA,CACE,MAAO,YACP,KAAM,kBACN,aAAc,kBACf,CACF,CACD,CAAC,aAAa,CAEd,CAAC,sBAAuB,CAAC,YAAa,MAAO,CAAE,MAAO,YAAa,KAAM,OAAQ,CAAC,CAAC,CACpF,CAED,gBAAiB,CACf,CAAC,IAAK,YAAa,yBAAyB,CAC5C,CACE,IACA,CACE,MAAO,YACP,KAAM,kBACN,aAAc,kBACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACvD,CAED,sBAAuB,CACrB,CACE,WACA,CACE,MAAO,kBACP,SAAU,wCACX,CACF,CACD,CACE,WACA,CACE,MAAO,kBACP,SAAU,wCACX,CACF,CACD,CACE,YACA,CACE,MAAO,kBACP,SAAU,2BACX,CACF,CACD,CACE,YACA,CACE,MAAO,kBACP,SAAU,2BACX,CACF,CACD,CACE,IACA,CACE,MAAO,YACP,KAAM,kBACN,aAAc,kBACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACvD,CAED,qBAAsB,CACpB,CACE,IACA,CACE,MAAO,YACP,KAAM,sBACN,aAAc,MACf,CACF,CACD,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CAAC,aAAa,CAEd,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACvD,CACD,eAAgB,CACd,CAAC,YAAa,CAAE,MAAO,WAAY,KAAM,OAAQ,aAAc,OAAQ,CAAC,CACxE,CAAC,QAAS,GAAG,CACd,CAID,MAAO,CACL,CAAC,OAAQ,iBAAkB,kBAAkB,CAC7C,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CACE,IACA,CACE,MAAO,YACP,KAAM,iBACN,aAAc,WACf,CACF,CACD,CAAC,aAAa,CAEd,CAAC,qBAAsB,CAAC,YAAa,MAAO,CAAE,MAAO,YAAa,KAAM,OAAQ,CAAC,CAAC,CACnF,CAED,eAAgB,CACd,CAAC,IAAK,YAAa,wBAAwB,CAC3C,CACE,IACA,CACE,MAAO,YACP,KAAM,iBACN,aAAc,WACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACtD,CAED,qBAAsB,CACpB,CACE,YACA,CACE,MAAO,kBACP,SAAU,0BACX,CACF,CACD,CACE,YACA,CACE,MAAO,kBACP,SAAU,0BACX,CACF,CACD,CACE,IACA,CACE,MAAO,YACP,KAAM,iBACN,aAAc,WACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACtD,CAED,oBAAqB,CACnB,CACE,IACA,CACE,MAAO,YACP,KAAM,qBACN,aAAc,MACf,CACF,CACD,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CAAC,aAAa,CAEd,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACtD,CACD,cAAe,CACb,CAAC,WAAY,CAAE,MAAO,WAAY,KAAM,OAAQ,aAAc,OAAQ,CAAC,CACvE,CAAC,QAAS,GAAG,CACd,CAEF,CACF"}