{"version": 3, "file": "python-Q22isuQM.js", "names": ["monaco_editor_core_star"], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/python/python.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/python/python.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"'''\", \"'''\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\n        \"^\\\\s*(?:def|class|for|if|elif|else|while|try|with|finally|except|async|match|case).*?:\\\\s*$\"\n      ),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ],\n  folding: {\n    offSide: true,\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".python\",\n  keywords: [\n    // This section is the result of running\n    // `import keyword; for k in sorted(keyword.kwlist + keyword.softkwlist): print(\"  '\" + k + \"',\")`\n    // in a Python REPL,\n    // though note that the output from Python 3 is not a strict superset of the\n    // output from Python 2.\n    \"False\",\n    // promoted to keyword.kwlist in Python 3\n    \"None\",\n    // promoted to keyword.kwlist in Python 3\n    \"True\",\n    // promoted to keyword.kwlist in Python 3\n    \"_\",\n    // new in Python 3.10\n    \"and\",\n    \"as\",\n    \"assert\",\n    \"async\",\n    // new in Python 3\n    \"await\",\n    // new in Python 3\n    \"break\",\n    \"case\",\n    // new in Python 3.10\n    \"class\",\n    \"continue\",\n    \"def\",\n    \"del\",\n    \"elif\",\n    \"else\",\n    \"except\",\n    \"exec\",\n    // Python 2, but not 3.\n    \"finally\",\n    \"for\",\n    \"from\",\n    \"global\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"is\",\n    \"lambda\",\n    \"match\",\n    // new in Python 3.10\n    \"nonlocal\",\n    // new in Python 3\n    \"not\",\n    \"or\",\n    \"pass\",\n    \"print\",\n    // Python 2, but not 3.\n    \"raise\",\n    \"return\",\n    \"try\",\n    \"type\",\n    // new in Python 3.12\n    \"while\",\n    \"with\",\n    \"yield\",\n    \"int\",\n    \"float\",\n    \"long\",\n    \"complex\",\n    \"hex\",\n    \"abs\",\n    \"all\",\n    \"any\",\n    \"apply\",\n    \"basestring\",\n    \"bin\",\n    \"bool\",\n    \"buffer\",\n    \"bytearray\",\n    \"callable\",\n    \"chr\",\n    \"classmethod\",\n    \"cmp\",\n    \"coerce\",\n    \"compile\",\n    \"complex\",\n    \"delattr\",\n    \"dict\",\n    \"dir\",\n    \"divmod\",\n    \"enumerate\",\n    \"eval\",\n    \"execfile\",\n    \"file\",\n    \"filter\",\n    \"format\",\n    \"frozenset\",\n    \"getattr\",\n    \"globals\",\n    \"hasattr\",\n    \"hash\",\n    \"help\",\n    \"id\",\n    \"input\",\n    \"intern\",\n    \"isinstance\",\n    \"issubclass\",\n    \"iter\",\n    \"len\",\n    \"locals\",\n    \"list\",\n    \"map\",\n    \"max\",\n    \"memoryview\",\n    \"min\",\n    \"next\",\n    \"object\",\n    \"oct\",\n    \"open\",\n    \"ord\",\n    \"pow\",\n    \"print\",\n    \"property\",\n    \"reversed\",\n    \"range\",\n    \"raw_input\",\n    \"reduce\",\n    \"reload\",\n    \"repr\",\n    \"reversed\",\n    \"round\",\n    \"self\",\n    \"set\",\n    \"setattr\",\n    \"slice\",\n    \"sorted\",\n    \"staticmethod\",\n    \"str\",\n    \"sum\",\n    \"super\",\n    \"tuple\",\n    \"type\",\n    \"unichr\",\n    \"unicode\",\n    \"vars\",\n    \"xrange\",\n    \"zip\",\n    \"__dict__\",\n    \"__methods__\",\n    \"__members__\",\n    \"__class__\",\n    \"__bases__\",\n    \"__name__\",\n    \"__mro__\",\n    \"__subclasses__\",\n    \"__init__\",\n    \"__import__\"\n  ],\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[,:;]/, \"delimiter\"],\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      [/@[a-zA-Z_]\\w*/, \"tag\"],\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    // Deal with white space, including single and multi-line comments\n    whitespace: [\n      [/\\s+/, \"white\"],\n      [/(^#.*$)/, \"comment\"],\n      [/'''/, \"string\", \"@endDocString\"],\n      [/\"\"\"/, \"string\", \"@endDblDocString\"]\n    ],\n    endDocString: [\n      [/[^']+/, \"string\"],\n      [/\\\\'/, \"string\"],\n      [/'''/, \"string\", \"@popall\"],\n      [/'/, \"string\"]\n    ],\n    endDblDocString: [\n      [/[^\"]+/, \"string\"],\n      [/\\\\\"/, \"string\"],\n      [/\"\"\"/, \"string\", \"@popall\"],\n      [/\"/, \"string\"]\n    ],\n    // Recognize hex, negatives, decimals, imaginaries, longs, and scientific notation\n    numbers: [\n      [/-?0x([abcdef]|[ABCDEF]|\\d)+[lL]?/, \"number.hex\"],\n      [/-?(\\d*\\.)?\\d+([eE][+\\-]?\\d+)?[jJ]?[lL]?/, \"number\"]\n    ],\n    // Recognize strings, including those broken across lines with \\ (but not without)\n    strings: [\n      [/'$/, \"string.escape\", \"@popall\"],\n      [/f'{1,3}/, \"string.escape\", \"@fStringBody\"],\n      [/'/, \"string.escape\", \"@stringBody\"],\n      [/\"$/, \"string.escape\", \"@popall\"],\n      [/f\"{1,3}/, \"string.escape\", \"@fDblStringBody\"],\n      [/\"/, \"string.escape\", \"@dblStringBody\"]\n    ],\n    fStringBody: [\n      [/[^\\\\'\\{\\}]+$/, \"string\", \"@popall\"],\n      [/[^\\\\'\\{\\}]+/, \"string\"],\n      [/\\{[^\\}':!=]+/, \"identifier\", \"@fStringDetail\"],\n      [/\\\\./, \"string\"],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    stringBody: [\n      [/[^\\\\']+$/, \"string\", \"@popall\"],\n      [/[^\\\\']+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    fDblStringBody: [\n      [/[^\\\\\"\\{\\}]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"\\{\\}]+/, \"string\"],\n      [/\\{[^\\}':!=]+/, \"identifier\", \"@fStringDetail\"],\n      [/\\\\./, \"string\"],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    dblStringBody: [\n      [/[^\\\\\"]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    fStringDetail: [\n      [/[:][^}]+/, \"string\"],\n      [/[!][ars]/, \"string\"],\n      // only !a, !r, !s are supported by f-strings: https://docs.python.org/3/tutorial/inputoutput.html#formatted-string-literals\n      [/=/, \"string\"],\n      [/\\}/, \"identifier\", \"@pop\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;;AAOA,IAAI,EAAY,OAAO,eACnB,EAAmB,OAAO,yBAC1B,EAAoB,OAAO,oBAC3B,EAAe,OAAO,UAAU,eAChC,GAAe,EAAI,EAAM,EAAQ,IAAS,CAC5C,GAAI,GAAQ,OAAO,GAAS,UAAY,OAAO,GAAS,eACjD,IAAI,KAAO,EAAkB,EAAK,CACjC,CAAC,EAAa,KAAK,EAAI,EAAI,EAAI,IAAQ,GACzC,EAAU,EAAI,EAAK,CAAE,QAAW,EAAK,GAAM,WAAY,EAAE,EAAO,EAAiB,EAAM,EAAI,GAAK,EAAK,WAAY,CAAC,CAExH,OAAO,GAEL,GAAc,EAAQ,EAAK,KAAkB,EAAY,EAAQ,EAAK,UAAU,CAAE,GAAgB,EAAY,EAAc,EAAK,UAAU,EAG3I,EAA6B,EAAE,CACnC,EAAW,EAA4BA,EAAwB,CAI/D,IAAI,EAAO,CACT,SAAU,CACR,YAAa,IACb,aAAc,CAAC,MAAO,MAAM,CAC7B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC5C,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACxD,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,aAAc,CACZ,CACE,WAAgB,OACd,8FACD,CACD,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,OAAQ,CACnF,CACF,CACD,QAAS,CACP,QAAS,GACT,QAAS,CACP,MAAW,OAAO,kBAAkB,CACpC,IAAS,OAAO,qBAAqB,CACtC,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,UACd,SAAU,i2BAuJT,CACD,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,oBAAqB,CACrD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CAC1D,CACD,UAAW,CACT,KAAM,CACJ,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,WAAY,CACvB,CAAC,QAAS,YAAY,CACtB,CAAC,aAAc,YAAY,CAC3B,CAAC,gBAAiB,MAAM,CACxB,CACE,eACA,CACE,MAAO,CACL,YAAa,UACb,WAAY,aACb,CACF,CACF,CACF,CAED,WAAY,CACV,CAAC,MAAO,QAAQ,CAChB,CAAC,UAAW,UAAU,CACtB,CAAC,MAAO,SAAU,gBAAgB,CAClC,CAAC,MAAO,SAAU,mBAAmB,CACtC,CACD,aAAc,CACZ,CAAC,QAAS,SAAS,CACnB,CAAC,MAAO,SAAS,CACjB,CAAC,MAAO,SAAU,UAAU,CAC5B,CAAC,IAAK,SAAS,CAChB,CACD,gBAAiB,CACf,CAAC,QAAS,SAAS,CACnB,CAAC,MAAO,SAAS,CACjB,CAAC,MAAO,SAAU,UAAU,CAC5B,CAAC,IAAK,SAAS,CAChB,CAED,QAAS,CACP,CAAC,mCAAoC,aAAa,CAClD,CAAC,0CAA2C,SAAS,CACtD,CAED,QAAS,CACP,CAAC,KAAM,gBAAiB,UAAU,CAClC,CAAC,UAAW,gBAAiB,eAAe,CAC5C,CAAC,IAAK,gBAAiB,cAAc,CACrC,CAAC,KAAM,gBAAiB,UAAU,CAClC,CAAC,UAAW,gBAAiB,kBAAkB,CAC/C,CAAC,IAAK,gBAAiB,iBAAiB,CACzC,CACD,YAAa,CACX,CAAC,eAAgB,SAAU,UAAU,CACrC,CAAC,cAAe,SAAS,CACzB,CAAC,eAAgB,aAAc,iBAAiB,CAChD,CAAC,MAAO,SAAS,CACjB,CAAC,IAAK,gBAAiB,UAAU,CACjC,CAAC,MAAO,SAAS,CAClB,CACD,WAAY,CACV,CAAC,WAAY,SAAU,UAAU,CACjC,CAAC,UAAW,SAAS,CACrB,CAAC,MAAO,SAAS,CACjB,CAAC,IAAK,gBAAiB,UAAU,CACjC,CAAC,MAAO,SAAS,CAClB,CACD,eAAgB,CACd,CAAC,eAAgB,SAAU,UAAU,CACrC,CAAC,cAAe,SAAS,CACzB,CAAC,eAAgB,aAAc,iBAAiB,CAChD,CAAC,MAAO,SAAS,CACjB,CAAC,IAAK,gBAAiB,UAAU,CACjC,CAAC,MAAO,SAAS,CAClB,CACD,cAAe,CACb,CAAC,WAAY,SAAU,UAAU,CACjC,CAAC,UAAW,SAAS,CACrB,CAAC,MAAO,SAAS,CACjB,CAAC,IAAK,gBAAiB,UAAU,CACjC,CAAC,MAAO,SAAS,CAClB,CACD,cAAe,CACb,CAAC,WAAY,SAAS,CACtB,CAAC,WAAY,SAAS,CAEtB,CAAC,IAAK,SAAS,CACf,CAAC,KAAM,aAAc,OAAO,CAC7B,CACF,CACF"}