import{n as e,t}from"./typescript-CsXDFW4Z.js";var n=t,r={defaultToken:`invalid`,tokenPostfix:`.js`,keywords:`break.case.catch.class.continue.const.constructor.debugger.default.delete.do.else.export.extends.false.finally.for.from.function.get.if.import.in.instanceof.let.new.null.return.set.static.super.switch.symbol.this.throw.true.try.typeof.undefined.var.void.while.with.yield.async.await.of`.split(`.`),typeKeywords:[],operators:e.operators,symbols:e.symbols,escapes:e.escapes,digits:e.digits,octaldigits:e.octaldigits,binarydigits:e.binarydigits,hexdigits:e.hexdigits,regexpctl:e.regexpctl,regexpesc:e.regexpesc,tokenizer:e.tokenizer};export{n as conf,r as language};
//# sourceMappingURL=javascript-DMErCVf3.js.map