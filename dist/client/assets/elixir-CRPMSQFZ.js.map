{"version": 3, "file": "elixir-CRPMSQFZ.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/elixir/elixir.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/elixir/elixir.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  autoClosingPairs: [\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"comment\"] },\n    { open: '\"\"\"', close: '\"\"\"' },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"<<\", close: \">>\" }\n  ],\n  indentationRules: {\n    increaseIndentPattern: /^\\s*(after|else|catch|rescue|fn|[^#]*(do|<\\-|\\->|\\{|\\[|\\=))\\s*$/,\n    decreaseIndentPattern: /^\\s*((\\}|\\])\\s*$|(after|else|catch|rescue|end)\\b)/\n  }\n};\nvar language = {\n  defaultToken: \"source\",\n  tokenPostfix: \".elixir\",\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"<<\", close: \">>\", token: \"delimiter.angle.special\" }\n  ],\n  // Below are lists/regexps to which we reference later.\n  declarationKeywords: [\n    \"def\",\n    \"defp\",\n    \"defn\",\n    \"defnp\",\n    \"defguard\",\n    \"defguardp\",\n    \"defmacro\",\n    \"defmacrop\",\n    \"defdelegate\",\n    \"defcallback\",\n    \"defmacrocallback\",\n    \"defmodule\",\n    \"defprotocol\",\n    \"defexception\",\n    \"defimpl\",\n    \"defstruct\"\n  ],\n  operatorKeywords: [\"and\", \"in\", \"not\", \"or\", \"when\"],\n  namespaceKeywords: [\"alias\", \"import\", \"require\", \"use\"],\n  otherKeywords: [\n    \"after\",\n    \"case\",\n    \"catch\",\n    \"cond\",\n    \"do\",\n    \"else\",\n    \"end\",\n    \"fn\",\n    \"for\",\n    \"if\",\n    \"quote\",\n    \"raise\",\n    \"receive\",\n    \"rescue\",\n    \"super\",\n    \"throw\",\n    \"try\",\n    \"unless\",\n    \"unquote_splicing\",\n    \"unquote\",\n    \"with\"\n  ],\n  constants: [\"true\", \"false\", \"nil\"],\n  nameBuiltin: [\"__MODULE__\", \"__DIR__\", \"__ENV__\", \"__CALLER__\", \"__STACKTRACE__\"],\n  // Matches any of the operator names:\n  // <<< >>> ||| &&& ^^^ ~~~ === !== ~>> <~> |~> <|> == != <= >= && || \\\\ <> ++ -- |> =~ -> <- ~> <~ :: .. = < > + - * / | . ^ & !\n  operator: /-[->]?|!={0,2}|\\*{1,2}|\\/|\\\\\\\\|&{1,3}|\\.\\.?|\\^(?:\\^\\^)?|\\+\\+?|<(?:-|<<|=|>|\\|>|~>?)?|=~|={1,3}|>(?:=|>>)?|\\|~>|\\|>|\\|{1,3}|~>>?|~~~|::/,\n  // See https://hexdocs.pm/elixir/syntax-reference.html#variables\n  variableName: /[a-z_][a-zA-Z0-9_]*[?!]?/,\n  // See https://hexdocs.pm/elixir/syntax-reference.html#atoms\n  atomName: /[a-zA-Z_][a-zA-Z0-9_@]*[?!]?|@specialAtomName|@operator/,\n  specialAtomName: /\\.\\.\\.|<<>>|%\\{\\}|%|\\{\\}/,\n  aliasPart: /[A-Z][a-zA-Z0-9_]*/,\n  moduleName: /@aliasPart(?:\\.@aliasPart)*/,\n  // Sigil pairs are: \"\"\" \"\"\", ''' ''', \" \", ' ', / /, | |, < >, { }, [ ], ( )\n  sigilSymmetricDelimiter: /\"\"\"|'''|\"|'|\\/|\\|/,\n  sigilStartDelimiter: /@sigilSymmetricDelimiter|<|\\{|\\[|\\(/,\n  sigilEndDelimiter: /@sigilSymmetricDelimiter|>|\\}|\\]|\\)/,\n  sigilModifiers: /[a-zA-Z0-9]*/,\n  decimal: /\\d(?:_?\\d)*/,\n  hex: /[0-9a-fA-F](_?[0-9a-fA-F])*/,\n  octal: /[0-7](_?[0-7])*/,\n  binary: /[01](_?[01])*/,\n  // See https://hexdocs.pm/elixir/master/String.html#module-escape-characters\n  escape: /\\\\u[0-9a-fA-F]{4}|\\\\x[0-9a-fA-F]{2}|\\\\./,\n  // The keys below correspond to tokenizer states.\n  // We start from the root state and match against its rules\n  // until we explicitly transition into another state.\n  // The `include` simply brings in all operations from the given state\n  // and is useful for improving readability.\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comments\" },\n      // Keywords start as either an identifier or a string,\n      // but end with a : so it's important to match this first.\n      { include: \"@keywordsShorthand\" },\n      { include: \"@numbers\" },\n      { include: \"@identifiers\" },\n      { include: \"@strings\" },\n      { include: \"@atoms\" },\n      { include: \"@sigils\" },\n      { include: \"@attributes\" },\n      { include: \"@symbols\" }\n    ],\n    // Whitespace\n    whitespace: [[/\\s+/, \"white\"]],\n    // Comments\n    comments: [[/(#)(.*)/, [\"comment.punctuation\", \"comment\"]]],\n    // Keyword list shorthand\n    keywordsShorthand: [\n      [/(@atomName)(:)(\\s+)/, [\"constant\", \"constant.punctuation\", \"white\"]],\n      // Use positive look-ahead to ensure the string is followed by :\n      // and should be considered a keyword.\n      [\n        /\"(?=([^\"]|#\\{.*?\\}|\\\\\")*\":)/,\n        { token: \"constant.delimiter\", next: \"@doubleQuotedStringKeyword\" }\n      ],\n      [\n        /'(?=([^']|#\\{.*?\\}|\\\\')*':)/,\n        { token: \"constant.delimiter\", next: \"@singleQuotedStringKeyword\" }\n      ]\n    ],\n    doubleQuotedStringKeyword: [\n      [/\":/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    singleQuotedStringKeyword: [\n      [/':/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    // Numbers\n    numbers: [\n      [/0b@binary/, \"number.binary\"],\n      [/0o@octal/, \"number.octal\"],\n      [/0x@hex/, \"number.hex\"],\n      [/@decimal\\.@decimal([eE]-?@decimal)?/, \"number.float\"],\n      [/@decimal/, \"number\"]\n    ],\n    // Identifiers\n    identifiers: [\n      // Tokenize identifier name in function-like definitions.\n      // Note: given `def a + b, do: nil`, `a` is not a function name,\n      // so we use negative look-ahead to ensure there's no operator.\n      [\n        /\\b(defp?|defnp?|defmacrop?|defguardp?|defdelegate)(\\s+)(@variableName)(?!\\s+@operator)/,\n        [\n          \"keyword.declaration\",\n          \"white\",\n          {\n            cases: {\n              unquote: \"keyword\",\n              \"@default\": \"function\"\n            }\n          }\n        ]\n      ],\n      // Tokenize function calls\n      [\n        // In-scope call - an identifier followed by ( or .(\n        /(@variableName)(?=\\s*\\.?\\s*\\()/,\n        {\n          cases: {\n            // Tokenize as keyword in cases like `if(..., do: ..., else: ...)`\n            \"@declarationKeywords\": \"keyword.declaration\",\n            \"@namespaceKeywords\": \"keyword\",\n            \"@otherKeywords\": \"keyword\",\n            \"@default\": \"function.call\"\n          }\n        }\n      ],\n      [\n        // Referencing function in a module\n        /(@moduleName)(\\s*)(\\.)(\\s*)(@variableName)/,\n        [\"type.identifier\", \"white\", \"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        // Referencing function in an Erlang module\n        /(:)(@atomName)(\\s*)(\\.)(\\s*)(@variableName)/,\n        [\"constant.punctuation\", \"constant\", \"white\", \"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        // Piping into a function (tokenized separately as it may not have parentheses)\n        /(\\|>)(\\s*)(@variableName)/,\n        [\n          \"operator\",\n          \"white\",\n          {\n            cases: {\n              \"@otherKeywords\": \"keyword\",\n              \"@default\": \"function.call\"\n            }\n          }\n        ]\n      ],\n      [\n        // Function reference passed to another function\n        /(&)(\\s*)(@variableName)/,\n        [\"operator\", \"white\", \"function.call\"]\n      ],\n      // Language keywords, builtins, constants and variables\n      [\n        /@variableName/,\n        {\n          cases: {\n            \"@declarationKeywords\": \"keyword.declaration\",\n            \"@operatorKeywords\": \"keyword.operator\",\n            \"@namespaceKeywords\": \"keyword\",\n            \"@otherKeywords\": \"keyword\",\n            \"@constants\": \"constant.language\",\n            \"@nameBuiltin\": \"variable.language\",\n            \"_.*\": \"comment.unused\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Module names\n      [/@moduleName/, \"type.identifier\"]\n    ],\n    // Strings\n    strings: [\n      [/\"\"\"/, { token: \"string.delimiter\", next: \"@doubleQuotedHeredoc\" }],\n      [/'''/, { token: \"string.delimiter\", next: \"@singleQuotedHeredoc\" }],\n      [/\"/, { token: \"string.delimiter\", next: \"@doubleQuotedString\" }],\n      [/'/, { token: \"string.delimiter\", next: \"@singleQuotedString\" }]\n    ],\n    doubleQuotedHeredoc: [\n      [/\"\"\"/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    singleQuotedHeredoc: [\n      [/'''/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    doubleQuotedString: [\n      [/\"/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    singleQuotedString: [\n      [/'/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    // Atoms\n    atoms: [\n      [/(:)(@atomName)/, [\"constant.punctuation\", \"constant\"]],\n      [/:\"/, { token: \"constant.delimiter\", next: \"@doubleQuotedStringAtom\" }],\n      [/:'/, { token: \"constant.delimiter\", next: \"@singleQuotedStringAtom\" }]\n    ],\n    doubleQuotedStringAtom: [\n      [/\"/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    singleQuotedStringAtom: [\n      [/'/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    // Sigils\n    // See https://elixir-lang.org/getting-started/sigils.html\n    // Sigils allow for typing values using their textual representation.\n    // All sigils start with ~ followed by a letter or\n    // multi-letter uppercase starting at Elixir v1.15.0, indicating sigil type\n    // and then a delimiter pair enclosing the textual representation.\n    // Optional modifiers are allowed after the closing delimiter.\n    // For instance a regular expressions can be written as:\n    // ~r/foo|bar/ ~r{foo|bar} ~r/foo|bar/g\n    //\n    // In general lowercase sigils allow for interpolation\n    // and escaped characters, whereas uppercase sigils don't\n    //\n    // During tokenization we want to distinguish some\n    // specific sigil types, namely string and regexp,\n    // so that they cen be themed separately.\n    //\n    // To reasonably handle all those combinations we leverage\n    // dot-separated states, so if we transition to @sigilStart.interpol.s.{.}\n    // then \"sigilStart.interpol.s\" state will match and also all\n    // the individual dot-separated parameters can be accessed.\n    sigils: [\n      [/~[a-z]@sigilStartDelimiter/, { token: \"@rematch\", next: \"@sigil.interpol\" }],\n      [/~([A-Z]+)@sigilStartDelimiter/, { token: \"@rematch\", next: \"@sigil.noInterpol\" }]\n    ],\n    sigil: [\n      [/~([a-z]|[A-Z]+)\\{/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.{.}\" }],\n      [/~([a-z]|[A-Z]+)\\[/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.[.]\" }],\n      [/~([a-z]|[A-Z]+)\\(/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.(.)\" }],\n      [/~([a-z]|[A-Z]+)\\</, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.<.>\" }],\n      [\n        /~([a-z]|[A-Z]+)(@sigilSymmetricDelimiter)/,\n        { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.$2.$2\" }\n      ]\n    ],\n    // The definitions below expect states to be of the form:\n    //\n    // sigilStart.<interpol-or-noInterpol>.<sigil-letter>.<start-delimiter>.<end-delimiter>\n    // sigilContinue.<interpol-or-noInterpol>.<sigil-letter>.<start-delimiter>.<end-delimiter>\n    //\n    // The sigilStart state is used only to properly classify the token (as string/regex/sigil)\n    // and immediately switches to the sigilContinue sate, which handles the actual content\n    // and waits for the corresponding end delimiter.\n    \"sigilStart.interpol.s\": [\n      [\n        /~s@sigilStartDelimiter/,\n        {\n          token: \"string.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol.s\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"string.delimiter\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      { include: \"@stringContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol.S\": [\n      [\n        /~S@sigilStartDelimiter/,\n        {\n          token: \"string.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol.S\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"string\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"string.delimiter\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      { include: \"@stringContent\" }\n    ],\n    \"sigilStart.interpol.r\": [\n      [\n        /~r@sigilStartDelimiter/,\n        {\n          token: \"regexp.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol.r\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"regexp.delimiter\", next: \"@pop\" },\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexpContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol.R\": [\n      [\n        /~R@sigilStartDelimiter/,\n        {\n          token: \"regexp.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol.R\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"regexp\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"regexp.delimiter\", next: \"@pop\" },\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexpContent\" }\n    ],\n    // Fallback to the generic sigil by default\n    \"sigilStart.interpol\": [\n      [\n        /~([a-z]|[A-Z]+)@sigilStartDelimiter/,\n        {\n          token: \"sigil.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"sigil.delimiter\", next: \"@pop\" },\n            \"@default\": \"sigil\"\n          }\n        }\n      ],\n      { include: \"@sigilContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol\": [\n      [\n        /~([a-z]|[A-Z]+)@sigilStartDelimiter/,\n        {\n          token: \"sigil.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"sigil\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"sigil.delimiter\", next: \"@pop\" },\n            \"@default\": \"sigil\"\n          }\n        }\n      ],\n      { include: \"@sigilContent\" }\n    ],\n    // Attributes\n    attributes: [\n      // Module @doc* attributes - tokenized as comments\n      [\n        /\\@(module|type)?doc (~[sS])?\"\"\"/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@doubleQuotedHeredocDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?'''/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@singleQuotedHeredocDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?\"/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@doubleQuotedStringDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?'/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@singleQuotedStringDocstring\"\n        }\n      ],\n      [/\\@(module|type)?doc false/, \"comment.block.documentation\"],\n      // Module attributes\n      [/\\@(@variableName)/, \"variable\"]\n    ],\n    doubleQuotedHeredocDocstring: [\n      [/\"\"\"/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    singleQuotedHeredocDocstring: [\n      [/'''/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    doubleQuotedStringDocstring: [\n      [/\"/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    singleQuotedStringDocstring: [\n      [/'/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    // Operators, punctuation, brackets\n    symbols: [\n      // Code point operator (either with regular character ?a or an escaped one ?\\n)\n      [/\\?(\\\\.|[^\\\\\\s])/, \"number.constant\"],\n      // Anonymous function arguments\n      [/&\\d+/, \"operator\"],\n      // Bitshift operators (must go before delimiters, so that << >> don't match first)\n      [/<<<|>>>/, \"operator\"],\n      // Delimiter pairs\n      [/[()\\[\\]\\{\\}]|<<|>>/, \"@brackets\"],\n      // Triple dot is a valid name (must go before operators, so that .. doesn't match instead)\n      [/\\.\\.\\./, \"identifier\"],\n      // Punctuation => (must go before operators, so it's not tokenized as = then >)\n      [/=>/, \"punctuation\"],\n      // Operators\n      [/@operator/, \"operator\"],\n      // Punctuation\n      [/[:;,.%]/, \"punctuation\"]\n    ],\n    // Generic helpers\n    stringContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@stringContent\" }\n    ],\n    stringContent: [[/./, \"string\"]],\n    stringConstantContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@stringConstantContent\" }\n    ],\n    stringConstantContent: [[/./, \"constant\"]],\n    regexpContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@regexpContent\" }\n    ],\n    regexpContent: [\n      // # may be a regular regexp char, so we use a heuristic\n      // assuming a # surrounded by whitespace is actually a comment.\n      [/(\\s)(#)(\\s.*)$/, [\"white\", \"comment.punctuation\", \"comment\"]],\n      [/./, \"regexp\"]\n    ],\n    sigilContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@sigilContent\" }\n    ],\n    sigilContent: [[/./, \"sigil\"]],\n    docstringContent: [[/./, \"comment.block.documentation\"]],\n    escapeChar: [[/@escape/, \"constant.character.escape\"]],\n    interpolation: [[/#{/, { token: \"delimiter.bracket.embed\", next: \"@interpolationContinue\" }]],\n    interpolationContinue: [\n      [/}/, { token: \"delimiter.bracket.embed\", next: \"@pop\" }],\n      // Interpolation brackets may contain arbitrary code,\n      // so we simply match against all the root rules,\n      // until we reach interpolation end (the above matches).\n      { include: \"@root\" }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,IACd,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,UAAU,CAAE,CAC7C,CAAE,KAAM,MAAO,MAAO,MAAO,CAC7B,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,KAAM,MAAO,KAAM,CAC5B,CACD,iBAAkB,CAChB,sBAAuB,kEACvB,sBAAuB,oDACxB,CACF,CACG,EAAW,CACb,aAAc,SACd,aAAc,UACd,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,mBAAoB,CACpD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CACzD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,KAAM,MAAO,KAAM,MAAO,0BAA2B,CAC9D,CAED,oBAAqB,CACnB,MACA,OACA,OACA,QACA,WACA,YACA,WACA,YACA,cACA,cACA,mBACA,YACA,cACA,eACA,UACA,YACD,CACD,iBAAkB,CAAC,MAAO,KAAM,MAAO,KAAM,OAAO,CACpD,kBAAmB,CAAC,QAAS,SAAU,UAAW,MAAM,CACxD,cAAe,CACb,QACA,OACA,QACA,OACA,KACA,OACA,MACA,KACA,MACA,KACA,QACA,QACA,UACA,SACA,QACA,QACA,MACA,SACA,mBACA,UACA,OACD,CACD,UAAW,CAAC,OAAQ,QAAS,MAAM,CACnC,YAAa,CAAC,aAAc,UAAW,UAAW,aAAc,iBAAiB,CAGjF,SAAU,yIAEV,aAAc,2BAEd,SAAU,0DACV,gBAAiB,2BACjB,UAAW,qBACX,WAAY,8BAEZ,wBAAyB,oBACzB,oBAAqB,sCACrB,kBAAmB,sCACnB,eAAgB,eAChB,QAAS,cACT,IAAK,8BACL,MAAO,kBACP,OAAQ,gBAER,OAAQ,0CAMR,UAAW,CACT,KAAM,CACJ,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,YAAa,CAGxB,CAAE,QAAS,qBAAsB,CACjC,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,eAAgB,CAC3B,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,SAAU,CACrB,CAAE,QAAS,UAAW,CACtB,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,WAAY,CACxB,CAED,WAAY,CAAC,CAAC,MAAO,QAAQ,CAAC,CAE9B,SAAU,CAAC,CAAC,UAAW,CAAC,sBAAuB,UAAU,CAAC,CAAC,CAE3D,kBAAmB,CACjB,CAAC,sBAAuB,CAAC,WAAY,uBAAwB,QAAQ,CAAC,CAGtE,CACE,8BACA,CAAE,MAAO,qBAAsB,KAAM,6BAA8B,CACpE,CACD,CACE,8BACA,CAAE,MAAO,qBAAsB,KAAM,6BAA8B,CACpE,CACF,CACD,0BAA2B,CACzB,CAAC,KAAM,CAAE,MAAO,qBAAsB,KAAM,OAAQ,CAAC,CACrD,CAAE,QAAS,iCAAkC,CAC9C,CACD,0BAA2B,CACzB,CAAC,KAAM,CAAE,MAAO,qBAAsB,KAAM,OAAQ,CAAC,CACrD,CAAE,QAAS,iCAAkC,CAC9C,CAED,QAAS,CACP,CAAC,YAAa,gBAAgB,CAC9B,CAAC,WAAY,eAAe,CAC5B,CAAC,SAAU,aAAa,CACxB,CAAC,sCAAuC,eAAe,CACvD,CAAC,WAAY,SAAS,CACvB,CAED,YAAa,CAIX,CACE,yFACA,CACE,sBACA,QACA,CACE,MAAO,CACL,QAAS,UACT,WAAY,WACb,CACF,CACF,CACF,CAED,CAEE,iCACA,CACE,MAAO,CAEL,uBAAwB,sBACxB,qBAAsB,UACtB,iBAAkB,UAClB,WAAY,gBACb,CACF,CACF,CACD,CAEE,6CACA,CAAC,kBAAmB,QAAS,WAAY,QAAS,gBAAgB,CACnE,CACD,CAEE,8CACA,CAAC,uBAAwB,WAAY,QAAS,WAAY,QAAS,gBAAgB,CACpF,CACD,CAEE,4BACA,CACE,WACA,QACA,CACE,MAAO,CACL,iBAAkB,UAClB,WAAY,gBACb,CACF,CACF,CACF,CACD,CAEE,0BACA,CAAC,WAAY,QAAS,gBAAgB,CACvC,CAED,CACE,gBACA,CACE,MAAO,CACL,uBAAwB,sBACxB,oBAAqB,mBACrB,qBAAsB,UACtB,iBAAkB,UAClB,aAAc,oBACd,eAAgB,oBAChB,MAAO,iBACP,WAAY,aACb,CACF,CACF,CAED,CAAC,cAAe,kBAAkB,CACnC,CAED,QAAS,CACP,CAAC,MAAO,CAAE,MAAO,mBAAoB,KAAM,uBAAwB,CAAC,CACpE,CAAC,MAAO,CAAE,MAAO,mBAAoB,KAAM,uBAAwB,CAAC,CACpE,CAAC,IAAK,CAAE,MAAO,mBAAoB,KAAM,sBAAuB,CAAC,CACjE,CAAC,IAAK,CAAE,MAAO,mBAAoB,KAAM,sBAAuB,CAAC,CAClE,CACD,oBAAqB,CACnB,CAAC,MAAO,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CAAC,CACpD,CAAE,QAAS,yBAA0B,CACtC,CACD,oBAAqB,CACnB,CAAC,MAAO,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CAAC,CACpD,CAAE,QAAS,yBAA0B,CACtC,CACD,mBAAoB,CAClB,CAAC,IAAK,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CAAC,CAClD,CAAE,QAAS,yBAA0B,CACtC,CACD,mBAAoB,CAClB,CAAC,IAAK,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CAAC,CAClD,CAAE,QAAS,yBAA0B,CACtC,CAED,MAAO,CACL,CAAC,iBAAkB,CAAC,uBAAwB,WAAW,CAAC,CACxD,CAAC,KAAM,CAAE,MAAO,qBAAsB,KAAM,0BAA2B,CAAC,CACxE,CAAC,KAAM,CAAE,MAAO,qBAAsB,KAAM,0BAA2B,CAAC,CACzE,CACD,uBAAwB,CACtB,CAAC,IAAK,CAAE,MAAO,qBAAsB,KAAM,OAAQ,CAAC,CACpD,CAAE,QAAS,iCAAkC,CAC9C,CACD,uBAAwB,CACtB,CAAC,IAAK,CAAE,MAAO,qBAAsB,KAAM,OAAQ,CAAC,CACpD,CAAE,QAAS,iCAAkC,CAC9C,CAsBD,OAAQ,CACN,CAAC,6BAA8B,CAAE,MAAO,WAAY,KAAM,kBAAmB,CAAC,CAC9E,CAAC,gCAAiC,CAAE,MAAO,WAAY,KAAM,oBAAqB,CAAC,CACpF,CACD,MAAO,CACL,CAAC,oBAAqB,CAAE,MAAO,WAAY,SAAU,yBAA0B,CAAC,CAChF,CAAC,oBAAqB,CAAE,MAAO,WAAY,SAAU,yBAA0B,CAAC,CAChF,CAAC,oBAAqB,CAAE,MAAO,WAAY,SAAU,yBAA0B,CAAC,CAChF,CAAC,oBAAqB,CAAE,MAAO,WAAY,SAAU,yBAA0B,CAAC,CAChF,CACE,4CACA,CAAE,MAAO,WAAY,SAAU,2BAA4B,CAC5D,CACF,CASD,wBAAyB,CACvB,CACE,yBACA,CACE,MAAO,mBACP,SAAU,iCACX,CACF,CACF,CACD,2BAA4B,CAC1B,CACE,sCACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CACtD,WAAY,SACb,CACF,CACF,CACD,CAAE,QAAS,yBAA0B,CACtC,CACD,0BAA2B,CACzB,CACE,yBACA,CACE,MAAO,mBACP,SAAU,iCACX,CACF,CACF,CACD,6BAA8B,CAE5B,CAAC,gCAAiC,SAAS,CAC3C,CACE,sCACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CACtD,WAAY,SACb,CACF,CACF,CACD,CAAE,QAAS,iBAAkB,CAC9B,CACD,wBAAyB,CACvB,CACE,yBACA,CACE,MAAO,mBACP,SAAU,iCACX,CACF,CACF,CACD,2BAA4B,CAC1B,CACE,sCACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CACtD,WAAY,SACb,CACF,CACF,CACD,CAAE,QAAS,yBAA0B,CACtC,CACD,0BAA2B,CACzB,CACE,yBACA,CACE,MAAO,mBACP,SAAU,iCACX,CACF,CACF,CACD,6BAA8B,CAE5B,CAAC,gCAAiC,SAAS,CAC3C,CACE,sCACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CACtD,WAAY,SACb,CACF,CACF,CACD,CAAE,QAAS,iBAAkB,CAC9B,CAED,sBAAuB,CACrB,CACE,sCACA,CACE,MAAO,kBACP,SAAU,iCACX,CACF,CACF,CACD,yBAA0B,CACxB,CACE,sCACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,kBAAmB,KAAM,OAAQ,CACrD,WAAY,QACb,CACF,CACF,CACD,CAAE,QAAS,wBAAyB,CACrC,CACD,wBAAyB,CACvB,CACE,sCACA,CACE,MAAO,kBACP,SAAU,iCACX,CACF,CACF,CACD,2BAA4B,CAE1B,CAAC,gCAAiC,QAAQ,CAC1C,CACE,sCACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,kBAAmB,KAAM,OAAQ,CACrD,WAAY,QACb,CACF,CACF,CACD,CAAE,QAAS,gBAAiB,CAC7B,CAED,WAAY,CAEV,CACE,kCACA,CACE,MAAO,8BACP,KAAM,gCACP,CACF,CACD,CACE,kCACA,CACE,MAAO,8BACP,KAAM,gCACP,CACF,CACD,CACE,gCACA,CACE,MAAO,8BACP,KAAM,+BACP,CACF,CACD,CACE,gCACA,CACE,MAAO,8BACP,KAAM,+BACP,CACF,CACD,CAAC,4BAA6B,8BAA8B,CAE5D,CAAC,oBAAqB,WAAW,CAClC,CACD,6BAA8B,CAC5B,CAAC,MAAO,CAAE,MAAO,8BAA+B,KAAM,OAAQ,CAAC,CAC/D,CAAE,QAAS,oBAAqB,CACjC,CACD,6BAA8B,CAC5B,CAAC,MAAO,CAAE,MAAO,8BAA+B,KAAM,OAAQ,CAAC,CAC/D,CAAE,QAAS,oBAAqB,CACjC,CACD,4BAA6B,CAC3B,CAAC,IAAK,CAAE,MAAO,8BAA+B,KAAM,OAAQ,CAAC,CAC7D,CAAE,QAAS,oBAAqB,CACjC,CACD,4BAA6B,CAC3B,CAAC,IAAK,CAAE,MAAO,8BAA+B,KAAM,OAAQ,CAAC,CAC7D,CAAE,QAAS,oBAAqB,CACjC,CAED,QAAS,CAEP,CAAC,kBAAmB,kBAAkB,CAEtC,CAAC,OAAQ,WAAW,CAEpB,CAAC,UAAW,WAAW,CAEvB,CAAC,qBAAsB,YAAY,CAEnC,CAAC,SAAU,aAAa,CAExB,CAAC,KAAM,cAAc,CAErB,CAAC,YAAa,WAAW,CAEzB,CAAC,UAAW,cAAc,CAC3B,CAED,sBAAuB,CACrB,CAAE,QAAS,iBAAkB,CAC7B,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,iBAAkB,CAC9B,CACD,cAAe,CAAC,CAAC,IAAK,SAAS,CAAC,CAChC,8BAA+B,CAC7B,CAAE,QAAS,iBAAkB,CAC7B,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,yBAA0B,CACtC,CACD,sBAAuB,CAAC,CAAC,IAAK,WAAW,CAAC,CAC1C,sBAAuB,CACrB,CAAE,QAAS,iBAAkB,CAC7B,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,iBAAkB,CAC9B,CACD,cAAe,CAGb,CAAC,iBAAkB,CAAC,QAAS,sBAAuB,UAAU,CAAC,CAC/D,CAAC,IAAK,SAAS,CAChB,CACD,qBAAsB,CACpB,CAAE,QAAS,iBAAkB,CAC7B,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,gBAAiB,CAC7B,CACD,aAAc,CAAC,CAAC,IAAK,QAAQ,CAAC,CAC9B,iBAAkB,CAAC,CAAC,IAAK,8BAA8B,CAAC,CACxD,WAAY,CAAC,CAAC,UAAW,4BAA4B,CAAC,CACtD,cAAe,CAAC,CAAC,KAAM,CAAE,MAAO,0BAA2B,KAAM,yBAA0B,CAAC,CAAC,CAC7F,sBAAuB,CACrB,CAAC,IAAK,CAAE,MAAO,0BAA2B,KAAM,OAAQ,CAAC,CAIzD,CAAE,QAAS,QAAS,CACrB,CACF,CACF"}