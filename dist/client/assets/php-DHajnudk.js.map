{"version": 3, "file": "php-DHajnudk.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/php/php.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/php/php.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*(#|//)region\\\\b\"),\n      end: new RegExp(\"^\\\\s*(#|//)endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  // ignoreCase: true,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.root\" }],\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@comment\"],\n      [/(<)(\\w+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)([:\\w]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)(\\w+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/[^<]+/]\n      // text\n    ],\n    doctype: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.comment\" }],\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.comment\" }],\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.otherTag\" }],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.script\" }],\n      [/type/, \"attribute.name\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.scriptAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.scriptAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.scriptWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInEmbeddedState.scriptEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.style\" }],\n      [/type/, \"attribute.name\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.styleAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.styleAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.styleWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInEmbeddedState.styleEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <style> tags handling\n    phpInSimpleState: [\n      [/<\\?((php)|=)?/, \"metatag.php\"],\n      [/\\?>/, { token: \"metatag.php\", switchTo: \"@$S2.$S3\" }],\n      { include: \"phpRoot\" }\n    ],\n    phpInEmbeddedState: [\n      [/<\\?((php)|=)?/, \"metatag.php\"],\n      [\n        /\\?>/,\n        {\n          token: \"metatag.php\",\n          switchTo: \"@$S2.$S3\",\n          nextEmbedded: \"$S3\"\n        }\n      ],\n      { include: \"phpRoot\" }\n    ],\n    phpRoot: [\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@phpKeywords\": { token: \"keyword.php\" },\n            \"@phpCompileTimeConstants\": { token: \"constant.php\" },\n            \"@default\": \"identifier.php\"\n          }\n        }\n      ],\n      [\n        /[$a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@phpPreDefinedVariables\": {\n              token: \"variable.predefined.php\"\n            },\n            \"@default\": \"variable.php\"\n          }\n        }\n      ],\n      // brackets\n      [/[{}]/, \"delimiter.bracket.php\"],\n      [/[\\[\\]]/, \"delimiter.array.php\"],\n      [/[()]/, \"delimiter.parenthesis.php\"],\n      // whitespace\n      [/[ \\t\\r\\n]+/],\n      // comments\n      [/(#|\\/\\/)$/, \"comment.php\"],\n      [/(#|\\/\\/)/, \"comment.php\", \"@phpLineComment\"],\n      // block comments\n      [/\\/\\*/, \"comment.php\", \"@phpComment\"],\n      // strings\n      [/\"/, \"string.php\", \"@phpDoubleQuoteString\"],\n      [/'/, \"string.php\", \"@phpSingleQuoteString\"],\n      // delimiters\n      [/[\\+\\-\\*\\%\\&\\|\\^\\~\\!\\=\\<\\>\\/\\?\\;\\:\\.\\,\\@]/, \"delimiter.php\"],\n      // numbers\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?/, \"number.float.php\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float.php\"],\n      [/0[xX][0-9a-fA-F']*[0-9a-fA-F]/, \"number.hex.php\"],\n      [/0[0-7']*[0-7]/, \"number.octal.php\"],\n      [/0[bB][0-1']*[0-1]/, \"number.binary.php\"],\n      [/\\d[\\d']*/, \"number.php\"],\n      [/\\d/, \"number.php\"]\n    ],\n    phpComment: [\n      [/\\*\\//, \"comment.php\", \"@pop\"],\n      [/[^*]+/, \"comment.php\"],\n      [/./, \"comment.php\"]\n    ],\n    phpLineComment: [\n      [/\\?>/, { token: \"@rematch\", next: \"@pop\" }],\n      [/.$/, \"comment.php\", \"@pop\"],\n      [/[^?]+$/, \"comment.php\", \"@pop\"],\n      [/[^?]+/, \"comment.php\"],\n      [/./, \"comment.php\"]\n    ],\n    phpDoubleQuoteString: [\n      [/[^\\\\\"]+/, \"string.php\"],\n      [/@escapes/, \"string.escape.php\"],\n      [/\\\\./, \"string.escape.invalid.php\"],\n      [/\"/, \"string.php\", \"@pop\"]\n    ],\n    phpSingleQuoteString: [\n      [/[^\\\\']+/, \"string.php\"],\n      [/@escapes/, \"string.escape.php\"],\n      [/\\\\./, \"string.escape.invalid.php\"],\n      [/'/, \"string.php\", \"@pop\"]\n    ]\n  },\n  phpKeywords: [\n    \"abstract\",\n    \"and\",\n    \"array\",\n    \"as\",\n    \"break\",\n    \"callable\",\n    \"case\",\n    \"catch\",\n    \"cfunction\",\n    \"class\",\n    \"clone\",\n    \"const\",\n    \"continue\",\n    \"declare\",\n    \"default\",\n    \"do\",\n    \"else\",\n    \"elseif\",\n    \"enddeclare\",\n    \"endfor\",\n    \"endforeach\",\n    \"endif\",\n    \"endswitch\",\n    \"endwhile\",\n    \"extends\",\n    \"false\",\n    \"final\",\n    \"for\",\n    \"foreach\",\n    \"function\",\n    \"global\",\n    \"goto\",\n    \"if\",\n    \"implements\",\n    \"interface\",\n    \"instanceof\",\n    \"insteadof\",\n    \"namespace\",\n    \"new\",\n    \"null\",\n    \"object\",\n    \"old_function\",\n    \"or\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"resource\",\n    \"static\",\n    \"switch\",\n    \"throw\",\n    \"trait\",\n    \"try\",\n    \"true\",\n    \"use\",\n    \"var\",\n    \"while\",\n    \"xor\",\n    \"die\",\n    \"echo\",\n    \"empty\",\n    \"exit\",\n    \"eval\",\n    \"include\",\n    \"include_once\",\n    \"isset\",\n    \"list\",\n    \"require\",\n    \"require_once\",\n    \"return\",\n    \"print\",\n    \"unset\",\n    \"yield\",\n    \"__construct\"\n  ],\n  phpCompileTimeConstants: [\n    \"__CLASS__\",\n    \"__DIR__\",\n    \"__FILE__\",\n    \"__LINE__\",\n    \"__NAMESPACE__\",\n    \"__METHOD__\",\n    \"__FUNCTION__\",\n    \"__TRAIT__\"\n  ],\n  phpPreDefinedVariables: [\n    \"$GLOBALS\",\n    \"$_SERVER\",\n    \"$_GET\",\n    \"$_POST\",\n    \"$_FILES\",\n    \"$_REQUEST\",\n    \"$_SESSION\",\n    \"$_ENV\",\n    \"$_COOKIE\",\n    \"$php_errormsg\",\n    \"$HTTP_RAW_POST_DATA\",\n    \"$http_response_header\",\n    \"$argc\",\n    \"$argv\"\n  ],\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,YAAa,uFACb,SAAU,CACR,YAAa,KACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC5C,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC5C,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC5C,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC5C,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACxD,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,uBAAuB,CACzC,IAAS,OAAO,0BAA0B,CAC3C,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,GAGd,UAAW,CACT,KAAM,CACJ,CAAC,gBAAiB,CAAE,MAAO,WAAY,SAAU,yBAA0B,CAAC,CAC5E,CAAC,YAAa,eAAgB,WAAW,CACzC,CAAC,OAAQ,eAAgB,WAAW,CACpC,CAAC,gBAAiB,CAAC,iBAAkB,WAAY,iBAAiB,CAAC,CACnE,CAAC,cAAe,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,UAAW,CAAC,CAAC,CAC3E,CAAC,aAAc,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,SAAU,CAAC,CAAC,CACzE,CAAC,cAAe,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,YAAa,CAAC,CAAC,CAC7E,CAAC,aAAc,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,YAAa,CAAC,CAAC,CAC5E,CAAC,IAAK,iBAAiB,CACvB,CAAC,QAAQ,CAEV,CACD,QAAS,CACP,CAAC,gBAAiB,CAAE,MAAO,WAAY,SAAU,4BAA6B,CAAC,CAC/E,CAAC,QAAS,uBAAuB,CACjC,CAAC,IAAK,eAAgB,OAAO,CAC9B,CACD,QAAS,CACP,CAAC,gBAAiB,CAAE,MAAO,WAAY,SAAU,4BAA6B,CAAC,CAC/E,CAAC,MAAO,eAAgB,OAAO,CAC/B,CAAC,QAAS,uBAAuB,CACjC,CAAC,IAAK,uBAAuB,CAC9B,CACD,SAAU,CACR,CAAC,gBAAiB,CAAE,MAAO,WAAY,SAAU,6BAA8B,CAAC,CAChF,CAAC,OAAQ,iBAAkB,OAAO,CAClC,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CAAC,aAAa,CAEf,CAGD,OAAQ,CACN,CAAC,gBAAiB,CAAE,MAAO,WAAY,SAAU,2BAA4B,CAAC,CAC9E,CAAC,OAAQ,iBAAkB,mBAAmB,CAC9C,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CACE,IACA,CACE,MAAO,iBACP,KAAM,kCACN,aAAc,kBACf,CACF,CACD,CAAC,aAAa,CAEd,CACE,sBACA,CAAC,iBAAkB,WAAY,CAAE,MAAO,iBAAkB,KAAM,OAAQ,CAAC,CAC1E,CACF,CAED,gBAAiB,CACf,CACE,gBACA,CACE,MAAO,WACP,SAAU,oCACX,CACF,CACD,CAAC,IAAK,YAAa,yBAAyB,CAC5C,CACE,IACA,CACE,MAAO,iBACP,KAAM,kCACN,aAAc,kBACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACvD,CAED,sBAAuB,CACrB,CACE,gBACA,CACE,MAAO,WACP,SAAU,0CACX,CACF,CACD,CACE,YACA,CACE,MAAO,kBACP,SAAU,2BACX,CACF,CACD,CACE,YACA,CACE,MAAO,kBACP,SAAU,2BACX,CACF,CACD,CACE,IACA,CACE,MAAO,iBACP,KAAM,kCACN,aAAc,kBACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACvD,CAED,qBAAsB,CACpB,CACE,gBACA,CACE,MAAO,WACP,SAAU,6CACX,CACF,CACD,CACE,IACA,CACE,MAAO,iBACP,KAAM,sBACN,aAAc,MACf,CACF,CACD,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CAAC,aAAa,CAEd,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACvD,CACD,eAAgB,CACd,CACE,gBACA,CACE,MAAO,WACP,SAAU,yCACV,aAAc,OACf,CACF,CACD,CAAC,YAAa,CAAE,MAAO,WAAY,KAAM,OAAQ,aAAc,OAAQ,CAAC,CACzE,CAID,MAAO,CACL,CAAC,gBAAiB,CAAE,MAAO,WAAY,SAAU,0BAA2B,CAAC,CAC7E,CAAC,OAAQ,iBAAkB,kBAAkB,CAC7C,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CACE,IACA,CACE,MAAO,iBACP,KAAM,0BACN,aAAc,WACf,CACF,CACD,CAAC,aAAa,CAEd,CACE,qBACA,CAAC,iBAAkB,WAAY,CAAE,MAAO,iBAAkB,KAAM,OAAQ,CAAC,CAC1E,CACF,CAED,eAAgB,CACd,CACE,gBACA,CACE,MAAO,WACP,SAAU,mCACX,CACF,CACD,CAAC,IAAK,YAAa,wBAAwB,CAC3C,CACE,IACA,CACE,MAAO,iBACP,KAAM,0BACN,aAAc,WACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACtD,CAED,qBAAsB,CACpB,CACE,gBACA,CACE,MAAO,WACP,SAAU,yCACX,CACF,CACD,CACE,YACA,CACE,MAAO,kBACP,SAAU,0BACX,CACF,CACD,CACE,YACA,CACE,MAAO,kBACP,SAAU,0BACX,CACF,CACD,CACE,IACA,CACE,MAAO,iBACP,KAAM,0BACN,aAAc,WACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACtD,CAED,oBAAqB,CACnB,CACE,gBACA,CACE,MAAO,WACP,SAAU,4CACX,CACF,CACD,CACE,IACA,CACE,MAAO,iBACP,KAAM,qBACN,aAAc,MACf,CACF,CACD,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CAAC,aAAa,CAEd,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACtD,CACD,cAAe,CACb,CACE,gBACA,CACE,MAAO,WACP,SAAU,wCACV,aAAc,OACf,CACF,CACD,CAAC,WAAY,CAAE,MAAO,WAAY,KAAM,OAAQ,aAAc,OAAQ,CAAC,CACxE,CAED,iBAAkB,CAChB,CAAC,gBAAiB,cAAc,CAChC,CAAC,MAAO,CAAE,MAAO,cAAe,SAAU,WAAY,CAAC,CACvD,CAAE,QAAS,UAAW,CACvB,CACD,mBAAoB,CAClB,CAAC,gBAAiB,cAAc,CAChC,CACE,MACA,CACE,MAAO,cACP,SAAU,WACV,aAAc,MACf,CACF,CACD,CAAE,QAAS,UAAW,CACvB,CACD,QAAS,CACP,CACE,eACA,CACE,MAAO,CACL,eAAgB,CAAE,MAAO,cAAe,CACxC,2BAA4B,CAAE,MAAO,eAAgB,CACrD,WAAY,iBACb,CACF,CACF,CACD,CACE,gBACA,CACE,MAAO,CACL,0BAA2B,CACzB,MAAO,0BACR,CACD,WAAY,eACb,CACF,CACF,CAED,CAAC,OAAQ,wBAAwB,CACjC,CAAC,SAAU,sBAAsB,CACjC,CAAC,OAAQ,4BAA4B,CAErC,CAAC,aAAa,CAEd,CAAC,YAAa,cAAc,CAC5B,CAAC,WAAY,cAAe,kBAAkB,CAE9C,CAAC,OAAQ,cAAe,cAAc,CAEtC,CAAC,IAAK,aAAc,wBAAwB,CAC5C,CAAC,IAAK,aAAc,wBAAwB,CAE5C,CAAC,2CAA4C,gBAAgB,CAE7D,CAAC,yBAA0B,mBAAmB,CAC9C,CAAC,2BAA4B,mBAAmB,CAChD,CAAC,gCAAiC,iBAAiB,CACnD,CAAC,gBAAiB,mBAAmB,CACrC,CAAC,oBAAqB,oBAAoB,CAC1C,CAAC,WAAY,aAAa,CAC1B,CAAC,KAAM,aAAa,CACrB,CACD,WAAY,CACV,CAAC,OAAQ,cAAe,OAAO,CAC/B,CAAC,QAAS,cAAc,CACxB,CAAC,IAAK,cAAc,CACrB,CACD,eAAgB,CACd,CAAC,MAAO,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CAC5C,CAAC,KAAM,cAAe,OAAO,CAC7B,CAAC,SAAU,cAAe,OAAO,CACjC,CAAC,QAAS,cAAc,CACxB,CAAC,IAAK,cAAc,CACrB,CACD,qBAAsB,CACpB,CAAC,UAAW,aAAa,CACzB,CAAC,WAAY,oBAAoB,CACjC,CAAC,MAAO,4BAA4B,CACpC,CAAC,IAAK,aAAc,OAAO,CAC5B,CACD,qBAAsB,CACpB,CAAC,UAAW,aAAa,CACzB,CAAC,WAAY,oBAAoB,CACjC,CAAC,MAAO,4BAA4B,CACpC,CAAC,IAAK,aAAc,OAAO,CAC5B,CACF,CACD,YAAa,4gBA0EZ,CACD,wBAAyB,CACvB,YACA,UACA,WACA,WACA,gBACA,aACA,eACA,YACD,CACD,uBAAwB,CACtB,WACA,WACA,QACA,SACA,UACA,YACA,YACA,QACA,WACA,gBACA,sBACA,wBACA,QACA,QACD,CACD,QAAS,wEACV"}