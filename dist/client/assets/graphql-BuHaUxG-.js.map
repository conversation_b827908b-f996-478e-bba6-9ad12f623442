{"version": 3, "file": "graphql-BuHaUxG-.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/graphql/graphql.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/graphql/graphql.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"\"\"', close: '\"\"\"', notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"\"\"', close: '\"\"\"' },\n    { open: '\"', close: '\"' }\n  ],\n  folding: {\n    offSide: true\n  }\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  defaultToken: \"invalid\",\n  tokenPostfix: \".gql\",\n  keywords: [\n    \"null\",\n    \"true\",\n    \"false\",\n    \"query\",\n    \"mutation\",\n    \"subscription\",\n    \"extend\",\n    \"schema\",\n    \"directive\",\n    \"scalar\",\n    \"type\",\n    \"interface\",\n    \"union\",\n    \"enum\",\n    \"input\",\n    \"implements\",\n    \"fragment\",\n    \"on\"\n  ],\n  typeKeywords: [\"Int\", \"Float\", \"String\", \"Boolean\", \"ID\"],\n  directiveLocations: [\n    \"SCHEMA\",\n    \"SCALAR\",\n    \"OBJECT\",\n    \"FIELD_DEFINITION\",\n    \"ARGUMENT_DEFINITION\",\n    \"INTERFACE\",\n    \"UNION\",\n    \"ENUM\",\n    \"ENUM_VALUE\",\n    \"INPUT_OBJECT\",\n    \"INPUT_FIELD_DEFINITION\",\n    \"QUERY\",\n    \"MUTATION\",\n    \"SUBSCRIPTION\",\n    \"FIELD\",\n    \"FRAGMENT_DEFINITION\",\n    \"FRAGMENT_SPREAD\",\n    \"INLINE_FRAGMENT\",\n    \"VARIABLE_DEFINITION\"\n  ],\n  operators: [\"=\", \"!\", \"?\", \":\", \"&\", \"|\"],\n  // we include these common regular expressions\n  symbols: /[=!?:&|]+/,\n  // https://facebook.github.io/graphql/draft/#sec-String-Value\n  escapes: /\\\\(?:[\"\\\\\\/bfnrt]|u[0-9A-Fa-f]{4})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // fields and argument names\n      [\n        /[a-z_][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"key.identifier\"\n          }\n        }\n      ],\n      // identify typed input variables\n      [\n        /[$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"argument.identifier\"\n          }\n        }\n      ],\n      // to show class names nicely\n      [\n        /[A-Z][\\w\\$]*/,\n        {\n          cases: {\n            \"@typeKeywords\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, { cases: { \"@operators\": \"operator\", \"@default\": \"\" } }],\n      // @ annotations.\n      // As an example, we emit a debugging log message on these tokens.\n      // Note: message are supressed during the first load -- change some lines to see them.\n      [/@\\s*[a-zA-Z_\\$][\\w\\$]*/, { token: \"annotation\", log: \"annotation token: $0\" }],\n      // numbers\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      [/\"\"\"/, { token: \"string\", next: \"@mlstring\", nextEmbedded: \"markdown\" }],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }]\n    ],\n    mlstring: [\n      [/[^\"]+/, \"string\"],\n      ['\"\"\"', { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/#.*$/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,IACd,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,MAAO,MAAO,MAAO,MAAO,CAAC,SAAU,UAAU,CAAE,CAC3D,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACxD,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,MAAO,MAAO,MAAO,CAC7B,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,GACV,CACF,CACG,EAAW,CAEb,aAAc,UACd,aAAc,OACd,SAAU,CACR,OACA,OACA,QACA,QACA,WACA,eACA,SACA,SACA,YACA,SACA,OACA,YACA,QACA,OACA,QACA,aACA,WACA,KACD,CACD,aAAc,CAAC,MAAO,QAAS,SAAU,UAAW,KAAK,CACzD,mBAAoB,CAClB,SACA,SACA,SACA,mBACA,sBACA,YACA,QACA,OACA,aACA,eACA,yBACA,QACA,WACA,eACA,QACA,sBACA,kBACA,kBACA,sBACD,CACD,UAAW,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CAEzC,QAAS,YAET,QAAS,qCAET,UAAW,CACT,KAAM,CAEJ,CACE,eACA,CACE,MAAO,CACL,YAAa,UACb,WAAY,iBACb,CACF,CACF,CAED,CACE,YACA,CACE,MAAO,CACL,YAAa,UACb,WAAY,sBACb,CACF,CACF,CAED,CACE,eACA,CACE,MAAO,CACL,gBAAiB,UACjB,WAAY,kBACb,CACF,CACF,CAED,CAAE,QAAS,cAAe,CAE1B,CAAC,aAAc,YAAY,CAC3B,CAAC,WAAY,CAAE,MAAO,CAAE,aAAc,WAAY,WAAY,GAAI,CAAE,CAAC,CAIrE,CAAC,yBAA0B,CAAE,MAAO,aAAc,IAAK,uBAAwB,CAAC,CAEhF,CAAC,2BAA4B,eAAe,CAC5C,CAAC,oBAAqB,aAAa,CACnC,CAAC,MAAO,SAAS,CAEjB,CAAC,QAAS,YAAY,CACtB,CAAC,MAAO,CAAE,MAAO,SAAU,KAAM,YAAa,aAAc,WAAY,CAAC,CAEzE,CAAC,kBAAmB,iBAAiB,CAErC,CAAC,IAAK,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,UAAW,CAAC,CACpE,CACD,SAAU,CACR,CAAC,QAAS,SAAS,CACnB,CAAC,MAAO,CAAE,MAAO,SAAU,KAAM,OAAQ,aAAc,OAAQ,CAAC,CACjE,CACD,OAAQ,CACN,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,CAAE,MAAO,eAAgB,QAAS,SAAU,KAAM,OAAQ,CAAC,CAClE,CACD,WAAY,CACV,CAAC,aAAc,GAAG,CAClB,CAAC,OAAQ,UAAU,CACpB,CACF,CACF"}