/*!-----------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation. All rights reserved.
* Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
* Released under the MIT license
* https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
*-----------------------------------------------------------------------------*/
var e=e=>`\\b${e}\\b`,t=e=>`(?!${e})`,n=e(`[_a-zA-Z][_a-zA-Z0-9]*`),r=e(`[_a-zA-Z-0-9]+`),i=[`import`,`model`,`scalar`,`namespace`,`op`,`interface`,`union`,`using`,`is`,`extends`,`enum`,`alias`,`return`,`void`,`if`,`else`,`projection`,`dec`,`extern`,`fn`],a=[`true`,`false`,`null`,`unknown`,`never`],o=`[ \\t\\r\\n]`,s=`[0-9]+`,c={comments:{lineComment:`//`,blockComment:[`/*`,`*/`]},brackets:[[`{`,`}`],[`[`,`]`],[`(`,`)`]],autoClosingPairs:[{open:`{`,close:`}`},{open:`[`,close:`]`},{open:`(`,close:`)`},{open:`"`,close:`"`},{open:`/**`,close:` */`,notIn:[`string`]}],surroundingPairs:[{open:`{`,close:`}`},{open:`[`,close:`]`},{open:`(`,close:`)`},{open:`"`,close:`"`}],indentationRules:{decreaseIndentPattern:RegExp(`^((?!.*?/\\*).*\\*/)?\\s*[\\}\\]].*$`),increaseIndentPattern:RegExp(`^((?!//).)*(\\{([^}"'\`/]*|(\\t|[ ])*//.*)|\\([^)"'\`/]*|\\[[^\\]"'\`/]*)$`),unIndentedLinePattern:RegExp(`^(\\t|[ ])*[ ]\\*[^/]*\\*/\\s*$|^(\\t|[ ])*[ ]\\*/\\s*$|^(\\t|[ ])*[ ]\\*([ ]([^\\*]|\\*(?!/))*)?$`)}},l={defaultToken:``,tokenPostfix:`.tsp`,brackets:[{open:`{`,close:`}`,token:`delimiter.curly`},{open:`[`,close:`]`,token:`delimiter.square`},{open:`(`,close:`)`,token:`delimiter.parenthesis`}],symbols:/[=:;<>]+/,keywords:i,namedLiterals:a,escapes:'\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\|"|\\${)',tokenizer:{root:[{include:`@expression`},{include:`@whitespace`}],stringVerbatim:[{regex:`(|"|"")[^"]`,action:{token:`string`}},{regex:`"""${t(`"`)}`,action:{token:`string`,next:`@pop`}}],stringLiteral:[{regex:"\\${",action:{token:`delimiter.bracket`,next:`@bracketCounting`}},{regex:`[^\\\\"$]+`,action:{token:`string`}},{regex:`@escapes`,action:{token:`string.escape`}},{regex:`\\\\.`,action:{token:`string.escape.invalid`}},{regex:`"`,action:{token:`string`,next:`@pop`}}],bracketCounting:[{regex:`{`,action:{token:`delimiter.bracket`,next:`@bracketCounting`}},{regex:`}`,action:{token:`delimiter.bracket`,next:`@pop`}},{include:`@expression`}],comment:[{regex:`[^\\*]+`,action:{token:`comment`}},{regex:`\\*\\/`,action:{token:`comment`,next:`@pop`}},{regex:`[\\/*]`,action:{token:`comment`}}],whitespace:[{regex:o},{regex:`\\/\\*`,action:{token:`comment`,next:`@comment`}},{regex:`\\/\\/.*$`,action:{token:`comment`}}],expression:[{regex:`"""`,action:{token:`string`,next:`@stringVerbatim`}},{regex:`"${t(`""`)}`,action:{token:`string`,next:`@stringLiteral`}},{regex:s,action:{token:`number`}},{regex:n,action:{cases:{"@keywords":{token:`keyword`},"@namedLiterals":{token:`keyword`},"@default":{token:`identifier`}}}},{regex:`@${n}`,action:{token:`tag`}},{regex:`#${r}`,action:{token:`directive`}}]}};export{c as conf,l as language};
//# sourceMappingURL=typespec-DGWi9L5L.js.map