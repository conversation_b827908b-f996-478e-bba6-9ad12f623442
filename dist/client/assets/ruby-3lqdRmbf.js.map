{"version": 3, "file": "ruby-3lqdRmbf.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/ruby/ruby.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/ruby/ruby.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"=begin\", \"=end\"]\n  },\n  brackets: [\n    [\"(\", \")\"],\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\n      `^\\\\s*((begin|class|(private|protected)\\\\s+def|def|else|elsif|ensure|for|if|module|rescue|unless|until|when|while|case)|([^#]*\\\\sdo\\\\b)|([^#]*=\\\\s*(case|if|unless)))\\\\b([^#\\\\{;]|(\"|'|/).*\\\\4)*(#.*)?$`\n    ),\n    decreaseIndentPattern: new RegExp(\n      \"^\\\\s*([}\\\\]]([,)]?\\\\s*(#|$)|\\\\.[a-zA-Z_]\\\\w*\\\\b)|(end|rescue|ensure|else|elsif|when)\\\\b)\"\n    )\n  }\n};\nvar language = {\n  tokenPostfix: \".ruby\",\n  keywords: [\n    \"__LINE__\",\n    \"__ENCODING__\",\n    \"__FILE__\",\n    \"BEGIN\",\n    \"END\",\n    \"alias\",\n    \"and\",\n    \"begin\",\n    \"break\",\n    \"case\",\n    \"class\",\n    \"def\",\n    \"defined?\",\n    \"do\",\n    \"else\",\n    \"elsif\",\n    \"end\",\n    \"ensure\",\n    \"for\",\n    \"false\",\n    \"if\",\n    \"in\",\n    \"module\",\n    \"next\",\n    \"nil\",\n    \"not\",\n    \"or\",\n    \"redo\",\n    \"rescue\",\n    \"retry\",\n    \"return\",\n    \"self\",\n    \"super\",\n    \"then\",\n    \"true\",\n    \"undef\",\n    \"unless\",\n    \"until\",\n    \"when\",\n    \"while\",\n    \"yield\"\n  ],\n  keywordops: [\"::\", \"..\", \"...\", \"?\", \":\", \"=>\"],\n  builtins: [\n    \"require\",\n    \"public\",\n    \"private\",\n    \"include\",\n    \"extend\",\n    \"attr_reader\",\n    \"protected\",\n    \"private_class_method\",\n    \"protected_class_method\",\n    \"new\"\n  ],\n  // these are closed by 'end' (if, while and until are handled separately)\n  declarations: [\n    \"module\",\n    \"class\",\n    \"def\",\n    \"case\",\n    \"do\",\n    \"begin\",\n    \"for\",\n    \"if\",\n    \"while\",\n    \"until\",\n    \"unless\"\n  ],\n  linedecls: [\"def\", \"case\", \"do\", \"begin\", \"for\", \"if\", \"while\", \"until\", \"unless\"],\n  operators: [\n    \"^\",\n    \"&\",\n    \"|\",\n    \"<=>\",\n    \"==\",\n    \"===\",\n    \"!~\",\n    \"=~\",\n    \">\",\n    \">=\",\n    \"<\",\n    \"<=\",\n    \"<<\",\n    \">>\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"**\",\n    \"~\",\n    \"+@\",\n    \"-@\",\n    \"[]\",\n    \"[]=\",\n    \"`\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"**=\",\n    \"/=\",\n    \"^=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\",\n    \"&=\",\n    \"&&=\",\n    \"||=\",\n    \"|=\"\n  ],\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%\\.]+/,\n  // escape sequences\n  escape: /(?:[abefnrstv\\\\\"'\\n\\r]|[0-7]{1,3}|x[0-9A-Fa-f]{1,2}|u[0-9A-Fa-f]{4})/,\n  escapes: /\\\\(?:C\\-(@escape|.)|c(@escape|.)|@escape)/,\n  decpart: /\\d(_?\\d)*/,\n  decimal: /0|@decpart/,\n  delim: /[^a-zA-Z0-9\\s\\n\\r]/,\n  heredelim: /(?:\\w+|'[^']*'|\"[^\"]*\"|`[^`]*`)/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[AzZbBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})?/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    // Main entry.\n    // root.<decl> where decl is the current opening declaration (like 'class')\n    root: [\n      // identifiers and keywords\n      // most complexity here is due to matching 'end' correctly with declarations.\n      // We distinguish a declaration that comes first on a line, versus declarations further on a line (which are most likey modifiers)\n      [\n        /^(\\s*)([a-z_]\\w*[!?=]?)/,\n        [\n          \"white\",\n          {\n            cases: {\n              \"for|until|while\": {\n                token: \"keyword.$2\",\n                next: \"@dodecl.$2\"\n              },\n              \"@declarations\": {\n                token: \"keyword.$2\",\n                next: \"@root.$2\"\n              },\n              end: { token: \"keyword.$S2\", next: \"@pop\" },\n              \"@keywords\": \"keyword\",\n              \"@builtins\": \"predefined\",\n              \"@default\": \"identifier\"\n            }\n          }\n        ]\n      ],\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            \"if|unless|while|until\": {\n              token: \"keyword.$0x\",\n              next: \"@modifier.$0x\"\n            },\n            for: { token: \"keyword.$2\", next: \"@dodecl.$2\" },\n            \"@linedecls\": { token: \"keyword.$0\", next: \"@root.$0\" },\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z][\\w]*[!?=]?/, \"constructor.identifier\"],\n      // constant\n      [/\\$[\\w]*/, \"global.constant\"],\n      // global\n      [/@[\\w]*/, \"namespace.instance.identifier\"],\n      // instance\n      [/@@@[\\w]*/, \"namespace.class.identifier\"],\n      // class\n      // here document\n      [/<<[-~](@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      [/[ \\t\\r\\n]+<<(@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      [/^<<(@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      // whitespace\n      { include: \"@whitespace\" },\n      // strings\n      [/\"/, { token: \"string.d.delim\", next: '@dstring.d.\"' }],\n      [/'/, { token: \"string.sq.delim\", next: \"@sstring.sq\" }],\n      // % literals. For efficiency, rematch in the 'pstring' state\n      [/%([rsqxwW]|Q?)/, { token: \"@rematch\", next: \"pstring\" }],\n      // commands and symbols\n      [/`/, { token: \"string.x.delim\", next: \"@dstring.x.`\" }],\n      [/:(\\w|[$@])\\w*[!?=]?/, \"string.s\"],\n      [/:\"/, { token: \"string.s.delim\", next: '@dstring.s.\"' }],\n      [/:'/, { token: \"string.s.delim\", next: \"@sstring.s\" }],\n      // regular expressions. Lookahead for a (not escaped) closing forwardslash on the same line\n      [/\\/(?=(\\\\\\/|[^\\/\\n])+\\/)/, { token: \"regexp.delim\", next: \"@regexp\" }],\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@keywordops\": \"keyword\",\n            \"@operators\": \"operator\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/[;,]/, \"delimiter\"],\n      // numbers\n      [/0[xX][0-9a-fA-F](_?[0-9a-fA-F])*/, \"number.hex\"],\n      [/0[_oO][0-7](_?[0-7])*/, \"number.octal\"],\n      [/0[bB][01](_?[01])*/, \"number.binary\"],\n      [/0[dD]@decpart/, \"number\"],\n      [\n        /@decimal((\\.@decpart)?([eE][\\-+]?@decpart)?)/,\n        {\n          cases: {\n            $1: \"number.float\",\n            \"@default\": \"number\"\n          }\n        }\n      ]\n    ],\n    // used to not treat a 'do' as a block opener if it occurs on the same\n    // line as a 'do' statement: 'while|until|for'\n    // dodecl.<decl> where decl is the declarations started, like 'while'\n    dodecl: [\n      [/^/, { token: \"\", switchTo: \"@root.$S2\" }],\n      // get out of do-skipping mode on a new line\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            // end on same line\n            do: { token: \"keyword\", switchTo: \"@root.$S2\" },\n            // do on same line: not an open bracket here\n            \"@linedecls\": {\n              token: \"@rematch\",\n              switchTo: \"@root.$S2\"\n            },\n            // other declaration on same line: rematch\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@root\" }\n    ],\n    // used to prevent potential modifiers ('if|until|while|unless') to match\n    // with 'end' keywords.\n    // modifier.<decl>x where decl is the declaration starter, like 'if'\n    modifier: [\n      [/^/, \"\", \"@pop\"],\n      // it was a modifier: get out of modifier mode on a new line\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            // end on same line\n            \"then|else|elsif|do\": {\n              token: \"keyword\",\n              switchTo: \"@root.$S2\"\n            },\n            // real declaration and not a modifier\n            \"@linedecls\": {\n              token: \"@rematch\",\n              switchTo: \"@root.$S2\"\n            },\n            // other declaration => not a modifier\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@root\" }\n    ],\n    // single quote strings (also used for symbols)\n    // sstring.<kind>  where kind is 'sq' (single quote) or 's' (symbol)\n    sstring: [\n      [/[^\\\\']+/, \"string.$S2\"],\n      [/\\\\\\\\|\\\\'|\\\\$/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.invalid\"],\n      [/'/, { token: \"string.$S2.delim\", next: \"@pop\" }]\n    ],\n    // double quoted \"string\".\n    // dstring.<kind>.<delim> where kind is 'd' (double quoted), 'x' (command), or 's' (symbol)\n    // and delim is the ending delimiter (\" or `)\n    dstring: [\n      [/[^\\\\`\"#]+/, \"string.$S2\"],\n      [/#/, \"string.$S2.escape\", \"@interpolated\"],\n      [/\\\\$/, \"string.$S2.escape\"],\n      [/@escapes/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.escape.invalid\"],\n      [\n        /[`\"]/,\n        {\n          cases: {\n            \"$#==$S3\": { token: \"string.$S2.delim\", next: \"@pop\" },\n            \"@default\": \"string.$S2\"\n          }\n        }\n      ]\n    ],\n    // literal documents\n    // heredoc.<close> where close is the closing delimiter\n    heredoc: [\n      [\n        /^(\\s*)(@heredelim)$/,\n        {\n          cases: {\n            \"$2==$S2\": [\"string.heredoc\", { token: \"string.heredoc.delimiter\", next: \"@pop\" }],\n            \"@default\": [\"string.heredoc\", \"string.heredoc\"]\n          }\n        }\n      ],\n      [/.*/, \"string.heredoc\"]\n    ],\n    // interpolated sequence\n    interpolated: [\n      [/\\$\\w*/, \"global.constant\", \"@pop\"],\n      [/@\\w*/, \"namespace.class.identifier\", \"@pop\"],\n      [/@@@\\w*/, \"namespace.instance.identifier\", \"@pop\"],\n      [\n        /[{]/,\n        {\n          token: \"string.escape.curly\",\n          switchTo: \"@interpolated_compound\"\n        }\n      ],\n      [\"\", \"\", \"@pop\"]\n      // just a # is interpreted as a #\n    ],\n    // any code\n    interpolated_compound: [\n      [/[}]/, { token: \"string.escape.curly\", next: \"@pop\" }],\n      { include: \"@root\" }\n    ],\n    // %r quoted regexp\n    // pregexp.<open>.<close> where open/close are the open/close delimiter\n    pregexp: [\n      { include: \"@whitespace\" },\n      // turns out that you can quote using regex control characters, aargh!\n      // for example; %r|kgjgaj| is ok (even though | is used for alternation)\n      // so, we need to match those first\n      [\n        /[^\\(\\{\\[\\\\]/,\n        {\n          cases: {\n            \"$#==$S3\": { token: \"regexp.delim\", next: \"@pop\" },\n            \"$#==$S2\": { token: \"regexp.delim\", next: \"@push\" },\n            // nested delimiters are allowed..\n            \"~[)}\\\\]]\": \"@brackets.regexp.escape.control\",\n            \"~@regexpctl\": \"regexp.escape.control\",\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexcontrol\" }\n    ],\n    // We match regular expression quite precisely\n    regexp: [\n      { include: \"@regexcontrol\" },\n      [/[^\\\\\\/]/, \"regexp\"],\n      [\"/[ixmp]*\", { token: \"regexp.delim\" }, \"@pop\"]\n    ],\n    regexcontrol: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\n          \"@brackets.regexp.escape.control\",\n          \"regexp.escape.control\",\n          \"@brackets.regexp.escape.control\"\n        ]\n      ],\n      [\n        /(\\[)(\\^?)/,\n        [\"@brackets.regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?[:=!])/, [\"@brackets.regexp.escape.control\", \"regexp.escape.control\"]],\n      [/\\(\\?#/, { token: \"regexp.escape.control\", next: \"@regexpcomment\" }],\n      [/[()]/, \"@brackets.regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/\\\\$/, \"regexp.escape\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/#/, \"regexp.escape\", \"@interpolated\"]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/\\\\$/, \"regexp.escape\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [/\\]/, \"@brackets.regexp.escape.control\", \"@pop\"]\n    ],\n    regexpcomment: [\n      [/[^)]+/, \"comment\"],\n      [/\\)/, { token: \"regexp.escape.control\", next: \"@pop\" }]\n    ],\n    // % quoted strings\n    // A bit repetitive since we need to often special case the kind of ending delimiter\n    pstring: [\n      [/%([qws])\\(/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.(.)\" }],\n      [/%([qws])\\[/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.[.]\" }],\n      [/%([qws])\\{/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.{.}\" }],\n      [/%([qws])</, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.<.>\" }],\n      [/%([qws])(@delim)/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.$2.$2\" }],\n      [/%r\\(/, { token: \"regexp.delim\", switchTo: \"@pregexp.(.)\" }],\n      [/%r\\[/, { token: \"regexp.delim\", switchTo: \"@pregexp.[.]\" }],\n      [/%r\\{/, { token: \"regexp.delim\", switchTo: \"@pregexp.{.}\" }],\n      [/%r</, { token: \"regexp.delim\", switchTo: \"@pregexp.<.>\" }],\n      [/%r(@delim)/, { token: \"regexp.delim\", switchTo: \"@pregexp.$1.$1\" }],\n      [/%(x|W|Q?)\\(/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.(.)\" }],\n      [/%(x|W|Q?)\\[/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.[.]\" }],\n      [/%(x|W|Q?)\\{/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.{.}\" }],\n      [/%(x|W|Q?)</, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.<.>\" }],\n      [/%(x|W|Q?)(@delim)/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.$2.$2\" }],\n      [/%([rqwsxW]|Q?)./, { token: \"invalid\", next: \"@pop\" }],\n      // recover\n      [/./, { token: \"invalid\", next: \"@pop\" }]\n      // recover\n    ],\n    // non-expanded quoted string.\n    // qstring.<kind>.<open>.<close>\n    //  kind = q|w|s  (single quote, array, symbol)\n    //  open = open delimiter\n    //  close = close delimiter\n    qstring: [\n      [/\\\\$/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.escape\"],\n      [\n        /./,\n        {\n          cases: {\n            \"$#==$S4\": { token: \"string.$S2.delim\", next: \"@pop\" },\n            \"$#==$S3\": { token: \"string.$S2.delim\", next: \"@push\" },\n            // nested delimiters are allowed..\n            \"@default\": \"string.$S2\"\n          }\n        }\n      ]\n    ],\n    // expanded quoted string.\n    // qqstring.<kind>.<open>.<close>\n    //  kind = Q|W|x  (double quote, array, command)\n    //  open = open delimiter\n    //  close = close delimiter\n    qqstring: [[/#/, \"string.$S2.escape\", \"@interpolated\"], { include: \"@qstring\" }],\n    // whitespace & comments\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/^\\s*=begin\\b/, \"comment\", \"@comment\"],\n      [/#.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^=]+/, \"comment\"],\n      [/^\\s*=begin\\b/, \"comment.invalid\"],\n      // nested comment\n      [/^\\s*=end\\b.*/, \"comment\", \"@pop\"],\n      [/[=]/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,IACb,aAAc,CAAC,SAAU,OAAO,CACjC,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,sBAA2B,OACzB,yMACD,CACD,sBAA2B,OACzB,2FACD,CACF,CACF,CACG,EAAW,CACb,aAAc,QACd,SAAU,qPA0CT,CACD,WAAY,CAAC,KAAM,KAAM,MAAO,IAAK,IAAK,KAAK,CAC/C,SAAU,CACR,UACA,SACA,UACA,UACA,SACA,cACA,YACA,uBACA,yBACA,MACD,CAED,aAAc,CACZ,SACA,QACA,MACA,OACA,KACA,QACA,MACA,KACA,QACA,QACA,SACD,CACD,UAAW,CAAC,MAAO,OAAQ,KAAM,QAAS,MAAO,KAAM,QAAS,QAAS,SAAS,CAClF,UAAW,6HAwCV,CACD,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CACzD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,mBAAoB,CACrD,CAED,QAAS,0BAET,OAAQ,uEACR,QAAS,4CACT,QAAS,YACT,QAAS,aACT,MAAO,qBACP,UAAW,kCACX,UAAW,yBACX,UAAW,mFAEX,UAAW,CAGT,KAAM,CAIJ,CACE,0BACA,CACE,QACA,CACE,MAAO,CACL,kBAAmB,CACjB,MAAO,aACP,KAAM,aACP,CACD,gBAAiB,CACf,MAAO,aACP,KAAM,WACP,CACD,IAAK,CAAE,MAAO,cAAe,KAAM,OAAQ,CAC3C,YAAa,UACb,YAAa,aACb,WAAY,aACb,CACF,CACF,CACF,CACD,CACE,kBACA,CACE,MAAO,CACL,wBAAyB,CACvB,MAAO,cACP,KAAM,gBACP,CACD,IAAK,CAAE,MAAO,aAAc,KAAM,aAAc,CAChD,aAAc,CAAE,MAAO,aAAc,KAAM,WAAY,CACvD,IAAK,CAAE,MAAO,cAAe,KAAM,OAAQ,CAC3C,YAAa,UACb,YAAa,aACb,WAAY,aACb,CACF,CACF,CACD,CAAC,mBAAoB,yBAAyB,CAE9C,CAAC,UAAW,kBAAkB,CAE9B,CAAC,SAAU,gCAAgC,CAE3C,CAAC,WAAY,6BAA6B,CAG1C,CAAC,uBAAwB,CAAE,MAAO,2BAA4B,KAAM,cAAe,CAAC,CACpF,CAAC,6BAA8B,CAAE,MAAO,2BAA4B,KAAM,cAAe,CAAC,CAC1F,CAAC,oBAAqB,CAAE,MAAO,2BAA4B,KAAM,cAAe,CAAC,CAEjF,CAAE,QAAS,cAAe,CAE1B,CAAC,IAAK,CAAE,MAAO,iBAAkB,KAAM,eAAgB,CAAC,CACxD,CAAC,IAAK,CAAE,MAAO,kBAAmB,KAAM,cAAe,CAAC,CAExD,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,UAAW,CAAC,CAE1D,CAAC,IAAK,CAAE,MAAO,iBAAkB,KAAM,eAAgB,CAAC,CACxD,CAAC,sBAAuB,WAAW,CACnC,CAAC,KAAM,CAAE,MAAO,iBAAkB,KAAM,eAAgB,CAAC,CACzD,CAAC,KAAM,CAAE,MAAO,iBAAkB,KAAM,aAAc,CAAC,CAEvD,CAAC,0BAA2B,CAAE,MAAO,eAAgB,KAAM,UAAW,CAAC,CAEvE,CAAC,aAAc,YAAY,CAC3B,CACE,WACA,CACE,MAAO,CACL,cAAe,UACf,aAAc,WACd,WAAY,GACb,CACF,CACF,CACD,CAAC,OAAQ,YAAY,CAErB,CAAC,mCAAoC,aAAa,CAClD,CAAC,wBAAyB,eAAe,CACzC,CAAC,qBAAsB,gBAAgB,CACvC,CAAC,gBAAiB,SAAS,CAC3B,CACE,+CACA,CACE,MAAO,CACL,GAAI,eACJ,WAAY,SACb,CACF,CACF,CACF,CAID,OAAQ,CACN,CAAC,IAAK,CAAE,MAAO,GAAI,SAAU,YAAa,CAAC,CAE3C,CACE,kBACA,CACE,MAAO,CACL,IAAK,CAAE,MAAO,cAAe,KAAM,OAAQ,CAE3C,GAAI,CAAE,MAAO,UAAW,SAAU,YAAa,CAE/C,aAAc,CACZ,MAAO,WACP,SAAU,YACX,CAED,YAAa,UACb,YAAa,aACb,WAAY,aACb,CACF,CACF,CACD,CAAE,QAAS,QAAS,CACrB,CAID,SAAU,CACR,CAAC,IAAK,GAAI,OAAO,CAEjB,CACE,kBACA,CACE,MAAO,CACL,IAAK,CAAE,MAAO,cAAe,KAAM,OAAQ,CAE3C,qBAAsB,CACpB,MAAO,UACP,SAAU,YACX,CAED,aAAc,CACZ,MAAO,WACP,SAAU,YACX,CAED,YAAa,UACb,YAAa,aACb,WAAY,aACb,CACF,CACF,CACD,CAAE,QAAS,QAAS,CACrB,CAGD,QAAS,CACP,CAAC,UAAW,aAAa,CACzB,CAAC,eAAgB,oBAAoB,CACrC,CAAC,MAAO,qBAAqB,CAC7B,CAAC,IAAK,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CAAC,CACnD,CAID,QAAS,CACP,CAAC,YAAa,aAAa,CAC3B,CAAC,IAAK,oBAAqB,gBAAgB,CAC3C,CAAC,MAAO,oBAAoB,CAC5B,CAAC,WAAY,oBAAoB,CACjC,CAAC,MAAO,4BAA4B,CACpC,CACE,OACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CACtD,WAAY,aACb,CACF,CACF,CACF,CAGD,QAAS,CACP,CACE,sBACA,CACE,MAAO,CACL,UAAW,CAAC,iBAAkB,CAAE,MAAO,2BAA4B,KAAM,OAAQ,CAAC,CAClF,WAAY,CAAC,iBAAkB,iBAAiB,CACjD,CACF,CACF,CACD,CAAC,KAAM,iBAAiB,CACzB,CAED,aAAc,CACZ,CAAC,QAAS,kBAAmB,OAAO,CACpC,CAAC,OAAQ,6BAA8B,OAAO,CAC9C,CAAC,SAAU,gCAAiC,OAAO,CACnD,CACE,MACA,CACE,MAAO,sBACP,SAAU,yBACX,CACF,CACD,CAAC,GAAI,GAAI,OAAO,CAEjB,CAED,sBAAuB,CACrB,CAAC,MAAO,CAAE,MAAO,sBAAuB,KAAM,OAAQ,CAAC,CACvD,CAAE,QAAS,QAAS,CACrB,CAGD,QAAS,CACP,CAAE,QAAS,cAAe,CAI1B,CACE,cACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,eAAgB,KAAM,OAAQ,CAClD,UAAW,CAAE,MAAO,eAAgB,KAAM,QAAS,CAEnD,WAAY,kCACZ,cAAe,wBACf,WAAY,SACb,CACF,CACF,CACD,CAAE,QAAS,gBAAiB,CAC7B,CAED,OAAQ,CACN,CAAE,QAAS,gBAAiB,CAC5B,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,CAAE,MAAO,eAAgB,CAAE,OAAO,CAChD,CACD,aAAc,CACZ,CACE,yBACA,CACE,kCACA,wBACA,kCACD,CACF,CACD,CACE,YACA,CAAC,kCAAmC,CAAE,MAAO,wBAAyB,KAAM,cAAe,CAAC,CAC7F,CACD,CAAC,gBAAiB,CAAC,kCAAmC,wBAAwB,CAAC,CAC/E,CAAC,QAAS,CAAE,MAAO,wBAAyB,KAAM,iBAAkB,CAAC,CACrE,CAAC,OAAQ,kCAAkC,CAC3C,CAAC,aAAc,wBAAwB,CACvC,CAAC,MAAO,gBAAgB,CACxB,CAAC,aAAc,gBAAgB,CAC/B,CAAC,OAAQ,iBAAiB,CAC1B,CAAC,IAAK,gBAAiB,gBAAgB,CACxC,CACD,WAAY,CACV,CAAC,IAAK,wBAAwB,CAC9B,CAAC,KAAM,iBAAiB,CACxB,CAAC,MAAO,gBAAgB,CACxB,CAAC,aAAc,gBAAgB,CAC/B,CAAC,QAAS,SAAS,CACnB,CAAC,KAAM,kCAAmC,OAAO,CAClD,CACD,cAAe,CACb,CAAC,QAAS,UAAU,CACpB,CAAC,KAAM,CAAE,MAAO,wBAAyB,KAAM,OAAQ,CAAC,CACzD,CAGD,QAAS,CACP,CAAC,aAAc,CAAE,MAAO,kBAAmB,SAAU,kBAAmB,CAAC,CACzE,CAAC,aAAc,CAAE,MAAO,kBAAmB,SAAU,kBAAmB,CAAC,CACzE,CAAC,aAAc,CAAE,MAAO,kBAAmB,SAAU,kBAAmB,CAAC,CACzE,CAAC,YAAa,CAAE,MAAO,kBAAmB,SAAU,kBAAmB,CAAC,CACxE,CAAC,mBAAoB,CAAE,MAAO,kBAAmB,SAAU,oBAAqB,CAAC,CACjF,CAAC,OAAQ,CAAE,MAAO,eAAgB,SAAU,eAAgB,CAAC,CAC7D,CAAC,OAAQ,CAAE,MAAO,eAAgB,SAAU,eAAgB,CAAC,CAC7D,CAAC,OAAQ,CAAE,MAAO,eAAgB,SAAU,eAAgB,CAAC,CAC7D,CAAC,MAAO,CAAE,MAAO,eAAgB,SAAU,eAAgB,CAAC,CAC5D,CAAC,aAAc,CAAE,MAAO,eAAgB,SAAU,iBAAkB,CAAC,CACrE,CAAC,cAAe,CAAE,MAAO,kBAAmB,SAAU,mBAAoB,CAAC,CAC3E,CAAC,cAAe,CAAE,MAAO,kBAAmB,SAAU,mBAAoB,CAAC,CAC3E,CAAC,cAAe,CAAE,MAAO,kBAAmB,SAAU,mBAAoB,CAAC,CAC3E,CAAC,aAAc,CAAE,MAAO,kBAAmB,SAAU,mBAAoB,CAAC,CAC1E,CAAC,oBAAqB,CAAE,MAAO,kBAAmB,SAAU,qBAAsB,CAAC,CACnF,CAAC,kBAAmB,CAAE,MAAO,UAAW,KAAM,OAAQ,CAAC,CAEvD,CAAC,IAAK,CAAE,MAAO,UAAW,KAAM,OAAQ,CAAC,CAE1C,CAMD,QAAS,CACP,CAAC,MAAO,oBAAoB,CAC5B,CAAC,MAAO,oBAAoB,CAC5B,CACE,IACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CACtD,UAAW,CAAE,MAAO,mBAAoB,KAAM,QAAS,CAEvD,WAAY,aACb,CACF,CACF,CACF,CAMD,SAAU,CAAC,CAAC,IAAK,oBAAqB,gBAAgB,CAAE,CAAE,QAAS,WAAY,CAAC,CAEhF,WAAY,CACV,CAAC,aAAc,GAAG,CAClB,CAAC,eAAgB,UAAW,WAAW,CACvC,CAAC,OAAQ,UAAU,CACpB,CACD,QAAS,CACP,CAAC,QAAS,UAAU,CACpB,CAAC,eAAgB,kBAAkB,CAEnC,CAAC,eAAgB,UAAW,OAAO,CACnC,CAAC,MAAO,UAAU,CACnB,CACF,CACF"}