{"version": 3, "file": "typespec-DGWi9L5L.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/typespec/typespec.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/typespec/typespec.ts\nvar bounded = (text) => `\\\\b${text}\\\\b`;\nvar notBefore = (regex) => `(?!${regex})`;\nvar identifierStart = \"[_a-zA-Z]\";\nvar identifierContinue = \"[_a-zA-Z0-9]\";\nvar identifier = bounded(`${identifierStart}${identifierContinue}*`);\nvar directive = bounded(`[_a-zA-Z-0-9]+`);\nvar keywords = [\n  \"import\",\n  \"model\",\n  \"scalar\",\n  \"namespace\",\n  \"op\",\n  \"interface\",\n  \"union\",\n  \"using\",\n  \"is\",\n  \"extends\",\n  \"enum\",\n  \"alias\",\n  \"return\",\n  \"void\",\n  \"if\",\n  \"else\",\n  \"projection\",\n  \"dec\",\n  \"extern\",\n  \"fn\"\n];\nvar namedLiterals = [\"true\", \"false\", \"null\", \"unknown\", \"never\"];\nvar nonCommentWs = `[ \\\\t\\\\r\\\\n]`;\nvar numericLiteral = `[0-9]+`;\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"/**\", close: \" */\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  indentationRules: {\n    decreaseIndentPattern: new RegExp(\"^((?!.*?/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\"),\n    increaseIndentPattern: new RegExp(\n      \"^((?!//).)*(\\\\{([^}\\\"'`/]*|(\\\\t|[ ])*//.*)|\\\\([^)\\\"'`/]*|\\\\[[^\\\\]\\\"'`/]*)$\"\n    ),\n    // e.g.  * ...| or */| or *-----*/|\n    unIndentedLinePattern: new RegExp(\n      \"^(\\\\t|[ ])*[ ]\\\\*[^/]*\\\\*/\\\\s*$|^(\\\\t|[ ])*[ ]\\\\*/\\\\s*$|^(\\\\t|[ ])*[ ]\\\\*([ ]([^\\\\*]|\\\\*(?!/))*)?$\"\n    )\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".tsp\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  symbols: /[=:;<>]+/,\n  keywords,\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|\"|\\\\\\${)`,\n  tokenizer: {\n    root: [{ include: \"@expression\" }, { include: \"@whitespace\" }],\n    stringVerbatim: [\n      { regex: `(|\"|\"\")[^\"]`, action: { token: \"string\" } },\n      { regex: `\"\"\"${notBefore(`\"`)}`, action: { token: \"string\", next: \"@pop\" } }\n    ],\n    stringLiteral: [\n      { regex: `\\\\\\${`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `[^\\\\\\\\\"$]+`, action: { token: \"string\" } },\n      { regex: \"@escapes\", action: { token: \"string.escape\" } },\n      { regex: `\\\\\\\\.`, action: { token: \"string.escape.invalid\" } },\n      { regex: `\"`, action: { token: \"string\", next: \"@pop\" } }\n    ],\n    bracketCounting: [\n      { regex: `{`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `}`, action: { token: \"delimiter.bracket\", next: \"@pop\" } },\n      { include: \"@expression\" }\n    ],\n    comment: [\n      { regex: `[^\\\\*]+`, action: { token: \"comment\" } },\n      { regex: `\\\\*\\\\/`, action: { token: \"comment\", next: \"@pop\" } },\n      { regex: `[\\\\/*]`, action: { token: \"comment\" } }\n    ],\n    whitespace: [\n      { regex: nonCommentWs },\n      { regex: `\\\\/\\\\*`, action: { token: \"comment\", next: \"@comment\" } },\n      { regex: `\\\\/\\\\/.*$`, action: { token: \"comment\" } }\n    ],\n    expression: [\n      { regex: `\"\"\"`, action: { token: \"string\", next: \"@stringVerbatim\" } },\n      { regex: `\"${notBefore(`\"\"`)}`, action: { token: \"string\", next: \"@stringLiteral\" } },\n      { regex: numericLiteral, action: { token: \"number\" } },\n      {\n        regex: identifier,\n        action: {\n          cases: {\n            \"@keywords\": { token: \"keyword\" },\n            \"@namedLiterals\": { token: \"keyword\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      { regex: `@${identifier}`, action: { token: \"tag\" } },\n      { regex: `#${directive}`, action: { token: \"directive\" } }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAW,GAAS,MAAM,EAAK,KAC/B,EAAa,GAAU,MAAM,EAAM,GAGnC,EAAa,EAAQ,yBAA2C,CAChE,EAAY,EAAQ,iBAAiB,CACrC,EAAW,CACb,SACA,QACA,SACA,YACA,KACA,YACA,QACA,QACA,KACA,UACA,OACA,QACA,SACA,OACA,KACA,OACA,aACA,MACA,SACA,KACD,CACG,EAAgB,CAAC,OAAQ,QAAS,OAAQ,UAAW,QAAQ,CAC7D,EAAe,eACf,EAAiB,SACjB,EAAO,CACT,SAAU,CACR,YAAa,KACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,MAAO,MAAO,MAAO,MAAO,CAAC,SAAS,CAAE,CACjD,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,sBAA2B,OAAO,uCAAuC,CACzE,sBAA2B,OACzB,6EACD,CAED,sBAA2B,OACzB,qGACD,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,OACd,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,mBAAoB,CACpD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CAC1D,CACD,QAAS,WACT,WACA,gBACA,QAAS,0CACT,UAAW,CACT,KAAM,CAAC,CAAE,QAAS,cAAe,CAAE,CAAE,QAAS,cAAe,CAAC,CAC9D,eAAgB,CACd,CAAE,MAAO,cAAe,OAAQ,CAAE,MAAO,SAAU,CAAE,CACrD,CAAE,MAAO,MAAM,EAAU,IAAI,GAAI,OAAQ,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAE,CAC7E,CACD,cAAe,CACb,CAAE,MAAO,OAAS,OAAQ,CAAE,MAAO,oBAAqB,KAAM,mBAAoB,CAAE,CACpF,CAAE,MAAO,aAAc,OAAQ,CAAE,MAAO,SAAU,CAAE,CACpD,CAAE,MAAO,WAAY,OAAQ,CAAE,MAAO,gBAAiB,CAAE,CACzD,CAAE,MAAO,QAAS,OAAQ,CAAE,MAAO,wBAAyB,CAAE,CAC9D,CAAE,MAAO,IAAK,OAAQ,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAE,CAC1D,CACD,gBAAiB,CACf,CAAE,MAAO,IAAK,OAAQ,CAAE,MAAO,oBAAqB,KAAM,mBAAoB,CAAE,CAChF,CAAE,MAAO,IAAK,OAAQ,CAAE,MAAO,oBAAqB,KAAM,OAAQ,CAAE,CACpE,CAAE,QAAS,cAAe,CAC3B,CACD,QAAS,CACP,CAAE,MAAO,UAAW,OAAQ,CAAE,MAAO,UAAW,CAAE,CAClD,CAAE,MAAO,SAAU,OAAQ,CAAE,MAAO,UAAW,KAAM,OAAQ,CAAE,CAC/D,CAAE,MAAO,SAAU,OAAQ,CAAE,MAAO,UAAW,CAAE,CAClD,CACD,WAAY,CACV,CAAE,MAAO,EAAc,CACvB,CAAE,MAAO,SAAU,OAAQ,CAAE,MAAO,UAAW,KAAM,WAAY,CAAE,CACnE,CAAE,MAAO,YAAa,OAAQ,CAAE,MAAO,UAAW,CAAE,CACrD,CACD,WAAY,CACV,CAAE,MAAO,MAAO,OAAQ,CAAE,MAAO,SAAU,KAAM,kBAAmB,CAAE,CACtE,CAAE,MAAO,IAAI,EAAU,KAAK,GAAI,OAAQ,CAAE,MAAO,SAAU,KAAM,iBAAkB,CAAE,CACrF,CAAE,MAAO,EAAgB,OAAQ,CAAE,MAAO,SAAU,CAAE,CACtD,CACE,MAAO,EACP,OAAQ,CACN,MAAO,CACL,YAAa,CAAE,MAAO,UAAW,CACjC,iBAAkB,CAAE,MAAO,UAAW,CACtC,WAAY,CAAE,MAAO,aAAc,CACpC,CACF,CACF,CACD,CAAE,MAAO,IAAI,IAAc,OAAQ,CAAE,MAAO,MAAO,CAAE,CACrD,CAAE,MAAO,IAAI,IAAa,OAAQ,CAAE,MAAO,YAAa,CAAE,CAC3D,CACF,CACF"}