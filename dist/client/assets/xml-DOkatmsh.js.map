{"version": 3, "file": "xml-DOkatmsh.js", "names": ["monaco_editor_core_star"], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/xml/xml.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/xml/xml.ts\nvar conf = {\n  comments: {\n    blockComment: [\"<!--\", \"-->\"]\n  },\n  brackets: [[\"<\", \">\"]],\n  autoClosingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  surroundingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(`<([_:\\\\w][_:\\\\w-.\\\\d]*)([^/>]*(?!/)>)[^<]*$`, \"i\"),\n      afterText: /^<\\/([_:\\w][_:\\w-.\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(`<(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`, \"i\"),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".xml\",\n  ignoreCase: true,\n  // Useful regular expressions\n  qualifiedName: /(?:[\\w\\.\\-]+:)?[\\w\\.\\-]+/,\n  tokenizer: {\n    root: [\n      [/[^<&]+/, \"\"],\n      { include: \"@whitespace\" },\n      // Standard opening tag\n      [/(<)(@qualifiedName)/, [{ token: \"delimiter\" }, { token: \"tag\", next: \"@tag\" }]],\n      // Standard closing tag\n      [\n        /(<\\/)(@qualifiedName)(\\s*)(>)/,\n        [{ token: \"delimiter\" }, { token: \"tag\" }, \"\", { token: \"delimiter\" }]\n      ],\n      // Meta tags - instruction\n      [/(<\\?)(@qualifiedName)/, [{ token: \"delimiter\" }, { token: \"metatag\", next: \"@tag\" }]],\n      // Meta tags - declaration\n      [/(<\\!)(@qualifiedName)/, [{ token: \"delimiter\" }, { token: \"metatag\", next: \"@tag\" }]],\n      // CDATA\n      [/<\\!\\[CDATA\\[/, { token: \"delimiter.cdata\", next: \"@cdata\" }],\n      [/&\\w+;/, \"string.escape\"]\n    ],\n    cdata: [\n      [/[^\\]]+/, \"\"],\n      [/\\]\\]>/, { token: \"delimiter.cdata\", next: \"@pop\" }],\n      [/\\]/, \"\"]\n    ],\n    tag: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/(@qualifiedName)(\\s*=\\s*)(\"[^\"]*\"|'[^']*')/, [\"attribute.name\", \"\", \"attribute.value\"]],\n      [\n        /(@qualifiedName)(\\s*=\\s*)(\"[^\">?\\/]*|'[^'>?\\/]*)(?=[\\?\\/]\\>)/,\n        [\"attribute.name\", \"\", \"attribute.value\"]\n      ],\n      [/(@qualifiedName)(\\s*=\\s*)(\"[^\">]*|'[^'>]*)/, [\"attribute.name\", \"\", \"attribute.value\"]],\n      [/@qualifiedName/, \"attribute.name\"],\n      [/\\?>/, { token: \"delimiter\", next: \"@pop\" }],\n      [/(\\/)(>)/, [{ token: \"tag\" }, { token: \"delimiter\", next: \"@pop\" }]],\n      [/>/, { token: \"delimiter\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/<!--/, { token: \"comment\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^<\\-]+/, \"comment.content\"],\n      [/-->/, { token: \"comment\", next: \"@pop\" }],\n      [/<!--/, \"comment.content.invalid\"],\n      [/[<\\-]/, \"comment.content\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;;AAOA,IAAI,EAAY,OAAO,eACnB,EAAmB,OAAO,yBAC1B,EAAoB,OAAO,oBAC3B,EAAe,OAAO,UAAU,eAChC,GAAe,EAAI,EAAM,EAAQ,IAAS,CAC5C,GAAI,GAAQ,OAAO,GAAS,UAAY,OAAO,GAAS,eACjD,IAAI,KAAO,EAAkB,EAAK,CACjC,CAAC,EAAa,KAAK,EAAI,EAAI,EAAI,IAAQ,GACzC,EAAU,EAAI,EAAK,CAAE,QAAW,EAAK,GAAM,WAAY,EAAE,EAAO,EAAiB,EAAM,EAAI,GAAK,EAAK,WAAY,CAAC,CAExH,OAAO,GAEL,GAAc,EAAQ,EAAK,KAAkB,EAAY,EAAQ,EAAK,UAAU,CAAE,GAAgB,EAAY,EAAc,EAAK,UAAU,EAG3I,EAA6B,EAAE,CACnC,EAAW,EAA4BA,EAAwB,CAI/D,IAAI,EAAO,CACT,SAAU,CACR,aAAc,CAAC,OAAQ,MAAM,CAC9B,CACD,SAAU,CAAC,CAAC,IAAK,IAAI,CAAC,CACtB,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,aAAc,CACZ,CACE,WAAgB,OAAO,8CAA+C,IAAI,CAC1E,UAAW,gCACX,OAAQ,CACN,aAAc,EAA2B,UAAU,aAAa,cACjE,CACF,CACD,CACE,WAAgB,OAAO,sCAAuC,IAAI,CAClE,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,OAAQ,CACnF,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,OACd,WAAY,GAEZ,cAAe,2BACf,UAAW,CACT,KAAM,CACJ,CAAC,SAAU,GAAG,CACd,CAAE,QAAS,cAAe,CAE1B,CAAC,sBAAuB,CAAC,CAAE,MAAO,YAAa,CAAE,CAAE,MAAO,MAAO,KAAM,OAAQ,CAAC,CAAC,CAEjF,CACE,gCACA,CAAC,CAAE,MAAO,YAAa,CAAE,CAAE,MAAO,MAAO,CAAE,GAAI,CAAE,MAAO,YAAa,CAAC,CACvE,CAED,CAAC,wBAAyB,CAAC,CAAE,MAAO,YAAa,CAAE,CAAE,MAAO,UAAW,KAAM,OAAQ,CAAC,CAAC,CAEvF,CAAC,wBAAyB,CAAC,CAAE,MAAO,YAAa,CAAE,CAAE,MAAO,UAAW,KAAM,OAAQ,CAAC,CAAC,CAEvF,CAAC,eAAgB,CAAE,MAAO,kBAAmB,KAAM,SAAU,CAAC,CAC9D,CAAC,QAAS,gBAAgB,CAC3B,CACD,MAAO,CACL,CAAC,SAAU,GAAG,CACd,CAAC,QAAS,CAAE,MAAO,kBAAmB,KAAM,OAAQ,CAAC,CACrD,CAAC,KAAM,GAAG,CACX,CACD,IAAK,CACH,CAAC,aAAc,GAAG,CAClB,CAAC,6CAA8C,CAAC,iBAAkB,GAAI,kBAAkB,CAAC,CACzF,CACE,+DACA,CAAC,iBAAkB,GAAI,kBAAkB,CAC1C,CACD,CAAC,6CAA8C,CAAC,iBAAkB,GAAI,kBAAkB,CAAC,CACzF,CAAC,iBAAkB,iBAAiB,CACpC,CAAC,MAAO,CAAE,MAAO,YAAa,KAAM,OAAQ,CAAC,CAC7C,CAAC,UAAW,CAAC,CAAE,MAAO,MAAO,CAAE,CAAE,MAAO,YAAa,KAAM,OAAQ,CAAC,CAAC,CACrE,CAAC,IAAK,CAAE,MAAO,YAAa,KAAM,OAAQ,CAAC,CAC5C,CACD,WAAY,CACV,CAAC,aAAc,GAAG,CAClB,CAAC,OAAQ,CAAE,MAAO,UAAW,KAAM,WAAY,CAAC,CACjD,CACD,QAAS,CACP,CAAC,UAAW,kBAAkB,CAC9B,CAAC,MAAO,CAAE,MAAO,UAAW,KAAM,OAAQ,CAAC,CAC3C,CAAC,OAAQ,0BAA0B,CACnC,CAAC,QAAS,kBAAkB,CAC7B,CACF,CACF"}