import{n as e}from"./index-BtNCOo_D.js";
/*!-----------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation. All rights reserved.
* Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
* Released under the MIT license
* https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
*-----------------------------------------------------------------------------*/
var t=Object.defineProperty,n=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a=(e,a,o,s)=>{if(a&&typeof a==`object`||typeof a==`function`)for(let c of r(a))!i.call(e,c)&&c!==o&&t(e,c,{get:()=>a[c],enumerable:!(s=n(a,c))||s.enumerable});return e},o=(e,t,n)=>(a(e,t,`default`),n&&a(n,t,`default`)),s={};o(s,e);var c={comments:{blockComment:[`<!--`,`-->`]},brackets:[[`<`,`>`]],autoClosingPairs:[{open:`<`,close:`>`},{open:`'`,close:`'`},{open:`"`,close:`"`}],surroundingPairs:[{open:`<`,close:`>`},{open:`'`,close:`'`},{open:`"`,close:`"`}],onEnterRules:[{beforeText:RegExp(`<([_:\\w][_:\\w-.\\d]*)([^/>]*(?!/)>)[^<]*$`,`i`),afterText:/^<\/([_:\w][_:\w-.\d]*)\s*>$/i,action:{indentAction:s.languages.IndentAction.IndentOutdent}},{beforeText:RegExp(`<(\\w[\\w\\d]*)([^/>]*(?!/)>)[^<]*$`,`i`),action:{indentAction:s.languages.IndentAction.Indent}}]},l={defaultToken:``,tokenPostfix:`.xml`,ignoreCase:!0,qualifiedName:/(?:[\w\.\-]+:)?[\w\.\-]+/,tokenizer:{root:[[/[^<&]+/,``],{include:`@whitespace`},[/(<)(@qualifiedName)/,[{token:`delimiter`},{token:`tag`,next:`@tag`}]],[/(<\/)(@qualifiedName)(\s*)(>)/,[{token:`delimiter`},{token:`tag`},``,{token:`delimiter`}]],[/(<\?)(@qualifiedName)/,[{token:`delimiter`},{token:`metatag`,next:`@tag`}]],[/(<\!)(@qualifiedName)/,[{token:`delimiter`},{token:`metatag`,next:`@tag`}]],[/<\!\[CDATA\[/,{token:`delimiter.cdata`,next:`@cdata`}],[/&\w+;/,`string.escape`]],cdata:[[/[^\]]+/,``],[/\]\]>/,{token:`delimiter.cdata`,next:`@pop`}],[/\]/,``]],tag:[[/[ \t\r\n]+/,``],[/(@qualifiedName)(\s*=\s*)("[^"]*"|'[^']*')/,[`attribute.name`,``,`attribute.value`]],[/(@qualifiedName)(\s*=\s*)("[^">?\/]*|'[^'>?\/]*)(?=[\?\/]\>)/,[`attribute.name`,``,`attribute.value`]],[/(@qualifiedName)(\s*=\s*)("[^">]*|'[^'>]*)/,[`attribute.name`,``,`attribute.value`]],[/@qualifiedName/,`attribute.name`],[/\?>/,{token:`delimiter`,next:`@pop`}],[/(\/)(>)/,[{token:`tag`},{token:`delimiter`,next:`@pop`}]],[/>/,{token:`delimiter`,next:`@pop`}]],whitespace:[[/[ \t\r\n]+/,``],[/<!--/,{token:`comment`,next:`@comment`}]],comment:[[/[^<\-]+/,`comment.content`],[/-->/,{token:`comment`,next:`@pop`}],[/<!--/,`comment.content.invalid`],[/[<\-]/,`comment.content`]]}};export{c as conf,l as language};
//# sourceMappingURL=xml-DOkatmsh.js.map