{"version": 3, "file": "scss-7HPeaxS9.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/scss/scss.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scss/scss.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|([@$#!.:]?[\\w-?]+%?)|[@#!.]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"],\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".scss\",\n  ws: \"[ \t\\n\\r\\f]*\",\n  // whitespaces (referenced in several rules)\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [{ include: \"@selector\" }],\n    selector: [\n      { include: \"@comments\" },\n      { include: \"@import\" },\n      { include: \"@variabledeclaration\" },\n      { include: \"@warndebug\" },\n      // sass: log statements\n      [\"[@](include)\", { token: \"keyword\", next: \"@includedeclaration\" }],\n      // sass: include statement\n      [\n        \"[@](keyframes|-webkit-keyframes|-moz-keyframes|-o-keyframes)\",\n        { token: \"keyword\", next: \"@keyframedeclaration\" }\n      ],\n      [\"[@](page|content|font-face|-moz-document)\", { token: \"keyword\" }],\n      // sass: placeholder for includes\n      [\"[@](charset|namespace)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\"[@](function)\", { token: \"keyword\", next: \"@functiondeclaration\" }],\n      [\"[@](mixin)\", { token: \"keyword\", next: \"@mixindeclaration\" }],\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"meta\", next: \"@urldeclaration\" }],\n      { include: \"@controlstatement\" },\n      // sass control statements\n      { include: \"@selectorname\" },\n      [\"[&\\\\*]\", \"tag\"],\n      // selector symbols\n      [\"[>\\\\+,]\", \"delimiter\"],\n      // selector operators\n      [\"\\\\[\", { token: \"delimiter.bracket\", next: \"@selectorattribute\" }],\n      [\"{\", { token: \"delimiter.curly\", next: \"@selectorbody\" }]\n    ],\n    selectorbody: [\n      [\"[*_]?@identifier@ws:(?=(\\\\s|\\\\d|[^{;}]*[;}]))\", \"attribute.name\", \"@rulevalue\"],\n      // rule definition: to distinguish from a nested selector check for whitespace, number or a semicolon\n      { include: \"@selector\" },\n      // sass: nested selectors\n      [\"[@](extend)\", { token: \"keyword\", next: \"@extendbody\" }],\n      // sass: extend other selectors\n      [\"[@](return)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    selectorname: [\n      [\"#{\", { token: \"meta\", next: \"@variableinterpolation\" }],\n      // sass: interpolation\n      [\"(\\\\.|#(?=[^{])|%|(@identifier)|:)+\", \"tag\"]\n      // selector (.foo, div, ...)\n    ],\n    selectorattribute: [{ include: \"@term\" }, [\"]\", { token: \"delimiter.bracket\", next: \"@pop\" }]],\n    term: [\n      { include: \"@comments\" },\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"meta\", next: \"@urldeclaration\" }],\n      { include: \"@functioninvocation\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@variablereference\" },\n      [\"(and\\\\b|or\\\\b|not\\\\b)\", \"operator\"],\n      { include: \"@name\" },\n      [\"([<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,])\", \"operator\"],\n      [\",\", \"delimiter\"],\n      [\"!default\", \"literal\"],\n      [\"\\\\(\", { token: \"delimiter.parenthesis\", next: \"@parenthizedterm\" }]\n    ],\n    rulevalue: [\n      { include: \"@term\" },\n      [\"!important\", \"literal\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@nestedproperty\" }],\n      // sass: nested properties\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    nestedproperty: [\n      [\"[*_]?@identifier@ws:\", \"attribute.name\", \"@rulevalue\"],\n      { include: \"@comments\" },\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    warndebug: [[\"[@](warn|debug)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    import: [[\"[@](import)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    variabledeclaration: [\n      // sass variables\n      [\"\\\\$@identifier@ws:\", \"variable.decl\", \"@declarationbody\"]\n    ],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    parenthizedterm: [\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    declarationbody: [\n      { include: \"@term\" },\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    extendbody: [\n      { include: \"@selectorname\" },\n      [\"!optional\", \"literal\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    variablereference: [\n      // sass variable reference\n      [\"\\\\$@identifier\", \"variable.ref\"],\n      [\"\\\\.\\\\.\\\\.\", \"operator\"],\n      // var args in reference\n      [\"#{\", { token: \"meta\", next: \"@variableinterpolation\" }]\n      // sass var resolve\n    ],\n    variableinterpolation: [\n      { include: \"@variablereference\" },\n      [\"}\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    name: [[\"@identifier\", \"attribute.value\"]],\n    numbers: [\n      [\"(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"number.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"number\",\n        \"@pop\"\n      ]\n    ],\n    functiondeclaration: [\n      [\"@identifier@ws\\\\(\", { token: \"meta\", next: \"@parameterdeclaration\" }],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@functionbody\" }]\n    ],\n    mixindeclaration: [\n      // mixin with parameters\n      [\"@identifier@ws\\\\(\", { token: \"meta\", next: \"@parameterdeclaration\" }],\n      // mixin without parameters\n      [\"@identifier\", \"meta\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    parameterdeclaration: [\n      [\"\\\\$@identifier@ws:\", \"variable.decl\"],\n      [\"\\\\.\\\\.\\\\.\", \"operator\"],\n      // var args in declaration\n      [\",\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    includedeclaration: [\n      { include: \"@functioninvocation\" },\n      [\"@identifier\", \"meta\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }],\n      // missing semicolon\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    keyframedeclaration: [\n      [\"@identifier\", \"meta\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@keyframebody\" }]\n    ],\n    keyframebody: [\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.curly\", next: \"@selectorbody\" }],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    controlstatement: [\n      [\n        \"[@](if|else|for|while|each|media)\",\n        { token: \"keyword.flow\", next: \"@controlstatementdeclaration\" }\n      ]\n    ],\n    controlstatementdeclaration: [\n      [\"(in|from|through|if|to)\\\\b\", { token: \"keyword.flow\" }],\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    functionbody: [\n      [\"[@](return)\", { token: \"keyword\" }],\n      { include: \"@variabledeclaration\" },\n      { include: \"@term\" },\n      { include: \"@controlstatement\" },\n      [\";\", \"delimiter\"],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    functioninvocation: [[\"@identifier\\\\(\", { token: \"meta\", next: \"@functionarguments\" }]],\n    functionarguments: [\n      [\"\\\\$@identifier@ws:\", \"attribute.name\"],\n      [\"[,]\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    strings: [\n      ['~?\"', { token: \"string.delimiter\", next: \"@stringenddoublequote\" }],\n      [\"~?'\", { token: \"string.delimiter\", next: \"@stringendquote\" }]\n    ],\n    stringenddoublequote: [\n      [\"\\\\\\\\.\", \"string\"],\n      ['\"', { token: \"string.delimiter\", next: \"@pop\" }],\n      [\".\", \"string\"]\n    ],\n    stringendquote: [\n      [\"\\\\\\\\.\", \"string\"],\n      [\"'\", { token: \"string.delimiter\", next: \"@pop\" }],\n      [\".\", \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,YAAa,kDACb,SAAU,CACR,aAAc,CAAC,KAAM,KAAK,CAC1B,YAAa,KACd,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACxD,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,+CAA+C,CACjE,IAAS,OAAO,uCAAuC,CACxD,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,QACd,GAAI;QAEJ,WAAY,sHACZ,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,oBAAqB,CACrD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CACzD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACpD,CACD,UAAW,CACT,KAAM,CAAC,CAAE,QAAS,YAAa,CAAC,CAChC,SAAU,CACR,CAAE,QAAS,YAAa,CACxB,CAAE,QAAS,UAAW,CACtB,CAAE,QAAS,uBAAwB,CACnC,CAAE,QAAS,aAAc,CAEzB,CAAC,eAAgB,CAAE,MAAO,UAAW,KAAM,sBAAuB,CAAC,CAEnE,CACE,+DACA,CAAE,MAAO,UAAW,KAAM,uBAAwB,CACnD,CACD,CAAC,4CAA6C,CAAE,MAAO,UAAW,CAAC,CAEnE,CAAC,yBAA0B,CAAE,MAAO,UAAW,KAAM,mBAAoB,CAAC,CAC1E,CAAC,gBAAiB,CAAE,MAAO,UAAW,KAAM,uBAAwB,CAAC,CACrE,CAAC,aAAc,CAAE,MAAO,UAAW,KAAM,oBAAqB,CAAC,CAC/D,CAAC,qBAAsB,CAAE,MAAO,OAAQ,KAAM,kBAAmB,CAAC,CAClE,CAAE,QAAS,oBAAqB,CAEhC,CAAE,QAAS,gBAAiB,CAC5B,CAAC,SAAU,MAAM,CAEjB,CAAC,UAAW,YAAY,CAExB,CAAC,MAAO,CAAE,MAAO,oBAAqB,KAAM,qBAAsB,CAAC,CACnE,CAAC,IAAK,CAAE,MAAO,kBAAmB,KAAM,gBAAiB,CAAC,CAC3D,CACD,aAAc,CACZ,CAAC,gDAAiD,iBAAkB,aAAa,CAEjF,CAAE,QAAS,YAAa,CAExB,CAAC,cAAe,CAAE,MAAO,UAAW,KAAM,cAAe,CAAC,CAE1D,CAAC,cAAe,CAAE,MAAO,UAAW,KAAM,mBAAoB,CAAC,CAC/D,CAAC,IAAK,CAAE,MAAO,kBAAmB,KAAM,OAAQ,CAAC,CAClD,CACD,aAAc,CACZ,CAAC,KAAM,CAAE,MAAO,OAAQ,KAAM,yBAA0B,CAAC,CAEzD,CAAC,qCAAsC,MAAM,CAE9C,CACD,kBAAmB,CAAC,CAAE,QAAS,QAAS,CAAE,CAAC,IAAK,CAAE,MAAO,oBAAqB,KAAM,OAAQ,CAAC,CAAC,CAC9F,KAAM,CACJ,CAAE,QAAS,YAAa,CACxB,CAAC,qBAAsB,CAAE,MAAO,OAAQ,KAAM,kBAAmB,CAAC,CAClE,CAAE,QAAS,sBAAuB,CAClC,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,qBAAsB,CACjC,CAAC,wBAAyB,WAAW,CACrC,CAAE,QAAS,QAAS,CACpB,CAAC,gCAAiC,WAAW,CAC7C,CAAC,IAAK,YAAY,CAClB,CAAC,WAAY,UAAU,CACvB,CAAC,MAAO,CAAE,MAAO,wBAAyB,KAAM,mBAAoB,CAAC,CACtE,CACD,UAAW,CACT,CAAE,QAAS,QAAS,CACpB,CAAC,aAAc,UAAU,CACzB,CAAC,IAAK,YAAa,OAAO,CAC1B,CAAC,IAAK,CAAE,MAAO,kBAAmB,SAAU,kBAAmB,CAAC,CAEhE,CAAC,QAAS,CAAE,MAAO,GAAI,KAAM,OAAQ,CAAC,CAEvC,CACD,eAAgB,CACd,CAAC,uBAAwB,iBAAkB,aAAa,CACxD,CAAE,QAAS,YAAa,CACxB,CAAC,IAAK,CAAE,MAAO,kBAAmB,KAAM,OAAQ,CAAC,CAClD,CACD,UAAW,CAAC,CAAC,kBAAmB,CAAE,MAAO,UAAW,KAAM,mBAAoB,CAAC,CAAC,CAChF,OAAQ,CAAC,CAAC,cAAe,CAAE,MAAO,UAAW,KAAM,mBAAoB,CAAC,CAAC,CACzE,oBAAqB,CAEnB,CAAC,qBAAsB,gBAAiB,mBAAmB,CAC5D,CACD,eAAgB,CACd,CAAE,QAAS,WAAY,CACvB,CAAC;IAAa,SAAS,CACvB,CAAC,MAAO,CAAE,MAAO,OAAQ,KAAM,OAAQ,CAAC,CACzC,CACD,gBAAiB,CACf,CAAE,QAAS,QAAS,CACpB,CAAC,MAAO,CAAE,MAAO,wBAAyB,KAAM,OAAQ,CAAC,CAC1D,CACD,gBAAiB,CACf,CAAE,QAAS,QAAS,CACpB,CAAC,IAAK,YAAa,OAAO,CAC1B,CAAC,QAAS,CAAE,MAAO,GAAI,KAAM,OAAQ,CAAC,CAEvC,CACD,WAAY,CACV,CAAE,QAAS,gBAAiB,CAC5B,CAAC,YAAa,UAAU,CACxB,CAAC,IAAK,YAAa,OAAO,CAC1B,CAAC,QAAS,CAAE,MAAO,GAAI,KAAM,OAAQ,CAAC,CAEvC,CACD,kBAAmB,CAEjB,CAAC,iBAAkB,eAAe,CAClC,CAAC,YAAa,WAAW,CAEzB,CAAC,KAAM,CAAE,MAAO,OAAQ,KAAM,yBAA0B,CAAC,CAE1D,CACD,sBAAuB,CACrB,CAAE,QAAS,qBAAsB,CACjC,CAAC,IAAK,CAAE,MAAO,OAAQ,KAAM,OAAQ,CAAC,CACvC,CACD,SAAU,CACR,CAAC,SAAU,UAAW,WAAW,CACjC,CAAC,YAAa,UAAU,CACzB,CACD,QAAS,CACP,CAAC,SAAU,UAAW,OAAO,CAC7B,CAAC,IAAK,UAAU,CACjB,CACD,KAAM,CAAC,CAAC,cAAe,kBAAkB,CAAC,CAC1C,QAAS,CACP,CAAC,mCAAoC,CAAE,MAAO,SAAU,KAAM,SAAU,CAAC,CACzE,CAAC,wBAAyB,aAAa,CACxC,CACD,MAAO,CACL,CACE,0FACA,SACA,OACD,CACF,CACD,oBAAqB,CACnB,CAAC,oBAAqB,CAAE,MAAO,OAAQ,KAAM,wBAAyB,CAAC,CACvE,CAAC,IAAK,CAAE,MAAO,kBAAmB,SAAU,gBAAiB,CAAC,CAC/D,CACD,iBAAkB,CAEhB,CAAC,oBAAqB,CAAE,MAAO,OAAQ,KAAM,wBAAyB,CAAC,CAEvE,CAAC,cAAe,OAAO,CACvB,CAAC,IAAK,CAAE,MAAO,kBAAmB,SAAU,gBAAiB,CAAC,CAC/D,CACD,qBAAsB,CACpB,CAAC,qBAAsB,gBAAgB,CACvC,CAAC,YAAa,WAAW,CAEzB,CAAC,IAAK,YAAY,CAClB,CAAE,QAAS,QAAS,CACpB,CAAC,MAAO,CAAE,MAAO,OAAQ,KAAM,OAAQ,CAAC,CACzC,CACD,mBAAoB,CAClB,CAAE,QAAS,sBAAuB,CAClC,CAAC,cAAe,OAAO,CACvB,CAAC,IAAK,YAAa,OAAO,CAC1B,CAAC,QAAS,CAAE,MAAO,GAAI,KAAM,OAAQ,CAAC,CAEtC,CAAC,IAAK,CAAE,MAAO,kBAAmB,SAAU,gBAAiB,CAAC,CAC/D,CACD,oBAAqB,CACnB,CAAC,cAAe,OAAO,CACvB,CAAC,IAAK,CAAE,MAAO,kBAAmB,SAAU,gBAAiB,CAAC,CAC/D,CACD,aAAc,CACZ,CAAE,QAAS,QAAS,CACpB,CAAC,IAAK,CAAE,MAAO,kBAAmB,KAAM,gBAAiB,CAAC,CAC1D,CAAC,IAAK,CAAE,MAAO,kBAAmB,KAAM,OAAQ,CAAC,CAClD,CACD,iBAAkB,CAChB,CACE,oCACA,CAAE,MAAO,eAAgB,KAAM,+BAAgC,CAChE,CACF,CACD,4BAA6B,CAC3B,CAAC,6BAA8B,CAAE,MAAO,eAAgB,CAAC,CACzD,CAAE,QAAS,QAAS,CACpB,CAAC,IAAK,CAAE,MAAO,kBAAmB,SAAU,gBAAiB,CAAC,CAC/D,CACD,aAAc,CACZ,CAAC,cAAe,CAAE,MAAO,UAAW,CAAC,CACrC,CAAE,QAAS,uBAAwB,CACnC,CAAE,QAAS,QAAS,CACpB,CAAE,QAAS,oBAAqB,CAChC,CAAC,IAAK,YAAY,CAClB,CAAC,IAAK,CAAE,MAAO,kBAAmB,KAAM,OAAQ,CAAC,CAClD,CACD,mBAAoB,CAAC,CAAC,iBAAkB,CAAE,MAAO,OAAQ,KAAM,qBAAsB,CAAC,CAAC,CACvF,kBAAmB,CACjB,CAAC,qBAAsB,iBAAiB,CACxC,CAAC,MAAO,YAAY,CACpB,CAAE,QAAS,QAAS,CACpB,CAAC,MAAO,CAAE,MAAO,OAAQ,KAAM,OAAQ,CAAC,CACzC,CACD,QAAS,CACP,CAAC,MAAO,CAAE,MAAO,mBAAoB,KAAM,wBAAyB,CAAC,CACrE,CAAC,MAAO,CAAE,MAAO,mBAAoB,KAAM,kBAAmB,CAAC,CAChE,CACD,qBAAsB,CACpB,CAAC,QAAS,SAAS,CACnB,CAAC,IAAK,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CAAC,CAClD,CAAC,IAAK,SAAS,CAChB,CACD,eAAgB,CACd,CAAC,QAAS,SAAS,CACnB,CAAC,IAAK,CAAE,MAAO,mBAAoB,KAAM,OAAQ,CAAC,CAClD,CAAC,IAAK,SAAS,CAChB,CACF,CACF"}