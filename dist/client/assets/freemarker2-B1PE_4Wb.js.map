{"version": 3, "file": "freemarker2-B1PE_4Wb.js", "names": ["monaco_editor_core_star"], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/freemarker2/freemarker2.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/freemarker2/freemarker2.ts\nvar EMPTY_ELEMENTS = [\n  \"assign\",\n  \"flush\",\n  \"ftl\",\n  \"return\",\n  \"global\",\n  \"import\",\n  \"include\",\n  \"break\",\n  \"continue\",\n  \"local\",\n  \"nested\",\n  \"nt\",\n  \"setting\",\n  \"stop\",\n  \"t\",\n  \"lt\",\n  \"rt\",\n  \"fallback\"\n];\nvar BLOCK_ELEMENTS = [\n  \"attempt\",\n  \"autoesc\",\n  \"autoEsc\",\n  \"compress\",\n  \"comment\",\n  \"escape\",\n  \"noescape\",\n  \"function\",\n  \"if\",\n  \"list\",\n  \"items\",\n  \"sep\",\n  \"macro\",\n  \"noparse\",\n  \"noParse\",\n  \"noautoesc\",\n  \"noAutoEsc\",\n  \"outputformat\",\n  \"switch\",\n  \"visit\",\n  \"recurse\"\n];\nvar TagSyntaxAngle = {\n  close: \">\",\n  id: \"angle\",\n  open: \"<\"\n};\nvar TagSyntaxBracket = {\n  close: \"\\\\]\",\n  id: \"bracket\",\n  open: \"\\\\[\"\n};\nvar TagSyntaxAuto = {\n  close: \"[>\\\\]]\",\n  id: \"auto\",\n  open: \"[<\\\\[]\"\n};\nvar InterpolationSyntaxDollar = {\n  close: \"\\\\}\",\n  id: \"dollar\",\n  open1: \"\\\\$\",\n  open2: \"\\\\{\"\n};\nvar InterpolationSyntaxBracket = {\n  close: \"\\\\]\",\n  id: \"bracket\",\n  open1: \"\\\\[\",\n  open2: \"=\"\n};\nfunction createLangConfiguration(ts) {\n  return {\n    brackets: [\n      [\"<\", \">\"],\n      [\"[\", \"]\"],\n      [\"(\", \")\"],\n      [\"{\", \"}\"]\n    ],\n    comments: {\n      blockComment: [`${ts.open}--`, `--${ts.close}`]\n    },\n    autoCloseBefore: \"\\n\\r\t }]),.:;=\",\n    autoClosingPairs: [\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: '\"', close: '\"', notIn: [\"string\"] },\n      { open: \"'\", close: \"'\", notIn: [\"string\"] }\n    ],\n    surroundingPairs: [\n      { open: '\"', close: '\"' },\n      { open: \"'\", close: \"'\" },\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: \"<\", close: \">\" }\n    ],\n    folding: {\n      markers: {\n        start: new RegExp(\n          `${ts.open}#(?:${BLOCK_ELEMENTS.join(\"|\")})([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`\n        ),\n        end: new RegExp(`${ts.open}/#(?:${BLOCK_ELEMENTS.join(\"|\")})[\\\\r\\\\n\\\\t ]*>`)\n      }\n    },\n    onEnterRules: [\n      {\n        beforeText: new RegExp(\n          `${ts.open}#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`\n        ),\n        afterText: new RegExp(`^${ts.open}/#([a-zA-Z_]+)[\\\\r\\\\n\\\\t ]*${ts.close}$`),\n        action: {\n          indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n        }\n      },\n      {\n        beforeText: new RegExp(\n          `${ts.open}#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`\n        ),\n        action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n      }\n    ]\n  };\n}\nfunction createLangConfigurationAuto() {\n  return {\n    // Cannot set block comment delimiter in auto mode...\n    // It depends on the content and the cursor position of the file...\n    brackets: [\n      [\"<\", \">\"],\n      [\"[\", \"]\"],\n      [\"(\", \")\"],\n      [\"{\", \"}\"]\n    ],\n    autoCloseBefore: \"\\n\\r\t }]),.:;=\",\n    autoClosingPairs: [\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: '\"', close: '\"', notIn: [\"string\"] },\n      { open: \"'\", close: \"'\", notIn: [\"string\"] }\n    ],\n    surroundingPairs: [\n      { open: '\"', close: '\"' },\n      { open: \"'\", close: \"'\" },\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: \"<\", close: \">\" }\n    ],\n    folding: {\n      markers: {\n        start: new RegExp(`[<\\\\[]#(?:${BLOCK_ELEMENTS.join(\"|\")})([^/>\\\\]]*(?!/)[>\\\\]])[^<\\\\[]*$`),\n        end: new RegExp(`[<\\\\[]/#(?:${BLOCK_ELEMENTS.join(\"|\")})[\\\\r\\\\n\\\\t ]*>`)\n      }\n    },\n    onEnterRules: [\n      {\n        beforeText: new RegExp(\n          `[<\\\\[]#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/>\\\\]]*(?!/)[>\\\\]])[^[<\\\\[]]*$`\n        ),\n        afterText: new RegExp(`^[<\\\\[]/#([a-zA-Z_]+)[\\\\r\\\\n\\\\t ]*[>\\\\]]$`),\n        action: {\n          indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n        }\n      },\n      {\n        beforeText: new RegExp(\n          `[<\\\\[]#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/>\\\\]]*(?!/)[>\\\\]])[^[<\\\\[]]*$`\n        ),\n        action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n      }\n    ]\n  };\n}\nfunction createMonarchLanguage(ts, is) {\n  const id = `_${ts.id}_${is.id}`;\n  const s = (name) => name.replace(/__id__/g, id);\n  const r = (regexp) => {\n    const source = regexp.source.replace(/__id__/g, id);\n    return new RegExp(source, regexp.flags);\n  };\n  return {\n    // Settings\n    unicode: true,\n    includeLF: false,\n    start: s(\"default__id__\"),\n    ignoreCase: false,\n    defaultToken: \"invalid\",\n    tokenPostfix: `.freemarker2`,\n    brackets: [\n      { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n      { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n      { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n      { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n    ],\n    // Dynamic RegExp\n    [s(\"open__id__\")]: new RegExp(ts.open),\n    [s(\"close__id__\")]: new RegExp(ts.close),\n    [s(\"iOpen1__id__\")]: new RegExp(is.open1),\n    [s(\"iOpen2__id__\")]: new RegExp(is.open2),\n    [s(\"iClose__id__\")]: new RegExp(is.close),\n    // <#START_TAG : \"<\" | \"<#\" | \"[#\">\n    // <#END_TAG : \"</\" | \"</#\" | \"[/#\">\n    [s(\"startTag__id__\")]: r(/(@open__id__)(#)/),\n    [s(\"endTag__id__\")]: r(/(@open__id__)(\\/#)/),\n    [s(\"startOrEndTag__id__\")]: r(/(@open__id__)(\\/?#)/),\n    // <#CLOSE_TAG1 : (<BLANK>)* (\">\" | \"]\")>\n    [s(\"closeTag1__id__\")]: r(/((?:@blank)*)(@close__id__)/),\n    // <#CLOSE_TAG2 : (<BLANK>)* (\"/\")? (\">\" | \"]\")>\n    [s(\"closeTag2__id__\")]: r(/((?:@blank)*\\/?)(@close__id__)/),\n    // Static RegExp\n    // <#BLANK : \" \" | \"\\t\" | \"\\n\" | \"\\r\">\n    blank: /[ \\t\\n\\r]/,\n    // <FALSE : \"false\">\n    // <TRUE : \"true\">\n    // <IN : \"in\">\n    // <AS : \"as\">\n    // <USING : \"using\">\n    keywords: [\"false\", \"true\", \"in\", \"as\", \"using\"],\n    // Directive names that cannot have an expression parameters and cannot be self-closing\n    // E.g. <#if id==2> ... </#if>\n    directiveStartCloseTag1: /attempt|recover|sep|auto[eE]sc|no(?:autoe|AutoE)sc|compress|default|no[eE]scape|comment|no[pP]arse/,\n    // Directive names that cannot have an expression parameter and can be self-closing\n    // E.g. <#if> ... <#else>  ... </#if>\n    // E.g. <#if> ... <#else /></#if>\n    directiveStartCloseTag2: /else|break|continue|return|stop|flush|t|lt|rt|nt|nested|recurse|fallback|ftl/,\n    // Directive names that can have an expression parameter and cannot be self-closing\n    // E.g. <#if id==2> ... </#if>\n    directiveStartBlank: /if|else[iI]f|list|for[eE]ach|switch|case|assign|global|local|include|import|function|macro|transform|visit|stop|return|call|setting|output[fF]ormat|nested|recurse|escape|ftl|items/,\n    // Directive names that can have an end tag\n    // E.g. </#if>\n    directiveEndCloseTag1: /if|list|items|sep|recover|attempt|for[eE]ach|local|global|assign|function|macro|output[fF]ormat|auto[eE]sc|no(?:autoe|AutoE)sc|compress|transform|switch|escape|no[eE]scape/,\n    // <#ESCAPED_CHAR :\n    //     \"\\\\\"\n    //     (\n    //         (\"n\" | \"t\" | \"r\" | \"f\" | \"b\" | \"g\" | \"l\" | \"a\" | \"\\\\\" | \"'\" | \"\\\"\" | \"{\" | \"=\")\n    //         |\n    //         (\"x\" [\"0\"-\"9\", \"A\"-\"F\", \"a\"-\"f\"])\n    //     )\n    // >\n    // Note: While the JavaCC tokenizer rule only specifies one hex digit,\n    // FreeMarker actually interprets up to 4 hex digits.\n    escapedChar: /\\\\(?:[ntrfbgla\\\\'\"\\{=]|(?:x[0-9A-Fa-f]{1,4}))/,\n    // <#ASCII_DIGIT: [\"0\" - \"9\"]>\n    asciiDigit: /[0-9]/,\n    // <INTEGER : ([\"0\"-\"9\"])+>\n    integer: /[0-9]+/,\n    // <#NON_ESCAPED_ID_START_CHAR:\n    // [\n    // \t  // This was generated on JDK 1.8.0_20 Win64 with src/main/misc/identifierChars/IdentifierCharGenerator.java\n    //    ...\n    // ]\n    nonEscapedIdStartChar: /[\\$@-Z_a-z\\u00AA\\u00B5\\u00BA\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u1FFF\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183-\\u2184\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3006\\u3031-\\u3035\\u303B-\\u303C\\u3040-\\u318F\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3300-\\u337F\\u3400-\\u4DB5\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66E\\uA67F-\\uA697\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA793\\uA7A0-\\uA7AA\\uA7F8-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8D0-\\uA8D9\\uA8F2-\\uA8F7\\uA8FB\\uA900-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF-\\uA9D9\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A\\uAA80-\\uAAAF\\uAAB1\\uAAB5-\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uABC0-\\uABE2\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40-\\uFB41\\uFB43-\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]/,\n    // <#ESCAPED_ID_CHAR: \"\\\\\" (\"-\" | \".\" | \":\" | \"#\")>\n    escapedIdChar: /\\\\[\\-\\.:#]/,\n    // <#ID_START_CHAR: <NON_ESCAPED_ID_START_CHAR>|<ESCAPED_ID_CHAR>>\n    idStartChar: /(?:@nonEscapedIdStartChar)|(?:@escapedIdChar)/,\n    // <ID: <ID_START_CHAR> (<ID_START_CHAR>|<ASCII_DIGIT>)*>\n    id: /(?:@idStartChar)(?:(?:@idStartChar)|(?:@asciiDigit))*/,\n    // Certain keywords / operators are allowed to index hashes\n    //\n    // Expression DotVariable(Expression exp) :\n    // {\n    // \tToken t;\n    // }\n    // {\n    // \t\t<DOT>\n    // \t\t(\n    // \t\t\tt = <ID> | t = <TIMES> | t = <DOUBLE_STAR>\n    // \t\t\t|\n    // \t\t\t(\n    // \t\t\t\tt = <LESS_THAN>\n    // \t\t\t\t|\n    // \t\t\t\tt = <LESS_THAN_EQUALS>\n    // \t\t\t\t|\n    // \t\t\t\tt = <ESCAPED_GT>\n    // \t\t\t\t|\n    // \t\t\t\tt = <ESCAPED_GTE>\n    // \t\t\t\t|\n    // \t\t\t\tt = <FALSE>\n    // \t\t\t\t|\n    // \t\t\t\tt = <TRUE>\n    // \t\t\t\t|\n    // \t\t\t\tt = <IN>\n    // \t\t\t\t|\n    // \t\t\t\tt = <AS>\n    // \t\t\t\t|\n    // \t\t\t\tt = <USING>\n    // \t\t\t)\n    // \t\t\t{\n    // \t\t\t\tif (!Character.isLetter(t.image.charAt(0))) {\n    // \t\t\t\t\tthrow new ParseException(t.image + \" is not a valid identifier.\", template, t);\n    // \t\t\t\t}\n    // \t\t\t}\n    // \t\t)\n    // \t\t{\n    // \t\t\tnotListLiteral(exp, \"hash\");\n    // \t\t\tnotStringLiteral(exp, \"hash\");\n    // \t\t\tnotBooleanLiteral(exp, \"hash\");\n    // \t\t\tDot dot = new Dot(exp, t.image);\n    // \t\t\tdot.setLocation(template, exp, t);\n    // \t\t\treturn dot;\n    // \t\t}\n    // }\n    specialHashKeys: /\\*\\*|\\*|false|true|in|as|using/,\n    // <DOUBLE_EQUALS : \"==\">\n    // <EQUALS : \"=\">\n    // <NOT_EQUALS : \"!=\">\n    // <PLUS_EQUALS : \"+=\">\n    // <MINUS_EQUALS : \"-=\">\n    // <TIMES_EQUALS : \"*=\">\n    // <DIV_EQUALS : \"/=\">\n    // <MOD_EQUALS : \"%=\">\n    // <PLUS_PLUS : \"++\">\n    // <MINUS_MINUS : \"--\">\n    // <LESS_THAN_EQUALS : \"lte\" | \"\\\\lte\" | \"<=\" | \"&lt;=\">\n    // <LESS_THAN : \"lt\" | \"\\\\lt\" | \"<\" | \"&lt;\">\n    // <ESCAPED_GTE : \"gte\" | \"\\\\gte\" | \"&gt;=\">\n    // <ESCAPED_GT: \"gt\" | \"\\\\gt\" |  \"&gt;\">\n    // <DOUBLE_STAR : \"**\">\n    // <PLUS : \"+\">\n    // <MINUS : \"-\">\n    // <TIMES : \"*\">\n    // <PERCENT : \"%\">\n    // <AND : \"&\" | \"&&\" | \"&amp;&amp;\" | \"\\\\and\" >\n    // <OR : \"|\" | \"||\">\n    // <EXCLAM : \"!\">\n    // <COMMA : \",\">\n    // <SEMICOLON : \";\">\n    // <COLON : \":\">\n    // <ELLIPSIS : \"...\">\n    // <DOT_DOT_ASTERISK : \"..*\" >\n    // <DOT_DOT_LESS : \"..<\" | \"..!\" >\n    // <DOT_DOT : \"..\">\n    // <EXISTS : \"??\">\n    // <BUILT_IN : \"?\">\n    // <LAMBDA_ARROW : \"->\" | \"-&gt;\">\n    namedSymbols: /&lt;=|&gt;=|\\\\lte|\\\\lt|&lt;|\\\\gte|\\\\gt|&gt;|&amp;&amp;|\\\\and|-&gt;|->|==|!=|\\+=|-=|\\*=|\\/=|%=|\\+\\+|--|<=|&&|\\|\\||:|\\.\\.\\.|\\.\\.\\*|\\.\\.<|\\.\\.!|\\?\\?|=|<|\\+|-|\\*|\\/|%|\\||\\.\\.|\\?|!|&|\\.|,|;/,\n    arrows: [\"->\", \"-&gt;\"],\n    delimiters: [\";\", \":\", \",\", \".\"],\n    stringOperators: [\"lte\", \"lt\", \"gte\", \"gt\"],\n    noParseTags: [\"noparse\", \"noParse\", \"comment\"],\n    tokenizer: {\n      // Parser states\n      // Plain text\n      [s(\"default__id__\")]: [\n        { include: s(\"@directive_token__id__\") },\n        { include: s(\"@interpolation_and_text_token__id__\") }\n      ],\n      // A FreeMarker expression inside a directive, e.g. <#if 2<3>\n      [s(\"fmExpression__id__.directive\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      // A FreeMarker expression inside an interpolation, e.g. ${2+3}\n      [s(\"fmExpression__id__.interpolation\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@expression_token__id__\") },\n        { include: s(\"@greater_operators_token__id__\") }\n      ],\n      // In an expression and inside a not-yet closed parenthesis / bracket\n      [s(\"inParen__id__.plain\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      [s(\"inParen__id__.gt\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@expression_token__id__\") },\n        { include: s(\"@greater_operators_token__id__\") }\n      ],\n      // Expression for the unified call, e.g. <@createMacro() ... >\n      [s(\"noSpaceExpression__id__\")]: [\n        { include: s(\"@no_space_expression_end_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      // For the function of a unified call. Special case for when the\n      // expression is a simple identifier.\n      // <@join [1,2] \",\">\n      // <@null!join [1,2] \",\">\n      [s(\"unifiedCall__id__\")]: [{ include: s(\"@unified_call_token__id__\") }],\n      // For singly and doubly quoted string (that may contain interpolations)\n      [s(\"singleString__id__\")]: [{ include: s(\"@string_single_token__id__\") }],\n      [s(\"doubleString__id__\")]: [{ include: s(\"@string_double_token__id__\") }],\n      // For singly and doubly quoted string (that may not contain interpolations)\n      [s(\"rawSingleString__id__\")]: [{ include: s(\"@string_single_raw_token__id__\") }],\n      [s(\"rawDoubleString__id__\")]: [{ include: s(\"@string_double_raw_token__id__\") }],\n      // For a comment in an expression\n      // ${ 1 + <#-- comment --> 2}\n      [s(\"expressionComment__id__\")]: [{ include: s(\"@expression_comment_token__id__\") }],\n      // For <#noparse> ... </#noparse>\n      // For <#noParse> ... </#noParse>\n      // For <#comment> ... </#comment>\n      [s(\"noParse__id__\")]: [{ include: s(\"@no_parse_token__id__\") }],\n      // For <#-- ... -->\n      [s(\"terseComment__id__\")]: [{ include: s(\"@terse_comment_token__id__\") }],\n      // Common rules\n      [s(\"directive_token__id__\")]: [\n        // <ATTEMPT : <START_TAG> \"attempt\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <RECOVER : <START_TAG> \"recover\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SEP : <START_TAG> \"sep\" <CLOSE_TAG1>>\n        // <AUTOESC : <START_TAG> \"auto\" (\"e\"|\"E\") \"sc\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 4), DEFAULT);\n        // }\n        // <NOAUTOESC : <START_TAG> \"no\" (\"autoe\"|\"AutoE\") \"sc\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        // <COMPRESS : <START_TAG> \"compress\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <DEFAUL : <START_TAG> \"default\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <NOESCAPE : <START_TAG> \"no\" (\"e\" | \"E\") \"scape\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        //\n        // <COMMENT : <START_TAG> \"comment\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, NO_PARSE); noparseTag = \"comment\";\n        // }\n        // <NOPARSE: <START_TAG> \"no\" (\"p\" | \"P\") \"arse\" <CLOSE_TAG1>> {\n        //     int tagNamingConvention = getTagNamingConvention(matchedToken, 2);\n        //     handleTagSyntaxAndSwitch(matchedToken, tagNamingConvention, NO_PARSE);\n        //     noparseTag = tagNamingConvention == Configuration.CAMEL_CASE_NAMING_CONVENTION ? \"noParse\" : \"noparse\";\n        // }\n        [\n          r(/(?:@startTag__id__)(@directiveStartCloseTag1)(?:@closeTag1__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            {\n              cases: {\n                \"@noParseTags\": { token: \"tag\", next: s(\"@noParse__id__.$3\") },\n                \"@default\": { token: \"tag\" }\n              }\n            },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <ELSE : <START_TAG> \"else\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <BREAK : <START_TAG> \"break\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <CONTINUE : <START_TAG> \"continue\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SIMPLE_RETURN : <START_TAG> \"return\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <HALT : <START_TAG> \"stop\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <FLUSH : <START_TAG> \"flush\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <TRIM : <START_TAG> \"t\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <LTRIM : <START_TAG> \"lt\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <RTRIM : <START_TAG> \"rt\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <NOTRIM : <START_TAG> \"nt\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SIMPLE_NESTED : <START_TAG> \"nested\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SIMPLE_RECURSE : <START_TAG> \"recurse\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <FALLBACK : <START_TAG> \"fallback\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <TRIVIAL_FTL_HEADER : (\"<#ftl\" | \"[#ftl\") (\"/\")? (\">\" | \"]\")> { ftlHeader(matchedToken); }\n        [\n          r(/(?:@startTag__id__)(@directiveStartCloseTag2)(?:@closeTag2__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <IF : <START_TAG> \"if\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <ELSE_IF : <START_TAG> \"else\" (\"i\" | \"I\") \"f\" <BLANK>> {\n        // \thandleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 4), FM_EXPRESSION);\n        // }\n        // <LIST : <START_TAG> \"list\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <FOREACH : <START_TAG> \"for\" (\"e\" | \"E\") \"ach\" <BLANK>> {\n        //    handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 3), FM_EXPRESSION);\n        // }\n        // <SWITCH : <START_TAG> \"switch\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <CASE : <START_TAG> \"case\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <ASSIGN : <START_TAG> \"assign\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <GLOBALASSIGN : <START_TAG> \"global\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <LOCALASSIGN : <START_TAG> \"local\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <_INCLUDE : <START_TAG> \"include\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <IMPORT : <START_TAG> \"import\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <FUNCTION : <START_TAG> \"function\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <MACRO : <START_TAG> \"macro\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <TRANSFORM : <START_TAG> \"transform\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <VISIT : <START_TAG> \"visit\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <STOP : <START_TAG> \"stop\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <RETURN : <START_TAG> \"return\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <CALL : <START_TAG> \"call\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <SETTING : <START_TAG> \"setting\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <OUTPUTFORMAT : <START_TAG> \"output\" (\"f\"|\"F\") \"ormat\" <BLANK>> {\n        //    handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 6), FM_EXPRESSION);\n        // }\n        // <NESTED : <START_TAG> \"nested\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <RECURSE : <START_TAG> \"recurse\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <ESCAPE : <START_TAG> \"escape\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        //\n        // Note: FreeMarker grammar appears to treat the FTL header as a special case,\n        // in order to remove new lines after the header (?), but since we only need\n        // to tokenize for highlighting, we can include this directive here.\n        // <FTL_HEADER : (\"<#ftl\" | \"[#ftl\") <BLANK>> { ftlHeader(matchedToken); }\n        //\n        // Note: FreeMarker grammar appears to treat the items directive as a special case for\n        // the AST parsing process, but since we only need to tokenize, we can include this\n        // directive here.\n        // <ITEMS : <START_TAG> \"items\" (<BLANK>)+ <AS> <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        [\n          r(/(?:@startTag__id__)(@directiveStartBlank)(@blank)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"\", next: s(\"@fmExpression__id__.directive\") }\n          ]\n        ],\n        // <END_IF : <END_TAG> \"if\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_LIST : <END_TAG> \"list\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_SEP : <END_TAG> \"sep\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_RECOVER : <END_TAG> \"recover\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_ATTEMPT : <END_TAG> \"attempt\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_FOREACH : <END_TAG> \"for\" (\"e\" | \"E\") \"ach\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 3), DEFAULT);\n        // }\n        // <END_LOCAL : <END_TAG> \"local\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_GLOBAL : <END_TAG> \"global\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_ASSIGN : <END_TAG> \"assign\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_FUNCTION : <END_TAG> \"function\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_MACRO : <END_TAG> \"macro\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_OUTPUTFORMAT : <END_TAG> \"output\" (\"f\" | \"F\") \"ormat\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 6), DEFAULT);\n        // }\n        // <END_AUTOESC : <END_TAG> \"auto\" (\"e\" | \"E\") \"sc\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 4), DEFAULT);\n        // }\n        // <END_NOAUTOESC : <END_TAG> \"no\" (\"autoe\"|\"AutoE\") \"sc\" <CLOSE_TAG1>> {\n        //   handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        // <END_COMPRESS : <END_TAG> \"compress\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_TRANSFORM : <END_TAG> \"transform\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_SWITCH : <END_TAG> \"switch\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_ESCAPE : <END_TAG> \"escape\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_NOESCAPE : <END_TAG> \"no\" (\"e\" | \"E\") \"scape\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        [\n          r(/(?:@endTag__id__)(@directiveEndCloseTag1)(?:@closeTag1__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <UNIFIED_CALL : \"<@\" | \"[@\" > { unifiedCall(matchedToken); }\n        [\n          r(/(@open__id__)(@)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\", next: s(\"@unifiedCall__id__\") }\n          ]\n        ],\n        // <UNIFIED_CALL_END : (\"<\" | \"[\") \"/@\" ((<ID>) (\".\"<ID>)*)? <CLOSE_TAG1>> { unifiedCallEnd(matchedToken); }\n        [\n          r(/(@open__id__)(\\/@)((?:(?:@id)(?:\\.(?:@id))*)?)(?:@closeTag1__id__)/),\n          [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <TERSE_COMMENT : (\"<\" | \"[\") \"#--\" > { noparseTag = \"-->\"; handleTagSyntaxAndSwitch(matchedToken, NO_PARSE); }\n        [\n          r(/(@open__id__)#--/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : { token: \"comment\", next: s(\"@terseComment__id__\") }\n        ],\n        // <UNKNOWN_DIRECTIVE : (\"[#\" | \"[/#\" | \"<#\" | \"</#\") ([\"a\"-\"z\", \"A\"-\"Z\", \"_\"])+>\n        [\n          r(/(?:@startOrEndTag__id__)([a-zA-Z_]+)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag.invalid\", next: s(\"@fmExpression__id__.directive\") }\n          ]\n        ]\n      ],\n      // <DEFAULT, NO_DIRECTIVE> TOKEN :\n      [s(\"interpolation_and_text_token__id__\")]: [\n        // <DOLLAR_INTERPOLATION_OPENING : \"${\"> { startInterpolation(matchedToken); }\n        // <SQUARE_BRACKET_INTERPOLATION_OPENING : \"[=\"> { startInterpolation(matchedToken); }\n        [\n          r(/(@iOpen1__id__)(@iOpen2__id__)/),\n          [\n            { token: is.id === \"bracket\" ? \"@brackets.interpolation\" : \"delimiter.interpolation\" },\n            {\n              token: is.id === \"bracket\" ? \"delimiter.interpolation\" : \"@brackets.interpolation\",\n              next: s(\"@fmExpression__id__.interpolation\")\n            }\n          ]\n        ],\n        // <STATIC_TEXT_FALSE_ALARM : \"$\" | \"#\" | \"<\" | \"[\" | \"{\"> // to handle a lone dollar sign or \"<\" or \"# or <@ with whitespace after\"\n        // <STATIC_TEXT_WS : (\"\\n\" | \"\\r\" | \"\\t\" | \" \")+>\n        // <STATIC_TEXT_NON_WS : (~[\"$\", \"<\", \"#\", \"[\", \"{\", \"\\n\", \"\\r\", \"\\t\", \" \"])+>\n        [/[\\$#<\\[\\{]|(?:@blank)+|[^\\$<#\\[\\{\\n\\r\\t ]+/, { token: \"source\" }]\n      ],\n      // <STRING_LITERAL :\n      // \t(\n      // \t\t\"\\\"\"\n      // \t\t((~[\"\\\"\", \"\\\\\"]) | <ESCAPED_CHAR>)*\n      // \t\t\"\\\"\"\n      // \t)\n      // \t|\n      // \t(\n      // \t\t\"'\"\n      // \t\t((~[\"'\", \"\\\\\"]) | <ESCAPED_CHAR>)*\n      // \t\t\"'\"\n      // \t)\n      // >\n      [s(\"string_single_token__id__\")]: [\n        [/[^'\\\\]/, { token: \"string\" }],\n        [/@escapedChar/, { token: \"string.escape\" }],\n        [/'/, { token: \"string\", next: \"@pop\" }]\n      ],\n      [s(\"string_double_token__id__\")]: [\n        [/[^\"\\\\]/, { token: \"string\" }],\n        [/@escapedChar/, { token: \"string.escape\" }],\n        [/\"/, { token: \"string\", next: \"@pop\" }]\n      ],\n      // <RAW_STRING : \"r\" ((\"\\\"\" (~[\"\\\"\"])* \"\\\"\") | (\"'\" (~[\"'\"])* \"'\"))>\n      [s(\"string_single_raw_token__id__\")]: [\n        [/[^']+/, { token: \"string.raw\" }],\n        [/'/, { token: \"string.raw\", next: \"@pop\" }]\n      ],\n      [s(\"string_double_raw_token__id__\")]: [\n        [/[^\"]+/, { token: \"string.raw\" }],\n        [/\"/, { token: \"string.raw\", next: \"@pop\" }]\n      ],\n      // <FM_EXPRESSION, IN_PAREN, NO_SPACE_EXPRESSION, NAMED_PARAMETER_EXPRESSION> TOKEN :\n      [s(\"expression_token__id__\")]: [\n        // Strings\n        [\n          /(r?)(['\"])/,\n          {\n            cases: {\n              \"r'\": [\n                { token: \"keyword\" },\n                { token: \"string.raw\", next: s(\"@rawSingleString__id__\") }\n              ],\n              'r\"': [\n                { token: \"keyword\" },\n                { token: \"string.raw\", next: s(\"@rawDoubleString__id__\") }\n              ],\n              \"'\": [{ token: \"source\" }, { token: \"string\", next: s(\"@singleString__id__\") }],\n              '\"': [{ token: \"source\" }, { token: \"string\", next: s(\"@doubleString__id__\") }]\n            }\n          }\n        ],\n        // Numbers\n        // <INTEGER : ([\"0\"-\"9\"])+>\n        // <DECIMAL : <INTEGER> \".\" <INTEGER>>\n        [\n          /(?:@integer)(?:\\.(?:@integer))?/,\n          {\n            cases: {\n              \"(?:@integer)\": { token: \"number\" },\n              \"@default\": { token: \"number.float\" }\n            }\n          }\n        ],\n        // Special hash keys that must not be treated as identifiers\n        // after a period, e.g. a.** is accessing the key \"**\" of a\n        [\n          /(\\.)(@blank*)(@specialHashKeys)/,\n          [{ token: \"delimiter\" }, { token: \"\" }, { token: \"identifier\" }]\n        ],\n        // Symbols / operators\n        [\n          /(?:@namedSymbols)/,\n          {\n            cases: {\n              \"@arrows\": { token: \"meta.arrow\" },\n              \"@delimiters\": { token: \"delimiter\" },\n              \"@default\": { token: \"operators\" }\n            }\n          }\n        ],\n        // Identifiers\n        [\n          /@id/,\n          {\n            cases: {\n              \"@keywords\": { token: \"keyword.$0\" },\n              \"@stringOperators\": { token: \"operators\" },\n              \"@default\": { token: \"identifier\" }\n            }\n          }\n        ],\n        // <OPEN_BRACKET : \"[\">\n        // <CLOSE_BRACKET : \"]\">\n        // <OPEN_PAREN : \"(\">\n        // <CLOSE_PAREN : \")\">\n        // <OPENING_CURLY_BRACKET : \"{\">\n        // <CLOSING_CURLY_BRACKET : \"}\">\n        [\n          /[\\[\\]\\(\\)\\{\\}]/,\n          {\n            cases: {\n              \"\\\\[\": {\n                cases: {\n                  \"$S2==gt\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n                  \"@default\": { token: \"@brackets\", next: s(\"@inParen__id__.plain\") }\n                }\n              },\n              \"\\\\]\": {\n                cases: {\n                  ...is.id === \"bracket\" ? {\n                    \"$S2==interpolation\": { token: \"@brackets.interpolation\", next: \"@popall\" }\n                  } : {},\n                  // This cannot happen while in auto mode, since this applies only to an\n                  // fmExpression inside a directive. But once we encounter the start of a\n                  // directive, we can establish the tag syntax mode.\n                  ...ts.id === \"bracket\" ? {\n                    \"$S2==directive\": { token: \"@brackets.directive\", next: \"@popall\" }\n                  } : {},\n                  // Ignore mismatched paren\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              },\n              \"\\\\(\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n              \"\\\\)\": {\n                cases: {\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              },\n              \"\\\\{\": {\n                cases: {\n                  \"$S2==gt\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n                  \"@default\": { token: \"@brackets\", next: s(\"@inParen__id__.plain\") }\n                }\n              },\n              \"\\\\}\": {\n                cases: {\n                  ...is.id === \"bracket\" ? {} : {\n                    \"$S2==interpolation\": { token: \"@brackets.interpolation\", next: \"@popall\" }\n                  },\n                  // Ignore mismatched paren\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              }\n            }\n          }\n        ],\n        // <OPEN_MISPLACED_INTERPOLATION : \"${\" | \"#{\" | \"[=\">\n        [/\\$\\{/, { token: \"delimiter.invalid\" }]\n      ],\n      // <FM_EXPRESSION, IN_PAREN, NAMED_PARAMETER_EXPRESSION> SKIP :\n      [s(\"blank_and_expression_comment_token__id__\")]: [\n        // < ( \" \" | \"\\t\" | \"\\n\" | \"\\r\" )+ >\n        [/(?:@blank)+/, { token: \"\" }],\n        // < (\"<\" | \"[\") (\"#\" | \"!\") \"--\"> : EXPRESSION_COMMENT\n        [/[<\\[][#!]--/, { token: \"comment\", next: s(\"@expressionComment__id__\") }]\n      ],\n      // <FM_EXPRESSION, NO_SPACE_EXPRESSION, NAMED_PARAMETER_EXPRESSION> TOKEN :\n      [s(\"directive_end_token__id__\")]: [\n        // <DIRECTIVE_END : \">\">\n        // {\n        //     if (inFTLHeader) {\n        //         eatNewline();\n        //         inFTLHeader = false;\n        //     }\n        //     if (squBracTagSyntax || postInterpolationLexState != -1 /* We are in an interpolation */) {\n        //         matchedToken.kind = NATURAL_GT;\n        //     } else {\n        //         SwitchTo(DEFAULT);\n        //     }\n        // }\n        // This cannot happen while in auto mode, since this applies only to an\n        // fmExpression inside a directive. But once we encounter the start of a\n        // directive, we can establish the tag syntax mode.\n        [\n          />/,\n          ts.id === \"bracket\" ? { token: \"operators\" } : { token: \"@brackets.directive\", next: \"@popall\" }\n        ],\n        // <EMPTY_DIRECTIVE_END : \"/>\" | \"/]\">\n        // It is a syntax error to end a tag with the wrong close token\n        // Let's indicate that to the user by not closing the tag\n        [\n          r(/(\\/)(@close__id__)/),\n          [{ token: \"delimiter.directive\" }, { token: \"@brackets.directive\", next: \"@popall\" }]\n        ]\n      ],\n      // <IN_PAREN> TOKEN :\n      [s(\"greater_operators_token__id__\")]: [\n        // <NATURAL_GT : \">\">\n        [/>/, { token: \"operators\" }],\n        // <NATURAL_GTE : \">=\">\n        [/>=/, { token: \"operators\" }]\n      ],\n      // <NO_SPACE_EXPRESSION> TOKEN :\n      [s(\"no_space_expression_end_token__id__\")]: [\n        // <TERMINATING_WHITESPACE :  ([\"\\n\", \"\\r\", \"\\t\", \" \"])+> : FM_EXPRESSION\n        [/(?:@blank)+/, { token: \"\", switchTo: s(\"@fmExpression__id__.directive\") }]\n      ],\n      [s(\"unified_call_token__id__\")]: [\n        // Special case for a call where the expression is just an ID\n        // <UNIFIED_CALL> <ID> <BLANK>+\n        [\n          /(@id)((?:@blank)+)/,\n          [{ token: \"tag\" }, { token: \"\", next: s(\"@fmExpression__id__.directive\") }]\n        ],\n        [\n          r(/(@id)(\\/?)(@close__id__)/),\n          [\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\", next: \"@popall\" }\n          ]\n        ],\n        [/./, { token: \"@rematch\", next: s(\"@noSpaceExpression__id__\") }]\n      ],\n      // <NO_PARSE> TOKEN :\n      [s(\"no_parse_token__id__\")]: [\n        // <MAYBE_END :\n        // \t (\"<\" | \"[\")\n        // \t \"/\"\n        // \t (\"#\")?\n        // \t ([\"a\"-\"z\", \"A\"-\"Z\"])+\n        // \t ( \" \" | \"\\t\" | \"\\n\" | \"\\r\" )*\n        // \t (\">\" | \"]\")\n        // >\n        [\n          r(/(@open__id__)(\\/#?)([a-zA-Z]+)((?:@blank)*)(@close__id__)/),\n          {\n            cases: {\n              \"$S2==$3\": [\n                { token: \"@brackets.directive\" },\n                { token: \"delimiter.directive\" },\n                { token: \"tag\" },\n                { token: \"\" },\n                { token: \"@brackets.directive\", next: \"@popall\" }\n              ],\n              \"$S2==comment\": [\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" }\n              ],\n              \"@default\": [\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" }\n              ]\n            }\n          }\n        ],\n        // <KEEP_GOING : (~[\"<\", \"[\", \"-\"])+>\n        // <LONE_LESS_THAN_OR_DASH : [\"<\", \"[\", \"-\"]>\n        [\n          /[^<\\[\\-]+|[<\\[\\-]/,\n          {\n            cases: {\n              \"$S2==comment\": { token: \"comment\" },\n              \"@default\": { token: \"source\" }\n            }\n          }\n        ]\n      ],\n      // <EXPRESSION_COMMENT> SKIP:\n      [s(\"expression_comment_token__id__\")]: [\n        // < \"-->\" | \"--]\">\n        [\n          /--[>\\]]/,\n          {\n            token: \"comment\",\n            next: \"@pop\"\n          }\n        ],\n        // < (~[\"-\", \">\", \"]\"])+ >\n        // < \">\">\n        // < \"]\">\n        // < \"-\">\n        [/[^\\->\\]]+|[>\\]\\-]/, { token: \"comment\" }]\n      ],\n      [s(\"terse_comment_token__id__\")]: [\n        //  <TERSE_COMMENT_END : \"-->\" | \"--]\">\n        [r(/--(?:@close__id__)/), { token: \"comment\", next: \"@popall\" }],\n        // <KEEP_GOING : (~[\"<\", \"[\", \"-\"])+>\n        // <LONE_LESS_THAN_OR_DASH : [\"<\", \"[\", \"-\"]>\n        [/[^<\\[\\-]+|[<\\[\\-]/, { token: \"comment\" }]\n      ]\n    }\n  };\n}\nfunction createMonarchLanguageAuto(is) {\n  const angle = createMonarchLanguage(TagSyntaxAngle, is);\n  const bracket = createMonarchLanguage(TagSyntaxBracket, is);\n  const auto = createMonarchLanguage(TagSyntaxAuto, is);\n  return {\n    // Angle and bracket syntax mode\n    // We switch to one of these once we have determined the mode\n    ...angle,\n    ...bracket,\n    ...auto,\n    // Settings\n    unicode: true,\n    includeLF: false,\n    start: `default_auto_${is.id}`,\n    ignoreCase: false,\n    defaultToken: \"invalid\",\n    tokenPostfix: `.freemarker2`,\n    brackets: [\n      { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n      { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n      { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n      { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n    ],\n    tokenizer: {\n      ...angle.tokenizer,\n      ...bracket.tokenizer,\n      ...auto.tokenizer\n    }\n  };\n}\nvar TagAngleInterpolationDollar = {\n  conf: createLangConfiguration(TagSyntaxAngle),\n  language: createMonarchLanguage(TagSyntaxAngle, InterpolationSyntaxDollar)\n};\nvar TagBracketInterpolationDollar = {\n  conf: createLangConfiguration(TagSyntaxBracket),\n  language: createMonarchLanguage(TagSyntaxBracket, InterpolationSyntaxDollar)\n};\nvar TagAngleInterpolationBracket = {\n  conf: createLangConfiguration(TagSyntaxAngle),\n  language: createMonarchLanguage(TagSyntaxAngle, InterpolationSyntaxBracket)\n};\nvar TagBracketInterpolationBracket = {\n  conf: createLangConfiguration(TagSyntaxBracket),\n  language: createMonarchLanguage(TagSyntaxBracket, InterpolationSyntaxBracket)\n};\nvar TagAutoInterpolationDollar = {\n  conf: createLangConfigurationAuto(),\n  language: createMonarchLanguageAuto(InterpolationSyntaxDollar)\n};\nvar TagAutoInterpolationBracket = {\n  conf: createLangConfigurationAuto(),\n  language: createMonarchLanguageAuto(InterpolationSyntaxBracket)\n};\nexport {\n  TagAngleInterpolationBracket,\n  TagAngleInterpolationDollar,\n  TagAutoInterpolationBracket,\n  TagAutoInterpolationDollar,\n  TagBracketInterpolationBracket,\n  TagBracketInterpolationDollar\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;;AAOA,IAAI,EAAY,OAAO,eACnB,EAAmB,OAAO,yBAC1B,EAAoB,OAAO,oBAC3B,EAAe,OAAO,UAAU,eAChC,GAAe,EAAI,EAAM,EAAQ,IAAS,CAC5C,GAAI,GAAQ,OAAO,GAAS,UAAY,OAAO,GAAS,eACjD,IAAI,KAAO,EAAkB,EAAK,CACjC,CAAC,EAAa,KAAK,EAAI,EAAI,EAAI,IAAQ,GACzC,EAAU,EAAI,EAAK,CAAE,QAAW,EAAK,GAAM,WAAY,EAAE,EAAO,EAAiB,EAAM,EAAI,GAAK,EAAK,WAAY,CAAC,CAExH,OAAO,GAEL,GAAc,EAAQ,EAAK,KAAkB,EAAY,EAAQ,EAAK,UAAU,CAAE,GAAgB,EAAY,EAAc,EAAK,UAAU,EAG3I,EAA6B,EAAE,CACnC,EAAW,EAA4BA,EAAwB,CAI/D,IAAI,EAAiB,CACnB,SACA,QACA,MACA,SACA,SACA,SACA,UACA,QACA,WACA,QACA,SACA,KACA,UACA,OACA,IACA,KACA,KACA,WACD,CACG,EAAiB,CACnB,UACA,UACA,UACA,WACA,UACA,SACA,WACA,WACA,KACA,OACA,QACA,MACA,QACA,UACA,UACA,YACA,YACA,eACA,SACA,QACA,UACD,CACG,EAAiB,CACnB,MAAO,IACP,GAAI,QACJ,KAAM,IACP,CACG,EAAmB,CACrB,MAAO,MACP,GAAI,UACJ,KAAM,MACP,CACG,EAAgB,CAClB,MAAO,SACP,GAAI,OACJ,KAAM,SACP,CACG,EAA4B,CAC9B,MAAO,MACP,GAAI,SACJ,MAAO,MACP,MAAO,MACR,CACG,EAA6B,CAC/B,MAAO,MACP,GAAI,UACJ,MAAO,MACP,MAAO,IACR,CACD,SAAS,EAAwB,EAAI,CACnC,MAAO,CACL,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,SAAU,CACR,aAAc,CAAC,GAAG,EAAG,KAAK,IAAK,KAAK,EAAG,QAAQ,CAChD,CACD,gBAAiB;cACjB,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC5C,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC7C,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAW,OACT,GAAG,EAAG,KAAK,MAAM,EAAe,KAAK,IAAI,CAAC,OAAO,EAAG,MAAM,SAAS,EAAG,MAAM,KAAK,EAAG,KAAK,KAC1F,CACD,IAAS,OAAO,GAAG,EAAG,KAAK,OAAO,EAAe,KAAK,IAAI,CAAC,iBAAiB,CAC7E,CACF,CACD,aAAc,CACZ,CACE,WAAgB,OACd,GAAG,EAAG,KAAK,SAAS,EAAe,KAAK,IAAI,CAAC,oBAAoB,EAAG,MAAM,SAAS,EAAG,MAAM,KAAK,EAAG,KAAK,KAC1G,CACD,UAAe,OAAO,IAAI,EAAG,KAAK,6BAA6B,EAAG,MAAM,GAAG,CAC3E,OAAQ,CACN,aAAc,EAA2B,UAAU,aAAa,cACjE,CACF,CACD,CACE,WAAgB,OACd,GAAG,EAAG,KAAK,SAAS,EAAe,KAAK,IAAI,CAAC,oBAAoB,EAAG,MAAM,SAAS,EAAG,MAAM,KAAK,EAAG,KAAK,KAC1G,CACD,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,OAAQ,CACnF,CACF,CACF,CAEH,SAAS,GAA8B,CACrC,MAAO,CAGL,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,gBAAiB;cACjB,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC5C,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC7C,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,aAAa,EAAe,KAAK,IAAI,CAAC,kCAAkC,CAC1F,IAAS,OAAO,cAAc,EAAe,KAAK,IAAI,CAAC,iBAAiB,CACzE,CACF,CACD,aAAc,CACZ,CACE,WAAgB,OACd,gBAAgB,EAAe,KAAK,IAAI,CAAC,iDAC1C,CACD,UAAe,OAAO,4CAA4C,CAClE,OAAQ,CACN,aAAc,EAA2B,UAAU,aAAa,cACjE,CACF,CACD,CACE,WAAgB,OACd,gBAAgB,EAAe,KAAK,IAAI,CAAC,iDAC1C,CACD,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,OAAQ,CACnF,CACF,CACF,CAEH,SAAS,EAAsB,EAAI,EAAI,CACrC,IAAM,EAAK,IAAI,EAAG,GAAG,GAAG,EAAG,KACrB,EAAK,GAAS,EAAK,QAAQ,UAAW,EAAG,CACzC,EAAK,GAAW,CACpB,IAAM,EAAS,EAAO,OAAO,QAAQ,UAAW,EAAG,CACnD,OAAO,IAAI,OAAO,EAAQ,EAAO,MAAM,EAEzC,MAAO,CAEL,QAAS,GACT,UAAW,GACX,MAAO,EAAE,gBAAgB,CACzB,WAAY,GACZ,aAAc,UACd,aAAc,eACd,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,mBAAoB,CACpD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CACzD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACpD,EAEA,EAAE,aAAa,EAAG,IAAI,OAAO,EAAG,KAAK,EACrC,EAAE,cAAc,EAAG,IAAI,OAAO,EAAG,MAAM,EACvC,EAAE,eAAe,EAAG,IAAI,OAAO,EAAG,MAAM,EACxC,EAAE,eAAe,EAAG,IAAI,OAAO,EAAG,MAAM,EACxC,EAAE,eAAe,EAAG,IAAI,OAAO,EAAG,MAAM,EAGxC,EAAE,iBAAiB,EAAG,EAAE,mBAAmB,EAC3C,EAAE,eAAe,EAAG,EAAE,qBAAqB,EAC3C,EAAE,sBAAsB,EAAG,EAAE,sBAAsB,EAEnD,EAAE,kBAAkB,EAAG,EAAE,8BAA8B,EAEvD,EAAE,kBAAkB,EAAG,EAAE,iCAAiC,CAG3D,MAAO,YAMP,SAAU,CAAC,QAAS,OAAQ,KAAM,KAAM,QAAQ,CAGhD,wBAAyB,qGAIzB,wBAAyB,+EAGzB,oBAAqB,sLAGrB,sBAAuB,8KAWvB,YAAa,gDAEb,WAAY,QAEZ,QAAS,SAMT,sBAAuB,m9CAEvB,cAAe,aAEf,YAAa,gDAEb,GAAI,wDA8CJ,gBAAiB,iCAiCjB,aAAc,2LACd,OAAQ,CAAC,KAAM,QAAQ,CACvB,WAAY,CAAC,IAAK,IAAK,IAAK,IAAI,CAChC,gBAAiB,CAAC,MAAO,KAAM,MAAO,KAAK,CAC3C,YAAa,CAAC,UAAW,UAAW,UAAU,CAC9C,UAAW,EAGR,EAAE,gBAAgB,EAAG,CACpB,CAAE,QAAS,EAAE,yBAAyB,CAAE,CACxC,CAAE,QAAS,EAAE,sCAAsC,CAAE,CACtD,EAEA,EAAE,+BAA+B,EAAG,CACnC,CAAE,QAAS,EAAE,4CAA4C,CAAE,CAC3D,CAAE,QAAS,EAAE,6BAA6B,CAAE,CAC5C,CAAE,QAAS,EAAE,0BAA0B,CAAE,CAC1C,EAEA,EAAE,mCAAmC,EAAG,CACvC,CAAE,QAAS,EAAE,4CAA4C,CAAE,CAC3D,CAAE,QAAS,EAAE,0BAA0B,CAAE,CACzC,CAAE,QAAS,EAAE,iCAAiC,CAAE,CACjD,EAEA,EAAE,sBAAsB,EAAG,CAC1B,CAAE,QAAS,EAAE,4CAA4C,CAAE,CAC3D,CAAE,QAAS,EAAE,6BAA6B,CAAE,CAC5C,CAAE,QAAS,EAAE,0BAA0B,CAAE,CAC1C,EACA,EAAE,mBAAmB,EAAG,CACvB,CAAE,QAAS,EAAE,4CAA4C,CAAE,CAC3D,CAAE,QAAS,EAAE,0BAA0B,CAAE,CACzC,CAAE,QAAS,EAAE,iCAAiC,CAAE,CACjD,EAEA,EAAE,0BAA0B,EAAG,CAC9B,CAAE,QAAS,EAAE,uCAAuC,CAAE,CACtD,CAAE,QAAS,EAAE,6BAA6B,CAAE,CAC5C,CAAE,QAAS,EAAE,0BAA0B,CAAE,CAC1C,EAKA,EAAE,oBAAoB,EAAG,CAAC,CAAE,QAAS,EAAE,4BAA4B,CAAE,CAAC,EAEtE,EAAE,qBAAqB,EAAG,CAAC,CAAE,QAAS,EAAE,6BAA6B,CAAE,CAAC,EACxE,EAAE,qBAAqB,EAAG,CAAC,CAAE,QAAS,EAAE,6BAA6B,CAAE,CAAC,EAExE,EAAE,wBAAwB,EAAG,CAAC,CAAE,QAAS,EAAE,iCAAiC,CAAE,CAAC,EAC/E,EAAE,wBAAwB,EAAG,CAAC,CAAE,QAAS,EAAE,iCAAiC,CAAE,CAAC,EAG/E,EAAE,0BAA0B,EAAG,CAAC,CAAE,QAAS,EAAE,kCAAkC,CAAE,CAAC,EAIlF,EAAE,gBAAgB,EAAG,CAAC,CAAE,QAAS,EAAE,wBAAwB,CAAE,CAAC,EAE9D,EAAE,qBAAqB,EAAG,CAAC,CAAE,QAAS,EAAE,6BAA6B,CAAE,CAAC,EAExE,EAAE,wBAAwB,EAAG,CAwB5B,CACE,EAAE,oEAAoE,CACtE,EAAG,KAAO,OAAS,CACjB,MAAO,CACL,QAAS,CAAE,MAAO,WAAY,SAAU,kBAAkB,EAAG,KAAM,CACnE,QAAS,CAAE,MAAO,WAAY,SAAU,oBAAoB,EAAG,KAAM,CACtE,CACF,CAAG,CACF,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,CAChC,CACE,MAAO,CACL,eAAgB,CAAE,MAAO,MAAO,KAAM,EAAE,oBAAoB,CAAE,CAC9D,WAAY,CAAE,MAAO,MAAO,CAC7B,CACF,CACD,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,CACjC,CACF,CAeD,CACE,EAAE,oEAAoE,CACtE,EAAG,KAAO,OAAS,CACjB,MAAO,CACL,QAAS,CAAE,MAAO,WAAY,SAAU,kBAAkB,EAAG,KAAM,CACnE,QAAS,CAAE,MAAO,WAAY,SAAU,oBAAoB,EAAG,KAAM,CACtE,CACF,CAAG,CACF,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,MAAO,CAChB,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,CACjC,CACF,CAwCD,CACE,EAAE,oDAAoD,CACtD,EAAG,KAAO,OAAS,CACjB,MAAO,CACL,QAAS,CAAE,MAAO,WAAY,SAAU,kBAAkB,EAAG,KAAM,CACnE,QAAS,CAAE,MAAO,WAAY,SAAU,oBAAoB,EAAG,KAAM,CACtE,CACF,CAAG,CACF,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,MAAO,CAChB,CAAE,MAAO,GAAI,KAAM,EAAE,gCAAgC,CAAE,CACxD,CACF,CA8BD,CACE,EAAE,gEAAgE,CAClE,EAAG,KAAO,OAAS,CACjB,MAAO,CACL,QAAS,CAAE,MAAO,WAAY,SAAU,kBAAkB,EAAG,KAAM,CACnE,QAAS,CAAE,MAAO,WAAY,SAAU,oBAAoB,EAAG,KAAM,CACtE,CACF,CAAG,CACF,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,MAAO,CAChB,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,CACjC,CACF,CAED,CACE,EAAE,mBAAmB,CACrB,EAAG,KAAO,OAAS,CACjB,MAAO,CACL,QAAS,CAAE,MAAO,WAAY,SAAU,kBAAkB,EAAG,KAAM,CACnE,QAAS,CAAE,MAAO,WAAY,SAAU,oBAAoB,EAAG,KAAM,CACtE,CACF,CAAG,CACF,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,KAAM,EAAE,qBAAqB,CAAE,CAChE,CACF,CAED,CACE,EAAE,qEAAqE,CACvE,CACE,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,MAAO,CAChB,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,CACjC,CACF,CAED,CACE,EAAE,mBAAmB,CACrB,EAAG,KAAO,OAAS,CACjB,MAAO,CACL,QAAS,CAAE,MAAO,WAAY,SAAU,kBAAkB,EAAG,KAAM,CACnE,QAAS,CAAE,MAAO,WAAY,SAAU,oBAAoB,EAAG,KAAM,CACtE,CACF,CAAG,CAAE,MAAO,UAAW,KAAM,EAAE,sBAAsB,CAAE,CACzD,CAED,CACE,EAAE,uCAAuC,CACzC,EAAG,KAAO,OAAS,CACjB,MAAO,CACL,QAAS,CAAE,MAAO,WAAY,SAAU,kBAAkB,EAAG,KAAM,CACnE,QAAS,CAAE,MAAO,WAAY,SAAU,oBAAoB,EAAG,KAAM,CACtE,CACF,CAAG,CACF,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,cAAe,KAAM,EAAE,gCAAgC,CAAE,CACnE,CACF,CACF,EAEA,EAAE,qCAAqC,EAAG,CAGzC,CACE,EAAE,iCAAiC,CACnC,CACE,CAAE,MAAO,EAAG,KAAO,UAAY,0BAA4B,0BAA2B,CACtF,CACE,MAAO,EAAG,KAAO,UAAY,0BAA4B,0BACzD,KAAM,EAAE,oCAAoC,CAC7C,CACF,CACF,CAID,CAAC,6CAA8C,CAAE,MAAO,SAAU,CAAC,CACpE,EAcA,EAAE,4BAA4B,EAAG,CAChC,CAAC,SAAU,CAAE,MAAO,SAAU,CAAC,CAC/B,CAAC,eAAgB,CAAE,MAAO,gBAAiB,CAAC,CAC5C,CAAC,IAAK,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAC,CACzC,EACA,EAAE,4BAA4B,EAAG,CAChC,CAAC,SAAU,CAAE,MAAO,SAAU,CAAC,CAC/B,CAAC,eAAgB,CAAE,MAAO,gBAAiB,CAAC,CAC5C,CAAC,IAAK,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAC,CACzC,EAEA,EAAE,gCAAgC,EAAG,CACpC,CAAC,QAAS,CAAE,MAAO,aAAc,CAAC,CAClC,CAAC,IAAK,CAAE,MAAO,aAAc,KAAM,OAAQ,CAAC,CAC7C,EACA,EAAE,gCAAgC,EAAG,CACpC,CAAC,QAAS,CAAE,MAAO,aAAc,CAAC,CAClC,CAAC,IAAK,CAAE,MAAO,aAAc,KAAM,OAAQ,CAAC,CAC7C,EAEA,EAAE,yBAAyB,EAAG,CAE7B,CACE,aACA,CACE,MAAO,CACL,KAAM,CACJ,CAAE,MAAO,UAAW,CACpB,CAAE,MAAO,aAAc,KAAM,EAAE,yBAAyB,CAAE,CAC3D,CACD,KAAM,CACJ,CAAE,MAAO,UAAW,CACpB,CAAE,MAAO,aAAc,KAAM,EAAE,yBAAyB,CAAE,CAC3D,CACD,IAAK,CAAC,CAAE,MAAO,SAAU,CAAE,CAAE,MAAO,SAAU,KAAM,EAAE,sBAAsB,CAAE,CAAC,CAC/E,IAAK,CAAC,CAAE,MAAO,SAAU,CAAE,CAAE,MAAO,SAAU,KAAM,EAAE,sBAAsB,CAAE,CAAC,CAChF,CACF,CACF,CAID,CACE,kCACA,CACE,MAAO,CACL,eAAgB,CAAE,MAAO,SAAU,CACnC,WAAY,CAAE,MAAO,eAAgB,CACtC,CACF,CACF,CAGD,CACE,kCACA,CAAC,CAAE,MAAO,YAAa,CAAE,CAAE,MAAO,GAAI,CAAE,CAAE,MAAO,aAAc,CAAC,CACjE,CAED,CACE,oBACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,aAAc,CAClC,cAAe,CAAE,MAAO,YAAa,CACrC,WAAY,CAAE,MAAO,YAAa,CACnC,CACF,CACF,CAED,CACE,MACA,CACE,MAAO,CACL,YAAa,CAAE,MAAO,aAAc,CACpC,mBAAoB,CAAE,MAAO,YAAa,CAC1C,WAAY,CAAE,MAAO,aAAc,CACpC,CACF,CACF,CAOD,CACE,iBACA,CACE,MAAO,CACL,MAAO,CACL,MAAO,CACL,UAAW,CAAE,MAAO,YAAa,KAAM,EAAE,oBAAoB,CAAE,CAC/D,WAAY,CAAE,MAAO,YAAa,KAAM,EAAE,uBAAuB,CAAE,CACpE,CACF,CACD,MAAO,CACL,MAAO,CACL,GAAG,EAAG,KAAO,UAAY,CACvB,qBAAsB,CAAE,MAAO,0BAA2B,KAAM,UAAW,CAC5E,CAAG,EAAE,CAIN,GAAG,EAAG,KAAO,UAAY,CACvB,iBAAkB,CAAE,MAAO,sBAAuB,KAAM,UAAW,CACpE,CAAG,EAAE,EAEL,EAAE,qBAAqB,EAAG,CAAE,MAAO,YAAa,KAAM,OAAQ,CAC/D,WAAY,CAAE,MAAO,YAAa,CACnC,CACF,CACD,MAAO,CAAE,MAAO,YAAa,KAAM,EAAE,oBAAoB,CAAE,CAC3D,MAAO,CACL,MAAO,EACJ,EAAE,qBAAqB,EAAG,CAAE,MAAO,YAAa,KAAM,OAAQ,CAC/D,WAAY,CAAE,MAAO,YAAa,CACnC,CACF,CACD,MAAO,CACL,MAAO,CACL,UAAW,CAAE,MAAO,YAAa,KAAM,EAAE,oBAAoB,CAAE,CAC/D,WAAY,CAAE,MAAO,YAAa,KAAM,EAAE,uBAAuB,CAAE,CACpE,CACF,CACD,MAAO,CACL,MAAO,CACL,GAAG,EAAG,KAAO,UAAY,EAAE,CAAG,CAC5B,qBAAsB,CAAE,MAAO,0BAA2B,KAAM,UAAW,CAC5E,EAEA,EAAE,qBAAqB,EAAG,CAAE,MAAO,YAAa,KAAM,OAAQ,CAC/D,WAAY,CAAE,MAAO,YAAa,CACnC,CACF,CACF,CACF,CACF,CAED,CAAC,OAAQ,CAAE,MAAO,oBAAqB,CAAC,CACzC,EAEA,EAAE,2CAA2C,EAAG,CAE/C,CAAC,cAAe,CAAE,MAAO,GAAI,CAAC,CAE9B,CAAC,cAAe,CAAE,MAAO,UAAW,KAAM,EAAE,2BAA2B,CAAE,CAAC,CAC3E,EAEA,EAAE,4BAA4B,EAAG,CAgBhC,CACE,IACA,EAAG,KAAO,UAAY,CAAE,MAAO,YAAa,CAAG,CAAE,MAAO,sBAAuB,KAAM,UAAW,CACjG,CAID,CACE,EAAE,qBAAqB,CACvB,CAAC,CAAE,MAAO,sBAAuB,CAAE,CAAE,MAAO,sBAAuB,KAAM,UAAW,CAAC,CACtF,CACF,EAEA,EAAE,gCAAgC,EAAG,CAEpC,CAAC,IAAK,CAAE,MAAO,YAAa,CAAC,CAE7B,CAAC,KAAM,CAAE,MAAO,YAAa,CAAC,CAC/B,EAEA,EAAE,sCAAsC,EAAG,CAE1C,CAAC,cAAe,CAAE,MAAO,GAAI,SAAU,EAAE,gCAAgC,CAAE,CAAC,CAC7E,EACA,EAAE,2BAA2B,EAAG,CAG/B,CACE,qBACA,CAAC,CAAE,MAAO,MAAO,CAAE,CAAE,MAAO,GAAI,KAAM,EAAE,gCAAgC,CAAE,CAAC,CAC5E,CACD,CACE,EAAE,2BAA2B,CAC7B,CACE,CAAE,MAAO,MAAO,CAChB,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,KAAM,UAAW,CAClD,CACF,CACD,CAAC,IAAK,CAAE,MAAO,WAAY,KAAM,EAAE,2BAA2B,CAAE,CAAC,CAClE,EAEA,EAAE,uBAAuB,EAAG,CAS3B,CACE,EAAE,4DAA4D,CAC9D,CACE,MAAO,CACL,UAAW,CACT,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,sBAAuB,CAChC,CAAE,MAAO,MAAO,CAChB,CAAE,MAAO,GAAI,CACb,CAAE,MAAO,sBAAuB,KAAM,UAAW,CAClD,CACD,eAAgB,CACd,CAAE,MAAO,UAAW,CACpB,CAAE,MAAO,UAAW,CACpB,CAAE,MAAO,UAAW,CACpB,CAAE,MAAO,UAAW,CACpB,CAAE,MAAO,UAAW,CACrB,CACD,WAAY,CACV,CAAE,MAAO,SAAU,CACnB,CAAE,MAAO,SAAU,CACnB,CAAE,MAAO,SAAU,CACnB,CAAE,MAAO,SAAU,CACnB,CAAE,MAAO,SAAU,CACpB,CACF,CACF,CACF,CAGD,CACE,oBACA,CACE,MAAO,CACL,eAAgB,CAAE,MAAO,UAAW,CACpC,WAAY,CAAE,MAAO,SAAU,CAChC,CACF,CACF,CACF,EAEA,EAAE,iCAAiC,EAAG,CAErC,CACE,UACA,CACE,MAAO,UACP,KAAM,OACP,CACF,CAKD,CAAC,oBAAqB,CAAE,MAAO,UAAW,CAAC,CAC5C,EACA,EAAE,4BAA4B,EAAG,CAEhC,CAAC,EAAE,qBAAqB,CAAE,CAAE,MAAO,UAAW,KAAM,UAAW,CAAC,CAGhE,CAAC,oBAAqB,CAAE,MAAO,UAAW,CAAC,CAC5C,CACF,CACF,CAEH,SAAS,EAA0B,EAAI,CACrC,IAAM,EAAQ,EAAsB,EAAgB,EAAG,CACjD,EAAU,EAAsB,EAAkB,EAAG,CACrD,EAAO,EAAsB,EAAe,EAAG,CACrD,MAAO,CAGL,GAAG,EACH,GAAG,EACH,GAAG,EAEH,QAAS,GACT,UAAW,GACX,MAAO,gBAAgB,EAAG,KAC1B,WAAY,GACZ,aAAc,UACd,aAAc,eACd,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,mBAAoB,CACpD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CACzD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACpD,CACD,UAAW,CACT,GAAG,EAAM,UACT,GAAG,EAAQ,UACX,GAAG,EAAK,UACT,CACF,CAEH,IAAI,EAA8B,CAChC,KAAM,EAAwB,EAAe,CAC7C,SAAU,EAAsB,EAAgB,EAA0B,CAC3E,CACG,EAAgC,CAClC,KAAM,EAAwB,EAAiB,CAC/C,SAAU,EAAsB,EAAkB,EAA0B,CAC7E,CACG,EAA+B,CACjC,KAAM,EAAwB,EAAe,CAC7C,SAAU,EAAsB,EAAgB,EAA2B,CAC5E,CACG,EAAiC,CACnC,KAAM,EAAwB,EAAiB,CAC/C,SAAU,EAAsB,EAAkB,EAA2B,CAC9E,CACG,EAA6B,CAC/B,KAAM,GAA6B,CACnC,SAAU,EAA0B,EAA0B,CAC/D,CACG,EAA8B,CAChC,KAAM,GAA6B,CACnC,SAAU,EAA0B,EAA2B,CAChE"}