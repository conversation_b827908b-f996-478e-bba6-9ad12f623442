import{n as e}from"./index-BtNCOo_D.js";
/*!-----------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation. All rights reserved.
* Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
* Released under the MIT license
* https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
*-----------------------------------------------------------------------------*/
var t=Object.defineProperty,n=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a=(e,a,o,s)=>{if(a&&typeof a==`object`||typeof a==`function`)for(let c of r(a))!i.call(e,c)&&c!==o&&t(e,c,{get:()=>a[c],enumerable:!(s=n(a,c))||s.enumerable});return e},o=(e,t,n)=>(a(e,t,`default`),n&&a(n,t,`default`)),s={};o(s,e);var c=class{constructor(e){this._defaults=e,this._worker=null,this._client=null,this._idleCheckInterval=window.setInterval(()=>this._checkIfIdle(),30*1e3),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker())}_stopWorker(){this._worker&&=(this._worker.dispose(),null),this._client=null}dispose(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()}_checkIfIdle(){this._worker&&Date.now()-this._lastUsedTime>12e4&&this._stopWorker()}_getClient(){return this._lastUsedTime=Date.now(),this._client||=(this._worker=s.editor.createWebWorker({moduleId:`vs/language/html/htmlWorker`,createData:{languageSettings:this._defaults.options,languageId:this._defaults.languageId},label:this._defaults.languageId}),this._worker.getProxy()),this._client}getLanguageServiceWorker(...e){let t;return this._getClient().then(e=>{t=e}).then(t=>{if(this._worker)return this._worker.withSyncedResources(e)}).then(e=>t)}},l;(function(e){function t(e){return typeof e==`string`}e.is=t})(l||={});var u;(function(e){function t(e){return typeof e==`string`}e.is=t})(u||={});var ee;(function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647;function t(t){return typeof t==`number`&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}e.is=t})(ee||={});var d;(function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647;function t(t){return typeof t==`number`&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}e.is=t})(d||={});var f;(function(e){function t(e,t){return e===Number.MAX_VALUE&&(e=d.MAX_VALUE),t===Number.MAX_VALUE&&(t=d.MAX_VALUE),{line:e,character:t}}e.create=t;function n(e){let t=e;return F.objectLiteral(t)&&F.uinteger(t.line)&&F.uinteger(t.character)}e.is=n})(f||={});var p;(function(e){function t(e,t,n,r){if(F.uinteger(e)&&F.uinteger(t)&&F.uinteger(n)&&F.uinteger(r))return{start:f.create(e,t),end:f.create(n,r)};if(f.is(e)&&f.is(t))return{start:e,end:t};throw Error(`Range#create called with invalid arguments[${e}, ${t}, ${n}, ${r}]`)}e.create=t;function n(e){let t=e;return F.objectLiteral(t)&&f.is(t.start)&&f.is(t.end)}e.is=n})(p||={});var m;(function(e){function t(e,t){return{uri:e,range:t}}e.create=t;function n(e){let t=e;return F.objectLiteral(t)&&p.is(t.range)&&(F.string(t.uri)||F.undefined(t.uri))}e.is=n})(m||={});var te;(function(e){function t(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}}e.create=t;function n(e){let t=e;return F.objectLiteral(t)&&p.is(t.targetRange)&&F.string(t.targetUri)&&p.is(t.targetSelectionRange)&&(p.is(t.originSelectionRange)||F.undefined(t.originSelectionRange))}e.is=n})(te||={});var h;(function(e){function t(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}}e.create=t;function n(e){let t=e;return F.objectLiteral(t)&&F.numberRange(t.red,0,1)&&F.numberRange(t.green,0,1)&&F.numberRange(t.blue,0,1)&&F.numberRange(t.alpha,0,1)}e.is=n})(h||={});var ne;(function(e){function t(e,t){return{range:e,color:t}}e.create=t;function n(e){let t=e;return F.objectLiteral(t)&&p.is(t.range)&&h.is(t.color)}e.is=n})(ne||={});var re;(function(e){function t(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}}e.create=t;function n(e){let t=e;return F.objectLiteral(t)&&F.string(t.label)&&(F.undefined(t.textEdit)||x.is(t))&&(F.undefined(t.additionalTextEdits)||F.typedArray(t.additionalTextEdits,x.is))}e.is=n})(re||={});var g;(function(e){e.Comment=`comment`,e.Imports=`imports`,e.Region=`region`})(g||={});var ie;(function(e){function t(e,t,n,r,i,a){let o={startLine:e,endLine:t};return F.defined(n)&&(o.startCharacter=n),F.defined(r)&&(o.endCharacter=r),F.defined(i)&&(o.kind=i),F.defined(a)&&(o.collapsedText=a),o}e.create=t;function n(e){let t=e;return F.objectLiteral(t)&&F.uinteger(t.startLine)&&F.uinteger(t.startLine)&&(F.undefined(t.startCharacter)||F.uinteger(t.startCharacter))&&(F.undefined(t.endCharacter)||F.uinteger(t.endCharacter))&&(F.undefined(t.kind)||F.string(t.kind))}e.is=n})(ie||={});var _;(function(e){function t(e,t){return{location:e,message:t}}e.create=t;function n(e){let t=e;return F.defined(t)&&m.is(t.location)&&F.string(t.message)}e.is=n})(_||={});var v;(function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4})(v||={});var ae;(function(e){e.Unnecessary=1,e.Deprecated=2})(ae||={});var oe;(function(e){function t(e){let t=e;return F.objectLiteral(t)&&F.string(t.href)}e.is=t})(oe||={});var y;(function(e){function t(e,t,n,r,i,a){let o={range:e,message:t};return F.defined(n)&&(o.severity=n),F.defined(r)&&(o.code=r),F.defined(i)&&(o.source=i),F.defined(a)&&(o.relatedInformation=a),o}e.create=t;function n(e){let t=e;return F.defined(t)&&p.is(t.range)&&F.string(t.message)&&(F.number(t.severity)||F.undefined(t.severity))&&(F.integer(t.code)||F.string(t.code)||F.undefined(t.code))&&(F.undefined(t.codeDescription)||F.string(t.codeDescription?.href))&&(F.string(t.source)||F.undefined(t.source))&&(F.undefined(t.relatedInformation)||F.typedArray(t.relatedInformation,_.is))}e.is=n})(y||={});var b;(function(e){function t(e,t,...n){let r={title:e,command:t};return F.defined(n)&&n.length>0&&(r.arguments=n),r}e.create=t;function n(e){let t=e;return F.defined(t)&&F.string(t.title)&&F.string(t.command)}e.is=n})(b||={});var x;(function(e){function t(e,t){return{range:e,newText:t}}e.replace=t;function n(e,t){return{range:{start:e,end:e},newText:t}}e.insert=n;function r(e){return{range:e,newText:``}}e.del=r;function i(e){let t=e;return F.objectLiteral(t)&&F.string(t.newText)&&p.is(t.range)}e.is=i})(x||={});var se;(function(e){function t(e,t,n){let r={label:e};return t!==void 0&&(r.needsConfirmation=t),n!==void 0&&(r.description=n),r}e.create=t;function n(e){let t=e;return F.objectLiteral(t)&&F.string(t.label)&&(F.boolean(t.needsConfirmation)||t.needsConfirmation===void 0)&&(F.string(t.description)||t.description===void 0)}e.is=n})(se||={});var S;(function(e){function t(e){let t=e;return F.string(t)}e.is=t})(S||={});var ce;(function(e){function t(e,t,n){return{range:e,newText:t,annotationId:n}}e.replace=t;function n(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}}e.insert=n;function r(e,t){return{range:e,newText:``,annotationId:t}}e.del=r;function i(e){let t=e;return x.is(t)&&(se.is(t.annotationId)||S.is(t.annotationId))}e.is=i})(ce||={});var C;(function(e){function t(e,t){return{textDocument:e,edits:t}}e.create=t;function n(e){let t=e;return F.defined(t)&&fe.is(t.textDocument)&&Array.isArray(t.edits)}e.is=n})(C||={});var w;(function(e){function t(e,t,n){let r={kind:`create`,uri:e};return t!==void 0&&(t.overwrite!==void 0||t.ignoreIfExists!==void 0)&&(r.options=t),n!==void 0&&(r.annotationId=n),r}e.create=t;function n(e){let t=e;return t&&t.kind===`create`&&F.string(t.uri)&&(t.options===void 0||(t.options.overwrite===void 0||F.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===void 0||F.boolean(t.options.ignoreIfExists)))&&(t.annotationId===void 0||S.is(t.annotationId))}e.is=n})(w||={});var T;(function(e){function t(e,t,n,r){let i={kind:`rename`,oldUri:e,newUri:t};return n!==void 0&&(n.overwrite!==void 0||n.ignoreIfExists!==void 0)&&(i.options=n),r!==void 0&&(i.annotationId=r),i}e.create=t;function n(e){let t=e;return t&&t.kind===`rename`&&F.string(t.oldUri)&&F.string(t.newUri)&&(t.options===void 0||(t.options.overwrite===void 0||F.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===void 0||F.boolean(t.options.ignoreIfExists)))&&(t.annotationId===void 0||S.is(t.annotationId))}e.is=n})(T||={});var E;(function(e){function t(e,t,n){let r={kind:`delete`,uri:e};return t!==void 0&&(t.recursive!==void 0||t.ignoreIfNotExists!==void 0)&&(r.options=t),n!==void 0&&(r.annotationId=n),r}e.create=t;function n(e){let t=e;return t&&t.kind===`delete`&&F.string(t.uri)&&(t.options===void 0||(t.options.recursive===void 0||F.boolean(t.options.recursive))&&(t.options.ignoreIfNotExists===void 0||F.boolean(t.options.ignoreIfNotExists)))&&(t.annotationId===void 0||S.is(t.annotationId))}e.is=n})(E||={});var le;(function(e){function t(e){let t=e;return t&&(t.changes!==void 0||t.documentChanges!==void 0)&&(t.documentChanges===void 0||t.documentChanges.every(e=>F.string(e.kind)?w.is(e)||T.is(e)||E.is(e):C.is(e)))}e.is=t})(le||={});var ue;(function(e){function t(e){return{uri:e}}e.create=t;function n(e){let t=e;return F.defined(t)&&F.string(t.uri)}e.is=n})(ue||={});var de;(function(e){function t(e,t){return{uri:e,version:t}}e.create=t;function n(e){let t=e;return F.defined(t)&&F.string(t.uri)&&F.integer(t.version)}e.is=n})(de||={});var fe;(function(e){function t(e,t){return{uri:e,version:t}}e.create=t;function n(e){let t=e;return F.defined(t)&&F.string(t.uri)&&(t.version===null||F.integer(t.version))}e.is=n})(fe||={});var pe;(function(e){function t(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}}e.create=t;function n(e){let t=e;return F.defined(t)&&F.string(t.uri)&&F.string(t.languageId)&&F.integer(t.version)&&F.string(t.text)}e.is=n})(pe||={});var D;(function(e){e.PlainText=`plaintext`,e.Markdown=`markdown`;function t(t){let n=t;return n===e.PlainText||n===e.Markdown}e.is=t})(D||={});var O;(function(e){function t(e){let t=e;return F.objectLiteral(e)&&D.is(t.kind)&&F.string(t.value)}e.is=t})(O||={});var k;(function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25})(k||={});var A;(function(e){e.PlainText=1,e.Snippet=2})(A||={});var me;(function(e){e.Deprecated=1})(me||={});var he;(function(e){function t(e,t,n){return{newText:e,insert:t,replace:n}}e.create=t;function n(e){let t=e;return t&&F.string(t.newText)&&p.is(t.insert)&&p.is(t.replace)}e.is=n})(he||={});var ge;(function(e){e.asIs=1,e.adjustIndentation=2})(ge||={});var _e;(function(e){function t(e){let t=e;return t&&(F.string(t.detail)||t.detail===void 0)&&(F.string(t.description)||t.description===void 0)}e.is=t})(_e||={});var ve;(function(e){function t(e){return{label:e}}e.create=t})(ve||={});var ye;(function(e){function t(e,t){return{items:e||[],isIncomplete:!!t}}e.create=t})(ye||={});var j;(function(e){function t(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,`\\$&`)}e.fromPlainText=t;function n(e){let t=e;return F.string(t)||F.objectLiteral(t)&&F.string(t.language)&&F.string(t.value)}e.is=n})(j||={});var be;(function(e){function t(e){let t=e;return!!t&&F.objectLiteral(t)&&(O.is(t.contents)||j.is(t.contents)||F.typedArray(t.contents,j.is))&&(e.range===void 0||p.is(e.range))}e.is=t})(be||={});var xe;(function(e){function t(e,t){return t?{label:e,documentation:t}:{label:e}}e.create=t})(xe||={});var Se;(function(e){function t(e,t,...n){let r={label:e};return F.defined(t)&&(r.documentation=t),F.defined(n)?r.parameters=n:r.parameters=[],r}e.create=t})(Se||={});var M;(function(e){e.Text=1,e.Read=2,e.Write=3})(M||={});var Ce;(function(e){function t(e,t){let n={range:e};return F.number(t)&&(n.kind=t),n}e.create=t})(Ce||={});var N;(function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26})(N||={});var we;(function(e){e.Deprecated=1})(we||={});var Te;(function(e){function t(e,t,n,r,i){let a={name:e,kind:t,location:{uri:r,range:n}};return i&&(a.containerName=i),a}e.create=t})(Te||={});var Ee;(function(e){function t(e,t,n,r){return r===void 0?{name:e,kind:t,location:{uri:n}}:{name:e,kind:t,location:{uri:n,range:r}}}e.create=t})(Ee||={});var De;(function(e){function t(e,t,n,r,i,a){let o={name:e,detail:t,kind:n,range:r,selectionRange:i};return a!==void 0&&(o.children=a),o}e.create=t;function n(e){let t=e;return t&&F.string(t.name)&&F.number(t.kind)&&p.is(t.range)&&p.is(t.selectionRange)&&(t.detail===void 0||F.string(t.detail))&&(t.deprecated===void 0||F.boolean(t.deprecated))&&(t.children===void 0||Array.isArray(t.children))&&(t.tags===void 0||Array.isArray(t.tags))}e.is=n})(De||={});var Oe;(function(e){e.Empty=``,e.QuickFix=`quickfix`,e.Refactor=`refactor`,e.RefactorExtract=`refactor.extract`,e.RefactorInline=`refactor.inline`,e.RefactorRewrite=`refactor.rewrite`,e.Source=`source`,e.SourceOrganizeImports=`source.organizeImports`,e.SourceFixAll=`source.fixAll`})(Oe||={});var P;(function(e){e.Invoked=1,e.Automatic=2})(P||={});var ke;(function(e){function t(e,t,n){let r={diagnostics:e};return t!=null&&(r.only=t),n!=null&&(r.triggerKind=n),r}e.create=t;function n(e){let t=e;return F.defined(t)&&F.typedArray(t.diagnostics,y.is)&&(t.only===void 0||F.typedArray(t.only,F.string))&&(t.triggerKind===void 0||t.triggerKind===P.Invoked||t.triggerKind===P.Automatic)}e.is=n})(ke||={});var Ae;(function(e){function t(e,t,n){let r={title:e},i=!0;return typeof t==`string`?(i=!1,r.kind=t):b.is(t)?r.command=t:r.edit=t,i&&n!==void 0&&(r.kind=n),r}e.create=t;function n(e){let t=e;return t&&F.string(t.title)&&(t.diagnostics===void 0||F.typedArray(t.diagnostics,y.is))&&(t.kind===void 0||F.string(t.kind))&&(t.edit!==void 0||t.command!==void 0)&&(t.command===void 0||b.is(t.command))&&(t.isPreferred===void 0||F.boolean(t.isPreferred))&&(t.edit===void 0||le.is(t.edit))}e.is=n})(Ae||={});var je;(function(e){function t(e,t){let n={range:e};return F.defined(t)&&(n.data=t),n}e.create=t;function n(e){let t=e;return F.defined(t)&&p.is(t.range)&&(F.undefined(t.command)||b.is(t.command))}e.is=n})(je||={});var Me;(function(e){function t(e,t){return{tabSize:e,insertSpaces:t}}e.create=t;function n(e){let t=e;return F.defined(t)&&F.uinteger(t.tabSize)&&F.boolean(t.insertSpaces)}e.is=n})(Me||={});var Ne;(function(e){function t(e,t,n){return{range:e,target:t,data:n}}e.create=t;function n(e){let t=e;return F.defined(t)&&p.is(t.range)&&(F.undefined(t.target)||F.string(t.target))}e.is=n})(Ne||={});var Pe;(function(e){function t(e,t){return{range:e,parent:t}}e.create=t;function n(t){let n=t;return F.objectLiteral(n)&&p.is(n.range)&&(n.parent===void 0||e.is(n.parent))}e.is=n})(Pe||={});var Fe;(function(e){e.namespace=`namespace`,e.type=`type`,e.class=`class`,e.enum=`enum`,e.interface=`interface`,e.struct=`struct`,e.typeParameter=`typeParameter`,e.parameter=`parameter`,e.variable=`variable`,e.property=`property`,e.enumMember=`enumMember`,e.event=`event`,e.function=`function`,e.method=`method`,e.macro=`macro`,e.keyword=`keyword`,e.modifier=`modifier`,e.comment=`comment`,e.string=`string`,e.number=`number`,e.regexp=`regexp`,e.operator=`operator`,e.decorator=`decorator`})(Fe||={});var Ie;(function(e){e.declaration=`declaration`,e.definition=`definition`,e.readonly=`readonly`,e.static=`static`,e.deprecated=`deprecated`,e.abstract=`abstract`,e.async=`async`,e.modification=`modification`,e.documentation=`documentation`,e.defaultLibrary=`defaultLibrary`})(Ie||={});var Le;(function(e){function t(e){let t=e;return F.objectLiteral(t)&&(t.resultId===void 0||typeof t.resultId==`string`)&&Array.isArray(t.data)&&(t.data.length===0||typeof t.data[0]==`number`)}e.is=t})(Le||={});var Re;(function(e){function t(e,t){return{range:e,text:t}}e.create=t;function n(e){let t=e;return t!=null&&p.is(t.range)&&F.string(t.text)}e.is=n})(Re||={});var ze;(function(e){function t(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}}e.create=t;function n(e){let t=e;return t!=null&&p.is(t.range)&&F.boolean(t.caseSensitiveLookup)&&(F.string(t.variableName)||t.variableName===void 0)}e.is=n})(ze||={});var Be;(function(e){function t(e,t){return{range:e,expression:t}}e.create=t;function n(e){let t=e;return t!=null&&p.is(t.range)&&(F.string(t.expression)||t.expression===void 0)}e.is=n})(Be||={});var Ve;(function(e){function t(e,t){return{frameId:e,stoppedLocation:t}}e.create=t;function n(e){let t=e;return F.defined(t)&&p.is(e.stoppedLocation)}e.is=n})(Ve||={});var He;(function(e){e.Type=1,e.Parameter=2;function t(e){return e===1||e===2}e.is=t})(He||={});var Ue;(function(e){function t(e){return{value:e}}e.create=t;function n(e){let t=e;return F.objectLiteral(t)&&(t.tooltip===void 0||F.string(t.tooltip)||O.is(t.tooltip))&&(t.location===void 0||m.is(t.location))&&(t.command===void 0||b.is(t.command))}e.is=n})(Ue||={});var We;(function(e){function t(e,t,n){let r={position:e,label:t};return n!==void 0&&(r.kind=n),r}e.create=t;function n(e){let t=e;return F.objectLiteral(t)&&f.is(t.position)&&(F.string(t.label)||F.typedArray(t.label,Ue.is))&&(t.kind===void 0||He.is(t.kind))&&t.textEdits===void 0||F.typedArray(t.textEdits,x.is)&&(t.tooltip===void 0||F.string(t.tooltip)||O.is(t.tooltip))&&(t.paddingLeft===void 0||F.boolean(t.paddingLeft))&&(t.paddingRight===void 0||F.boolean(t.paddingRight))}e.is=n})(We||={});var Ge;(function(e){function t(e){return{kind:`snippet`,value:e}}e.createSnippet=t})(Ge||={});var Ke;(function(e){function t(e,t,n,r){return{insertText:e,filterText:t,range:n,command:r}}e.create=t})(Ke||={});var qe;(function(e){function t(e){return{items:e}}e.create=t})(qe||={});var Je;(function(e){e.Invoked=0,e.Automatic=1})(Je||={});var Ye;(function(e){function t(e,t){return{range:e,text:t}}e.create=t})(Ye||={});var Xe;(function(e){function t(e,t){return{triggerKind:e,selectedCompletionInfo:t}}e.create=t})(Xe||={});var Ze;(function(e){function t(e){let t=e;return F.objectLiteral(t)&&u.is(t.uri)&&F.string(t.name)}e.is=t})(Ze||={});var Qe;(function(e){function t(e,t,n,r){return new $e(e,t,n,r)}e.create=t;function n(e){let t=e;return!!(F.defined(t)&&F.string(t.uri)&&(F.undefined(t.languageId)||F.string(t.languageId))&&F.uinteger(t.lineCount)&&F.func(t.getText)&&F.func(t.positionAt)&&F.func(t.offsetAt))}e.is=n;function r(e,t){let n=e.getText(),r=i(t,(e,t)=>{let n=e.range.start.line-t.range.start.line;return n===0?e.range.start.character-t.range.start.character:n}),a=n.length;for(let t=r.length-1;t>=0;t--){let i=r[t],o=e.offsetAt(i.range.start),s=e.offsetAt(i.range.end);if(s<=a)n=n.substring(0,o)+i.newText+n.substring(s,n.length);else throw Error(`Overlapping edit`);a=o}return n}e.applyEdits=r;function i(e,t){if(e.length<=1)return e;let n=e.length/2|0,r=e.slice(0,n),a=e.slice(n);i(r,t),i(a,t);let o=0,s=0,c=0;for(;o<r.length&&s<a.length;)t(r[o],a[s])<=0?e[c++]=r[o++]:e[c++]=a[s++];for(;o<r.length;)e[c++]=r[o++];for(;s<a.length;)e[c++]=a[s++];return e}})(Qe||={});var $e=class{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],t=this._content,n=!0;for(let r=0;r<t.length;r++){n&&=(e.push(r),!1);let i=t.charAt(r);n=i===`\r`||i===`
`,i===`\r`&&r+1<t.length&&t.charAt(r+1)===`
`&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(r===0)return f.create(0,e);for(;n<r;){let i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}let i=n-1;return f.create(i,e-t[i])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}},F;(function(e){let t=Object.prototype.toString;function n(e){return e!==void 0}e.defined=n;function r(e){return e===void 0}e.undefined=r;function i(e){return e===!0||e===!1}e.boolean=i;function a(e){return t.call(e)===`[object String]`}e.string=a;function o(e){return t.call(e)===`[object Number]`}e.number=o;function s(e,n,r){return t.call(e)===`[object Number]`&&n<=e&&e<=r}e.numberRange=s;function c(e){return t.call(e)===`[object Number]`&&-2147483648<=e&&e<=2147483647}e.integer=c;function l(e){return t.call(e)===`[object Number]`&&0<=e&&e<=2147483647}e.uinteger=l;function u(e){return t.call(e)===`[object Function]`}e.func=u;function ee(e){return typeof e==`object`&&!!e}e.objectLiteral=ee;function d(e,t){return Array.isArray(e)&&e.every(t)}e.typedArray=d})(F||={});var et=class{constructor(e,t,n){this._languageId=e,this._worker=t,this._disposables=[],this._listener=Object.create(null);let r=e=>{let t=e.getLanguageId();if(t!==this._languageId)return;let n;this._listener[e.uri.toString()]=e.onDidChangeContent(()=>{window.clearTimeout(n),n=window.setTimeout(()=>this._doValidate(e.uri,t),500)}),this._doValidate(e.uri,t)},i=e=>{s.editor.setModelMarkers(e,this._languageId,[]);let t=e.uri.toString(),n=this._listener[t];n&&(n.dispose(),delete this._listener[t])};this._disposables.push(s.editor.onDidCreateModel(r)),this._disposables.push(s.editor.onWillDisposeModel(i)),this._disposables.push(s.editor.onDidChangeModelLanguage(e=>{i(e.model),r(e.model)})),this._disposables.push(n(e=>{s.editor.getModels().forEach(e=>{e.getLanguageId()===this._languageId&&(i(e),r(e))})})),this._disposables.push({dispose:()=>{for(let e in s.editor.getModels().forEach(i),this._listener)this._listener[e].dispose()}}),s.editor.getModels().forEach(r)}dispose(){this._disposables.forEach(e=>e&&e.dispose()),this._disposables.length=0}_doValidate(e,t){this._worker(e).then(t=>t.doValidation(e.toString())).then(n=>{let r=n.map(t=>nt(e,t)),i=s.editor.getModel(e);i&&i.getLanguageId()===t&&s.editor.setModelMarkers(i,t,r)}).then(void 0,e=>{console.error(e)})}};function tt(e){switch(e){case v.Error:return s.MarkerSeverity.Error;case v.Warning:return s.MarkerSeverity.Warning;case v.Information:return s.MarkerSeverity.Info;case v.Hint:return s.MarkerSeverity.Hint;default:return s.MarkerSeverity.Info}}function nt(e,t){let n=typeof t.code==`number`?String(t.code):t.code;return{severity:tt(t.severity),startLineNumber:t.range.start.line+1,startColumn:t.range.start.character+1,endLineNumber:t.range.end.line+1,endColumn:t.range.end.character+1,message:t.message,code:n,source:t.source}}var rt=class{constructor(e,t){this._worker=e,this._triggerCharacters=t}get triggerCharacters(){return this._triggerCharacters}provideCompletionItems(e,t,n,r){let i=e.uri;return this._worker(i).then(e=>e.doComplete(i.toString(),I(t))).then(n=>{if(!n)return;let r=e.getWordUntilPosition(t),i=new s.Range(t.lineNumber,r.startColumn,t.lineNumber,r.endColumn),a=n.items.map(e=>{let t={label:e.label,insertText:e.insertText||e.label,sortText:e.sortText,filterText:e.filterText,documentation:e.documentation,detail:e.detail,command:ot(e.command),range:i,kind:at(e.kind)};return e.textEdit&&(it(e.textEdit)?t.range={insert:R(e.textEdit.insert),replace:R(e.textEdit.replace)}:t.range=R(e.textEdit.range),t.insertText=e.textEdit.newText),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(z)),e.insertTextFormat===A.Snippet&&(t.insertTextRules=s.languages.CompletionItemInsertTextRule.InsertAsSnippet),t});return{isIncomplete:n.isIncomplete,suggestions:a}})}};function I(e){if(e)return{character:e.column-1,line:e.lineNumber-1}}function L(e){if(e)return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function R(e){if(e)return new s.Range(e.start.line+1,e.start.character+1,e.end.line+1,e.end.character+1)}function it(e){return e.insert!==void 0&&e.replace!==void 0}function at(e){let t=s.languages.CompletionItemKind;switch(e){case k.Text:return t.Text;case k.Method:return t.Method;case k.Function:return t.Function;case k.Constructor:return t.Constructor;case k.Field:return t.Field;case k.Variable:return t.Variable;case k.Class:return t.Class;case k.Interface:return t.Interface;case k.Module:return t.Module;case k.Property:return t.Property;case k.Unit:return t.Unit;case k.Value:return t.Value;case k.Enum:return t.Enum;case k.Keyword:return t.Keyword;case k.Snippet:return t.Snippet;case k.Color:return t.Color;case k.File:return t.File;case k.Reference:return t.Reference}return t.Property}function z(e){if(e)return{range:R(e.range),text:e.newText}}function ot(e){return e&&e.command===`editor.action.triggerSuggest`?{id:e.command,title:e.title,arguments:e.arguments}:void 0}var B=class{constructor(e){this._worker=e}provideHover(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.doHover(r.toString(),I(t))).then(e=>{if(e)return{range:R(e.range),contents:ct(e.contents)}})}};function st(e){return e&&typeof e==`object`&&typeof e.kind==`string`}function V(e){return typeof e==`string`?{value:e}:st(e)?e.kind===`plaintext`?{value:e.value.replace(/[\\`*_{}[\]()#+\-.!]/g,`\\$&`)}:{value:e.value}:{value:"```"+e.language+`
`+e.value+"\n```\n"}}function ct(e){if(e)return Array.isArray(e)?e.map(V):[V(e)]}var H=class{constructor(e){this._worker=e}provideDocumentHighlights(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.findDocumentHighlights(r.toString(),I(t))).then(e=>{if(e)return e.map(e=>({range:R(e.range),kind:lt(e.kind)}))})}};function lt(e){switch(e){case M.Read:return s.languages.DocumentHighlightKind.Read;case M.Write:return s.languages.DocumentHighlightKind.Write;case M.Text:return s.languages.DocumentHighlightKind.Text}return s.languages.DocumentHighlightKind.Text}var ut=class{constructor(e){this._worker=e}provideDefinition(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.findDefinition(r.toString(),I(t))).then(e=>{if(e)return[U(e)]})}};function U(e){return{uri:s.Uri.parse(e.uri),range:R(e.range)}}var dt=class{constructor(e){this._worker=e}provideReferences(e,t,n,r){let i=e.uri;return this._worker(i).then(e=>e.findReferences(i.toString(),I(t))).then(e=>{if(e)return e.map(U)})}},W=class{constructor(e){this._worker=e}provideRenameEdits(e,t,n,r){let i=e.uri;return this._worker(i).then(e=>e.doRename(i.toString(),I(t),n)).then(e=>ft(e))}};function ft(e){if(!e||!e.changes)return;let t=[];for(let n in e.changes){let r=s.Uri.parse(n);for(let i of e.changes[n])t.push({resource:r,versionId:void 0,textEdit:{range:R(i.range),text:i.newText}})}return{edits:t}}var G=class{constructor(e){this._worker=e}provideDocumentSymbols(e,t){let n=e.uri;return this._worker(n).then(e=>e.findDocumentSymbols(n.toString())).then(e=>{if(e)return e.map(e=>pt(e)?K(e):{name:e.name,detail:``,containerName:e.containerName,kind:q(e.kind),range:R(e.location.range),selectionRange:R(e.location.range),tags:[]})})}};function pt(e){return`children`in e}function K(e){return{name:e.name,detail:e.detail??``,kind:q(e.kind),range:R(e.range),selectionRange:R(e.selectionRange),tags:e.tags??[],children:(e.children??[]).map(e=>K(e))}}function q(e){let t=s.languages.SymbolKind;switch(e){case N.File:return t.File;case N.Module:return t.Module;case N.Namespace:return t.Namespace;case N.Package:return t.Package;case N.Class:return t.Class;case N.Method:return t.Method;case N.Property:return t.Property;case N.Field:return t.Field;case N.Constructor:return t.Constructor;case N.Enum:return t.Enum;case N.Interface:return t.Interface;case N.Function:return t.Function;case N.Variable:return t.Variable;case N.Constant:return t.Constant;case N.String:return t.String;case N.Number:return t.Number;case N.Boolean:return t.Boolean;case N.Array:return t.Array}return t.Function}var J=class{constructor(e){this._worker=e}provideLinks(e,t){let n=e.uri;return this._worker(n).then(e=>e.findDocumentLinks(n.toString())).then(e=>{if(e)return{links:e.map(e=>({range:R(e.range),url:e.target}))}})}},Y=class{constructor(e){this._worker=e}provideDocumentFormattingEdits(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.format(r.toString(),null,Z(t)).then(e=>{if(!(!e||e.length===0))return e.map(z)}))}},X=class{constructor(e){this._worker=e,this.canFormatMultipleRanges=!1}provideDocumentRangeFormattingEdits(e,t,n,r){let i=e.uri;return this._worker(i).then(e=>e.format(i.toString(),L(t),Z(n)).then(e=>{if(!(!e||e.length===0))return e.map(z)}))}};function Z(e){return{tabSize:e.tabSize,insertSpaces:e.insertSpaces}}var mt=class{constructor(e){this._worker=e}provideDocumentColors(e,t){let n=e.uri;return this._worker(n).then(e=>e.findDocumentColors(n.toString())).then(e=>{if(e)return e.map(e=>({color:e.color,range:R(e.range)}))})}provideColorPresentations(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.getColorPresentations(r.toString(),t.color,L(t.range))).then(e=>{if(e)return e.map(e=>{let t={label:e.label};return e.textEdit&&(t.textEdit=z(e.textEdit)),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(z)),t})})}},Q=class{constructor(e){this._worker=e}provideFoldingRanges(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.getFoldingRanges(r.toString(),t)).then(e=>{if(e)return e.map(e=>{let t={start:e.startLine+1,end:e.endLine+1};return e.kind!==void 0&&(t.kind=ht(e.kind)),t})})}};function ht(e){switch(e){case g.Comment:return s.languages.FoldingRangeKind.Comment;case g.Imports:return s.languages.FoldingRangeKind.Imports;case g.Region:return s.languages.FoldingRangeKind.Region}}var $=class{constructor(e){this._worker=e}provideSelectionRanges(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.getSelectionRanges(r.toString(),t.map(I))).then(e=>{if(e)return e.map(e=>{let t=[];for(;e;)t.push({range:R(e.range)}),e=e.parent;return t})})}},gt=class extends rt{constructor(e){super(e,[`.`,`:`,`<`,`"`,`=`,`/`])}};function _t(e){let t=new c(e),n=(...e)=>t.getLanguageServiceWorker(...e),r=e.languageId;s.languages.registerCompletionItemProvider(r,new gt(n)),s.languages.registerHoverProvider(r,new B(n)),s.languages.registerDocumentHighlightProvider(r,new H(n)),s.languages.registerLinkProvider(r,new J(n)),s.languages.registerFoldingRangeProvider(r,new Q(n)),s.languages.registerDocumentSymbolProvider(r,new G(n)),s.languages.registerSelectionRangeProvider(r,new $(n)),s.languages.registerRenameProvider(r,new W(n)),r===`html`&&(s.languages.registerDocumentFormattingEditProvider(r,new Y(n)),s.languages.registerDocumentRangeFormattingEditProvider(r,new X(n)))}function vt(e){let t=[],n=[],r=new c(e);t.push(r);let i=(...e)=>r.getLanguageServiceWorker(...e);function a(){let{languageId:t,modeConfiguration:r}=e;bt(n),r.completionItems&&n.push(s.languages.registerCompletionItemProvider(t,new gt(i))),r.hovers&&n.push(s.languages.registerHoverProvider(t,new B(i))),r.documentHighlights&&n.push(s.languages.registerDocumentHighlightProvider(t,new H(i))),r.links&&n.push(s.languages.registerLinkProvider(t,new J(i))),r.documentSymbols&&n.push(s.languages.registerDocumentSymbolProvider(t,new G(i))),r.rename&&n.push(s.languages.registerRenameProvider(t,new W(i))),r.foldingRanges&&n.push(s.languages.registerFoldingRangeProvider(t,new Q(i))),r.selectionRanges&&n.push(s.languages.registerSelectionRangeProvider(t,new $(i))),r.documentFormattingEdits&&n.push(s.languages.registerDocumentFormattingEditProvider(t,new Y(i))),r.documentRangeFormattingEdits&&n.push(s.languages.registerDocumentRangeFormattingEditProvider(t,new X(i)))}return a(),t.push(yt(n)),yt(t)}function yt(e){return{dispose:()=>bt(e)}}function bt(e){for(;e.length;)e.pop().dispose()}export{rt as CompletionAdapter,ut as DefinitionAdapter,et as DiagnosticsAdapter,mt as DocumentColorAdapter,Y as DocumentFormattingEditProvider,H as DocumentHighlightAdapter,J as DocumentLinkAdapter,X as DocumentRangeFormattingEditProvider,G as DocumentSymbolAdapter,Q as FoldingRangeAdapter,B as HoverAdapter,dt as ReferenceAdapter,W as RenameAdapter,$ as SelectionRangeAdapter,c as WorkerManager,I as fromPosition,L as fromRange,vt as setupMode,_t as setupMode1,R as toRange,z as toTextEdit};
//# sourceMappingURL=htmlMode-BF7ELLc1.js.map