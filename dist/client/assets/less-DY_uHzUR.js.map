{"version": 3, "file": "less-DY_uHzUR.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/less/less.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/less/less.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|([@#!.:]?[\\w-?]+%?)|[@#!.]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"],\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".less\",\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  identifierPlus: \"-?-?([a-zA-Z:.]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-:.]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@nestedJSBegin\" },\n      [\"[ \\\\t\\\\r\\\\n]+\", \"\"],\n      { include: \"@comments\" },\n      { include: \"@keyword\" },\n      { include: \"@strings\" },\n      { include: \"@numbers\" },\n      [\"[*_]?[a-zA-Z\\\\-\\\\s]+(?=:.*(;|(\\\\\\\\$)))\", \"attribute.name\", \"@attribute\"],\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"tag\", next: \"@urldeclaration\" }],\n      [\"[{}()\\\\[\\\\]]\", \"@brackets\"],\n      [\"[,:;]\", \"delimiter\"],\n      [\"#@identifierPlus\", \"tag.id\"],\n      [\"&\", \"tag\"],\n      [\"\\\\.@identifierPlus(?=\\\\()\", \"tag.class\", \"@attribute\"],\n      [\"\\\\.@identifierPlus\", \"tag.class\"],\n      [\"@identifierPlus\", \"tag\"],\n      { include: \"@operators\" },\n      [\"@(@identifier(?=[:,\\\\)]))\", \"variable\", \"@attribute\"],\n      [\"@(@identifier)\", \"variable\"],\n      [\"@\", \"key\", \"@atRules\"]\n    ],\n    nestedJSBegin: [\n      [\"``\", \"delimiter.backtick\"],\n      [\n        \"`\",\n        {\n          token: \"delimiter.backtick\",\n          next: \"@nestedJSEnd\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ]\n    ],\n    nestedJSEnd: [\n      [\n        \"`\",\n        {\n          token: \"delimiter.backtick\",\n          next: \"@pop\",\n          nextEmbedded: \"@pop\"\n        }\n      ]\n    ],\n    operators: [[\"[<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~]\", \"operator\"]],\n    keyword: [\n      [\n        \"(@[\\\\s]*import|![\\\\s]*important|true|false|when|iscolor|isnumber|isstring|iskeyword|isurl|ispixel|ispercentage|isem|hue|saturation|lightness|alpha|lighten|darken|saturate|desaturate|fadein|fadeout|fade|spin|mix|round|ceil|floor|percentage)\\\\b\",\n        \"keyword\"\n      ]\n    ],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"tag\", next: \"@pop\" }]\n    ],\n    attribute: [\n      { include: \"@nestedJSBegin\" },\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      { include: \"@numbers\" },\n      { include: \"@keyword\" },\n      [\"[a-zA-Z\\\\-]+(?=\\\\()\", \"attribute.value\", \"@attribute\"],\n      [\">\", \"operator\", \"@pop\"],\n      [\"@identifier\", \"attribute.value\"],\n      { include: \"@operators\" },\n      [\"@(@identifier)\", \"variable\"],\n      [\"[)\\\\}]\", \"@brackets\", \"@pop\"],\n      [\"[{}()\\\\[\\\\]>]\", \"@brackets\"],\n      [\"[;]\", \"delimiter\", \"@pop\"],\n      [\"[,=:]\", \"delimiter\"],\n      [\"\\\\s\", \"\"],\n      [\".\", \"attribute.value\"]\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    numbers: [\n      [\"(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"attribute.value.number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"attribute.value.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"attribute.value.unit\",\n        \"@pop\"\n      ]\n    ],\n    strings: [\n      ['~?\"', { token: \"string.delimiter\", next: \"@stringsEndDoubleQuote\" }],\n      [\"~?'\", { token: \"string.delimiter\", next: \"@stringsEndQuote\" }]\n    ],\n    stringsEndDoubleQuote: [\n      ['\\\\\\\\\"', \"string\"],\n      ['\"', { token: \"string.delimiter\", next: \"@popall\" }],\n      [\".\", \"string\"]\n    ],\n    stringsEndQuote: [\n      [\"\\\\\\\\'\", \"string\"],\n      [\"'\", { token: \"string.delimiter\", next: \"@popall\" }],\n      [\".\", \"string\"]\n    ],\n    atRules: [\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      [\"[()]\", \"delimiter\"],\n      [\"[\\\\{;]\", \"delimiter\", \"@pop\"],\n      [\".\", \"key\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,YAAa,iDACb,SAAU,CACR,aAAc,CAAC,KAAM,KAAK,CAC1B,YAAa,KACd,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACxD,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,+CAA+C,CACjE,IAAS,OAAO,uCAAuC,CACxD,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,QACd,WAAY,sHACZ,eAAgB,0HAChB,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,oBAAqB,CACrD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CACzD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACpD,CACD,UAAW,CACT,KAAM,CACJ,CAAE,QAAS,iBAAkB,CAC7B,CAAC,gBAAiB,GAAG,CACrB,CAAE,QAAS,YAAa,CACxB,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,WAAY,CACvB,CAAC,yCAA0C,iBAAkB,aAAa,CAC1E,CAAC,qBAAsB,CAAE,MAAO,MAAO,KAAM,kBAAmB,CAAC,CACjE,CAAC,eAAgB,YAAY,CAC7B,CAAC,QAAS,YAAY,CACtB,CAAC,mBAAoB,SAAS,CAC9B,CAAC,IAAK,MAAM,CACZ,CAAC,4BAA6B,YAAa,aAAa,CACxD,CAAC,qBAAsB,YAAY,CACnC,CAAC,kBAAmB,MAAM,CAC1B,CAAE,QAAS,aAAc,CACzB,CAAC,4BAA6B,WAAY,aAAa,CACvD,CAAC,iBAAkB,WAAW,CAC9B,CAAC,IAAK,MAAO,WAAW,CACzB,CACD,cAAe,CACb,CAAC,KAAM,qBAAqB,CAC5B,CACE,IACA,CACE,MAAO,qBACP,KAAM,eACN,aAAc,kBACf,CACF,CACF,CACD,YAAa,CACX,CACE,IACA,CACE,MAAO,qBACP,KAAM,OACN,aAAc,OACf,CACF,CACF,CACD,UAAW,CAAC,CAAC,6BAA8B,WAAW,CAAC,CACvD,QAAS,CACP,CACE,qPACA,UACD,CACF,CACD,eAAgB,CACd,CAAE,QAAS,WAAY,CACvB,CAAC;IAAa,SAAS,CACvB,CAAC,MAAO,CAAE,MAAO,MAAO,KAAM,OAAQ,CAAC,CACxC,CACD,UAAW,CACT,CAAE,QAAS,iBAAkB,CAC7B,CAAE,QAAS,YAAa,CACxB,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,WAAY,CACvB,CAAC,sBAAuB,kBAAmB,aAAa,CACxD,CAAC,IAAK,WAAY,OAAO,CACzB,CAAC,cAAe,kBAAkB,CAClC,CAAE,QAAS,aAAc,CACzB,CAAC,iBAAkB,WAAW,CAC9B,CAAC,SAAU,YAAa,OAAO,CAC/B,CAAC,gBAAiB,YAAY,CAC9B,CAAC,MAAO,YAAa,OAAO,CAC5B,CAAC,QAAS,YAAY,CACtB,CAAC,MAAO,GAAG,CACX,CAAC,IAAK,kBAAkB,CACzB,CACD,SAAU,CACR,CAAC,SAAU,UAAW,WAAW,CACjC,CAAC,YAAa,UAAU,CACzB,CACD,QAAS,CACP,CAAC,SAAU,UAAW,OAAO,CAC7B,CAAC,IAAK,UAAU,CACjB,CACD,QAAS,CACP,CAAC,mCAAoC,CAAE,MAAO,yBAA0B,KAAM,SAAU,CAAC,CACzF,CAAC,wBAAyB,sBAAsB,CACjD,CACD,MAAO,CACL,CACE,0FACA,uBACA,OACD,CACF,CACD,QAAS,CACP,CAAC,MAAO,CAAE,MAAO,mBAAoB,KAAM,yBAA0B,CAAC,CACtE,CAAC,MAAO,CAAE,MAAO,mBAAoB,KAAM,mBAAoB,CAAC,CACjE,CACD,sBAAuB,CACrB,CAAC,QAAS,SAAS,CACnB,CAAC,IAAK,CAAE,MAAO,mBAAoB,KAAM,UAAW,CAAC,CACrD,CAAC,IAAK,SAAS,CAChB,CACD,gBAAiB,CACf,CAAC,QAAS,SAAS,CACnB,CAAC,IAAK,CAAE,MAAO,mBAAoB,KAAM,UAAW,CAAC,CACrD,CAAC,IAAK,SAAS,CAChB,CACD,QAAS,CACP,CAAE,QAAS,YAAa,CACxB,CAAE,QAAS,WAAY,CACvB,CAAC,OAAQ,YAAY,CACrB,CAAC,SAAU,YAAa,OAAO,CAC/B,CAAC,IAAK,MAAM,CACb,CACF,CACF"}