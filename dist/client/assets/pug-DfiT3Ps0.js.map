{"version": 3, "file": "pug-DfiT3Ps0.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/pug/pug.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/pug/pug.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] }\n  ],\n  folding: {\n    offSide: true\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".pug\",\n  ignoreCase: true,\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.array\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" }\n  ],\n  keywords: [\n    \"append\",\n    \"block\",\n    \"case\",\n    \"default\",\n    \"doctype\",\n    \"each\",\n    \"else\",\n    \"extends\",\n    \"for\",\n    \"if\",\n    \"in\",\n    \"include\",\n    \"mixin\",\n    \"typeof\",\n    \"unless\",\n    \"var\",\n    \"when\"\n  ],\n  tags: [\n    \"a\",\n    \"abbr\",\n    \"acronym\",\n    \"address\",\n    \"area\",\n    \"article\",\n    \"aside\",\n    \"audio\",\n    \"b\",\n    \"base\",\n    \"basefont\",\n    \"bdi\",\n    \"bdo\",\n    \"blockquote\",\n    \"body\",\n    \"br\",\n    \"button\",\n    \"canvas\",\n    \"caption\",\n    \"center\",\n    \"cite\",\n    \"code\",\n    \"col\",\n    \"colgroup\",\n    \"command\",\n    \"datalist\",\n    \"dd\",\n    \"del\",\n    \"details\",\n    \"dfn\",\n    \"div\",\n    \"dl\",\n    \"dt\",\n    \"em\",\n    \"embed\",\n    \"fieldset\",\n    \"figcaption\",\n    \"figure\",\n    \"font\",\n    \"footer\",\n    \"form\",\n    \"frame\",\n    \"frameset\",\n    \"h1\",\n    \"h2\",\n    \"h3\",\n    \"h4\",\n    \"h5\",\n    \"h6\",\n    \"head\",\n    \"header\",\n    \"hgroup\",\n    \"hr\",\n    \"html\",\n    \"i\",\n    \"iframe\",\n    \"img\",\n    \"input\",\n    \"ins\",\n    \"keygen\",\n    \"kbd\",\n    \"label\",\n    \"li\",\n    \"link\",\n    \"map\",\n    \"mark\",\n    \"menu\",\n    \"meta\",\n    \"meter\",\n    \"nav\",\n    \"noframes\",\n    \"noscript\",\n    \"object\",\n    \"ol\",\n    \"optgroup\",\n    \"option\",\n    \"output\",\n    \"p\",\n    \"param\",\n    \"pre\",\n    \"progress\",\n    \"q\",\n    \"rp\",\n    \"rt\",\n    \"ruby\",\n    \"s\",\n    \"samp\",\n    \"script\",\n    \"section\",\n    \"select\",\n    \"small\",\n    \"source\",\n    \"span\",\n    \"strike\",\n    \"strong\",\n    \"style\",\n    \"sub\",\n    \"summary\",\n    \"sup\",\n    \"table\",\n    \"tbody\",\n    \"td\",\n    \"textarea\",\n    \"tfoot\",\n    \"th\",\n    \"thead\",\n    \"time\",\n    \"title\",\n    \"tr\",\n    \"tracks\",\n    \"tt\",\n    \"u\",\n    \"ul\",\n    \"video\",\n    \"wbr\"\n  ],\n  // we include these common regular expressions\n  symbols: /[\\+\\-\\*\\%\\&\\|\\!\\=\\/\\.\\,\\:]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      // Tag or a keyword at start\n      [\n        /^(\\s*)([a-zA-Z_-][\\w-]*)/,\n        {\n          cases: {\n            \"$2@tags\": {\n              cases: {\n                \"@eos\": [\"\", \"tag\"],\n                \"@default\": [\"\", { token: \"tag\", next: \"@tag.$1\" }]\n              }\n            },\n            \"$2@keywords\": [\"\", { token: \"keyword.$2\" }],\n            \"@default\": [\"\", \"\"]\n          }\n        }\n      ],\n      // id\n      [\n        /^(\\s*)(#[a-zA-Z_-][\\w-]*)/,\n        {\n          cases: {\n            \"@eos\": [\"\", \"tag.id\"],\n            \"@default\": [\"\", { token: \"tag.id\", next: \"@tag.$1\" }]\n          }\n        }\n      ],\n      // class\n      [\n        /^(\\s*)(\\.[a-zA-Z_-][\\w-]*)/,\n        {\n          cases: {\n            \"@eos\": [\"\", \"tag.class\"],\n            \"@default\": [\"\", { token: \"tag.class\", next: \"@tag.$1\" }]\n          }\n        }\n      ],\n      // plain text with pipe\n      [/^(\\s*)(\\|.*)$/, \"\"],\n      { include: \"@whitespace\" },\n      // keywords\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d+\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d+/, \"number\"],\n      // strings:\n      [/\"/, \"string\", '@string.\"'],\n      [/'/, \"string\", \"@string.'\"]\n    ],\n    tag: [\n      [/(\\.)(\\s*$)/, [{ token: \"delimiter\", next: \"@blockText.$S2.\" }, \"\"]],\n      [/\\s+/, { token: \"\", next: \"@simpleText\" }],\n      // id\n      [\n        /#[a-zA-Z_-][\\w-]*/,\n        {\n          cases: {\n            \"@eos\": { token: \"tag.id\", next: \"@pop\" },\n            \"@default\": \"tag.id\"\n          }\n        }\n      ],\n      // class\n      [\n        /\\.[a-zA-Z_-][\\w-]*/,\n        {\n          cases: {\n            \"@eos\": { token: \"tag.class\", next: \"@pop\" },\n            \"@default\": \"tag.class\"\n          }\n        }\n      ],\n      // attributes\n      [/\\(/, { token: \"delimiter.parenthesis\", next: \"@attributeList\" }]\n    ],\n    simpleText: [\n      [/[^#]+$/, { token: \"\", next: \"@popall\" }],\n      [/[^#]+/, { token: \"\" }],\n      // interpolation\n      [\n        /(#{)([^}]*)(})/,\n        {\n          cases: {\n            \"@eos\": [\n              \"interpolation.delimiter\",\n              \"interpolation\",\n              {\n                token: \"interpolation.delimiter\",\n                next: \"@popall\"\n              }\n            ],\n            \"@default\": [\"interpolation.delimiter\", \"interpolation\", \"interpolation.delimiter\"]\n          }\n        }\n      ],\n      [/#$/, { token: \"\", next: \"@popall\" }],\n      [/#/, \"\"]\n    ],\n    attributeList: [\n      [/\\s+/, \"\"],\n      [\n        /(\\w+)(\\s*=\\s*)(\"|')/,\n        [\"attribute.name\", \"delimiter\", { token: \"attribute.value\", next: \"@value.$3\" }]\n      ],\n      [/\\w+/, \"attribute.name\"],\n      [\n        /,/,\n        {\n          cases: {\n            \"@eos\": {\n              token: \"attribute.delimiter\",\n              next: \"@popall\"\n            },\n            \"@default\": \"attribute.delimiter\"\n          }\n        }\n      ],\n      [/\\)$/, { token: \"delimiter.parenthesis\", next: \"@popall\" }],\n      [/\\)/, { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/^(\\s*)(\\/\\/.*)$/, { token: \"comment\", next: \"@blockText.$1.comment\" }],\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/<!--/, { token: \"comment\", next: \"@comment\" }]\n    ],\n    blockText: [\n      [\n        /^\\s+.*$/,\n        {\n          cases: {\n            \"($S2\\\\s+.*$)\": { token: \"$S3\" },\n            \"@default\": { token: \"@rematch\", next: \"@popall\" }\n          }\n        }\n      ],\n      [/./, { token: \"@rematch\", next: \"@popall\" }]\n    ],\n    comment: [\n      [/[^<\\-]+/, \"comment.content\"],\n      [/-->/, { token: \"comment\", next: \"@pop\" }],\n      [/<!--/, \"comment.content.invalid\"],\n      [/[<\\-]/, \"comment.content\"]\n    ],\n    string: [\n      [\n        /[^\\\\\"'#]+/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /@escapes/,\n        {\n          cases: {\n            \"@eos\": { token: \"string.escape\", next: \"@popall\" },\n            \"@default\": \"string.escape\"\n          }\n        }\n      ],\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@eos\": {\n              token: \"string.escape.invalid\",\n              next: \"@popall\"\n            },\n            \"@default\": \"string.escape.invalid\"\n          }\n        }\n      ],\n      // interpolation\n      [/(#{)([^}]*)(})/, [\"interpolation.delimiter\", \"interpolation\", \"interpolation.delimiter\"]],\n      [/#/, \"string\"],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": { token: \"string\" }\n          }\n        }\n      ]\n    ],\n    // Almost identical to above, except for escapes and the output token\n    value: [\n      [\n        /[^\\\\\"']+/,\n        {\n          cases: {\n            \"@eos\": { token: \"attribute.value\", next: \"@popall\" },\n            \"@default\": \"attribute.value\"\n          }\n        }\n      ],\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@eos\": { token: \"attribute.value\", next: \"@popall\" },\n            \"@default\": \"attribute.value\"\n          }\n        }\n      ],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"attribute.value\", next: \"@pop\" },\n            \"@default\": { token: \"attribute.value\" }\n          }\n        }\n      ]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,KACd,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACxD,CACD,QAAS,CACP,QAAS,GACV,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,OACd,WAAY,GACZ,SAAU,CACR,CAAE,MAAO,kBAAmB,KAAM,IAAK,MAAO,IAAK,CACnD,CAAE,MAAO,kBAAmB,KAAM,IAAK,MAAO,IAAK,CACnD,CAAE,MAAO,wBAAyB,KAAM,IAAK,MAAO,IAAK,CAC1D,CACD,SAAU,CACR,SACA,QACA,OACA,UACA,UACA,OACA,OACA,UACA,MACA,KACA,KACA,UACA,QACA,SACA,SACA,MACA,OACD,CACD,KAAM,6nBAoHL,CAED,QAAS,8BACT,QAAS,wEACT,UAAW,CACT,KAAM,CAEJ,CACE,2BACA,CACE,MAAO,CACL,UAAW,CACT,MAAO,CACL,OAAQ,CAAC,GAAI,MAAM,CACnB,WAAY,CAAC,GAAI,CAAE,MAAO,MAAO,KAAM,UAAW,CAAC,CACpD,CACF,CACD,cAAe,CAAC,GAAI,CAAE,MAAO,aAAc,CAAC,CAC5C,WAAY,CAAC,GAAI,GAAG,CACrB,CACF,CACF,CAED,CACE,4BACA,CACE,MAAO,CACL,OAAQ,CAAC,GAAI,SAAS,CACtB,WAAY,CAAC,GAAI,CAAE,MAAO,SAAU,KAAM,UAAW,CAAC,CACvD,CACF,CACF,CAED,CACE,6BACA,CACE,MAAO,CACL,OAAQ,CAAC,GAAI,YAAY,CACzB,WAAY,CAAC,GAAI,CAAE,MAAO,YAAa,KAAM,UAAW,CAAC,CAC1D,CACF,CACF,CAED,CAAC,gBAAiB,GAAG,CACrB,CAAE,QAAS,cAAe,CAE1B,CACE,mBACA,CACE,MAAO,CACL,YAAa,CAAE,MAAO,aAAc,CACpC,WAAY,GACb,CACF,CACF,CAED,CAAC,aAAc,YAAY,CAC3B,CAAC,WAAY,YAAY,CAEzB,CAAC,2BAA4B,eAAe,CAC5C,CAAC,MAAO,SAAS,CAEjB,CAAC,IAAK,SAAU,YAAY,CAC5B,CAAC,IAAK,SAAU,YAAY,CAC7B,CACD,IAAK,CACH,CAAC,aAAc,CAAC,CAAE,MAAO,YAAa,KAAM,kBAAmB,CAAE,GAAG,CAAC,CACrE,CAAC,MAAO,CAAE,MAAO,GAAI,KAAM,cAAe,CAAC,CAE3C,CACE,oBACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,SAAU,KAAM,OAAQ,CACzC,WAAY,SACb,CACF,CACF,CAED,CACE,qBACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,YAAa,KAAM,OAAQ,CAC5C,WAAY,YACb,CACF,CACF,CAED,CAAC,KAAM,CAAE,MAAO,wBAAyB,KAAM,iBAAkB,CAAC,CACnE,CACD,WAAY,CACV,CAAC,SAAU,CAAE,MAAO,GAAI,KAAM,UAAW,CAAC,CAC1C,CAAC,QAAS,CAAE,MAAO,GAAI,CAAC,CAExB,CACE,iBACA,CACE,MAAO,CACL,OAAQ,CACN,0BACA,gBACA,CACE,MAAO,0BACP,KAAM,UACP,CACF,CACD,WAAY,CAAC,0BAA2B,gBAAiB,0BAA0B,CACpF,CACF,CACF,CACD,CAAC,KAAM,CAAE,MAAO,GAAI,KAAM,UAAW,CAAC,CACtC,CAAC,IAAK,GAAG,CACV,CACD,cAAe,CACb,CAAC,MAAO,GAAG,CACX,CACE,sBACA,CAAC,iBAAkB,YAAa,CAAE,MAAO,kBAAmB,KAAM,YAAa,CAAC,CACjF,CACD,CAAC,MAAO,iBAAiB,CACzB,CACE,IACA,CACE,MAAO,CACL,OAAQ,CACN,MAAO,sBACP,KAAM,UACP,CACD,WAAY,sBACb,CACF,CACF,CACD,CAAC,MAAO,CAAE,MAAO,wBAAyB,KAAM,UAAW,CAAC,CAC5D,CAAC,KAAM,CAAE,MAAO,wBAAyB,KAAM,OAAQ,CAAC,CACzD,CACD,WAAY,CACV,CAAC,kBAAmB,CAAE,MAAO,UAAW,KAAM,wBAAyB,CAAC,CACxE,CAAC,aAAc,GAAG,CAClB,CAAC,OAAQ,CAAE,MAAO,UAAW,KAAM,WAAY,CAAC,CACjD,CACD,UAAW,CACT,CACE,UACA,CACE,MAAO,CACL,eAAgB,CAAE,MAAO,MAAO,CAChC,WAAY,CAAE,MAAO,WAAY,KAAM,UAAW,CACnD,CACF,CACF,CACD,CAAC,IAAK,CAAE,MAAO,WAAY,KAAM,UAAW,CAAC,CAC9C,CACD,QAAS,CACP,CAAC,UAAW,kBAAkB,CAC9B,CAAC,MAAO,CAAE,MAAO,UAAW,KAAM,OAAQ,CAAC,CAC3C,CAAC,OAAQ,0BAA0B,CACnC,CAAC,QAAS,kBAAkB,CAC7B,CACD,OAAQ,CACN,CACE,YACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,SAAU,KAAM,UAAW,CAC5C,WAAY,SACb,CACF,CACF,CACD,CACE,WACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,gBAAiB,KAAM,UAAW,CACnD,WAAY,gBACb,CACF,CACF,CACD,CACE,MACA,CACE,MAAO,CACL,OAAQ,CACN,MAAO,wBACP,KAAM,UACP,CACD,WAAY,wBACb,CACF,CACF,CAED,CAAC,iBAAkB,CAAC,0BAA2B,gBAAiB,0BAA0B,CAAC,CAC3F,CAAC,IAAK,SAAS,CACf,CACE,OACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,SAAU,KAAM,OAAQ,CAC5C,WAAY,CAAE,MAAO,SAAU,CAChC,CACF,CACF,CACF,CAED,MAAO,CACL,CACE,WACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,kBAAmB,KAAM,UAAW,CACrD,WAAY,kBACb,CACF,CACF,CACD,CACE,MACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,kBAAmB,KAAM,UAAW,CACrD,WAAY,kBACb,CACF,CACF,CACD,CACE,OACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,kBAAmB,KAAM,OAAQ,CACrD,WAAY,CAAE,MAAO,kBAAmB,CACzC,CACF,CACF,CACF,CACF,CACF"}