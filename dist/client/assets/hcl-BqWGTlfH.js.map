{"version": 3, "file": "hcl-BqWGTlfH.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/hcl/hcl.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/hcl/hcl.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".hcl\",\n  keywords: [\n    \"var\",\n    \"local\",\n    \"path\",\n    \"for_each\",\n    \"any\",\n    \"string\",\n    \"number\",\n    \"bool\",\n    \"true\",\n    \"false\",\n    \"null\",\n    \"if \",\n    \"else \",\n    \"endif \",\n    \"for \",\n    \"in\",\n    \"endfor\"\n  ],\n  operators: [\n    \"=\",\n    \">=\",\n    \"<=\",\n    \"==\",\n    \"!=\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"&&\",\n    \"||\",\n    \"!\",\n    \"<\",\n    \">\",\n    \"?\",\n    \"...\",\n    \":\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  terraformFunctions: /(abs|ceil|floor|log|max|min|pow|signum|chomp|format|formatlist|indent|join|lower|regex|regexall|replace|split|strrev|substr|title|trimspace|upper|chunklist|coalesce|coalescelist|compact|concat|contains|distinct|element|flatten|index|keys|length|list|lookup|map|matchkeys|merge|range|reverse|setintersection|setproduct|setunion|slice|sort|transpose|values|zipmap|base64decode|base64encode|base64gzip|csvdecode|jsondecode|jsonencode|urlencode|yamldecode|yamlencode|abspath|dirname|pathexpand|basename|file|fileexists|fileset|filebase64|templatefile|formatdate|timeadd|timestamp|base64sha256|base64sha512|bcrypt|filebase64sha256|filebase64sha512|filemd5|filemd1|filesha256|filesha512|md5|rsadecrypt|sha1|sha256|sha512|uuid|uuidv5|cidrhost|cidrnetmask|cidrsubnet|tobool|tolist|tomap|tonumber|toset|tostring)/,\n  terraformMainBlocks: /(module|data|terraform|resource|provider|variable|output|locals)/,\n  tokenizer: {\n    root: [\n      // highlight main blocks\n      [\n        /^@terraformMainBlocks([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)(\\{)/,\n        [\"type\", \"\", \"string\", \"\", \"string\", \"\", \"@brackets\"]\n      ],\n      // highlight all the remaining blocks\n      [\n        /(\\w+[ \\t]+)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)(\\{)/,\n        [\"identifier\", \"\", \"string\", \"\", \"string\", \"\", \"@brackets\"]\n      ],\n      // highlight block\n      [\n        /(\\w+[ \\t]+)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)(=)(\\{)/,\n        [\"identifier\", \"\", \"string\", \"\", \"operator\", \"\", \"@brackets\"]\n      ],\n      // terraform general highlight - shared with expressions\n      { include: \"@terraform\" }\n    ],\n    terraform: [\n      // highlight terraform functions\n      [/@terraformFunctions(\\()/, [\"type\", \"@brackets\"]],\n      // all other words are variables or keywords\n      [\n        /[a-zA-Z_]\\w*-*/,\n        // must work with variables such as foo-bar and also with negative numbers\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"variable\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      { include: \"@heredoc\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d[\\d']*/, \"number\"],\n      [/\\d/, \"number\"],\n      [/[;,.]/, \"delimiter\"],\n      // delimiter: after number because of .\\d floats\n      // strings\n      [/\"/, \"string\", \"@string\"],\n      // this will include expressions\n      [/'/, \"invalid\"]\n    ],\n    heredoc: [\n      [/<<[-]*\\s*[\"]?([\\w\\-]+)[\"]?/, { token: \"string.heredoc.delimiter\", next: \"@heredocBody.$1\" }]\n    ],\n    heredocBody: [\n      [\n        /([\\w\\-]+)$/,\n        {\n          cases: {\n            \"$1==$S2\": [\n              {\n                token: \"string.heredoc.delimiter\",\n                next: \"@popall\"\n              }\n            ],\n            \"@default\": \"string.heredoc\"\n          }\n        }\n      ],\n      [/./, \"string.heredoc\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"],\n      [/#.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/\\$\\{/, { token: \"delimiter\", next: \"@stringExpression\" }],\n      [/[^\\\\\"\\$]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@popall\"]\n    ],\n    stringInsideExpression: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    stringExpression: [\n      [/\\}/, { token: \"delimiter\", next: \"@pop\" }],\n      [/\"/, \"string\", \"@stringInsideExpression\"],\n      { include: \"@terraform\" }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,IACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC7C,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,OACd,SAAU,CACR,MACA,QACA,OACA,WACA,MACA,SACA,SACA,OACA,OACA,QACA,OACA,MACA,QACA,SACA,OACA,KACA,SACD,CACD,UAAW,CACT,IACA,KACA,KACA,KACA,KACA,IACA,IACA,IACA,IACA,IACA,KACA,KACA,IACA,IACA,IACA,IACA,MACA,IACD,CACD,QAAS,wBACT,QAAS,wEACT,mBAAoB,syBACpB,oBAAqB,mEACrB,UAAW,CACT,KAAM,CAEJ,CACE,wFACA,CAAC,OAAQ,GAAI,SAAU,GAAI,SAAU,GAAI,YAAY,CACtD,CAED,CACE,8EACA,CAAC,aAAc,GAAI,SAAU,GAAI,SAAU,GAAI,YAAY,CAC5D,CAED,CACE,yEACA,CAAC,aAAc,GAAI,SAAU,GAAI,WAAY,GAAI,YAAY,CAC9D,CAED,CAAE,QAAS,aAAc,CAC1B,CACD,UAAW,CAET,CAAC,0BAA2B,CAAC,OAAQ,YAAY,CAAC,CAElD,CACE,iBAEA,CACE,MAAO,CACL,YAAa,CAAE,MAAO,aAAc,CACpC,WAAY,WACb,CACF,CACF,CACD,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,WAAY,CAEvB,CAAC,aAAc,YAAY,CAC3B,CAAC,mBAAoB,YAAY,CACjC,CACE,WACA,CACE,MAAO,CACL,aAAc,WACd,WAAY,GACb,CACF,CACF,CAED,CAAC,yBAA0B,eAAe,CAC1C,CAAC,2BAA4B,eAAe,CAC5C,CAAC,WAAY,SAAS,CACtB,CAAC,KAAM,SAAS,CAChB,CAAC,QAAS,YAAY,CAGtB,CAAC,IAAK,SAAU,UAAU,CAE1B,CAAC,IAAK,UAAU,CACjB,CACD,QAAS,CACP,CAAC,6BAA8B,CAAE,MAAO,2BAA4B,KAAM,kBAAmB,CAAC,CAC/F,CACD,YAAa,CACX,CACE,aACA,CACE,MAAO,CACL,UAAW,CACT,CACE,MAAO,2BACP,KAAM,UACP,CACF,CACD,WAAY,iBACb,CACF,CACF,CACD,CAAC,IAAK,iBAAiB,CACxB,CACD,WAAY,CACV,CAAC,aAAc,GAAG,CAClB,CAAC,OAAQ,UAAW,WAAW,CAC/B,CAAC,UAAW,UAAU,CACtB,CAAC,OAAQ,UAAU,CACpB,CACD,QAAS,CACP,CAAC,UAAW,UAAU,CACtB,CAAC,OAAQ,UAAW,OAAO,CAC3B,CAAC,QAAS,UAAU,CACrB,CACD,OAAQ,CACN,CAAC,OAAQ,CAAE,MAAO,YAAa,KAAM,oBAAqB,CAAC,CAC3D,CAAC,YAAa,SAAS,CACvB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,SAAU,UAAU,CAC3B,CACD,uBAAwB,CACtB,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,SAAU,OAAO,CACxB,CACD,iBAAkB,CAChB,CAAC,KAAM,CAAE,MAAO,YAAa,KAAM,OAAQ,CAAC,CAC5C,CAAC,IAAK,SAAU,0BAA0B,CAC1C,CAAE,QAAS,aAAc,CAC1B,CACF,CACF"}