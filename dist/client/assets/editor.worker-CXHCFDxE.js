const e=new class{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout(()=>{throw e.stack?o.isErrorNoTelemetry(e)?new o(e.message+`

`+e.stack):Error(e.message+`

`+e.stack):e},0)}}emit(e){this.listeners.forEach(t=>{t(e)})}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}};function t(t){i(t)||e.onUnexpectedError(t)}function n(e){if(e instanceof Error){let{name:t,message:n}=e,r=e.stacktrace||e.stack;return{$isError:!0,name:t,message:n,stack:r,noTelemetry:o.isErrorNoTelemetry(e)}}return e}const r=`Canceled`;function i(e){return e instanceof a?!0:e instanceof Error&&e.name===r&&e.message===r}var a=class extends Error{constructor(){super(r),this.name=this.message}},o=class e extends Error{constructor(e){super(e),this.name=`CodeExpectedError`}static fromError(t){if(t instanceof e)return t;let n=new e;return n.message=t.message,n.stack=t.stack,n}static isErrorNoTelemetry(e){return e.name===`CodeExpectedError`}},s=class e extends Error{constructor(t){super(t||`An unexpected bug occurred.`),Object.setPrototypeOf(this,e.prototype)}};function c(e,t){let n=this,r=!1,i;return function(){if(r)return i;if(r=!0,t)try{i=e.apply(n,arguments)}finally{t()}else i=e.apply(n,arguments);return i}}var l;(function(e){function t(e){return e&&typeof e==`object`&&typeof e[Symbol.iterator]==`function`}e.is=t;let n=Object.freeze([]);function r(){return n}e.empty=r;function*i(e){yield e}e.single=i;function a(e){return t(e)?e:i(e)}e.wrap=a;function o(e){return e||n}e.from=o;function*s(e){for(let t=e.length-1;t>=0;t--)yield e[t]}e.reverse=s;function c(e){return!e||e[Symbol.iterator]().next().done===!0}e.isEmpty=c;function l(e){return e[Symbol.iterator]().next().value}e.first=l;function u(e,t){let n=0;for(let r of e)if(t(r,n++))return!0;return!1}e.some=u;function d(e,t){for(let n of e)if(t(n))return n}e.find=d;function*f(e,t){for(let n of e)t(n)&&(yield n)}e.filter=f;function*p(e,t){let n=0;for(let r of e)yield t(r,n++)}e.map=p;function*m(e,t){let n=0;for(let r of e)yield*t(r,n++)}e.flatMap=m;function*h(...e){for(let t of e)yield*t}e.concat=h;function g(e,t,n){let r=n;for(let n of e)r=t(r,n);return r}e.reduce=g;function*_(e,t,n=e.length){for(t<0&&(t+=e.length),n<0?n+=e.length:n>e.length&&(n=e.length);t<n;t++)yield e[t]}e.slice=_;function v(t,n=1/0){let r=[];if(n===0)return[r,t];let i=t[Symbol.iterator]();for(let t=0;t<n;t++){let t=i.next();if(t.done)return[r,e.empty()];r.push(t.value)}return[r,{[Symbol.iterator](){return i}}]}e.consume=v;async function y(e){let t=[];for await(let n of e)t.push(n);return Promise.resolve(t)}e.asyncToArray=y})(l||={});function u(e){return null?.trackDisposable(e),e}function d(e){null?.markAsDisposed(e)}function f(e,t){null?.setParent(e,t)}function p(e){if(l.is(e)){let t=[];for(let n of e)if(n)try{n.dispose()}catch(e){t.push(e)}if(t.length===1)throw t[0];if(t.length>1)throw AggregateError(t,`Encountered errors while disposing of store`);return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function m(...e){return h(()=>p(e))}function h(e){let t=u({dispose:c(()=>{d(t),e()})});return t}var g=class e{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this._toDispose=new Set,this._isDisposed=!1,u(this)}dispose(){this._isDisposed||(d(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(this._toDispose.size!==0)try{p(this._toDispose)}finally{this._toDispose.clear()}}add(t){if(!t)return t;if(t===this)throw Error(`Cannot register a disposable on itself!`);return f(t,this),this._isDisposed?e.DISABLE_DISPOSED_WARNING||console.warn(Error(`Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!`).stack):this._toDispose.add(t),t}deleteAndLeak(e){e&&this._toDispose.has(e)&&(this._toDispose.delete(e),f(e,null))}},_=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this._store=new g,u(this),f(this._store,this)}dispose(){d(this),this._store.dispose()}_register(e){if(e===this)throw Error(`Cannot register a disposable on itself!`);return this._store.add(e)}},v=class e{static{this.Undefined=new e(void 0)}constructor(t){this.element=t,this.next=e.Undefined,this.prev=e.Undefined}},y=class{constructor(){this._first=v.Undefined,this._last=v.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===v.Undefined}clear(){let e=this._first;for(;e!==v.Undefined;){let t=e.next;e.prev=v.Undefined,e.next=v.Undefined,e=t}this._first=v.Undefined,this._last=v.Undefined,this._size=0}unshift(e){return this._insert(e,!1)}push(e){return this._insert(e,!0)}_insert(e,t){let n=new v(e);if(this._first===v.Undefined)this._first=n,this._last=n;else if(t){let e=this._last;this._last=n,n.prev=e,e.next=n}else{let e=this._first;this._first=n,n.next=e,e.prev=n}this._size+=1;let r=!1;return()=>{r||(r=!0,this._remove(n))}}shift(){if(this._first!==v.Undefined){let e=this._first.element;return this._remove(this._first),e}}pop(){if(this._last!==v.Undefined){let e=this._last.element;return this._remove(this._last),e}}_remove(e){if(e.prev!==v.Undefined&&e.next!==v.Undefined){let t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===v.Undefined&&e.next===v.Undefined?(this._first=v.Undefined,this._last=v.Undefined):e.next===v.Undefined?(this._last=this._last.prev,this._last.next=v.Undefined):e.prev===v.Undefined&&(this._first=this._first.next,this._first.prev=v.Undefined);--this._size}*[Symbol.iterator](){let e=this._first;for(;e!==v.Undefined;)yield e.element,e=e.next}};const b=globalThis.performance&&typeof globalThis.performance.now==`function`;var x=class e{static create(t){return new e(t)}constructor(e){this._now=b&&e===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}reset(){this._startTime=this._now(),this._stopTime=-1}elapsed(){return this._stopTime===-1?this._now()-this._startTime:this._stopTime-this._startTime}},S;(function(e){e.None=()=>_.None;function t(e,t){return f(e,()=>void 0,0,void 0,!0,void 0,t)}e.defer=t;function n(e){return(t,n=null,r)=>{let i=!1,a;return a=e(e=>{if(!i)return a?a.dispose():i=!0,t.call(n,e)},null,r),i&&a.dispose(),a}}e.once=n;function r(t,n){return e.once(e.filter(t,n))}e.onceIf=r;function i(e,t,n){return u((n,r=null,i)=>e(e=>n.call(r,t(e)),null,i),n)}e.map=i;function a(e,t,n){return u((n,r=null,i)=>e(e=>{t(e),n.call(r,e)},null,i),n)}e.forEach=a;function o(e,t,n){return u((n,r=null,i)=>e(e=>t(e)&&n.call(r,e),null,i),n)}e.filter=o;function s(e){return e}e.signal=s;function c(...e){return(t,n=null,r)=>{let i=m(...e.map(e=>e(e=>t.call(n,e))));return d(i,r)}}e.any=c;function l(e,t,n,r){let a=n;return i(e,e=>(a=t(a,e),a),r)}e.reduce=l;function u(e,t){let n,r={onWillAddFirstListener(){n=e(i.fire,i)},onDidRemoveLastListener(){n?.dispose()}},i=new E(r);return t?.add(i),i.event}function d(e,t){return t instanceof Array?t.push(e):t&&t.add(e),e}function f(e,t,n=100,r=!1,i=!1,a,o){let s,c,l,u=0,d,f={leakWarningThreshold:a,onWillAddFirstListener(){s=e(e=>{u++,c=t(c,e),r&&!l&&(p.fire(c),c=void 0),d=()=>{let e=c;c=void 0,l=void 0,(!r||u>1)&&p.fire(e),u=0},typeof n==`number`?(clearTimeout(l),l=setTimeout(d,n)):l===void 0&&(l=0,queueMicrotask(d))})},onWillRemoveListener(){i&&u>0&&d?.()},onDidRemoveLastListener(){d=void 0,s.dispose()}},p=new E(f);return o?.add(p),p.event}e.debounce=f;function p(t,n=0,r){return e.debounce(t,(e,t)=>e?(e.push(t),e):[t],n,void 0,!0,void 0,r)}e.accumulate=p;function h(e,t=(e,t)=>e===t,n){let r=!0,i;return o(e,e=>{let n=r||!t(e,i);return r=!1,i=e,n},n)}e.latch=h;function v(t,n,r){return[e.filter(t,n,r),e.filter(t,e=>!n(e),r)]}e.split=v;function y(e,t=!1,n=[],r){let i=n.slice(),a=e(e=>{i?i.push(e):s.fire(e)});r&&r.add(a);let o=()=>{i?.forEach(e=>s.fire(e)),i=null},s=new E({onWillAddFirstListener(){a||(a=e(e=>s.fire(e)),r&&r.add(a))},onDidAddFirstListener(){i&&(t?setTimeout(o):o())},onDidRemoveLastListener(){a&&a.dispose(),a=null}});return r&&r.add(s),s.event}e.buffer=y;function b(e,t){return(n,r,i)=>{let a=t(new S);return e(function(e){let t=a.evaluate(e);t!==x&&n.call(r,t)},void 0,i)}}e.chain=b;let x=Symbol(`HaltChainable`);class S{constructor(){this.steps=[]}map(e){return this.steps.push(e),this}forEach(e){return this.steps.push(t=>(e(t),t)),this}filter(e){return this.steps.push(t=>e(t)?t:x),this}reduce(e,t){let n=t;return this.steps.push(t=>(n=e(n,t),n)),this}latch(e=(e,t)=>e===t){let t=!0,n;return this.steps.push(r=>{let i=t||!e(r,n);return t=!1,n=r,i?r:x}),this}evaluate(e){for(let t of this.steps)if(e=t(e),e===x)break;return e}}function C(e,t,n=e=>e){let r=(...e)=>i.fire(n(...e)),i=new E({onWillAddFirstListener:()=>e.on(t,r),onDidRemoveLastListener:()=>e.removeListener(t,r)});return i.event}e.fromNodeEventEmitter=C;function w(e,t,n=e=>e){let r=(...e)=>i.fire(n(...e)),i=new E({onWillAddFirstListener:()=>e.addEventListener(t,r),onDidRemoveLastListener:()=>e.removeEventListener(t,r)});return i.event}e.fromDOMEventEmitter=w;function T(e){return new Promise(t=>n(e)(t))}e.toPromise=T;function ee(e){let t=new E;return e.then(e=>{t.fire(e)},()=>{t.fire(void 0)}).finally(()=>{t.dispose()}),t.event}e.fromPromise=ee;function te(e,t){return e(e=>t.fire(e))}e.forward=te;function ne(e,t,n){return t(n),e(e=>t(e))}e.runAndSubscribe=ne;class re{constructor(e,t){this._observable=e,this._counter=0,this._hasChanged=!1,this.emitter=new E({onWillAddFirstListener:()=>{e.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{e.removeObserver(this)}}),t&&t.add(this.emitter)}beginUpdate(e){this._counter++}handlePossibleChange(e){}handleChange(e,t){this._hasChanged=!0}endUpdate(e){this._counter--,this._counter===0&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}function ie(e,t){return new re(e,t).emitter.event}e.fromObservable=ie;function ae(e){return(t,n,r)=>{let i=0,a=!1,o={beginUpdate(){i++},endUpdate(){i--,i===0&&(e.reportChanges(),a&&(a=!1,t.call(n)))},handlePossibleChange(){},handleChange(){a=!0}};e.addObserver(o),e.reportChanges();let s={dispose(){e.removeObserver(o)}};return r instanceof g?r.add(s):Array.isArray(r)&&r.push(s),s}}e.fromObservableLight=ae})(S||={});var C=class e{static{this.all=new Set}static{this._idPool=0}constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${e._idPool++}`,e.all.add(this)}start(e){this._stopWatch=new x,this.listenerCount=e}stop(){if(this._stopWatch){let e=this._stopWatch.elapsed();this.durations.push(e),this.elapsedOverall+=e,this.invocationCount+=1,this._stopWatch=void 0}}},w=class e{static{this._idPool=1}constructor(t,n,r=(e._idPool++).toString(16).padStart(3,`0`)){this._errorHandler=t,this.threshold=n,this.name=r,this._warnCountdown=0}dispose(){this._stacks?.clear()}check(e,t){let n=this.threshold;if(n<=0||t<n)return;this._stacks||=new Map;let r=this._stacks.get(e.value)||0;if(this._stacks.set(e.value,r+1),--this._warnCountdown,this._warnCountdown<=0){this._warnCountdown=n*.5;let[e,r]=this.getMostFrequentStack(),i=`[${this.name}] potential listener LEAK detected, having ${t} listeners already. MOST frequent listener (${r}):`;console.warn(i),console.warn(e);let a=new ee(i,e);this._errorHandler(a)}return()=>{let t=this._stacks.get(e.value)||0;this._stacks.set(e.value,t-1)}}getMostFrequentStack(){if(!this._stacks)return;let e,t=0;for(let[n,r]of this._stacks)(!e||t<r)&&(e=[n,r],t=r);return e}},T=class e{static create(){return new e(Error().stack??``)}constructor(e){this.value=e}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},ee=class extends Error{constructor(e,t){super(e),this.name=`ListenerLeakError`,this.stack=t}},te=class extends Error{constructor(e,t){super(e),this.name=`ListenerRefusalError`,this.stack=t}},ne=class{constructor(e){this.value=e}},E=class{constructor(e){this._size=0,this._options=e,this._leakageMon=this._options?.leakWarningThreshold?new w(e?.onListenerError??t,this._options?.leakWarningThreshold??-1):void 0,this._perfMon=this._options?._profName?new C(this._options._profName):void 0,this._deliveryQueue=this._options?.deliveryQueue}dispose(){this._disposed||(this._disposed=!0,this._deliveryQueue?.current===this&&this._deliveryQueue.reset(),this._listeners&&(this._listeners=void 0,this._size=0),this._options?.onDidRemoveLastListener?.(),this._leakageMon?.dispose())}get event(){return this._event??=(e,n,r)=>{if(this._leakageMon&&this._size>this._leakageMon.threshold**2){let e=`[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this._size} vs ${this._leakageMon.threshold})`;console.warn(e);let n=this._leakageMon.getMostFrequentStack()??[`UNKNOWN stack`,-1],r=new te(`${e}. HINT: Stack shows most frequent listener (${n[1]}-times)`,n[0]);return(this._options?.onListenerError||t)(r),_.None}if(this._disposed)return _.None;n&&(e=e.bind(n));let i=new ne(e),a;this._leakageMon&&this._size>=Math.ceil(this._leakageMon.threshold*.2)&&(i.stack=T.create(),a=this._leakageMon.check(i.stack,this._size+1)),this._listeners?this._listeners instanceof ne?(this._deliveryQueue??=new re,this._listeners=[this._listeners,i]):this._listeners.push(i):(this._options?.onWillAddFirstListener?.(this),this._listeners=i,this._options?.onDidAddFirstListener?.(this)),this._size++;let o=h(()=>{(void 0)?.unregister(o),a?.(),this._removeListener(i)});return r instanceof g?r.add(o):Array.isArray(r)&&r.push(o),o},this._event}_removeListener(e){if(this._options?.onWillRemoveListener?.(this),!this._listeners)return;if(this._size===1){this._listeners=void 0,this._options?.onDidRemoveLastListener?.(this),this._size=0;return}let t=this._listeners,n=t.indexOf(e);if(n===-1)throw console.log(`disposed?`,this._disposed),console.log(`size?`,this._size),console.log(`arr?`,JSON.stringify(this._listeners)),Error(`Attempted to dispose unknown listener`);this._size--,t[n]=void 0;let r=this._deliveryQueue.current===this;if(this._size*2<=t.length){let e=0;for(let n=0;n<t.length;n++)t[n]?t[e++]=t[n]:r&&(this._deliveryQueue.end--,e<this._deliveryQueue.i&&this._deliveryQueue.i--);t.length=e}}_deliver(e,n){if(!e)return;let r=this._options?.onListenerError||t;if(!r){e.value(n);return}try{e.value(n)}catch(e){r(e)}}_deliverQueue(e){let t=e.current._listeners;for(;e.i<e.end;)this._deliver(t[e.i++],e.value);e.reset()}fire(e){if(this._deliveryQueue?.current&&(this._deliverQueue(this._deliveryQueue),this._perfMon?.stop()),this._perfMon?.start(this._size),this._listeners)if(this._listeners instanceof ne)this._deliver(this._listeners,e);else{let t=this._deliveryQueue;t.enqueue(this,e,this._listeners.length),this._deliverQueue(t)}this._perfMon?.stop()}hasListeners(){return this._size>0}},re=class{constructor(){this.i=-1,this.end=0}enqueue(e,t,n){this.i=0,this.end=n,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}};function ie(){return globalThis._VSCODE_NLS_MESSAGES}function ae(){return globalThis._VSCODE_NLS_LANGUAGE}const oe=ae()===`pseudo`||typeof document<`u`&&document.location&&document.location.hash.indexOf(`pseudo=true`)>=0;function se(e,t){let n;return n=t.length===0?e:e.replace(/\{(\d+)\}/g,(e,n)=>{let r=n[0],i=t[r],a=e;return typeof i==`string`?a=i:(typeof i==`number`||typeof i==`boolean`||i==null)&&(a=String(i)),a}),oe&&(n=`［`+n.replace(/[aouei]/g,`$&$&`)+`］`),n}function D(e,t,...n){return se(typeof e==`number`?ce(e,t):t,n)}function ce(e,t){let n=ie()?.[e];if(typeof n!=`string`){if(typeof t==`string`)return t;throw Error(`!!! NLS MISSING: ${e} !!!`)}return n}let le=!1,ue=!1,de=!1,fe=!1,pe=!1,O;const k=globalThis;let A;k.vscode!==void 0&&k.vscode.process!==void 0?A=k.vscode.process:typeof process<`u`&&typeof process?.versions?.node==`string`&&(A=process);const me=typeof A?.versions?.electron==`string`&&A?.type===`renderer`;if(typeof A==`object`){le=A.platform===`win32`,ue=A.platform===`darwin`,de=A.platform===`linux`,de&&A.env.SNAP&&A.env.SNAP_REVISION,A.env.CI||A.env.BUILD_ARTIFACTSTAGINGDIRECTORY;let e=A.env.VSCODE_NLS_CONFIG;if(e)try{let t=JSON.parse(e);t.userLocale,t.osLocale,t.resolvedLanguage,t.languagePack?.translationsConfigFile}catch{}fe=!0}else typeof navigator==`object`&&!me?(O=navigator.userAgent,le=O.indexOf(`Windows`)>=0,ue=O.indexOf(`Macintosh`)>=0,(O.indexOf(`Macintosh`)>=0||O.indexOf(`iPad`)>=0||O.indexOf(`iPhone`)>=0)&&navigator.maxTouchPoints&&navigator.maxTouchPoints,de=O.indexOf(`Linux`)>=0,O?.indexOf(`Mobi`),pe=!0,ae(),navigator.language.toLowerCase()):console.error(`Unable to resolve platform.`);const he=le,ge=ue,_e=fe,ve=pe,ye=pe&&typeof k.importScripts==`function`?k.origin:void 0,j=O,be=typeof k.postMessage==`function`&&!k.importScripts;(()=>{if(be){let e=[];k.addEventListener(`message`,t=>{if(t.data&&t.data.vscodeScheduleAsyncWork)for(let n=0,r=e.length;n<r;n++){let r=e[n];if(r.id===t.data.vscodeScheduleAsyncWork){e.splice(n,1),r.callback();return}}});let t=0;return n=>{let r=++t;e.push({id:r,callback:n}),k.postMessage({vscodeScheduleAsyncWork:r},`*`)}}return e=>setTimeout(e)})();const xe=!!(j&&j.indexOf(`Chrome`)>=0);j&&j.indexOf(`Firefox`),!xe&&j&&j.indexOf(`Safari`),j&&j.indexOf(`Edg/`),j&&j.indexOf(`Android`);function Se(e){return e}var Ce=class{constructor(e,t){this.lastCache=void 0,this.lastArgKey=void 0,typeof e==`function`?(this._fn=e,this._computeKey=Se):(this._fn=t,this._computeKey=e.getCacheKey)}get(e){let t=this._computeKey(e);return this.lastArgKey!==t&&(this.lastArgKey=t,this.lastCache=this._fn(e)),this.lastCache}},we=class{constructor(e){this.executor=e,this._didRun=!1}get value(){if(!this._didRun)try{this._value=this.executor()}catch(e){this._error=e}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}};function Te(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,`\\$&`)}function Ee(e){return e.split(/\r\n|\r|\n/)}function De(e){for(let t=0,n=e.length;t<n;t++){let n=e.charCodeAt(t);if(n!==32&&n!==9)return t}return-1}function Oe(e,t=e.length-1){for(let n=t;n>=0;n--){let t=e.charCodeAt(n);if(t!==32&&t!==9)return n}return-1}function ke(e){return e>=65&&e<=90}function Ae(e){return 55296<=e&&e<=56319}function je(e){return 56320<=e&&e<=57343}function Me(e,t){return(e-55296<<10)+(t-56320)+65536}function Ne(e,t,n){let r=e.charCodeAt(n);if(Ae(r)&&n+1<t){let t=e.charCodeAt(n+1);if(je(t))return Me(r,t)}return r}const Pe=/^[\t\n\r\x20-\x7E]*$/;function Fe(e){return Pe.test(e)}(class e{static{this._INSTANCE=null}static getInstance(){return e._INSTANCE||=new e,e._INSTANCE}constructor(){this._data=Ie()}getGraphemeBreakType(e){if(e<32)return e===10?3:e===13?2:4;if(e<127)return 0;let t=this._data,n=t.length/3,r=1;for(;r<=n;)if(e<t[3*r])r=2*r;else if(e>t[3*r+1])r=2*r+1;else return t[3*r+2];return 0}});function Ie(){return JSON.parse(`[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]`)}var Le=class e{static{this.ambiguousCharacterData=new we(()=>JSON.parse(`{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}`))}static{this.cache=new Ce({getCacheKey:JSON.stringify},t=>{function n(e){let t=new Map;for(let n=0;n<e.length;n+=2)t.set(e[n],e[n+1]);return t}function r(e,t){let n=new Map(e);for(let[e,r]of t)n.set(e,r);return n}function i(e,t){if(!e)return t;let n=new Map;for(let[r,i]of e)t.has(r)&&n.set(r,i);return n}let a=this.ambiguousCharacterData.value,o=t.filter(e=>!e.startsWith(`_`)&&e in a);o.length===0&&(o=[`_default`]);let s;for(let e of o){let t=n(a[e]);s=i(s,t)}let c=n(a._common),l=r(c,s);return new e(l)})}static getInstance(t){return e.cache.get(Array.from(t))}static{this._locales=new we(()=>Object.keys(e.ambiguousCharacterData.value).filter(e=>!e.startsWith(`_`)))}static getLocales(){return e._locales.value}constructor(e){this.confusableDictionary=e}isAmbiguous(e){return this.confusableDictionary.has(e)}getPrimaryConfusable(e){return this.confusableDictionary.get(e)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}},Re=class e{static getRawData(){return JSON.parse(`[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]`)}static{this._data=void 0}static getData(){return this._data||=new Set(e.getRawData()),this._data}static isInvisibleCharacter(t){return e.getData().has(t)}static get codePoints(){return e.getData()}};let ze;const Be=globalThis.vscode;if(Be!==void 0&&Be.process!==void 0){let e=Be.process;ze={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else ze=typeof process<`u`&&typeof process?.versions?.node==`string`?{get platform(){return process.platform},get arch(){return process.arch},get env(){return{}},cwd(){return{}.VSCODE_CWD||process.cwd()}}:{get platform(){return he?`win32`:ge?`darwin`:`linux`},get arch(){},get env(){return{}},cwd(){return`/`}};const Ve=ze.cwd,He=ze.env,Ue=ze.platform;var We=class extends Error{constructor(e,t,n){let r;typeof t==`string`&&t.indexOf(`not `)===0?(r=`must not be`,t=t.replace(/^not /,``)):r=`must be`;let i=e.indexOf(`.`)===-1?`argument`:`property`,a=`The "${e}" ${i} ${r} of type ${t}`;a+=`. Received type ${typeof n}`,super(a),this.code=`ERR_INVALID_ARG_TYPE`}};function Ge(e,t){if(typeof e!=`object`||!e)throw new We(t,`Object`,e)}function M(e,t){if(typeof e!=`string`)throw new We(t,`string`,e)}const N=Ue===`win32`;function P(e){return e===47||e===92}function Ke(e){return e===47}function F(e){return e>=65&&e<=90||e>=97&&e<=122}function qe(e,t,n,r){let i=``,a=0,o=-1,s=0,c=0;for(let l=0;l<=e.length;++l){if(l<e.length)c=e.charCodeAt(l);else if(r(c))break;else c=47;if(r(c)){if(!(o===l-1||s===1))if(s===2){if(i.length<2||a!==2||i.charCodeAt(i.length-1)!==46||i.charCodeAt(i.length-2)!==46){if(i.length>2){let e=i.lastIndexOf(n);e===-1?(i=``,a=0):(i=i.slice(0,e),a=i.length-1-i.lastIndexOf(n)),o=l,s=0;continue}else if(i.length!==0){i=``,a=0,o=l,s=0;continue}}t&&(i+=i.length>0?`${n}..`:`..`,a=2)}else i.length>0?i+=`${n}${e.slice(o+1,l)}`:i=e.slice(o+1,l),a=l-o-1;o=l,s=0}else c===46&&s!==-1?++s:s=-1}return i}function Je(e){return e?`${e[0]===`.`?``:`.`}${e}`:``}function Ye(e,t){Ge(t,`pathObject`);let n=t.dir||t.root,r=t.base||`${t.name||``}${Je(t.ext)}`;return n?n===t.root?`${n}${r}`:`${n}${e}${r}`:r}const I={resolve(...e){let t=``,n=``,r=!1;for(let i=e.length-1;i>=-1;i--){let a;if(i>=0){if(a=e[i],M(a,`paths[${i}]`),a.length===0)continue}else t.length===0?a=Ve():(a=He[`=${t}`]||Ve(),(a===void 0||a.slice(0,2).toLowerCase()!==t.toLowerCase()&&a.charCodeAt(2)===92)&&(a=`${t}\\`));let o=a.length,s=0,c=``,l=!1,u=a.charCodeAt(0);if(o===1)P(u)&&(s=1,l=!0);else if(P(u))if(l=!0,P(a.charCodeAt(1))){let e=2,t=e;for(;e<o&&!P(a.charCodeAt(e));)e++;if(e<o&&e!==t){let n=a.slice(t,e);for(t=e;e<o&&P(a.charCodeAt(e));)e++;if(e<o&&e!==t){for(t=e;e<o&&!P(a.charCodeAt(e));)e++;(e===o||e!==t)&&(c=`\\\\${n}\\${a.slice(t,e)}`,s=e)}}}else s=1;else F(u)&&a.charCodeAt(1)===58&&(c=a.slice(0,2),s=2,o>2&&P(a.charCodeAt(2))&&(l=!0,s=3));if(c.length>0)if(t.length>0){if(c.toLowerCase()!==t.toLowerCase())continue}else t=c;if(r){if(t.length>0)break}else if(n=`${a.slice(s)}\\${n}`,r=l,l&&t.length>0)break}return n=qe(n,!r,`\\`,P),r?`${t}\\${n}`:`${t}${n}`||`.`},normalize(e){M(e,`path`);let t=e.length;if(t===0)return`.`;let n=0,r,i=!1,a=e.charCodeAt(0);if(t===1)return Ke(a)?`\\`:e;if(P(a))if(i=!0,P(e.charCodeAt(1))){let i=2,a=i;for(;i<t&&!P(e.charCodeAt(i));)i++;if(i<t&&i!==a){let o=e.slice(a,i);for(a=i;i<t&&P(e.charCodeAt(i));)i++;if(i<t&&i!==a){for(a=i;i<t&&!P(e.charCodeAt(i));)i++;if(i===t)return`\\\\${o}\\${e.slice(a)}\\`;i!==a&&(r=`\\\\${o}\\${e.slice(a,i)}`,n=i)}}}else n=1;else F(a)&&e.charCodeAt(1)===58&&(r=e.slice(0,2),n=2,t>2&&P(e.charCodeAt(2))&&(i=!0,n=3));let o=n<t?qe(e.slice(n),!i,`\\`,P):``;return o.length===0&&!i&&(o=`.`),o.length>0&&P(e.charCodeAt(t-1))&&(o+=`\\`),r===void 0?i?`\\${o}`:o:i?`${r}\\${o}`:`${r}${o}`},isAbsolute(e){M(e,`path`);let t=e.length;if(t===0)return!1;let n=e.charCodeAt(0);return P(n)||t>2&&F(n)&&e.charCodeAt(1)===58&&P(e.charCodeAt(2))},join(...e){if(e.length===0)return`.`;let t,n;for(let r=0;r<e.length;++r){let i=e[r];M(i,`path`),i.length>0&&(t===void 0?t=n=i:t+=`\\${i}`)}if(t===void 0)return`.`;let r=!0,i=0;if(typeof n==`string`&&P(n.charCodeAt(0))){++i;let e=n.length;e>1&&P(n.charCodeAt(1))&&(++i,e>2&&(P(n.charCodeAt(2))?++i:r=!1))}if(r){for(;i<t.length&&P(t.charCodeAt(i));)i++;i>=2&&(t=`\\${t.slice(i)}`)}return I.normalize(t)},relative(e,t){if(M(e,`from`),M(t,`to`),e===t)return``;let n=I.resolve(e),r=I.resolve(t);if(n===r||(e=n.toLowerCase(),t=r.toLowerCase(),e===t))return``;let i=0;for(;i<e.length&&e.charCodeAt(i)===92;)i++;let a=e.length;for(;a-1>i&&e.charCodeAt(a-1)===92;)a--;let o=a-i,s=0;for(;s<t.length&&t.charCodeAt(s)===92;)s++;let c=t.length;for(;c-1>s&&t.charCodeAt(c-1)===92;)c--;let l=c-s,u=o<l?o:l,d=-1,f=0;for(;f<u;f++){let n=e.charCodeAt(i+f);if(n!==t.charCodeAt(s+f))break;n===92&&(d=f)}if(f!==u){if(d===-1)return r}else{if(l>u){if(t.charCodeAt(s+f)===92)return r.slice(s+f+1);if(f===2)return r.slice(s+f)}o>u&&(e.charCodeAt(i+f)===92?d=f:f===2&&(d=3)),d===-1&&(d=0)}let p=``;for(f=i+d+1;f<=a;++f)(f===a||e.charCodeAt(f)===92)&&(p+=p.length===0?`..`:`\\..`);return s+=d,p.length>0?`${p}${r.slice(s,c)}`:(r.charCodeAt(s)===92&&++s,r.slice(s,c))},toNamespacedPath(e){if(typeof e!=`string`||e.length===0)return e;let t=I.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===92){if(t.charCodeAt(1)===92){let e=t.charCodeAt(2);if(e!==63&&e!==46)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(F(t.charCodeAt(0))&&t.charCodeAt(1)===58&&t.charCodeAt(2)===92)return`\\\\?\\${t}`;return e},dirname(e){M(e,`path`);let t=e.length;if(t===0)return`.`;let n=-1,r=0,i=e.charCodeAt(0);if(t===1)return P(i)?e:`.`;if(P(i)){if(n=r=1,P(e.charCodeAt(1))){let i=2,a=i;for(;i<t&&!P(e.charCodeAt(i));)i++;if(i<t&&i!==a){for(a=i;i<t&&P(e.charCodeAt(i));)i++;if(i<t&&i!==a){for(a=i;i<t&&!P(e.charCodeAt(i));)i++;if(i===t)return e;i!==a&&(n=r=i+1)}}}}else F(i)&&e.charCodeAt(1)===58&&(n=t>2&&P(e.charCodeAt(2))?3:2,r=n);let a=-1,o=!0;for(let n=t-1;n>=r;--n)if(P(e.charCodeAt(n))){if(!o){a=n;break}}else o=!1;if(a===-1){if(n===-1)return`.`;a=n}return e.slice(0,a)},basename(e,t){t!==void 0&&M(t,`suffix`),M(e,`path`);let n=0,r=-1,i=!0,a;if(e.length>=2&&F(e.charCodeAt(0))&&e.charCodeAt(1)===58&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return``;let o=t.length-1,s=-1;for(a=e.length-1;a>=n;--a){let c=e.charCodeAt(a);if(P(c)){if(!i){n=a+1;break}}else s===-1&&(i=!1,s=a+1),o>=0&&(c===t.charCodeAt(o)?--o===-1&&(r=a):(o=-1,r=s))}return n===r?r=s:r===-1&&(r=e.length),e.slice(n,r)}for(a=e.length-1;a>=n;--a)if(P(e.charCodeAt(a))){if(!i){n=a+1;break}}else r===-1&&(i=!1,r=a+1);return r===-1?``:e.slice(n,r)},extname(e){M(e,`path`);let t=0,n=-1,r=0,i=-1,a=!0,o=0;e.length>=2&&e.charCodeAt(1)===58&&F(e.charCodeAt(0))&&(t=r=2);for(let s=e.length-1;s>=t;--s){let t=e.charCodeAt(s);if(P(t)){if(!a){r=s+1;break}continue}i===-1&&(a=!1,i=s+1),t===46?n===-1?n=s:o!==1&&(o=1):n!==-1&&(o=-1)}return n===-1||i===-1||o===0||o===1&&n===i-1&&n===r+1?``:e.slice(n,i)},format:Ye.bind(null,`\\`),parse(e){M(e,`path`);let t={root:``,dir:``,base:``,ext:``,name:``};if(e.length===0)return t;let n=e.length,r=0,i=e.charCodeAt(0);if(n===1)return P(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(P(i)){if(r=1,P(e.charCodeAt(1))){let t=2,i=t;for(;t<n&&!P(e.charCodeAt(t));)t++;if(t<n&&t!==i){for(i=t;t<n&&P(e.charCodeAt(t));)t++;if(t<n&&t!==i){for(i=t;t<n&&!P(e.charCodeAt(t));)t++;t===n?r=t:t!==i&&(r=t+1)}}}}else if(F(i)&&e.charCodeAt(1)===58){if(n<=2)return t.root=t.dir=e,t;if(r=2,P(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let a=-1,o=r,s=-1,c=!0,l=e.length-1,u=0;for(;l>=r;--l){if(i=e.charCodeAt(l),P(i)){if(!c){o=l+1;break}continue}s===-1&&(c=!1,s=l+1),i===46?a===-1?a=l:u!==1&&(u=1):a!==-1&&(u=-1)}return s!==-1&&(a===-1||u===0||u===1&&a===s-1&&a===o+1?t.base=t.name=e.slice(o,s):(t.name=e.slice(o,a),t.base=e.slice(o,s),t.ext=e.slice(a,s))),o>0&&o!==r?t.dir=e.slice(0,o-1):t.dir=t.root,t},sep:`\\`,delimiter:`;`,win32:null,posix:null},Xe=(()=>{if(N){let e=/\\/g;return()=>{let t=Ve().replace(e,`/`);return t.slice(t.indexOf(`/`))}}return()=>Ve()})(),L={resolve(...e){let t=``,n=!1;for(let r=e.length-1;r>=-1&&!n;r--){let i=r>=0?e[r]:Xe();M(i,`paths[${r}]`),i.length!==0&&(t=`${i}/${t}`,n=i.charCodeAt(0)===47)}return t=qe(t,!n,`/`,Ke),n?`/${t}`:t.length>0?t:`.`},normalize(e){if(M(e,`path`),e.length===0)return`.`;let t=e.charCodeAt(0)===47,n=e.charCodeAt(e.length-1)===47;return e=qe(e,!t,`/`,Ke),e.length===0?t?`/`:n?`./`:`.`:(n&&(e+=`/`),t?`/${e}`:e)},isAbsolute(e){return M(e,`path`),e.length>0&&e.charCodeAt(0)===47},join(...e){if(e.length===0)return`.`;let t;for(let n=0;n<e.length;++n){let r=e[n];M(r,`path`),r.length>0&&(t===void 0?t=r:t+=`/${r}`)}return t===void 0?`.`:L.normalize(t)},relative(e,t){if(M(e,`from`),M(t,`to`),e===t||(e=L.resolve(e),t=L.resolve(t),e===t))return``;let n=e.length,r=n-1,i=t.length-1,a=r<i?r:i,o=-1,s=0;for(;s<a;s++){let n=e.charCodeAt(1+s);if(n!==t.charCodeAt(1+s))break;n===47&&(o=s)}if(s===a)if(i>a){if(t.charCodeAt(1+s)===47)return t.slice(1+s+1);if(s===0)return t.slice(1+s)}else r>a&&(e.charCodeAt(1+s)===47?o=s:s===0&&(o=0));let c=``;for(s=1+o+1;s<=n;++s)(s===n||e.charCodeAt(s)===47)&&(c+=c.length===0?`..`:`/..`);return`${c}${t.slice(1+o)}`},toNamespacedPath(e){return e},dirname(e){if(M(e,`path`),e.length===0)return`.`;let t=e.charCodeAt(0)===47,n=-1,r=!0;for(let t=e.length-1;t>=1;--t)if(e.charCodeAt(t)===47){if(!r){n=t;break}}else r=!1;return n===-1?t?`/`:`.`:t&&n===1?`//`:e.slice(0,n)},basename(e,t){t!==void 0&&M(t,`ext`),M(e,`path`);let n=0,r=-1,i=!0,a;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return``;let o=t.length-1,s=-1;for(a=e.length-1;a>=0;--a){let c=e.charCodeAt(a);if(c===47){if(!i){n=a+1;break}}else s===-1&&(i=!1,s=a+1),o>=0&&(c===t.charCodeAt(o)?--o===-1&&(r=a):(o=-1,r=s))}return n===r?r=s:r===-1&&(r=e.length),e.slice(n,r)}for(a=e.length-1;a>=0;--a)if(e.charCodeAt(a)===47){if(!i){n=a+1;break}}else r===-1&&(i=!1,r=a+1);return r===-1?``:e.slice(n,r)},extname(e){M(e,`path`);let t=-1,n=0,r=-1,i=!0,a=0;for(let o=e.length-1;o>=0;--o){let s=e.charCodeAt(o);if(s===47){if(!i){n=o+1;break}continue}r===-1&&(i=!1,r=o+1),s===46?t===-1?t=o:a!==1&&(a=1):t!==-1&&(a=-1)}return t===-1||r===-1||a===0||a===1&&t===r-1&&t===n+1?``:e.slice(t,r)},format:Ye.bind(null,`/`),parse(e){M(e,`path`);let t={root:``,dir:``,base:``,ext:``,name:``};if(e.length===0)return t;let n=e.charCodeAt(0)===47,r;n?(t.root=`/`,r=1):r=0;let i=-1,a=0,o=-1,s=!0,c=e.length-1,l=0;for(;c>=r;--c){let t=e.charCodeAt(c);if(t===47){if(!s){a=c+1;break}continue}o===-1&&(s=!1,o=c+1),t===46?i===-1?i=c:l!==1&&(l=1):i!==-1&&(l=-1)}if(o!==-1){let r=a===0&&n?1:a;i===-1||l===0||l===1&&i===o-1&&i===a+1?t.base=t.name=e.slice(r,o):(t.name=e.slice(r,i),t.base=e.slice(r,o),t.ext=e.slice(i,o))}return a>0?t.dir=e.slice(0,a-1):n&&(t.dir=`/`),t},sep:`/`,delimiter:`:`,win32:null,posix:null};L.win32=I.win32=I,L.posix=I.posix=L,N?I.normalize:L.normalize;const Ze=N?I.join:L.join;N?I.resolve:L.resolve,N?I.relative:L.relative,N?I.dirname:L.dirname,N?I.basename:L.basename,N?I.extname:L.extname,N?I.sep:L.sep;const Qe=/^\w[\w\d+.-]*$/,$e=/^\//,et=/^\/\//;function tt(e,t){if(!e.scheme&&t)throw Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!Qe.test(e.scheme))throw Error(`[UriError]: Scheme contains illegal characters.`);if(e.path){if(e.authority){if(!$e.test(e.path))throw Error(`[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character`)}else if(et.test(e.path))throw Error(`[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")`)}}function nt(e,t){return!e&&!t?`file`:e}function rt(e,t){switch(e){case`https`:case`http`:case`file`:t?t[0]!==it&&(t=it+t):t=it;break}return t}const it=`/`,at=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;var R=class e{static isUri(t){return t instanceof e?!0:t?typeof t.authority==`string`&&typeof t.fragment==`string`&&typeof t.path==`string`&&typeof t.query==`string`&&typeof t.scheme==`string`&&typeof t.fsPath==`string`&&typeof t.with==`function`&&typeof t.toString==`function`:!1}constructor(e,t,n,r,i,a=!1){typeof e==`object`?(this.scheme=e.scheme||``,this.authority=e.authority||``,this.path=e.path||``,this.query=e.query||``,this.fragment=e.fragment||``):(this.scheme=nt(e,a),this.authority=t||``,this.path=rt(this.scheme,n||``),this.query=r||``,this.fragment=i||``,tt(this,a))}get fsPath(){return dt(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:r,query:i,fragment:a}=e;return t===void 0?t=this.scheme:t===null&&(t=``),n===void 0?n=this.authority:n===null&&(n=``),r===void 0?r=this.path:r===null&&(r=``),i===void 0?i=this.query:i===null&&(i=``),a===void 0?a=this.fragment:a===null&&(a=``),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&a===this.fragment?this:new st(t,n,r,i,a)}static parse(e,t=!1){let n=at.exec(e);return n?new st(n[2]||``,ht(n[4]||``),ht(n[5]||``),ht(n[7]||``),ht(n[9]||``),t):new st(``,``,``,``,``)}static file(e){let t=``;if(he&&(e=e.replace(/\\/g,`/`)),e[0]===`/`&&e[1]===`/`){let n=e.indexOf(`/`,2);n===-1?(t=e.substring(2),e=`/`):(t=e.substring(2,n),e=e.substring(n)||`/`)}return new st(`file`,t,e,``,``)}static from(e,t){return new st(e.scheme,e.authority,e.path,e.query,e.fragment,t)}static joinPath(t,...n){if(!t.path)throw Error(`[UriError]: cannot call joinPath on URI without path`);let r;return r=he&&t.scheme===`file`?e.file(I.join(dt(t,!0),...n)).path:L.join(t.path,...n),t.with({path:r})}toString(e=!1){return ft(this,e)}toJSON(){return this}static revive(t){if(t){if(t instanceof e)return t;{let e=new st(t);return e._formatted=t.external??null,e._fsPath=t._sep===ot?t.fsPath??null:null,e}}else return t}};const ot=he?1:void 0;var st=class extends R{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||=dt(this,!1),this._fsPath}toString(e=!1){return e?ft(this,!0):(this._formatted||=ft(this,!1),this._formatted)}toJSON(){let e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=ot),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}};const ct={58:`%3A`,47:`%2F`,63:`%3F`,35:`%23`,91:`%5B`,93:`%5D`,64:`%40`,33:`%21`,36:`%24`,38:`%26`,39:`%27`,40:`%28`,41:`%29`,42:`%2A`,43:`%2B`,44:`%2C`,59:`%3B`,61:`%3D`,32:`%20`};function lt(e,t,n){let r,i=-1;for(let a=0;a<e.length;a++){let o=e.charCodeAt(a);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||o===45||o===46||o===95||o===126||t&&o===47||n&&o===91||n&&o===93||n&&o===58)i!==-1&&(r+=encodeURIComponent(e.substring(i,a)),i=-1),r!==void 0&&(r+=e.charAt(a));else{r===void 0&&(r=e.substr(0,a));let t=ct[o];t===void 0?i===-1&&(i=a):(i!==-1&&(r+=encodeURIComponent(e.substring(i,a)),i=-1),r+=t)}}return i!==-1&&(r+=encodeURIComponent(e.substring(i))),r===void 0?e:r}function ut(e){let t;for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);r===35||r===63?(t===void 0&&(t=e.substr(0,n)),t+=ct[r]):t!==void 0&&(t+=e[n])}return t===void 0?e:t}function dt(e,t){let n;return n=e.authority&&e.path.length>1&&e.scheme===`file`?`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,he&&(n=n.replace(/\//g,`\\`)),n}function ft(e,t){let n=t?ut:lt,r=``,{scheme:i,authority:a,path:o,query:s,fragment:c}=e;if(i&&(r+=i,r+=`:`),(a||i===`file`)&&(r+=`/`,r+=`/`),a){let e=a.indexOf(`@`);if(e!==-1){let t=a.substr(0,e);a=a.substr(e+1),e=t.lastIndexOf(`:`),e===-1?r+=n(t,!1,!1):(r+=n(t.substr(0,e),!1,!1),r+=`:`,r+=n(t.substr(e+1),!1,!0)),r+=`@`}a=a.toLowerCase(),e=a.lastIndexOf(`:`),e===-1?r+=n(a,!1,!0):(r+=n(a.substr(0,e),!1,!0),r+=a.substr(e))}if(o){if(o.length>=3&&o.charCodeAt(0)===47&&o.charCodeAt(2)===58){let e=o.charCodeAt(1);e>=65&&e<=90&&(o=`/${String.fromCharCode(e+32)}:${o.substr(3)}`)}else if(o.length>=2&&o.charCodeAt(1)===58){let e=o.charCodeAt(0);e>=65&&e<=90&&(o=`${String.fromCharCode(e+32)}:${o.substr(2)}`)}r+=n(o,!0,!1)}return s&&(r+=`?`,r+=n(s,!1,!1)),c&&(r+=`#`,r+=t?c:lt(c,!1,!1)),r}function pt(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+pt(e.substr(3)):e}}const mt=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function ht(e){return e.match(mt)?e.replace(mt,e=>pt(e)):e}var z;(function(e){e.inMemory=`inmemory`,e.vscode=`vscode`,e.internal=`private`,e.walkThrough=`walkThrough`,e.walkThroughSnippet=`walkThroughSnippet`,e.http=`http`,e.https=`https`,e.file=`file`,e.mailto=`mailto`,e.untitled=`untitled`,e.data=`data`,e.command=`command`,e.vscodeRemote=`vscode-remote`,e.vscodeRemoteResource=`vscode-remote-resource`,e.vscodeManagedRemoteResource=`vscode-managed-remote-resource`,e.vscodeUserData=`vscode-userdata`,e.vscodeCustomEditor=`vscode-custom-editor`,e.vscodeNotebookCell=`vscode-notebook-cell`,e.vscodeNotebookCellMetadata=`vscode-notebook-cell-metadata`,e.vscodeNotebookCellMetadataDiff=`vscode-notebook-cell-metadata-diff`,e.vscodeNotebookCellOutput=`vscode-notebook-cell-output`,e.vscodeNotebookCellOutputDiff=`vscode-notebook-cell-output-diff`,e.vscodeNotebookMetadata=`vscode-notebook-metadata`,e.vscodeInteractiveInput=`vscode-interactive-input`,e.vscodeSettings=`vscode-settings`,e.vscodeWorkspaceTrust=`vscode-workspace-trust`,e.vscodeTerminal=`vscode-terminal`,e.vscodeChatCodeBlock=`vscode-chat-code-block`,e.vscodeChatCodeCompareBlock=`vscode-chat-code-compare-block`,e.vscodeChatSesssion=`vscode-chat-editor`,e.webviewPanel=`webview-panel`,e.vscodeWebview=`vscode-webview`,e.extension=`extension`,e.vscodeFileResource=`vscode-file`,e.tmp=`tmp`,e.vsls=`vsls`,e.vscodeSourceControl=`vscode-scm`,e.commentsInput=`comment`,e.codeSetting=`code-setting`,e.outputChannel=`output`})(z||={});const gt=new class{constructor(){this._hosts=Object.create(null),this._ports=Object.create(null),this._connectionTokens=Object.create(null),this._preferredWebSchema=`http`,this._delegate=null,this._serverRootPath=`/`}setPreferredWebSchema(e){this._preferredWebSchema=e}get _remoteResourcesPath(){return L.join(this._serverRootPath,z.vscodeRemoteResource)}rewrite(e){if(this._delegate)try{return this._delegate(e)}catch(n){return t(n),e}let n=e.authority,r=this._hosts[n];r&&r.indexOf(`:`)!==-1&&r.indexOf(`[`)===-1&&(r=`[${r}]`);let i=this._ports[n],a=this._connectionTokens[n],o=`path=${encodeURIComponent(e.path)}`;return typeof a==`string`&&(o+=`&tkn=${encodeURIComponent(a)}`),R.from({scheme:ve?this._preferredWebSchema:z.vscodeRemoteResource,authority:`${r}:${i}`,path:this._remoteResourcesPath,query:o})}},_t=new class e{static{this.FALLBACK_AUTHORITY=`vscode-app`}asBrowserUri(e){let t=(this.toUri(e));return this.uriToBrowserUri(t)}uriToBrowserUri(t){return t.scheme===z.vscodeRemote?gt.rewrite(t):t.scheme===z.file&&(_e||ye===`${z.vscodeFileResource}://${e.FALLBACK_AUTHORITY}`)?t.with({scheme:z.vscodeFileResource,authority:t.authority||e.FALLBACK_AUTHORITY,query:null,fragment:null}):t}toUri(e,t){if(R.isUri(e))return e;if(globalThis._VSCODE_FILE_ROOT){let t=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(t))return R.joinPath(R.parse(t,!0),e);let n=(Ze(t,e));return R.file(n)}return R.parse(t.toUrl(e))}};var vt;(function(e){let t=new Map([[`1`,{"Cross-Origin-Opener-Policy":`same-origin`}],[`2`,{"Cross-Origin-Embedder-Policy":`require-corp`}],[`3`,{"Cross-Origin-Opener-Policy":`same-origin`,"Cross-Origin-Embedder-Policy":`require-corp`}]]);e.CoopAndCoep=Object.freeze(t.get(`3`));let n=`vscode-coi`;function r(e){let r;typeof e==`string`?r=new URL(e).searchParams:e instanceof URL?r=e.searchParams:R.isUri(e)&&(r=new URL(e.toString(!0)).searchParams);let i=r?.get(n);if(i)return t.get(i)}e.getHeadersFromQuery=r;function i(e,t,r){if(!globalThis.crossOriginIsolated)return;let i=t&&r?`3`:r?`2`:`1`;e instanceof URLSearchParams?e.set(n,i):e[n]=i}e.addSearchParam=i})(vt||={});const yt=`default`;var bt=class{constructor(e,t,n,r,i){this.vsWorker=e,this.req=t,this.channel=n,this.method=r,this.args=i,this.type=0}},xt=class{constructor(e,t,n,r){this.vsWorker=e,this.seq=t,this.res=n,this.err=r,this.type=1}},St=class{constructor(e,t,n,r,i){this.vsWorker=e,this.req=t,this.channel=n,this.eventName=r,this.arg=i,this.type=2}},Ct=class{constructor(e,t,n){this.vsWorker=e,this.req=t,this.event=n,this.type=3}},wt=class{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}},Tt=class{constructor(e){this._workerId=-1,this._handler=e,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(e){this._workerId=e}sendMessage(e,t,n){let r=String(++this._lastSentReq);return new Promise((i,a)=>{this._pendingReplies[r]={resolve:i,reject:a},this._send(new bt(this._workerId,r,e,t,n))})}listen(e,t,n){let r=null,i=new E({onWillAddFirstListener:()=>{r=String(++this._lastSentReq),this._pendingEmitters.set(r,i),this._send(new St(this._workerId,r,e,t,n))},onDidRemoveLastListener:()=>{this._pendingEmitters.delete(r),this._send(new wt(this._workerId,r)),r=null}});return i.event}handleMessage(e){!e||!e.vsWorker||this._workerId!==-1&&e.vsWorker!==this._workerId||this._handleMessage(e)}createProxyToRemoteChannel(e,t){return new Proxy(Object.create(null),{get:(n,r)=>(typeof r==`string`&&!n[r]&&(Dt(r)?n[r]=t=>this.listen(e,r,t):Et(r)?n[r]=this.listen(e,r,void 0):r.charCodeAt(0)===36&&(n[r]=async(...n)=>(await t?.(),this.sendMessage(e,r,n)))),n[r])})}_handleMessage(e){switch(e.type){case 1:return this._handleReplyMessage(e);case 0:return this._handleRequestMessage(e);case 2:return this._handleSubscribeEventMessage(e);case 3:return this._handleEventMessage(e);case 4:return this._handleUnsubscribeEventMessage(e)}}_handleReplyMessage(e){if(!this._pendingReplies[e.seq]){console.warn(`Got reply to unknown seq`);return}let t=this._pendingReplies[e.seq];if(delete this._pendingReplies[e.seq],e.err){let n=e.err;e.err.$isError&&(n=Error(),n.name=e.err.name,n.message=e.err.message,n.stack=e.err.stack),t.reject(n);return}t.resolve(e.res)}_handleRequestMessage(e){let t=e.req;this._handler.handleMessage(e.channel,e.method,e.args).then(e=>{this._send(new xt(this._workerId,t,e,void 0))},e=>{e.detail instanceof Error&&(e.detail=n(e.detail)),this._send(new xt(this._workerId,t,void 0,n(e)))})}_handleSubscribeEventMessage(e){let t=e.req,n=this._handler.handleEvent(e.channel,e.eventName,e.arg)(e=>{this._send(new Ct(this._workerId,t,e))});this._pendingEvents.set(t,n)}_handleEventMessage(e){if(!this._pendingEmitters.has(e.req)){console.warn(`Got event for unknown req`);return}this._pendingEmitters.get(e.req).fire(e.event)}_handleUnsubscribeEventMessage(e){if(!this._pendingEvents.has(e.req)){console.warn(`Got unsubscribe for unknown req`);return}this._pendingEvents.get(e.req).dispose(),this._pendingEvents.delete(e.req)}_send(e){let t=[];if(e.type===0)for(let n=0;n<e.args.length;n++)e.args[n]instanceof ArrayBuffer&&t.push(e.args[n]);else e.type===1&&e.res instanceof ArrayBuffer&&t.push(e.res);this._handler.sendMessage(e,t)}};function Et(e){return e[0]===`o`&&e[1]===`n`&&ke(e.charCodeAt(2))}function Dt(e){return/^onDynamic/.test(e)&&ke(e.charCodeAt(9))}var Ot=class{constructor(e,t){this._localChannels=new Map,this._remoteChannels=new Map,this._requestHandlerFactory=t,this._requestHandler=null,this._protocol=new Tt({sendMessage:(t,n)=>{e(t,n)},handleMessage:(e,t,n)=>this._handleMessage(e,t,n),handleEvent:(e,t,n)=>this._handleEvent(e,t,n)})}onmessage(e){this._protocol.handleMessage(e)}_handleMessage(e,t,n){if(e===yt&&t===`$initialize`)return this.initialize(n[0],n[1],n[2]);let r=e===yt?this._requestHandler:this._localChannels.get(e);if(!r)return Promise.reject(Error(`Missing channel ${e} on worker thread`));if(typeof r[t]!=`function`)return Promise.reject(Error(`Missing method ${t} on worker thread channel ${e}`));try{return Promise.resolve(r[t].apply(r,n))}catch(e){return Promise.reject(e)}}_handleEvent(e,t,n){let r=e===yt?this._requestHandler:this._localChannels.get(e);if(!r)throw Error(`Missing channel ${e} on worker thread`);if(Dt(t)){let e=r[t].call(r,n);if(typeof e!=`function`)throw Error(`Missing dynamic event ${t} on request handler.`);return e}if(Et(t)){let e=r[t];if(typeof e!=`function`)throw Error(`Missing event ${t} on request handler.`);return e}throw Error(`Malformed event name ${t}`)}getChannel(e){if(!this._remoteChannels.has(e)){let t=this._protocol.createProxyToRemoteChannel(e);this._remoteChannels.set(e,t)}return this._remoteChannels.get(e)}async initialize(e,t,n){if(this._protocol.setWorkerId(e),this._requestHandlerFactory){this._requestHandler=this._requestHandlerFactory(this);return}return t&&(t.baseUrl!==void 0&&delete t.baseUrl,t.paths!==void 0&&t.paths.vs!==void 0&&delete t.paths.vs,t.trustedTypesPolicy!==void 0&&delete t.trustedTypesPolicy,t.catchError=!0,globalThis.require.config(t)),import(`${_t.asBrowserUri(`${n}.js`).toString(!0)}`).then(e=>{if(this._requestHandler=e.create(this),!this._requestHandler)throw Error(`No RequestHandler!`)})}},B=class{constructor(e,t,n,r){this.originalStart=e,this.originalLength=t,this.modifiedStart=n,this.modifiedLength=r}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}};function kt(e,t){return(t<<5)-t+e|0}function At(e,t){t=kt(149417,t);for(let n=0,r=e.length;n<r;n++)t=kt(e.charCodeAt(n),t);return t}function jt(e,t,n=32){let r=n-t,i=~((1<<r)-1);return(e<<t|(i&e)>>>r)>>>0}function Mt(e,t=0,n=e.byteLength,r=0){for(let i=0;i<n;i++)e[t+i]=r}function Nt(e,t,n=`0`){for(;e.length<t;)e=n+e;return e}function Pt(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map(e=>e.toString(16).padStart(2,`0`)).join(``):Nt((e>>>0).toString(16),t/4)}(class e{static{this._bigBlock32=new DataView(new ArrayBuffer(320))}constructor(){this._h0=1732584193,this._h1=4023233417,this._h2=2562383102,this._h3=271733878,this._h4=3285377520,this._buff=new Uint8Array(67),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}update(e){let t=e.length;if(t===0)return;let n=this._buff,r=this._buffLen,i=this._leftoverHighSurrogate,a,o;for(i===0?(a=e.charCodeAt(0),o=0):(a=i,o=-1,i=0);;){let s=a;if(Ae(a))if(o+1<t){let t=e.charCodeAt(o+1);je(t)?(o++,s=Me(a,t)):s=65533}else{i=a;break}else je(a)&&(s=65533);if(r=this._push(n,r,s),o++,o<t)a=e.charCodeAt(o);else break}this._buffLen=r,this._leftoverHighSurrogate=i}_push(e,t,n){return n<128?e[t++]=n:n<2048?(e[t++]=192|(n&1984)>>>6,e[t++]=128|(n&63)>>>0):n<65536?(e[t++]=224|(n&61440)>>>12,e[t++]=128|(n&4032)>>>6,e[t++]=128|(n&63)>>>0):(e[t++]=240|(n&1835008)>>>18,e[t++]=128|(n&258048)>>>12,e[t++]=128|(n&4032)>>>6,e[t++]=128|(n&63)>>>0),t>=64&&(this._step(),t-=64,this._totalLen+=64,e[0]=e[64],e[1]=e[65],e[2]=e[66]),t}digest(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),Pt(this._h0)+Pt(this._h1)+Pt(this._h2)+Pt(this._h3)+Pt(this._h4)}_wrapUp(){this._buff[this._buffLen++]=128,Mt(this._buff,this._buffLen),this._buffLen>56&&(this._step(),Mt(this._buff));let e=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(e/4294967296),!1),this._buffDV.setUint32(60,e%4294967296,!1),this._step()}_step(){let t=e._bigBlock32,n=this._buffDV;for(let e=0;e<64;e+=4)t.setUint32(e,n.getUint32(e,!1),!1);for(let e=64;e<320;e+=4)t.setUint32(e,jt(t.getUint32(e-12,!1)^t.getUint32(e-32,!1)^t.getUint32(e-56,!1)^t.getUint32(e-64,!1),1),!1);let r=this._h0,i=this._h1,a=this._h2,o=this._h3,s=this._h4,c,l,u;for(let e=0;e<80;e++)e<20?(c=i&a|~i&o,l=1518500249):e<40?(c=i^a^o,l=1859775393):e<60?(c=i&a|i&o|a&o,l=2400959708):(c=i^a^o,l=3395469782),u=jt(r,5)+c+s+l+t.getUint32(e*4,!1)&4294967295,s=o,o=a,a=jt(i,30),i=r,r=u;this._h0=this._h0+r&4294967295,this._h1=this._h1+i&4294967295,this._h2=this._h2+a&4294967295,this._h3=this._h3+o&4294967295,this._h4=this._h4+s&4294967295}});var Ft=class{constructor(e){this.source=e}getElements(){let e=this.source,t=new Int32Array(e.length);for(let n=0,r=e.length;n<r;n++)t[n]=e.charCodeAt(n);return t}};function It(e,t,n){return new Bt(new Ft(e),new Ft(t)).ComputeDiff(n).changes}var Lt=class{static Assert(e,t){if(!e)throw Error(t)}},Rt=class{static Copy(e,t,n,r,i){for(let a=0;a<i;a++)n[r+a]=e[t+a]}static Copy2(e,t,n,r,i){for(let a=0;a<i;a++)n[r+a]=e[t+a]}},zt=class{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new B(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_originalCount++}AddModifiedElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}},Bt=class e{constructor(t,n,r=null){this.ContinueProcessingPredicate=r,this._originalSequence=t,this._modifiedSequence=n;let[i,a,o]=e._getElements(t),[s,c,l]=e._getElements(n);this._hasStrings=o&&l,this._originalStringElements=i,this._originalElementsOrHash=a,this._modifiedStringElements=s,this._modifiedElementsOrHash=c,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(e){return e.length>0&&typeof e[0]==`string`}static _getElements(t){let n=t.getElements();if(e._isStringArray(n)){let e=new Int32Array(n.length);for(let t=0,r=n.length;t<r;t++)e[t]=At(n[t],0);return[n,e,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}ElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._modifiedElementsOrHash[t]?this._hasStrings?this._originalStringElements[e]===this._modifiedStringElements[t]:!0:!1}ElementsAreStrictEqual(t,n){if(!this.ElementsAreEqual(t,n))return!1;let r=e._getStrictElement(this._originalSequence,t),i=e._getStrictElement(this._modifiedSequence,n);return r===i}static _getStrictElement(e,t){return typeof e.getStrictElement==`function`?e.getStrictElement(t):null}OriginalElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._originalElementsOrHash[t]?this._hasStrings?this._originalStringElements[e]===this._originalStringElements[t]:!0:!1}ModifiedElementsAreEqual(e,t){return this._modifiedElementsOrHash[e]===this._modifiedElementsOrHash[t]?this._hasStrings?this._modifiedStringElements[e]===this._modifiedStringElements[t]:!0:!1}ComputeDiff(e){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,e)}_ComputeDiff(e,t,n,r,i){let a=[!1],o=this.ComputeDiffRecursive(e,t,n,r,a);return i&&(o=this.PrettifyChanges(o)),{quitEarly:a[0],changes:o}}ComputeDiffRecursive(e,t,n,r,i){for(i[0]=!1;e<=t&&n<=r&&this.ElementsAreEqual(e,n);)e++,n++;for(;t>=e&&r>=n&&this.ElementsAreEqual(t,r);)t--,r--;if(e>t||n>r){let i;return n<=r?(Lt.Assert(e===t+1,`originalStart should only be one more than originalEnd`),i=[new B(e,0,n,r-n+1)]):e<=t?(Lt.Assert(n===r+1,`modifiedStart should only be one more than modifiedEnd`),i=[new B(e,t-e+1,n,0)]):(Lt.Assert(e===t+1,`originalStart should only be one more than originalEnd`),Lt.Assert(n===r+1,`modifiedStart should only be one more than modifiedEnd`),i=[]),i}let a=[0],o=[0],s=this.ComputeRecursionPoint(e,t,n,r,a,o,i),c=a[0],l=o[0];if(s!==null)return s;if(!i[0]){let a=this.ComputeDiffRecursive(e,c,n,l,i),o=[];return o=i[0]?[new B(c+1,t-(c+1)+1,l+1,r-(l+1)+1)]:this.ComputeDiffRecursive(c+1,t,l+1,r,i),this.ConcatenateChanges(a,o)}return[new B(e,t-e+1,n,r-n+1)]}WALKTRACE(e,t,n,r,i,a,o,s,c,l,u,d,f,p,m,h,g,_){let v=null,y=null,b=new zt,x=t,S=n,C=f[0]-h[0]-r,w=-1073741824,T=this.m_forwardHistory.length-1;do{let t=C+e;t===x||t<S&&c[t-1]<c[t+1]?(u=c[t+1],p=u-C-r,u<w&&b.MarkNextChange(),w=u,b.AddModifiedElement(u+1,p),C=t+1-e):(u=c[t-1]+1,p=u-C-r,u<w&&b.MarkNextChange(),w=u-1,b.AddOriginalElement(u,p+1),C=t-1-e),T>=0&&(c=this.m_forwardHistory[T],e=c[0],x=1,S=c.length-1)}while(--T>=-1);if(v=b.getReverseChanges(),_[0]){let e=f[0]+1,t=h[0]+1;if(v!==null&&v.length>0){let n=v[v.length-1];e=Math.max(e,n.getOriginalEnd()),t=Math.max(t,n.getModifiedEnd())}y=[new B(e,d-e+1,t,m-t+1)]}else{b=new zt,x=a,S=o,C=f[0]-h[0]-s,w=1073741824,T=g?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{let e=C+i;e===x||e<S&&l[e-1]>=l[e+1]?(u=l[e+1]-1,p=u-C-s,u>w&&b.MarkNextChange(),w=u+1,b.AddOriginalElement(u+1,p+1),C=e+1-i):(u=l[e-1],p=u-C-s,u>w&&b.MarkNextChange(),w=u,b.AddModifiedElement(u+1,p+1),C=e-1-i),T>=0&&(l=this.m_reverseHistory[T],i=l[0],x=1,S=l.length-1)}while(--T>=-1);y=b.getChanges()}return this.ConcatenateChanges(v,y)}ComputeRecursionPoint(e,t,n,r,i,a,o){let s=0,c=0,l=0,u=0,d=0,f=0;e--,n--,i[0]=0,a[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];let p=t-e+(r-n),m=p+1,h=new Int32Array(m),g=new Int32Array(m),_=r-n,v=t-e,y=e-n,b=t-r,x=(v-_)%2==0;h[_]=e,g[v]=t,o[0]=!1;for(let S=1;S<=p/2+1;S++){let p=0,C=0;l=this.ClipDiagonalBound(_-S,S,_,m),u=this.ClipDiagonalBound(_+S,S,_,m);for(let e=l;e<=u;e+=2){s=e===l||e<u&&h[e-1]<h[e+1]?h[e+1]:h[e-1]+1,c=s-(e-_)-y;let n=s;for(;s<t&&c<r&&this.ElementsAreEqual(s+1,c+1);)s++,c++;if(h[e]=s,s+c>p+C&&(p=s,C=c),!x&&Math.abs(e-v)<=S-1&&s>=g[e])return i[0]=s,a[0]=c,n<=g[e]&&S<=1448?this.WALKTRACE(_,l,u,y,v,d,f,b,h,g,s,t,i,c,r,a,x,o):null}let w=(p-e+(C-n)-S)/2;if(this.ContinueProcessingPredicate!==null&&!this.ContinueProcessingPredicate(p,w))return o[0]=!0,i[0]=p,a[0]=C,w>0&&S<=1448?this.WALKTRACE(_,l,u,y,v,d,f,b,h,g,s,t,i,c,r,a,x,o):(e++,n++,[new B(e,t-e+1,n,r-n+1)]);d=this.ClipDiagonalBound(v-S,S,v,m),f=this.ClipDiagonalBound(v+S,S,v,m);for(let p=d;p<=f;p+=2){s=p===d||p<f&&g[p-1]>=g[p+1]?g[p+1]-1:g[p-1],c=s-(p-v)-b;let m=s;for(;s>e&&c>n&&this.ElementsAreEqual(s,c);)s--,c--;if(g[p]=s,x&&Math.abs(p-_)<=S&&s<=h[p])return i[0]=s,a[0]=c,m>=h[p]&&S<=1448?this.WALKTRACE(_,l,u,y,v,d,f,b,h,g,s,t,i,c,r,a,x,o):null}if(S<=1447){let e=new Int32Array(u-l+2);e[0]=_-l+1,Rt.Copy2(h,l,e,1,u-l+1),this.m_forwardHistory.push(e),e=new Int32Array(f-d+2),e[0]=v-d+1,Rt.Copy2(g,d,e,1,f-d+1),this.m_reverseHistory.push(e)}}return this.WALKTRACE(_,l,u,y,v,d,f,b,h,g,s,t,i,c,r,a,x,o)}PrettifyChanges(e){for(let t=0;t<e.length;t++){let n=e[t],r=t<e.length-1?e[t+1].originalStart:this._originalElementsOrHash.length,i=t<e.length-1?e[t+1].modifiedStart:this._modifiedElementsOrHash.length,a=n.originalLength>0,o=n.modifiedLength>0;for(;n.originalStart+n.originalLength<r&&n.modifiedStart+n.modifiedLength<i&&(!a||this.OriginalElementsAreEqual(n.originalStart,n.originalStart+n.originalLength))&&(!o||this.ModifiedElementsAreEqual(n.modifiedStart,n.modifiedStart+n.modifiedLength));){let e=this.ElementsAreStrictEqual(n.originalStart,n.modifiedStart);if(this.ElementsAreStrictEqual(n.originalStart+n.originalLength,n.modifiedStart+n.modifiedLength)&&!e)break;n.originalStart++,n.modifiedStart++}let s=[null];if(t<e.length-1&&this.ChangesOverlap(e[t],e[t+1],s)){e[t]=s[0],e.splice(t+1,1),t--;continue}}for(let t=e.length-1;t>=0;t--){let n=e[t],r=0,i=0;if(t>0){let n=e[t-1];r=n.originalStart+n.originalLength,i=n.modifiedStart+n.modifiedLength}let a=n.originalLength>0,o=n.modifiedLength>0,s=0,c=this._boundaryScore(n.originalStart,n.originalLength,n.modifiedStart,n.modifiedLength);for(let e=1;;e++){let t=n.originalStart-e,l=n.modifiedStart-e;if(t<r||l<i||a&&!this.OriginalElementsAreEqual(t,t+n.originalLength)||o&&!this.ModifiedElementsAreEqual(l,l+n.modifiedLength))break;let u=(t===r&&l===i?5:0)+this._boundaryScore(t,n.originalLength,l,n.modifiedLength);u>c&&(c=u,s=e)}n.originalStart-=s,n.modifiedStart-=s;let l=[null];if(t>0&&this.ChangesOverlap(e[t-1],e[t],l)){e[t-1]=l[0],e.splice(t,1),t++;continue}}if(this._hasStrings)for(let t=1,n=e.length;t<n;t++){let n=e[t-1],r=e[t],i=r.originalStart-n.originalStart-n.originalLength,a=n.originalStart,o=r.originalStart+r.originalLength,s=o-a,c=n.modifiedStart,l=r.modifiedStart+r.modifiedLength,u=l-c;if(i<5&&s<20&&u<20){let e=this._findBetterContiguousSequence(a,s,c,u,i);if(e){let[t,a]=e;(t!==n.originalStart+n.originalLength||a!==n.modifiedStart+n.modifiedLength)&&(n.originalLength=t-n.originalStart,n.modifiedLength=a-n.modifiedStart,r.originalStart=t+i,r.modifiedStart=a+i,r.originalLength=o-r.originalStart,r.modifiedLength=l-r.modifiedStart)}}}return e}_findBetterContiguousSequence(e,t,n,r,i){if(t<i||r<i)return null;let a=e+t-i+1,o=n+r-i+1,s=0,c=0,l=0;for(let t=e;t<a;t++)for(let e=n;e<o;e++){let n=this._contiguousSequenceScore(t,e,i);n>0&&n>s&&(s=n,c=t,l=e)}return s>0?[c,l]:null}_contiguousSequenceScore(e,t,n){let r=0;for(let i=0;i<n;i++){if(!this.ElementsAreEqual(e+i,t+i))return 0;r+=this._originalStringElements[e+i].length}return r}_OriginalIsBoundary(e){return e<=0||e>=this._originalElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._originalStringElements[e])}_OriginalRegionIsBoundary(e,t){if(this._OriginalIsBoundary(e)||this._OriginalIsBoundary(e-1))return!0;if(t>0){let n=e+t;if(this._OriginalIsBoundary(n-1)||this._OriginalIsBoundary(n))return!0}return!1}_ModifiedIsBoundary(e){return e<=0||e>=this._modifiedElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[e])}_ModifiedRegionIsBoundary(e,t){if(this._ModifiedIsBoundary(e)||this._ModifiedIsBoundary(e-1))return!0;if(t>0){let n=e+t;if(this._ModifiedIsBoundary(n-1)||this._ModifiedIsBoundary(n))return!0}return!1}_boundaryScore(e,t,n,r){let i=this._OriginalRegionIsBoundary(e,t)?1:0,a=this._ModifiedRegionIsBoundary(n,r)?1:0;return i+a}ConcatenateChanges(e,t){let n=[];if(e.length===0||t.length===0)return t.length>0?t:e;if(this.ChangesOverlap(e[e.length-1],t[0],n)){let r=Array(e.length+t.length-1);return Rt.Copy(e,0,r,0,e.length-1),r[e.length-1]=n[0],Rt.Copy(t,1,r,e.length,t.length-1),r}else{let n=Array(e.length+t.length);return Rt.Copy(e,0,n,0,e.length),Rt.Copy(t,0,n,e.length,t.length),n}}ChangesOverlap(e,t,n){if(Lt.Assert(e.originalStart<=t.originalStart,`Left change is not less than or equal to right change`),Lt.Assert(e.modifiedStart<=t.modifiedStart,`Left change is not less than or equal to right change`),e.originalStart+e.originalLength>=t.originalStart||e.modifiedStart+e.modifiedLength>=t.modifiedStart){let r=e.originalStart,i=e.originalLength,a=e.modifiedStart,o=e.modifiedLength;return e.originalStart+e.originalLength>=t.originalStart&&(i=t.originalStart+t.originalLength-e.originalStart),e.modifiedStart+e.modifiedLength>=t.modifiedStart&&(o=t.modifiedStart+t.modifiedLength-e.modifiedStart),n[0]=new B(r,i,a,o),!0}else return n[0]=null,!1}ClipDiagonalBound(e,t,n,r){if(e>=0&&e<r)return e;let i=n,a=r-n-1,o=t%2==0;if(e<0){let e=i%2==0;return o===e?0:1}else{let e=a%2==0;return o===e?r-1:r-2}}},V=class e{constructor(e,t){this.lineNumber=e,this.column=t}with(t=this.lineNumber,n=this.column){return t===this.lineNumber&&n===this.column?this:new e(t,n)}delta(e=0,t=0){return this.with(this.lineNumber+e,this.column+t)}equals(t){return e.equals(this,t)}static equals(e,t){return!e&&!t?!0:!!e&&!!t&&e.lineNumber===t.lineNumber&&e.column===t.column}isBefore(t){return e.isBefore(this,t)}static isBefore(e,t){return e.lineNumber<t.lineNumber?!0:t.lineNumber<e.lineNumber?!1:e.column<t.column}isBeforeOrEqual(t){return e.isBeforeOrEqual(this,t)}static isBeforeOrEqual(e,t){return e.lineNumber<t.lineNumber?!0:t.lineNumber<e.lineNumber?!1:e.column<=t.column}static compare(e,t){let n=e.lineNumber|0,r=t.lineNumber|0;if(n===r){let n=e.column|0,r=t.column|0;return n-r}return n-r}clone(){return new e(this.lineNumber,this.column)}toString(){return`(`+this.lineNumber+`,`+this.column+`)`}static lift(t){return new e(t.lineNumber,t.column)}static isIPosition(e){return e&&typeof e.lineNumber==`number`&&typeof e.column==`number`}toJSON(){return{lineNumber:this.lineNumber,column:this.column}}},H=class e{constructor(e,t,n,r){e>n||e===n&&t>r?(this.startLineNumber=n,this.startColumn=r,this.endLineNumber=e,this.endColumn=t):(this.startLineNumber=e,this.startColumn=t,this.endLineNumber=n,this.endColumn=r)}isEmpty(){return e.isEmpty(this)}static isEmpty(e){return e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn}containsPosition(t){return e.containsPosition(this,t)}static containsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber||t.lineNumber===e.startLineNumber&&t.column<e.startColumn||t.lineNumber===e.endLineNumber&&t.column>e.endColumn)}static strictContainsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber||t.lineNumber===e.startLineNumber&&t.column<=e.startColumn||t.lineNumber===e.endLineNumber&&t.column>=e.endColumn)}containsRange(t){return e.containsRange(this,t)}static containsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber||t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber||t.startLineNumber===e.startLineNumber&&t.startColumn<e.startColumn||t.endLineNumber===e.endLineNumber&&t.endColumn>e.endColumn)}strictContainsRange(t){return e.strictContainsRange(this,t)}static strictContainsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber||t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber||t.startLineNumber===e.startLineNumber&&t.startColumn<=e.startColumn||t.endLineNumber===e.endLineNumber&&t.endColumn>=e.endColumn)}plusRange(t){return e.plusRange(this,t)}static plusRange(t,n){let r,i,a,o;return n.startLineNumber<t.startLineNumber?(r=n.startLineNumber,i=n.startColumn):n.startLineNumber===t.startLineNumber?(r=n.startLineNumber,i=Math.min(n.startColumn,t.startColumn)):(r=t.startLineNumber,i=t.startColumn),n.endLineNumber>t.endLineNumber?(a=n.endLineNumber,o=n.endColumn):n.endLineNumber===t.endLineNumber?(a=n.endLineNumber,o=Math.max(n.endColumn,t.endColumn)):(a=t.endLineNumber,o=t.endColumn),new e(r,i,a,o)}intersectRanges(t){return e.intersectRanges(this,t)}static intersectRanges(t,n){let r=t.startLineNumber,i=t.startColumn,a=t.endLineNumber,o=t.endColumn,s=n.startLineNumber,c=n.startColumn,l=n.endLineNumber,u=n.endColumn;return r<s?(r=s,i=c):r===s&&(i=Math.max(i,c)),a>l?(a=l,o=u):a===l&&(o=Math.min(o,u)),r>a||r===a&&i>o?null:new e(r,i,a,o)}equalsRange(t){return e.equalsRange(this,t)}static equalsRange(e,t){return!e&&!t?!0:!!e&&!!t&&e.startLineNumber===t.startLineNumber&&e.startColumn===t.startColumn&&e.endLineNumber===t.endLineNumber&&e.endColumn===t.endColumn}getEndPosition(){return e.getEndPosition(this)}static getEndPosition(e){return new V(e.endLineNumber,e.endColumn)}getStartPosition(){return e.getStartPosition(this)}static getStartPosition(e){return new V(e.startLineNumber,e.startColumn)}toString(){return`[`+this.startLineNumber+`,`+this.startColumn+` -> `+this.endLineNumber+`,`+this.endColumn+`]`}setEndPosition(t,n){return new e(this.startLineNumber,this.startColumn,t,n)}setStartPosition(t,n){return new e(t,n,this.endLineNumber,this.endColumn)}collapseToStart(){return e.collapseToStart(this)}static collapseToStart(t){return new e(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}collapseToEnd(){return e.collapseToEnd(this)}static collapseToEnd(t){return new e(t.endLineNumber,t.endColumn,t.endLineNumber,t.endColumn)}delta(t){return new e(this.startLineNumber+t,this.startColumn,this.endLineNumber+t,this.endColumn)}static fromPositions(t,n=t){return new e(t.lineNumber,t.column,n.lineNumber,n.column)}static lift(t){return t?new e(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(e){return e&&typeof e.startLineNumber==`number`&&typeof e.startColumn==`number`&&typeof e.endLineNumber==`number`&&typeof e.endColumn==`number`}static areIntersectingOrTouching(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<t.startColumn||t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<e.startColumn)}static areIntersecting(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<=t.startColumn||t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<=e.startColumn)}static compareRangesUsingStarts(e,t){if(e&&t){let n=e.startLineNumber|0,r=t.startLineNumber|0;if(n===r){let n=e.startColumn|0,r=t.startColumn|0;if(n===r){let n=e.endLineNumber|0,r=t.endLineNumber|0;if(n===r){let n=e.endColumn|0,r=t.endColumn|0;return n-r}return n-r}return n-r}return n-r}return(e?1:0)-(t?1:0)}static compareRangesUsingEnds(e,t){return e.endLineNumber===t.endLineNumber?e.endColumn===t.endColumn?e.startLineNumber===t.startLineNumber?e.startColumn-t.startColumn:e.startLineNumber-t.startLineNumber:e.endColumn-t.endColumn:e.endLineNumber-t.endLineNumber}static spansMultipleLines(e){return e.endLineNumber>e.startLineNumber}toJSON(){return this}};function Vt(e){return e<0?0:e>255?255:e|0}function Ht(e){return e<0?0:e>4294967295?4294967295:e|0}var Ut=class e{constructor(t){let n=Vt(t);this._defaultValue=n,this._asciiMap=e._createAsciiMap(n),this._map=new Map}static _createAsciiMap(e){let t=new Uint8Array(256);return t.fill(e),t}set(e,t){let n=Vt(t);e>=0&&e<256?this._asciiMap[e]=n:this._map.set(e,n)}get(e){return e>=0&&e<256?this._asciiMap[e]:this._map.get(e)||this._defaultValue}clear(){this._asciiMap.fill(this._defaultValue),this._map.clear()}},Wt=class{constructor(e,t,n){let r=new Uint8Array(e*t);for(let i=0,a=e*t;i<a;i++)r[i]=n;this._data=r,this.rows=e,this.cols=t}get(e,t){return this._data[e*this.cols+t]}set(e,t,n){this._data[e*this.cols+t]=n}},Gt=class{constructor(e){let t=0,n=0;for(let r=0,i=e.length;r<i;r++){let[i,a,o]=e[r];a>t&&(t=a),i>n&&(n=i),o>n&&(n=o)}t++,n++;let r=new Wt(n,t,0);for(let t=0,n=e.length;t<n;t++){let[n,i,a]=e[t];r.set(n,i,a)}this._states=r,this._maxCharCode=t}nextState(e,t){return t<0||t>=this._maxCharCode?0:this._states.get(e,t)}};let Kt=null;function qt(){return Kt===null&&(Kt=new Gt([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),Kt}let Jt=null;function Yt(){if(Jt===null){Jt=new Ut(0);for(let e=0;e<35;e++)Jt.set(` 	<>'"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…`.charCodeAt(e),1);for(let e=0;e<4;e++)Jt.set(`.,;:`.charCodeAt(e),2)}return Jt}var Xt=class e{static _createLink(e,t,n,r,i){let a=i-1;do{let n=t.charCodeAt(a);if(e.get(n)!==2)break;a--}while(a>r);if(r>0){let e=t.charCodeAt(r-1),n=t.charCodeAt(a);(e===40&&n===41||e===91&&n===93||e===123&&n===125)&&a--}return{range:{startLineNumber:n,startColumn:r+1,endLineNumber:n,endColumn:a+2},url:t.substring(r,a+1)}}static computeLinks(t,n=qt()){let r=Yt(),i=[];for(let a=1,o=t.getLineCount();a<=o;a++){let o=t.getLineContent(a),s=o.length,c=0,l=0,u=0,d=1,f=!1,p=!1,m=!1,h=!1;for(;c<s;){let t=!1,s=o.charCodeAt(c);if(d===13){let n;switch(s){case 40:f=!0,n=0;break;case 41:n=f?0:1;break;case 91:m=!0,p=!0,n=0;break;case 93:m=!1,n=p?0:1;break;case 123:h=!0,n=0;break;case 125:n=h?0:1;break;case 39:case 34:case 96:n=u===s?1:u===39||u===34||u===96?0:1;break;case 42:n=u===42?1:0;break;case 124:n=u===124?1:0;break;case 32:n=m?0:1;break;default:n=r.get(s)}n===1&&(i.push(e._createLink(r,o,a,l,c)),t=!0)}else if(d===12){let e;s===91?(p=!0,e=0):e=r.get(s),e===1?t=!0:d=13}else d=n.nextState(d,s),d===0&&(t=!0);t&&(d=1,f=!1,p=!1,h=!1,l=c+1,u=s),c++}d===13&&i.push(e._createLink(r,o,a,l,s))}return i}};function Zt(e){return!e||typeof e.getLineCount!=`function`||typeof e.getLineContent!=`function`?[]:Xt.computeLinks(e)}var Qt=class e{constructor(){this._defaultValueSet=[[`true`,`false`],[`True`,`False`],[`Private`,`Public`,`Friend`,`ReadOnly`,`Partial`,`Protected`,`WriteOnly`],[`public`,`protected`,`private`]]}static{this.INSTANCE=new e}navigateValueSet(e,t,n,r,i){if(e&&t){let n=this.doNavigateValueSet(t,i);if(n)return{range:e,value:n}}if(n&&r){let e=this.doNavigateValueSet(r,i);if(e)return{range:n,value:e}}return null}doNavigateValueSet(e,t){let n=this.numberReplace(e,t);return n===null?this.textReplace(e,t):n}numberReplace(e,t){let n=10**(e.length-(e.lastIndexOf(`.`)+1)),r=Number(e),i=parseFloat(e);return!isNaN(r)&&!isNaN(i)&&r===i?r===0&&!t?null:(r=Math.floor(r*n),r+=t?n:-n,String(r/n)):null}textReplace(e,t){return this.valueSetsReplace(this._defaultValueSet,e,t)}valueSetsReplace(e,t,n){let r=null;for(let i=0,a=e.length;r===null&&i<a;i++)r=this.valueSetReplace(e[i],t,n);return r}valueSetReplace(e,t,n){let r=e.indexOf(t);return r>=0?(r+=n?1:-1,r<0?r=e.length-1:r%=e.length,e[r]):null}};const $t=Object.freeze(function(e,t){let n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}});var en;(function(e){function t(t){return t===e.None||t===e.Cancelled||t instanceof tn?!0:!t||typeof t!=`object`?!1:typeof t.isCancellationRequested==`boolean`&&typeof t.onCancellationRequested==`function`}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:S.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:$t})})(en||={});var tn=class{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?$t:(this._emitter||=new E,this._emitter.event)}dispose(){this._emitter&&=(this._emitter.dispose(),null)}},nn=class{constructor(e){this._token=void 0,this._parentListener=void 0,this._parentListener=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this._token||=new tn,this._token}cancel(){this._token?this._token instanceof tn&&this._token.cancel():this._token=en.Cancelled}dispose(e=!1){e&&this.cancel(),this._parentListener?.dispose(),this._token?this._token instanceof tn&&this._token.dispose():this._token=en.None}},rn=class{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(e,t){this._keyCodeToStr[e]=t,this._strToKeyCode[t.toLowerCase()]=e}keyCodeToStr(e){return this._keyCodeToStr[e]}strToKeyCode(e){return this._strToKeyCode[e.toLowerCase()]||0}};const an=new rn,on=new rn,sn=new rn,cn=Array(230),ln={},un=[],dn=Object.create(null),fn=Object.create(null),pn=[],mn=[];for(let e=0;e<=193;e++)pn[e]=-1;for(let e=0;e<=132;e++)mn[e]=-1;(function(){let e=[[1,0,`None`,0,`unknown`,0,`VK_UNKNOWN`,``,``],[1,1,`Hyper`,0,``,0,``,``,``],[1,2,`Super`,0,``,0,``,``,``],[1,3,`Fn`,0,``,0,``,``,``],[1,4,`FnLock`,0,``,0,``,``,``],[1,5,`Suspend`,0,``,0,``,``,``],[1,6,`Resume`,0,``,0,``,``,``],[1,7,`Turbo`,0,``,0,``,``,``],[1,8,`Sleep`,0,``,0,`VK_SLEEP`,``,``],[1,9,`WakeUp`,0,``,0,``,``,``],[0,10,`KeyA`,31,`A`,65,`VK_A`,``,``],[0,11,`KeyB`,32,`B`,66,`VK_B`,``,``],[0,12,`KeyC`,33,`C`,67,`VK_C`,``,``],[0,13,`KeyD`,34,`D`,68,`VK_D`,``,``],[0,14,`KeyE`,35,`E`,69,`VK_E`,``,``],[0,15,`KeyF`,36,`F`,70,`VK_F`,``,``],[0,16,`KeyG`,37,`G`,71,`VK_G`,``,``],[0,17,`KeyH`,38,`H`,72,`VK_H`,``,``],[0,18,`KeyI`,39,`I`,73,`VK_I`,``,``],[0,19,`KeyJ`,40,`J`,74,`VK_J`,``,``],[0,20,`KeyK`,41,`K`,75,`VK_K`,``,``],[0,21,`KeyL`,42,`L`,76,`VK_L`,``,``],[0,22,`KeyM`,43,`M`,77,`VK_M`,``,``],[0,23,`KeyN`,44,`N`,78,`VK_N`,``,``],[0,24,`KeyO`,45,`O`,79,`VK_O`,``,``],[0,25,`KeyP`,46,`P`,80,`VK_P`,``,``],[0,26,`KeyQ`,47,`Q`,81,`VK_Q`,``,``],[0,27,`KeyR`,48,`R`,82,`VK_R`,``,``],[0,28,`KeyS`,49,`S`,83,`VK_S`,``,``],[0,29,`KeyT`,50,`T`,84,`VK_T`,``,``],[0,30,`KeyU`,51,`U`,85,`VK_U`,``,``],[0,31,`KeyV`,52,`V`,86,`VK_V`,``,``],[0,32,`KeyW`,53,`W`,87,`VK_W`,``,``],[0,33,`KeyX`,54,`X`,88,`VK_X`,``,``],[0,34,`KeyY`,55,`Y`,89,`VK_Y`,``,``],[0,35,`KeyZ`,56,`Z`,90,`VK_Z`,``,``],[0,36,`Digit1`,22,`1`,49,`VK_1`,``,``],[0,37,`Digit2`,23,`2`,50,`VK_2`,``,``],[0,38,`Digit3`,24,`3`,51,`VK_3`,``,``],[0,39,`Digit4`,25,`4`,52,`VK_4`,``,``],[0,40,`Digit5`,26,`5`,53,`VK_5`,``,``],[0,41,`Digit6`,27,`6`,54,`VK_6`,``,``],[0,42,`Digit7`,28,`7`,55,`VK_7`,``,``],[0,43,`Digit8`,29,`8`,56,`VK_8`,``,``],[0,44,`Digit9`,30,`9`,57,`VK_9`,``,``],[0,45,`Digit0`,21,`0`,48,`VK_0`,``,``],[1,46,`Enter`,3,`Enter`,13,`VK_RETURN`,``,``],[1,47,`Escape`,9,`Escape`,27,`VK_ESCAPE`,``,``],[1,48,`Backspace`,1,`Backspace`,8,`VK_BACK`,``,``],[1,49,`Tab`,2,`Tab`,9,`VK_TAB`,``,``],[1,50,`Space`,10,`Space`,32,`VK_SPACE`,``,``],[0,51,`Minus`,88,`-`,189,`VK_OEM_MINUS`,`-`,`OEM_MINUS`],[0,52,`Equal`,86,`=`,187,`VK_OEM_PLUS`,`=`,`OEM_PLUS`],[0,53,`BracketLeft`,92,`[`,219,`VK_OEM_4`,`[`,`OEM_4`],[0,54,`BracketRight`,94,`]`,221,`VK_OEM_6`,`]`,`OEM_6`],[0,55,`Backslash`,93,`\\`,220,`VK_OEM_5`,`\\`,`OEM_5`],[0,56,`IntlHash`,0,``,0,``,``,``],[0,57,`Semicolon`,85,`;`,186,`VK_OEM_1`,`;`,`OEM_1`],[0,58,`Quote`,95,`'`,222,`VK_OEM_7`,`'`,`OEM_7`],[0,59,`Backquote`,91,"`",192,`VK_OEM_3`,"`",`OEM_3`],[0,60,`Comma`,87,`,`,188,`VK_OEM_COMMA`,`,`,`OEM_COMMA`],[0,61,`Period`,89,`.`,190,`VK_OEM_PERIOD`,`.`,`OEM_PERIOD`],[0,62,`Slash`,90,`/`,191,`VK_OEM_2`,`/`,`OEM_2`],[1,63,`CapsLock`,8,`CapsLock`,20,`VK_CAPITAL`,``,``],[1,64,`F1`,59,`F1`,112,`VK_F1`,``,``],[1,65,`F2`,60,`F2`,113,`VK_F2`,``,``],[1,66,`F3`,61,`F3`,114,`VK_F3`,``,``],[1,67,`F4`,62,`F4`,115,`VK_F4`,``,``],[1,68,`F5`,63,`F5`,116,`VK_F5`,``,``],[1,69,`F6`,64,`F6`,117,`VK_F6`,``,``],[1,70,`F7`,65,`F7`,118,`VK_F7`,``,``],[1,71,`F8`,66,`F8`,119,`VK_F8`,``,``],[1,72,`F9`,67,`F9`,120,`VK_F9`,``,``],[1,73,`F10`,68,`F10`,121,`VK_F10`,``,``],[1,74,`F11`,69,`F11`,122,`VK_F11`,``,``],[1,75,`F12`,70,`F12`,123,`VK_F12`,``,``],[1,76,`PrintScreen`,0,``,0,``,``,``],[1,77,`ScrollLock`,84,`ScrollLock`,145,`VK_SCROLL`,``,``],[1,78,`Pause`,7,`PauseBreak`,19,`VK_PAUSE`,``,``],[1,79,`Insert`,19,`Insert`,45,`VK_INSERT`,``,``],[1,80,`Home`,14,`Home`,36,`VK_HOME`,``,``],[1,81,`PageUp`,11,`PageUp`,33,`VK_PRIOR`,``,``],[1,82,`Delete`,20,`Delete`,46,`VK_DELETE`,``,``],[1,83,`End`,13,`End`,35,`VK_END`,``,``],[1,84,`PageDown`,12,`PageDown`,34,`VK_NEXT`,``,``],[1,85,`ArrowRight`,17,`RightArrow`,39,`VK_RIGHT`,`Right`,``],[1,86,`ArrowLeft`,15,`LeftArrow`,37,`VK_LEFT`,`Left`,``],[1,87,`ArrowDown`,18,`DownArrow`,40,`VK_DOWN`,`Down`,``],[1,88,`ArrowUp`,16,`UpArrow`,38,`VK_UP`,`Up`,``],[1,89,`NumLock`,83,`NumLock`,144,`VK_NUMLOCK`,``,``],[1,90,`NumpadDivide`,113,`NumPad_Divide`,111,`VK_DIVIDE`,``,``],[1,91,`NumpadMultiply`,108,`NumPad_Multiply`,106,`VK_MULTIPLY`,``,``],[1,92,`NumpadSubtract`,111,`NumPad_Subtract`,109,`VK_SUBTRACT`,``,``],[1,93,`NumpadAdd`,109,`NumPad_Add`,107,`VK_ADD`,``,``],[1,94,`NumpadEnter`,3,``,0,``,``,``],[1,95,`Numpad1`,99,`NumPad1`,97,`VK_NUMPAD1`,``,``],[1,96,`Numpad2`,100,`NumPad2`,98,`VK_NUMPAD2`,``,``],[1,97,`Numpad3`,101,`NumPad3`,99,`VK_NUMPAD3`,``,``],[1,98,`Numpad4`,102,`NumPad4`,100,`VK_NUMPAD4`,``,``],[1,99,`Numpad5`,103,`NumPad5`,101,`VK_NUMPAD5`,``,``],[1,100,`Numpad6`,104,`NumPad6`,102,`VK_NUMPAD6`,``,``],[1,101,`Numpad7`,105,`NumPad7`,103,`VK_NUMPAD7`,``,``],[1,102,`Numpad8`,106,`NumPad8`,104,`VK_NUMPAD8`,``,``],[1,103,`Numpad9`,107,`NumPad9`,105,`VK_NUMPAD9`,``,``],[1,104,`Numpad0`,98,`NumPad0`,96,`VK_NUMPAD0`,``,``],[1,105,`NumpadDecimal`,112,`NumPad_Decimal`,110,`VK_DECIMAL`,``,``],[0,106,`IntlBackslash`,97,`OEM_102`,226,`VK_OEM_102`,``,``],[1,107,`ContextMenu`,58,`ContextMenu`,93,``,``,``],[1,108,`Power`,0,``,0,``,``,``],[1,109,`NumpadEqual`,0,``,0,``,``,``],[1,110,`F13`,71,`F13`,124,`VK_F13`,``,``],[1,111,`F14`,72,`F14`,125,`VK_F14`,``,``],[1,112,`F15`,73,`F15`,126,`VK_F15`,``,``],[1,113,`F16`,74,`F16`,127,`VK_F16`,``,``],[1,114,`F17`,75,`F17`,128,`VK_F17`,``,``],[1,115,`F18`,76,`F18`,129,`VK_F18`,``,``],[1,116,`F19`,77,`F19`,130,`VK_F19`,``,``],[1,117,`F20`,78,`F20`,131,`VK_F20`,``,``],[1,118,`F21`,79,`F21`,132,`VK_F21`,``,``],[1,119,`F22`,80,`F22`,133,`VK_F22`,``,``],[1,120,`F23`,81,`F23`,134,`VK_F23`,``,``],[1,121,`F24`,82,`F24`,135,`VK_F24`,``,``],[1,122,`Open`,0,``,0,``,``,``],[1,123,`Help`,0,``,0,``,``,``],[1,124,`Select`,0,``,0,``,``,``],[1,125,`Again`,0,``,0,``,``,``],[1,126,`Undo`,0,``,0,``,``,``],[1,127,`Cut`,0,``,0,``,``,``],[1,128,`Copy`,0,``,0,``,``,``],[1,129,`Paste`,0,``,0,``,``,``],[1,130,`Find`,0,``,0,``,``,``],[1,131,`AudioVolumeMute`,117,`AudioVolumeMute`,173,`VK_VOLUME_MUTE`,``,``],[1,132,`AudioVolumeUp`,118,`AudioVolumeUp`,175,`VK_VOLUME_UP`,``,``],[1,133,`AudioVolumeDown`,119,`AudioVolumeDown`,174,`VK_VOLUME_DOWN`,``,``],[1,134,`NumpadComma`,110,`NumPad_Separator`,108,`VK_SEPARATOR`,``,``],[0,135,`IntlRo`,115,`ABNT_C1`,193,`VK_ABNT_C1`,``,``],[1,136,`KanaMode`,0,``,0,``,``,``],[0,137,`IntlYen`,0,``,0,``,``,``],[1,138,`Convert`,0,``,0,``,``,``],[1,139,`NonConvert`,0,``,0,``,``,``],[1,140,`Lang1`,0,``,0,``,``,``],[1,141,`Lang2`,0,``,0,``,``,``],[1,142,`Lang3`,0,``,0,``,``,``],[1,143,`Lang4`,0,``,0,``,``,``],[1,144,`Lang5`,0,``,0,``,``,``],[1,145,`Abort`,0,``,0,``,``,``],[1,146,`Props`,0,``,0,``,``,``],[1,147,`NumpadParenLeft`,0,``,0,``,``,``],[1,148,`NumpadParenRight`,0,``,0,``,``,``],[1,149,`NumpadBackspace`,0,``,0,``,``,``],[1,150,`NumpadMemoryStore`,0,``,0,``,``,``],[1,151,`NumpadMemoryRecall`,0,``,0,``,``,``],[1,152,`NumpadMemoryClear`,0,``,0,``,``,``],[1,153,`NumpadMemoryAdd`,0,``,0,``,``,``],[1,154,`NumpadMemorySubtract`,0,``,0,``,``,``],[1,155,`NumpadClear`,131,`Clear`,12,`VK_CLEAR`,``,``],[1,156,`NumpadClearEntry`,0,``,0,``,``,``],[1,0,``,5,`Ctrl`,17,`VK_CONTROL`,``,``],[1,0,``,4,`Shift`,16,`VK_SHIFT`,``,``],[1,0,``,6,`Alt`,18,`VK_MENU`,``,``],[1,0,``,57,`Meta`,91,`VK_COMMAND`,``,``],[1,157,`ControlLeft`,5,``,0,`VK_LCONTROL`,``,``],[1,158,`ShiftLeft`,4,``,0,`VK_LSHIFT`,``,``],[1,159,`AltLeft`,6,``,0,`VK_LMENU`,``,``],[1,160,`MetaLeft`,57,``,0,`VK_LWIN`,``,``],[1,161,`ControlRight`,5,``,0,`VK_RCONTROL`,``,``],[1,162,`ShiftRight`,4,``,0,`VK_RSHIFT`,``,``],[1,163,`AltRight`,6,``,0,`VK_RMENU`,``,``],[1,164,`MetaRight`,57,``,0,`VK_RWIN`,``,``],[1,165,`BrightnessUp`,0,``,0,``,``,``],[1,166,`BrightnessDown`,0,``,0,``,``,``],[1,167,`MediaPlay`,0,``,0,``,``,``],[1,168,`MediaRecord`,0,``,0,``,``,``],[1,169,`MediaFastForward`,0,``,0,``,``,``],[1,170,`MediaRewind`,0,``,0,``,``,``],[1,171,`MediaTrackNext`,124,`MediaTrackNext`,176,`VK_MEDIA_NEXT_TRACK`,``,``],[1,172,`MediaTrackPrevious`,125,`MediaTrackPrevious`,177,`VK_MEDIA_PREV_TRACK`,``,``],[1,173,`MediaStop`,126,`MediaStop`,178,`VK_MEDIA_STOP`,``,``],[1,174,`Eject`,0,``,0,``,``,``],[1,175,`MediaPlayPause`,127,`MediaPlayPause`,179,`VK_MEDIA_PLAY_PAUSE`,``,``],[1,176,`MediaSelect`,128,`LaunchMediaPlayer`,181,`VK_MEDIA_LAUNCH_MEDIA_SELECT`,``,``],[1,177,`LaunchMail`,129,`LaunchMail`,180,`VK_MEDIA_LAUNCH_MAIL`,``,``],[1,178,`LaunchApp2`,130,`LaunchApp2`,183,`VK_MEDIA_LAUNCH_APP2`,``,``],[1,179,`LaunchApp1`,0,``,0,`VK_MEDIA_LAUNCH_APP1`,``,``],[1,180,`SelectTask`,0,``,0,``,``,``],[1,181,`LaunchScreenSaver`,0,``,0,``,``,``],[1,182,`BrowserSearch`,120,`BrowserSearch`,170,`VK_BROWSER_SEARCH`,``,``],[1,183,`BrowserHome`,121,`BrowserHome`,172,`VK_BROWSER_HOME`,``,``],[1,184,`BrowserBack`,122,`BrowserBack`,166,`VK_BROWSER_BACK`,``,``],[1,185,`BrowserForward`,123,`BrowserForward`,167,`VK_BROWSER_FORWARD`,``,``],[1,186,`BrowserStop`,0,``,0,`VK_BROWSER_STOP`,``,``],[1,187,`BrowserRefresh`,0,``,0,`VK_BROWSER_REFRESH`,``,``],[1,188,`BrowserFavorites`,0,``,0,`VK_BROWSER_FAVORITES`,``,``],[1,189,`ZoomToggle`,0,``,0,``,``,``],[1,190,`MailReply`,0,``,0,``,``,``],[1,191,`MailForward`,0,``,0,``,``,``],[1,192,`MailSend`,0,``,0,``,``,``],[1,0,``,114,`KeyInComposition`,229,``,``,``],[1,0,``,116,`ABNT_C2`,194,`VK_ABNT_C2`,``,``],[1,0,``,96,`OEM_8`,223,`VK_OEM_8`,``,``],[1,0,``,0,``,0,`VK_KANA`,``,``],[1,0,``,0,``,0,`VK_HANGUL`,``,``],[1,0,``,0,``,0,`VK_JUNJA`,``,``],[1,0,``,0,``,0,`VK_FINAL`,``,``],[1,0,``,0,``,0,`VK_HANJA`,``,``],[1,0,``,0,``,0,`VK_KANJI`,``,``],[1,0,``,0,``,0,`VK_CONVERT`,``,``],[1,0,``,0,``,0,`VK_NONCONVERT`,``,``],[1,0,``,0,``,0,`VK_ACCEPT`,``,``],[1,0,``,0,``,0,`VK_MODECHANGE`,``,``],[1,0,``,0,``,0,`VK_SELECT`,``,``],[1,0,``,0,``,0,`VK_PRINT`,``,``],[1,0,``,0,``,0,`VK_EXECUTE`,``,``],[1,0,``,0,``,0,`VK_SNAPSHOT`,``,``],[1,0,``,0,``,0,`VK_HELP`,``,``],[1,0,``,0,``,0,`VK_APPS`,``,``],[1,0,``,0,``,0,`VK_PROCESSKEY`,``,``],[1,0,``,0,``,0,`VK_PACKET`,``,``],[1,0,``,0,``,0,`VK_DBE_SBCSCHAR`,``,``],[1,0,``,0,``,0,`VK_DBE_DBCSCHAR`,``,``],[1,0,``,0,``,0,`VK_ATTN`,``,``],[1,0,``,0,``,0,`VK_CRSEL`,``,``],[1,0,``,0,``,0,`VK_EXSEL`,``,``],[1,0,``,0,``,0,`VK_EREOF`,``,``],[1,0,``,0,``,0,`VK_PLAY`,``,``],[1,0,``,0,``,0,`VK_ZOOM`,``,``],[1,0,``,0,``,0,`VK_NONAME`,``,``],[1,0,``,0,``,0,`VK_PA1`,``,``],[1,0,``,0,``,0,`VK_OEM_CLEAR`,``,``]],t=[],n=[];for(let r of e){let[e,i,a,o,s,c,l,u,d]=r;if(n[i]||(n[i]=!0,un[i]=a,dn[a]=i,fn[a.toLowerCase()]=i,e&&(pn[i]=o,o!==0&&o!==3&&o!==5&&o!==4&&o!==6&&o!==57&&(mn[o]=i))),!t[o]){if(t[o]=!0,!s)throw Error(`String representation missing for key code ${o} around scan code ${a}`);an.define(o,s),on.define(o,u||s),sn.define(o,d||u||s)}c&&(cn[c]=o),l&&(ln[l]=o)}mn[3]=46})();var hn;(function(e){function t(e){return an.keyCodeToStr(e)}e.toString=t;function n(e){return an.strToKeyCode(e)}e.fromString=n;function r(e){return on.keyCodeToStr(e)}e.toUserSettingsUS=r;function i(e){return sn.keyCodeToStr(e)}e.toUserSettingsGeneral=i;function a(e){return on.strToKeyCode(e)||sn.strToKeyCode(e)}e.fromUserSettings=a;function o(e){if(e>=98&&e<=113)return null;switch(e){case 16:return`Up`;case 18:return`Down`;case 15:return`Left`;case 17:return`Right`}return an.keyCodeToStr(e)}e.toElectronAccelerator=o})(hn||={});function gn(e,t){let n=(t&65535)<<16>>>0;return(e|n)>>>0}var _n=class e extends H{constructor(e,t,n,r){super(e,t,n,r),this.selectionStartLineNumber=e,this.selectionStartColumn=t,this.positionLineNumber=n,this.positionColumn=r}toString(){return`[`+this.selectionStartLineNumber+`,`+this.selectionStartColumn+` -> `+this.positionLineNumber+`,`+this.positionColumn+`]`}equalsSelection(t){return e.selectionsEqual(this,t)}static selectionsEqual(e,t){return e.selectionStartLineNumber===t.selectionStartLineNumber&&e.selectionStartColumn===t.selectionStartColumn&&e.positionLineNumber===t.positionLineNumber&&e.positionColumn===t.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(t,n){return this.getDirection()===0?new e(this.startLineNumber,this.startColumn,t,n):new e(t,n,this.startLineNumber,this.startColumn)}getPosition(){return new V(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new V(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(t,n){return this.getDirection()===0?new e(t,n,this.endLineNumber,this.endColumn):new e(this.endLineNumber,this.endColumn,t,n)}static fromPositions(t,n=t){return new e(t.lineNumber,t.column,n.lineNumber,n.column)}static fromRange(t,n){return n===0?new e(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):new e(t.endLineNumber,t.endColumn,t.startLineNumber,t.startColumn)}static liftSelection(t){return new e(t.selectionStartLineNumber,t.selectionStartColumn,t.positionLineNumber,t.positionColumn)}static selectionsArrEqual(e,t){if(e&&!t||!e&&t)return!1;if(!e&&!t)return!0;if(e.length!==t.length)return!1;for(let n=0,r=e.length;n<r;n++)if(!this.selectionsEqual(e[n],t[n]))return!1;return!0}static isISelection(e){return e&&typeof e.selectionStartLineNumber==`number`&&typeof e.selectionStartColumn==`number`&&typeof e.positionLineNumber==`number`&&typeof e.positionColumn==`number`}static createWithDirection(t,n,r,i,a){return a===0?new e(t,n,r,i):new e(r,i,t,n)}};function vn(e){return typeof e==`string`}const yn=Object.create(null);function U(e,t){if(vn(t)){let n=yn[t];if(n===void 0)throw Error(`${e} references an unknown codicon: ${t}`);t=n}return yn[e]=t,{id:e}}const bn={add:U(`add`,6e4),plus:U(`plus`,6e4),gistNew:U(`gist-new`,6e4),repoCreate:U(`repo-create`,6e4),lightbulb:U(`lightbulb`,60001),lightBulb:U(`light-bulb`,60001),repo:U(`repo`,60002),repoDelete:U(`repo-delete`,60002),gistFork:U(`gist-fork`,60003),repoForked:U(`repo-forked`,60003),gitPullRequest:U(`git-pull-request`,60004),gitPullRequestAbandoned:U(`git-pull-request-abandoned`,60004),recordKeys:U(`record-keys`,60005),keyboard:U(`keyboard`,60005),tag:U(`tag`,60006),gitPullRequestLabel:U(`git-pull-request-label`,60006),tagAdd:U(`tag-add`,60006),tagRemove:U(`tag-remove`,60006),person:U(`person`,60007),personFollow:U(`person-follow`,60007),personOutline:U(`person-outline`,60007),personFilled:U(`person-filled`,60007),gitBranch:U(`git-branch`,60008),gitBranchCreate:U(`git-branch-create`,60008),gitBranchDelete:U(`git-branch-delete`,60008),sourceControl:U(`source-control`,60008),mirror:U(`mirror`,60009),mirrorPublic:U(`mirror-public`,60009),star:U(`star`,60010),starAdd:U(`star-add`,60010),starDelete:U(`star-delete`,60010),starEmpty:U(`star-empty`,60010),comment:U(`comment`,60011),commentAdd:U(`comment-add`,60011),alert:U(`alert`,60012),warning:U(`warning`,60012),search:U(`search`,60013),searchSave:U(`search-save`,60013),logOut:U(`log-out`,60014),signOut:U(`sign-out`,60014),logIn:U(`log-in`,60015),signIn:U(`sign-in`,60015),eye:U(`eye`,60016),eyeUnwatch:U(`eye-unwatch`,60016),eyeWatch:U(`eye-watch`,60016),circleFilled:U(`circle-filled`,60017),primitiveDot:U(`primitive-dot`,60017),closeDirty:U(`close-dirty`,60017),debugBreakpoint:U(`debug-breakpoint`,60017),debugBreakpointDisabled:U(`debug-breakpoint-disabled`,60017),debugHint:U(`debug-hint`,60017),terminalDecorationSuccess:U(`terminal-decoration-success`,60017),primitiveSquare:U(`primitive-square`,60018),edit:U(`edit`,60019),pencil:U(`pencil`,60019),info:U(`info`,60020),issueOpened:U(`issue-opened`,60020),gistPrivate:U(`gist-private`,60021),gitForkPrivate:U(`git-fork-private`,60021),lock:U(`lock`,60021),mirrorPrivate:U(`mirror-private`,60021),close:U(`close`,60022),removeClose:U(`remove-close`,60022),x:U(`x`,60022),repoSync:U(`repo-sync`,60023),sync:U(`sync`,60023),clone:U(`clone`,60024),desktopDownload:U(`desktop-download`,60024),beaker:U(`beaker`,60025),microscope:U(`microscope`,60025),vm:U(`vm`,60026),deviceDesktop:U(`device-desktop`,60026),file:U(`file`,60027),fileText:U(`file-text`,60027),more:U(`more`,60028),ellipsis:U(`ellipsis`,60028),kebabHorizontal:U(`kebab-horizontal`,60028),mailReply:U(`mail-reply`,60029),reply:U(`reply`,60029),organization:U(`organization`,60030),organizationFilled:U(`organization-filled`,60030),organizationOutline:U(`organization-outline`,60030),newFile:U(`new-file`,60031),fileAdd:U(`file-add`,60031),newFolder:U(`new-folder`,60032),fileDirectoryCreate:U(`file-directory-create`,60032),trash:U(`trash`,60033),trashcan:U(`trashcan`,60033),history:U(`history`,60034),clock:U(`clock`,60034),folder:U(`folder`,60035),fileDirectory:U(`file-directory`,60035),symbolFolder:U(`symbol-folder`,60035),logoGithub:U(`logo-github`,60036),markGithub:U(`mark-github`,60036),github:U(`github`,60036),terminal:U(`terminal`,60037),console:U(`console`,60037),repl:U(`repl`,60037),zap:U(`zap`,60038),symbolEvent:U(`symbol-event`,60038),error:U(`error`,60039),stop:U(`stop`,60039),variable:U(`variable`,60040),symbolVariable:U(`symbol-variable`,60040),array:U(`array`,60042),symbolArray:U(`symbol-array`,60042),symbolModule:U(`symbol-module`,60043),symbolPackage:U(`symbol-package`,60043),symbolNamespace:U(`symbol-namespace`,60043),symbolObject:U(`symbol-object`,60043),symbolMethod:U(`symbol-method`,60044),symbolFunction:U(`symbol-function`,60044),symbolConstructor:U(`symbol-constructor`,60044),symbolBoolean:U(`symbol-boolean`,60047),symbolNull:U(`symbol-null`,60047),symbolNumeric:U(`symbol-numeric`,60048),symbolNumber:U(`symbol-number`,60048),symbolStructure:U(`symbol-structure`,60049),symbolStruct:U(`symbol-struct`,60049),symbolParameter:U(`symbol-parameter`,60050),symbolTypeParameter:U(`symbol-type-parameter`,60050),symbolKey:U(`symbol-key`,60051),symbolText:U(`symbol-text`,60051),symbolReference:U(`symbol-reference`,60052),goToFile:U(`go-to-file`,60052),symbolEnum:U(`symbol-enum`,60053),symbolValue:U(`symbol-value`,60053),symbolRuler:U(`symbol-ruler`,60054),symbolUnit:U(`symbol-unit`,60054),activateBreakpoints:U(`activate-breakpoints`,60055),archive:U(`archive`,60056),arrowBoth:U(`arrow-both`,60057),arrowDown:U(`arrow-down`,60058),arrowLeft:U(`arrow-left`,60059),arrowRight:U(`arrow-right`,60060),arrowSmallDown:U(`arrow-small-down`,60061),arrowSmallLeft:U(`arrow-small-left`,60062),arrowSmallRight:U(`arrow-small-right`,60063),arrowSmallUp:U(`arrow-small-up`,60064),arrowUp:U(`arrow-up`,60065),bell:U(`bell`,60066),bold:U(`bold`,60067),book:U(`book`,60068),bookmark:U(`bookmark`,60069),debugBreakpointConditionalUnverified:U(`debug-breakpoint-conditional-unverified`,60070),debugBreakpointConditional:U(`debug-breakpoint-conditional`,60071),debugBreakpointConditionalDisabled:U(`debug-breakpoint-conditional-disabled`,60071),debugBreakpointDataUnverified:U(`debug-breakpoint-data-unverified`,60072),debugBreakpointData:U(`debug-breakpoint-data`,60073),debugBreakpointDataDisabled:U(`debug-breakpoint-data-disabled`,60073),debugBreakpointLogUnverified:U(`debug-breakpoint-log-unverified`,60074),debugBreakpointLog:U(`debug-breakpoint-log`,60075),debugBreakpointLogDisabled:U(`debug-breakpoint-log-disabled`,60075),briefcase:U(`briefcase`,60076),broadcast:U(`broadcast`,60077),browser:U(`browser`,60078),bug:U(`bug`,60079),calendar:U(`calendar`,60080),caseSensitive:U(`case-sensitive`,60081),check:U(`check`,60082),checklist:U(`checklist`,60083),chevronDown:U(`chevron-down`,60084),chevronLeft:U(`chevron-left`,60085),chevronRight:U(`chevron-right`,60086),chevronUp:U(`chevron-up`,60087),chromeClose:U(`chrome-close`,60088),chromeMaximize:U(`chrome-maximize`,60089),chromeMinimize:U(`chrome-minimize`,60090),chromeRestore:U(`chrome-restore`,60091),circleOutline:U(`circle-outline`,60092),circle:U(`circle`,60092),debugBreakpointUnverified:U(`debug-breakpoint-unverified`,60092),terminalDecorationIncomplete:U(`terminal-decoration-incomplete`,60092),circleSlash:U(`circle-slash`,60093),circuitBoard:U(`circuit-board`,60094),clearAll:U(`clear-all`,60095),clippy:U(`clippy`,60096),closeAll:U(`close-all`,60097),cloudDownload:U(`cloud-download`,60098),cloudUpload:U(`cloud-upload`,60099),code:U(`code`,60100),collapseAll:U(`collapse-all`,60101),colorMode:U(`color-mode`,60102),commentDiscussion:U(`comment-discussion`,60103),creditCard:U(`credit-card`,60105),dash:U(`dash`,60108),dashboard:U(`dashboard`,60109),database:U(`database`,60110),debugContinue:U(`debug-continue`,60111),debugDisconnect:U(`debug-disconnect`,60112),debugPause:U(`debug-pause`,60113),debugRestart:U(`debug-restart`,60114),debugStart:U(`debug-start`,60115),debugStepInto:U(`debug-step-into`,60116),debugStepOut:U(`debug-step-out`,60117),debugStepOver:U(`debug-step-over`,60118),debugStop:U(`debug-stop`,60119),debug:U(`debug`,60120),deviceCameraVideo:U(`device-camera-video`,60121),deviceCamera:U(`device-camera`,60122),deviceMobile:U(`device-mobile`,60123),diffAdded:U(`diff-added`,60124),diffIgnored:U(`diff-ignored`,60125),diffModified:U(`diff-modified`,60126),diffRemoved:U(`diff-removed`,60127),diffRenamed:U(`diff-renamed`,60128),diff:U(`diff`,60129),diffSidebyside:U(`diff-sidebyside`,60129),discard:U(`discard`,60130),editorLayout:U(`editor-layout`,60131),emptyWindow:U(`empty-window`,60132),exclude:U(`exclude`,60133),extensions:U(`extensions`,60134),eyeClosed:U(`eye-closed`,60135),fileBinary:U(`file-binary`,60136),fileCode:U(`file-code`,60137),fileMedia:U(`file-media`,60138),filePdf:U(`file-pdf`,60139),fileSubmodule:U(`file-submodule`,60140),fileSymlinkDirectory:U(`file-symlink-directory`,60141),fileSymlinkFile:U(`file-symlink-file`,60142),fileZip:U(`file-zip`,60143),files:U(`files`,60144),filter:U(`filter`,60145),flame:U(`flame`,60146),foldDown:U(`fold-down`,60147),foldUp:U(`fold-up`,60148),fold:U(`fold`,60149),folderActive:U(`folder-active`,60150),folderOpened:U(`folder-opened`,60151),gear:U(`gear`,60152),gift:U(`gift`,60153),gistSecret:U(`gist-secret`,60154),gist:U(`gist`,60155),gitCommit:U(`git-commit`,60156),gitCompare:U(`git-compare`,60157),compareChanges:U(`compare-changes`,60157),gitMerge:U(`git-merge`,60158),githubAction:U(`github-action`,60159),githubAlt:U(`github-alt`,60160),globe:U(`globe`,60161),grabber:U(`grabber`,60162),graph:U(`graph`,60163),gripper:U(`gripper`,60164),heart:U(`heart`,60165),home:U(`home`,60166),horizontalRule:U(`horizontal-rule`,60167),hubot:U(`hubot`,60168),inbox:U(`inbox`,60169),issueReopened:U(`issue-reopened`,60171),issues:U(`issues`,60172),italic:U(`italic`,60173),jersey:U(`jersey`,60174),json:U(`json`,60175),kebabVertical:U(`kebab-vertical`,60176),key:U(`key`,60177),law:U(`law`,60178),lightbulbAutofix:U(`lightbulb-autofix`,60179),linkExternal:U(`link-external`,60180),link:U(`link`,60181),listOrdered:U(`list-ordered`,60182),listUnordered:U(`list-unordered`,60183),liveShare:U(`live-share`,60184),loading:U(`loading`,60185),location:U(`location`,60186),mailRead:U(`mail-read`,60187),mail:U(`mail`,60188),markdown:U(`markdown`,60189),megaphone:U(`megaphone`,60190),mention:U(`mention`,60191),milestone:U(`milestone`,60192),gitPullRequestMilestone:U(`git-pull-request-milestone`,60192),mortarBoard:U(`mortar-board`,60193),move:U(`move`,60194),multipleWindows:U(`multiple-windows`,60195),mute:U(`mute`,60196),noNewline:U(`no-newline`,60197),note:U(`note`,60198),octoface:U(`octoface`,60199),openPreview:U(`open-preview`,60200),package:U(`package`,60201),paintcan:U(`paintcan`,60202),pin:U(`pin`,60203),play:U(`play`,60204),run:U(`run`,60204),plug:U(`plug`,60205),preserveCase:U(`preserve-case`,60206),preview:U(`preview`,60207),project:U(`project`,60208),pulse:U(`pulse`,60209),question:U(`question`,60210),quote:U(`quote`,60211),radioTower:U(`radio-tower`,60212),reactions:U(`reactions`,60213),references:U(`references`,60214),refresh:U(`refresh`,60215),regex:U(`regex`,60216),remoteExplorer:U(`remote-explorer`,60217),remote:U(`remote`,60218),remove:U(`remove`,60219),replaceAll:U(`replace-all`,60220),replace:U(`replace`,60221),repoClone:U(`repo-clone`,60222),repoForcePush:U(`repo-force-push`,60223),repoPull:U(`repo-pull`,60224),repoPush:U(`repo-push`,60225),report:U(`report`,60226),requestChanges:U(`request-changes`,60227),rocket:U(`rocket`,60228),rootFolderOpened:U(`root-folder-opened`,60229),rootFolder:U(`root-folder`,60230),rss:U(`rss`,60231),ruby:U(`ruby`,60232),saveAll:U(`save-all`,60233),saveAs:U(`save-as`,60234),save:U(`save`,60235),screenFull:U(`screen-full`,60236),screenNormal:U(`screen-normal`,60237),searchStop:U(`search-stop`,60238),server:U(`server`,60240),settingsGear:U(`settings-gear`,60241),settings:U(`settings`,60242),shield:U(`shield`,60243),smiley:U(`smiley`,60244),sortPrecedence:U(`sort-precedence`,60245),splitHorizontal:U(`split-horizontal`,60246),splitVertical:U(`split-vertical`,60247),squirrel:U(`squirrel`,60248),starFull:U(`star-full`,60249),starHalf:U(`star-half`,60250),symbolClass:U(`symbol-class`,60251),symbolColor:U(`symbol-color`,60252),symbolConstant:U(`symbol-constant`,60253),symbolEnumMember:U(`symbol-enum-member`,60254),symbolField:U(`symbol-field`,60255),symbolFile:U(`symbol-file`,60256),symbolInterface:U(`symbol-interface`,60257),symbolKeyword:U(`symbol-keyword`,60258),symbolMisc:U(`symbol-misc`,60259),symbolOperator:U(`symbol-operator`,60260),symbolProperty:U(`symbol-property`,60261),wrench:U(`wrench`,60261),wrenchSubaction:U(`wrench-subaction`,60261),symbolSnippet:U(`symbol-snippet`,60262),tasklist:U(`tasklist`,60263),telescope:U(`telescope`,60264),textSize:U(`text-size`,60265),threeBars:U(`three-bars`,60266),thumbsdown:U(`thumbsdown`,60267),thumbsup:U(`thumbsup`,60268),tools:U(`tools`,60269),triangleDown:U(`triangle-down`,60270),triangleLeft:U(`triangle-left`,60271),triangleRight:U(`triangle-right`,60272),triangleUp:U(`triangle-up`,60273),twitter:U(`twitter`,60274),unfold:U(`unfold`,60275),unlock:U(`unlock`,60276),unmute:U(`unmute`,60277),unverified:U(`unverified`,60278),verified:U(`verified`,60279),versions:U(`versions`,60280),vmActive:U(`vm-active`,60281),vmOutline:U(`vm-outline`,60282),vmRunning:U(`vm-running`,60283),watch:U(`watch`,60284),whitespace:U(`whitespace`,60285),wholeWord:U(`whole-word`,60286),window:U(`window`,60287),wordWrap:U(`word-wrap`,60288),zoomIn:U(`zoom-in`,60289),zoomOut:U(`zoom-out`,60290),listFilter:U(`list-filter`,60291),listFlat:U(`list-flat`,60292),listSelection:U(`list-selection`,60293),selection:U(`selection`,60293),listTree:U(`list-tree`,60294),debugBreakpointFunctionUnverified:U(`debug-breakpoint-function-unverified`,60295),debugBreakpointFunction:U(`debug-breakpoint-function`,60296),debugBreakpointFunctionDisabled:U(`debug-breakpoint-function-disabled`,60296),debugStackframeActive:U(`debug-stackframe-active`,60297),circleSmallFilled:U(`circle-small-filled`,60298),debugStackframeDot:U(`debug-stackframe-dot`,60298),terminalDecorationMark:U(`terminal-decoration-mark`,60298),debugStackframe:U(`debug-stackframe`,60299),debugStackframeFocused:U(`debug-stackframe-focused`,60299),debugBreakpointUnsupported:U(`debug-breakpoint-unsupported`,60300),symbolString:U(`symbol-string`,60301),debugReverseContinue:U(`debug-reverse-continue`,60302),debugStepBack:U(`debug-step-back`,60303),debugRestartFrame:U(`debug-restart-frame`,60304),debugAlt:U(`debug-alt`,60305),callIncoming:U(`call-incoming`,60306),callOutgoing:U(`call-outgoing`,60307),menu:U(`menu`,60308),expandAll:U(`expand-all`,60309),feedback:U(`feedback`,60310),gitPullRequestReviewer:U(`git-pull-request-reviewer`,60310),groupByRefType:U(`group-by-ref-type`,60311),ungroupByRefType:U(`ungroup-by-ref-type`,60312),account:U(`account`,60313),gitPullRequestAssignee:U(`git-pull-request-assignee`,60313),bellDot:U(`bell-dot`,60314),debugConsole:U(`debug-console`,60315),library:U(`library`,60316),output:U(`output`,60317),runAll:U(`run-all`,60318),syncIgnored:U(`sync-ignored`,60319),pinned:U(`pinned`,60320),githubInverted:U(`github-inverted`,60321),serverProcess:U(`server-process`,60322),serverEnvironment:U(`server-environment`,60323),pass:U(`pass`,60324),issueClosed:U(`issue-closed`,60324),stopCircle:U(`stop-circle`,60325),playCircle:U(`play-circle`,60326),record:U(`record`,60327),debugAltSmall:U(`debug-alt-small`,60328),vmConnect:U(`vm-connect`,60329),cloud:U(`cloud`,60330),merge:U(`merge`,60331),export:U(`export`,60332),graphLeft:U(`graph-left`,60333),magnet:U(`magnet`,60334),notebook:U(`notebook`,60335),redo:U(`redo`,60336),checkAll:U(`check-all`,60337),pinnedDirty:U(`pinned-dirty`,60338),passFilled:U(`pass-filled`,60339),circleLargeFilled:U(`circle-large-filled`,60340),circleLarge:U(`circle-large`,60341),circleLargeOutline:U(`circle-large-outline`,60341),combine:U(`combine`,60342),gather:U(`gather`,60342),table:U(`table`,60343),variableGroup:U(`variable-group`,60344),typeHierarchy:U(`type-hierarchy`,60345),typeHierarchySub:U(`type-hierarchy-sub`,60346),typeHierarchySuper:U(`type-hierarchy-super`,60347),gitPullRequestCreate:U(`git-pull-request-create`,60348),runAbove:U(`run-above`,60349),runBelow:U(`run-below`,60350),notebookTemplate:U(`notebook-template`,60351),debugRerun:U(`debug-rerun`,60352),workspaceTrusted:U(`workspace-trusted`,60353),workspaceUntrusted:U(`workspace-untrusted`,60354),workspaceUnknown:U(`workspace-unknown`,60355),terminalCmd:U(`terminal-cmd`,60356),terminalDebian:U(`terminal-debian`,60357),terminalLinux:U(`terminal-linux`,60358),terminalPowershell:U(`terminal-powershell`,60359),terminalTmux:U(`terminal-tmux`,60360),terminalUbuntu:U(`terminal-ubuntu`,60361),terminalBash:U(`terminal-bash`,60362),arrowSwap:U(`arrow-swap`,60363),copy:U(`copy`,60364),personAdd:U(`person-add`,60365),filterFilled:U(`filter-filled`,60366),wand:U(`wand`,60367),debugLineByLine:U(`debug-line-by-line`,60368),inspect:U(`inspect`,60369),layers:U(`layers`,60370),layersDot:U(`layers-dot`,60371),layersActive:U(`layers-active`,60372),compass:U(`compass`,60373),compassDot:U(`compass-dot`,60374),compassActive:U(`compass-active`,60375),azure:U(`azure`,60376),issueDraft:U(`issue-draft`,60377),gitPullRequestClosed:U(`git-pull-request-closed`,60378),gitPullRequestDraft:U(`git-pull-request-draft`,60379),debugAll:U(`debug-all`,60380),debugCoverage:U(`debug-coverage`,60381),runErrors:U(`run-errors`,60382),folderLibrary:U(`folder-library`,60383),debugContinueSmall:U(`debug-continue-small`,60384),beakerStop:U(`beaker-stop`,60385),graphLine:U(`graph-line`,60386),graphScatter:U(`graph-scatter`,60387),pieChart:U(`pie-chart`,60388),bracket:U(`bracket`,60175),bracketDot:U(`bracket-dot`,60389),bracketError:U(`bracket-error`,60390),lockSmall:U(`lock-small`,60391),azureDevops:U(`azure-devops`,60392),verifiedFilled:U(`verified-filled`,60393),newline:U(`newline`,60394),layout:U(`layout`,60395),layoutActivitybarLeft:U(`layout-activitybar-left`,60396),layoutActivitybarRight:U(`layout-activitybar-right`,60397),layoutPanelLeft:U(`layout-panel-left`,60398),layoutPanelCenter:U(`layout-panel-center`,60399),layoutPanelJustify:U(`layout-panel-justify`,60400),layoutPanelRight:U(`layout-panel-right`,60401),layoutPanel:U(`layout-panel`,60402),layoutSidebarLeft:U(`layout-sidebar-left`,60403),layoutSidebarRight:U(`layout-sidebar-right`,60404),layoutStatusbar:U(`layout-statusbar`,60405),layoutMenubar:U(`layout-menubar`,60406),layoutCentered:U(`layout-centered`,60407),target:U(`target`,60408),indent:U(`indent`,60409),recordSmall:U(`record-small`,60410),errorSmall:U(`error-small`,60411),terminalDecorationError:U(`terminal-decoration-error`,60411),arrowCircleDown:U(`arrow-circle-down`,60412),arrowCircleLeft:U(`arrow-circle-left`,60413),arrowCircleRight:U(`arrow-circle-right`,60414),arrowCircleUp:U(`arrow-circle-up`,60415),layoutSidebarRightOff:U(`layout-sidebar-right-off`,60416),layoutPanelOff:U(`layout-panel-off`,60417),layoutSidebarLeftOff:U(`layout-sidebar-left-off`,60418),blank:U(`blank`,60419),heartFilled:U(`heart-filled`,60420),map:U(`map`,60421),mapHorizontal:U(`map-horizontal`,60421),foldHorizontal:U(`fold-horizontal`,60421),mapFilled:U(`map-filled`,60422),mapHorizontalFilled:U(`map-horizontal-filled`,60422),foldHorizontalFilled:U(`fold-horizontal-filled`,60422),circleSmall:U(`circle-small`,60423),bellSlash:U(`bell-slash`,60424),bellSlashDot:U(`bell-slash-dot`,60425),commentUnresolved:U(`comment-unresolved`,60426),gitPullRequestGoToChanges:U(`git-pull-request-go-to-changes`,60427),gitPullRequestNewChanges:U(`git-pull-request-new-changes`,60428),searchFuzzy:U(`search-fuzzy`,60429),commentDraft:U(`comment-draft`,60430),send:U(`send`,60431),sparkle:U(`sparkle`,60432),insert:U(`insert`,60433),mic:U(`mic`,60434),thumbsdownFilled:U(`thumbsdown-filled`,60435),thumbsupFilled:U(`thumbsup-filled`,60436),coffee:U(`coffee`,60437),snake:U(`snake`,60438),game:U(`game`,60439),vr:U(`vr`,60440),chip:U(`chip`,60441),piano:U(`piano`,60442),music:U(`music`,60443),micFilled:U(`mic-filled`,60444),repoFetch:U(`repo-fetch`,60445),copilot:U(`copilot`,60446),lightbulbSparkle:U(`lightbulb-sparkle`,60447),robot:U(`robot`,60448),sparkleFilled:U(`sparkle-filled`,60449),diffSingle:U(`diff-single`,60450),diffMultiple:U(`diff-multiple`,60451),surroundWith:U(`surround-with`,60452),share:U(`share`,60453),gitStash:U(`git-stash`,60454),gitStashApply:U(`git-stash-apply`,60455),gitStashPop:U(`git-stash-pop`,60456),vscode:U(`vscode`,60457),vscodeInsiders:U(`vscode-insiders`,60458),codeOss:U(`code-oss`,60459),runCoverage:U(`run-coverage`,60460),runAllCoverage:U(`run-all-coverage`,60461),coverage:U(`coverage`,60462),githubProject:U(`github-project`,60463),mapVertical:U(`map-vertical`,60464),foldVertical:U(`fold-vertical`,60464),mapVerticalFilled:U(`map-vertical-filled`,60465),foldVerticalFilled:U(`fold-vertical-filled`,60465),goToSearch:U(`go-to-search`,60466),percentage:U(`percentage`,60467),sortPercentage:U(`sort-percentage`,60467),attach:U(`attach`,60468)},xn={dialogError:U(`dialog-error`,`error`),dialogWarning:U(`dialog-warning`,`warning`),dialogInfo:U(`dialog-info`,`info`),dialogClose:U(`dialog-close`,`close`),treeItemExpanded:U(`tree-item-expanded`,`chevron-down`),treeFilterOnTypeOn:U(`tree-filter-on-type-on`,`list-filter`),treeFilterOnTypeOff:U(`tree-filter-on-type-off`,`list-selection`),treeFilterClear:U(`tree-filter-clear`,`close`),treeItemLoading:U(`tree-item-loading`,`loading`),menuSelection:U(`menu-selection`,`check`),menuSubmenu:U(`menu-submenu`,`chevron-right`),menuBarMore:U(`menubar-more`,`more`),scrollbarButtonLeft:U(`scrollbar-button-left`,`triangle-left`),scrollbarButtonRight:U(`scrollbar-button-right`,`triangle-right`),scrollbarButtonUp:U(`scrollbar-button-up`,`triangle-up`),scrollbarButtonDown:U(`scrollbar-button-down`,`triangle-down`),toolBarMore:U(`toolbar-more`,`more`),quickInputBack:U(`quick-input-back`,`arrow-left`),dropDownButton:U(`drop-down-button`,60084),symbolCustomColor:U(`symbol-customcolor`,60252),exportIcon:U(`export`,60332),workspaceUnspecified:U(`workspace-unspecified`,60355),newLine:U(`newline`,60394),thumbsDownFilled:U(`thumbsdown-filled`,60435),thumbsUpFilled:U(`thumbsup-filled`,60436),gitFetch:U(`git-fetch`,60445),lightbulbSparkleAutofix:U(`lightbulb-sparkle-autofix`,60447),debugBreakpointPending:U(`debug-breakpoint-pending`,60377)},W={...bn,...xn};var Sn=class{constructor(){this._tokenizationSupports=new Map,this._factories=new Map,this._onDidChange=new E,this.onDidChange=this._onDidChange.event,this._colorMap=null}handleChange(e){this._onDidChange.fire({changedLanguages:e,changedColorMap:!1})}register(e,t){return this._tokenizationSupports.set(e,t),this.handleChange([e]),h(()=>{this._tokenizationSupports.get(e)===t&&(this._tokenizationSupports.delete(e),this.handleChange([e]))})}get(e){return this._tokenizationSupports.get(e)||null}registerFactory(e,t){this._factories.get(e)?.dispose();let n=new Cn(this,e,t);return this._factories.set(e,n),h(()=>{let t=this._factories.get(e);!t||t!==n||(this._factories.delete(e),t.dispose())})}async getOrCreate(e){let t=this.get(e);if(t)return t;let n=this._factories.get(e);return!n||n.isResolved?null:(await n.resolve(),this.get(e))}isResolved(e){if(this.get(e))return!0;let t=this._factories.get(e);return!!(!t||t.isResolved)}setColorMap(e){this._colorMap=e,this._onDidChange.fire({changedLanguages:Array.from(this._tokenizationSupports.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}},Cn=class extends _{get isResolved(){return this._isResolved}constructor(e,t,n){super(),this._registry=e,this._languageId=t,this._factory=n,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}dispose(){this._isDisposed=!0,super.dispose()}async resolve(){return this._resolvePromise||=this._create(),this._resolvePromise}async _create(){let e=await this._factory.tokenizationSupport;this._isResolved=!0,e&&!this._isDisposed&&this._register(this._registry.register(this._languageId,e))}},wn=class{constructor(e,t,n){this.offset=e,this.type=t,this.language=n,this._tokenBrand=void 0}toString(){return`(`+this.offset+`, `+this.type+`)`}},Tn;(function(e){e[e.Increase=0]=`Increase`,e[e.Decrease=1]=`Decrease`})(Tn||={});var En;(function(e){let t=new Map;t.set(0,W.symbolMethod),t.set(1,W.symbolFunction),t.set(2,W.symbolConstructor),t.set(3,W.symbolField),t.set(4,W.symbolVariable),t.set(5,W.symbolClass),t.set(6,W.symbolStruct),t.set(7,W.symbolInterface),t.set(8,W.symbolModule),t.set(9,W.symbolProperty),t.set(10,W.symbolEvent),t.set(11,W.symbolOperator),t.set(12,W.symbolUnit),t.set(13,W.symbolValue),t.set(15,W.symbolEnum),t.set(14,W.symbolConstant),t.set(15,W.symbolEnum),t.set(16,W.symbolEnumMember),t.set(17,W.symbolKeyword),t.set(27,W.symbolSnippet),t.set(18,W.symbolText),t.set(19,W.symbolColor),t.set(20,W.symbolFile),t.set(21,W.symbolReference),t.set(22,W.symbolCustomColor),t.set(23,W.symbolFolder),t.set(24,W.symbolTypeParameter),t.set(25,W.account),t.set(26,W.issues);function n(e){let n=t.get(e);return n||=(console.info(`No codicon found for CompletionItemKind `+e),W.symbolProperty),n}e.toIcon=n;let r=new Map;r.set(`method`,0),r.set(`function`,1),r.set(`constructor`,2),r.set(`field`,3),r.set(`variable`,4),r.set(`class`,5),r.set(`struct`,6),r.set(`interface`,7),r.set(`module`,8),r.set(`property`,9),r.set(`event`,10),r.set(`operator`,11),r.set(`unit`,12),r.set(`value`,13),r.set(`constant`,14),r.set(`enum`,15),r.set(`enum-member`,16),r.set(`enumMember`,16),r.set(`keyword`,17),r.set(`snippet`,27),r.set(`text`,18),r.set(`color`,19),r.set(`file`,20),r.set(`reference`,21),r.set(`customcolor`,22),r.set(`folder`,23),r.set(`type-parameter`,24),r.set(`typeParameter`,24),r.set(`account`,25),r.set(`issue`,26);function i(e,t){let n=r.get(e);return n===void 0&&!t&&(n=9),n}e.fromString=i})(En||={});var Dn;(function(e){e[e.Automatic=0]=`Automatic`,e[e.Explicit=1]=`Explicit`})(Dn||={});var On;(function(e){e[e.Automatic=0]=`Automatic`,e[e.PasteAs=1]=`PasteAs`})(On||={});var kn;(function(e){e[e.Invoke=1]=`Invoke`,e[e.TriggerCharacter=2]=`TriggerCharacter`,e[e.ContentChange=3]=`ContentChange`})(kn||={});var An;(function(e){e[e.Text=0]=`Text`,e[e.Read=1]=`Read`,e[e.Write=2]=`Write`})(An||={}),D(`Array`,`array`),D(`Boolean`,`boolean`),D(`Class`,`class`),D(`Constant`,`constant`),D(`Constructor`,`constructor`),D(`Enum`,`enumeration`),D(`EnumMember`,`enumeration member`),D(`Event`,`event`),D(`Field`,`field`),D(`File`,`file`),D(`Function`,`function`),D(`Interface`,`interface`),D(`Key`,`key`),D(`Method`,`method`),D(`Module`,`module`),D(`Namespace`,`namespace`),D(`Null`,`null`),D(`Number`,`number`),D(`Object`,`object`),D(`Operator`,`operator`),D(`Package`,`package`),D(`Property`,`property`),D(`String`,`string`),D(`Struct`,`struct`),D(`TypeParameter`,`type parameter`),D(`Variable`,`variable`);var jn;(function(e){let t=new Map;t.set(0,W.symbolFile),t.set(1,W.symbolModule),t.set(2,W.symbolNamespace),t.set(3,W.symbolPackage),t.set(4,W.symbolClass),t.set(5,W.symbolMethod),t.set(6,W.symbolProperty),t.set(7,W.symbolField),t.set(8,W.symbolConstructor),t.set(9,W.symbolEnum),t.set(10,W.symbolInterface),t.set(11,W.symbolFunction),t.set(12,W.symbolVariable),t.set(13,W.symbolConstant),t.set(14,W.symbolString),t.set(15,W.symbolNumber),t.set(16,W.symbolBoolean),t.set(17,W.symbolArray),t.set(18,W.symbolObject),t.set(19,W.symbolKey),t.set(20,W.symbolNull),t.set(21,W.symbolEnumMember),t.set(22,W.symbolStruct),t.set(23,W.symbolEvent),t.set(24,W.symbolOperator),t.set(25,W.symbolTypeParameter);function n(e){let n=t.get(e);return n||=(console.info(`No codicon found for SymbolKind `+e),W.symbolProperty),n}e.toIcon=n})(jn||={}),class e{static{this.Comment=new e(`comment`)}static{this.Imports=new e(`imports`)}static{this.Region=new e(`region`)}static fromValue(t){switch(t){case`comment`:return e.Comment;case`imports`:return e.Imports;case`region`:return e.Region}return new e(t)}constructor(e){this.value=e}};var Mn;(function(e){e[e.AIGenerated=1]=`AIGenerated`})(Mn||={});var Nn;(function(e){e[e.Invoke=0]=`Invoke`,e[e.Automatic=1]=`Automatic`})(Nn||={});var Pn;(function(e){function t(e){return!e||typeof e!=`object`?!1:typeof e.id==`string`&&typeof e.title==`string`}e.is=t})(Pn||={});var Fn;(function(e){e[e.Type=1]=`Type`,e[e.Parameter=2]=`Parameter`})(Fn||={}),new Sn,new Sn;var In;(function(e){e[e.Invoke=0]=`Invoke`,e[e.Automatic=1]=`Automatic`})(In||={});var Ln;(function(e){e[e.Unknown=0]=`Unknown`,e[e.Disabled=1]=`Disabled`,e[e.Enabled=2]=`Enabled`})(Ln||={});var Rn;(function(e){e[e.Invoke=1]=`Invoke`,e[e.Auto=2]=`Auto`})(Rn||={});var zn;(function(e){e[e.None=0]=`None`,e[e.KeepWhitespace=1]=`KeepWhitespace`,e[e.InsertAsSnippet=4]=`InsertAsSnippet`})(zn||={});var Bn;(function(e){e[e.Method=0]=`Method`,e[e.Function=1]=`Function`,e[e.Constructor=2]=`Constructor`,e[e.Field=3]=`Field`,e[e.Variable=4]=`Variable`,e[e.Class=5]=`Class`,e[e.Struct=6]=`Struct`,e[e.Interface=7]=`Interface`,e[e.Module=8]=`Module`,e[e.Property=9]=`Property`,e[e.Event=10]=`Event`,e[e.Operator=11]=`Operator`,e[e.Unit=12]=`Unit`,e[e.Value=13]=`Value`,e[e.Constant=14]=`Constant`,e[e.Enum=15]=`Enum`,e[e.EnumMember=16]=`EnumMember`,e[e.Keyword=17]=`Keyword`,e[e.Text=18]=`Text`,e[e.Color=19]=`Color`,e[e.File=20]=`File`,e[e.Reference=21]=`Reference`,e[e.Customcolor=22]=`Customcolor`,e[e.Folder=23]=`Folder`,e[e.TypeParameter=24]=`TypeParameter`,e[e.User=25]=`User`,e[e.Issue=26]=`Issue`,e[e.Snippet=27]=`Snippet`})(Bn||={});var Vn;(function(e){e[e.Deprecated=1]=`Deprecated`})(Vn||={});var Hn;(function(e){e[e.Invoke=0]=`Invoke`,e[e.TriggerCharacter=1]=`TriggerCharacter`,e[e.TriggerForIncompleteCompletions=2]=`TriggerForIncompleteCompletions`})(Hn||={});var Un;(function(e){e[e.EXACT=0]=`EXACT`,e[e.ABOVE=1]=`ABOVE`,e[e.BELOW=2]=`BELOW`})(Un||={});var Wn;(function(e){e[e.NotSet=0]=`NotSet`,e[e.ContentFlush=1]=`ContentFlush`,e[e.RecoverFromMarkers=2]=`RecoverFromMarkers`,e[e.Explicit=3]=`Explicit`,e[e.Paste=4]=`Paste`,e[e.Undo=5]=`Undo`,e[e.Redo=6]=`Redo`})(Wn||={});var Gn;(function(e){e[e.LF=1]=`LF`,e[e.CRLF=2]=`CRLF`})(Gn||={});var Kn;(function(e){e[e.Text=0]=`Text`,e[e.Read=1]=`Read`,e[e.Write=2]=`Write`})(Kn||={});var qn;(function(e){e[e.None=0]=`None`,e[e.Keep=1]=`Keep`,e[e.Brackets=2]=`Brackets`,e[e.Advanced=3]=`Advanced`,e[e.Full=4]=`Full`})(qn||={});var Jn;(function(e){e[e.acceptSuggestionOnCommitCharacter=0]=`acceptSuggestionOnCommitCharacter`,e[e.acceptSuggestionOnEnter=1]=`acceptSuggestionOnEnter`,e[e.accessibilitySupport=2]=`accessibilitySupport`,e[e.accessibilityPageSize=3]=`accessibilityPageSize`,e[e.ariaLabel=4]=`ariaLabel`,e[e.ariaRequired=5]=`ariaRequired`,e[e.autoClosingBrackets=6]=`autoClosingBrackets`,e[e.autoClosingComments=7]=`autoClosingComments`,e[e.screenReaderAnnounceInlineSuggestion=8]=`screenReaderAnnounceInlineSuggestion`,e[e.autoClosingDelete=9]=`autoClosingDelete`,e[e.autoClosingOvertype=10]=`autoClosingOvertype`,e[e.autoClosingQuotes=11]=`autoClosingQuotes`,e[e.autoIndent=12]=`autoIndent`,e[e.automaticLayout=13]=`automaticLayout`,e[e.autoSurround=14]=`autoSurround`,e[e.bracketPairColorization=15]=`bracketPairColorization`,e[e.guides=16]=`guides`,e[e.codeLens=17]=`codeLens`,e[e.codeLensFontFamily=18]=`codeLensFontFamily`,e[e.codeLensFontSize=19]=`codeLensFontSize`,e[e.colorDecorators=20]=`colorDecorators`,e[e.colorDecoratorsLimit=21]=`colorDecoratorsLimit`,e[e.columnSelection=22]=`columnSelection`,e[e.comments=23]=`comments`,e[e.contextmenu=24]=`contextmenu`,e[e.copyWithSyntaxHighlighting=25]=`copyWithSyntaxHighlighting`,e[e.cursorBlinking=26]=`cursorBlinking`,e[e.cursorSmoothCaretAnimation=27]=`cursorSmoothCaretAnimation`,e[e.cursorStyle=28]=`cursorStyle`,e[e.cursorSurroundingLines=29]=`cursorSurroundingLines`,e[e.cursorSurroundingLinesStyle=30]=`cursorSurroundingLinesStyle`,e[e.cursorWidth=31]=`cursorWidth`,e[e.disableLayerHinting=32]=`disableLayerHinting`,e[e.disableMonospaceOptimizations=33]=`disableMonospaceOptimizations`,e[e.domReadOnly=34]=`domReadOnly`,e[e.dragAndDrop=35]=`dragAndDrop`,e[e.dropIntoEditor=36]=`dropIntoEditor`,e[e.emptySelectionClipboard=37]=`emptySelectionClipboard`,e[e.experimentalWhitespaceRendering=38]=`experimentalWhitespaceRendering`,e[e.extraEditorClassName=39]=`extraEditorClassName`,e[e.fastScrollSensitivity=40]=`fastScrollSensitivity`,e[e.find=41]=`find`,e[e.fixedOverflowWidgets=42]=`fixedOverflowWidgets`,e[e.folding=43]=`folding`,e[e.foldingStrategy=44]=`foldingStrategy`,e[e.foldingHighlight=45]=`foldingHighlight`,e[e.foldingImportsByDefault=46]=`foldingImportsByDefault`,e[e.foldingMaximumRegions=47]=`foldingMaximumRegions`,e[e.unfoldOnClickAfterEndOfLine=48]=`unfoldOnClickAfterEndOfLine`,e[e.fontFamily=49]=`fontFamily`,e[e.fontInfo=50]=`fontInfo`,e[e.fontLigatures=51]=`fontLigatures`,e[e.fontSize=52]=`fontSize`,e[e.fontWeight=53]=`fontWeight`,e[e.fontVariations=54]=`fontVariations`,e[e.formatOnPaste=55]=`formatOnPaste`,e[e.formatOnType=56]=`formatOnType`,e[e.glyphMargin=57]=`glyphMargin`,e[e.gotoLocation=58]=`gotoLocation`,e[e.hideCursorInOverviewRuler=59]=`hideCursorInOverviewRuler`,e[e.hover=60]=`hover`,e[e.inDiffEditor=61]=`inDiffEditor`,e[e.inlineSuggest=62]=`inlineSuggest`,e[e.inlineEdit=63]=`inlineEdit`,e[e.letterSpacing=64]=`letterSpacing`,e[e.lightbulb=65]=`lightbulb`,e[e.lineDecorationsWidth=66]=`lineDecorationsWidth`,e[e.lineHeight=67]=`lineHeight`,e[e.lineNumbers=68]=`lineNumbers`,e[e.lineNumbersMinChars=69]=`lineNumbersMinChars`,e[e.linkedEditing=70]=`linkedEditing`,e[e.links=71]=`links`,e[e.matchBrackets=72]=`matchBrackets`,e[e.minimap=73]=`minimap`,e[e.mouseStyle=74]=`mouseStyle`,e[e.mouseWheelScrollSensitivity=75]=`mouseWheelScrollSensitivity`,e[e.mouseWheelZoom=76]=`mouseWheelZoom`,e[e.multiCursorMergeOverlapping=77]=`multiCursorMergeOverlapping`,e[e.multiCursorModifier=78]=`multiCursorModifier`,e[e.multiCursorPaste=79]=`multiCursorPaste`,e[e.multiCursorLimit=80]=`multiCursorLimit`,e[e.occurrencesHighlight=81]=`occurrencesHighlight`,e[e.overviewRulerBorder=82]=`overviewRulerBorder`,e[e.overviewRulerLanes=83]=`overviewRulerLanes`,e[e.padding=84]=`padding`,e[e.pasteAs=85]=`pasteAs`,e[e.parameterHints=86]=`parameterHints`,e[e.peekWidgetDefaultFocus=87]=`peekWidgetDefaultFocus`,e[e.placeholder=88]=`placeholder`,e[e.definitionLinkOpensInPeek=89]=`definitionLinkOpensInPeek`,e[e.quickSuggestions=90]=`quickSuggestions`,e[e.quickSuggestionsDelay=91]=`quickSuggestionsDelay`,e[e.readOnly=92]=`readOnly`,e[e.readOnlyMessage=93]=`readOnlyMessage`,e[e.renameOnType=94]=`renameOnType`,e[e.renderControlCharacters=95]=`renderControlCharacters`,e[e.renderFinalNewline=96]=`renderFinalNewline`,e[e.renderLineHighlight=97]=`renderLineHighlight`,e[e.renderLineHighlightOnlyWhenFocus=98]=`renderLineHighlightOnlyWhenFocus`,e[e.renderValidationDecorations=99]=`renderValidationDecorations`,e[e.renderWhitespace=100]=`renderWhitespace`,e[e.revealHorizontalRightPadding=101]=`revealHorizontalRightPadding`,e[e.roundedSelection=102]=`roundedSelection`,e[e.rulers=103]=`rulers`,e[e.scrollbar=104]=`scrollbar`,e[e.scrollBeyondLastColumn=105]=`scrollBeyondLastColumn`,e[e.scrollBeyondLastLine=106]=`scrollBeyondLastLine`,e[e.scrollPredominantAxis=107]=`scrollPredominantAxis`,e[e.selectionClipboard=108]=`selectionClipboard`,e[e.selectionHighlight=109]=`selectionHighlight`,e[e.selectOnLineNumbers=110]=`selectOnLineNumbers`,e[e.showFoldingControls=111]=`showFoldingControls`,e[e.showUnused=112]=`showUnused`,e[e.snippetSuggestions=113]=`snippetSuggestions`,e[e.smartSelect=114]=`smartSelect`,e[e.smoothScrolling=115]=`smoothScrolling`,e[e.stickyScroll=116]=`stickyScroll`,e[e.stickyTabStops=117]=`stickyTabStops`,e[e.stopRenderingLineAfter=118]=`stopRenderingLineAfter`,e[e.suggest=119]=`suggest`,e[e.suggestFontSize=120]=`suggestFontSize`,e[e.suggestLineHeight=121]=`suggestLineHeight`,e[e.suggestOnTriggerCharacters=122]=`suggestOnTriggerCharacters`,e[e.suggestSelection=123]=`suggestSelection`,e[e.tabCompletion=124]=`tabCompletion`,e[e.tabIndex=125]=`tabIndex`,e[e.unicodeHighlighting=126]=`unicodeHighlighting`,e[e.unusualLineTerminators=127]=`unusualLineTerminators`,e[e.useShadowDOM=128]=`useShadowDOM`,e[e.useTabStops=129]=`useTabStops`,e[e.wordBreak=130]=`wordBreak`,e[e.wordSegmenterLocales=131]=`wordSegmenterLocales`,e[e.wordSeparators=132]=`wordSeparators`,e[e.wordWrap=133]=`wordWrap`,e[e.wordWrapBreakAfterCharacters=134]=`wordWrapBreakAfterCharacters`,e[e.wordWrapBreakBeforeCharacters=135]=`wordWrapBreakBeforeCharacters`,e[e.wordWrapColumn=136]=`wordWrapColumn`,e[e.wordWrapOverride1=137]=`wordWrapOverride1`,e[e.wordWrapOverride2=138]=`wordWrapOverride2`,e[e.wrappingIndent=139]=`wrappingIndent`,e[e.wrappingStrategy=140]=`wrappingStrategy`,e[e.showDeprecated=141]=`showDeprecated`,e[e.inlayHints=142]=`inlayHints`,e[e.editorClassName=143]=`editorClassName`,e[e.pixelRatio=144]=`pixelRatio`,e[e.tabFocusMode=145]=`tabFocusMode`,e[e.layoutInfo=146]=`layoutInfo`,e[e.wrappingInfo=147]=`wrappingInfo`,e[e.defaultColorDecorators=148]=`defaultColorDecorators`,e[e.colorDecoratorsActivatedOn=149]=`colorDecoratorsActivatedOn`,e[e.inlineCompletionsAccessibilityVerbose=150]=`inlineCompletionsAccessibilityVerbose`})(Jn||={});var Yn;(function(e){e[e.TextDefined=0]=`TextDefined`,e[e.LF=1]=`LF`,e[e.CRLF=2]=`CRLF`})(Yn||={});var Xn;(function(e){e[e.LF=0]=`LF`,e[e.CRLF=1]=`CRLF`})(Xn||={});var Zn;(function(e){e[e.Left=1]=`Left`,e[e.Center=2]=`Center`,e[e.Right=3]=`Right`})(Zn||={});var Qn;(function(e){e[e.Increase=0]=`Increase`,e[e.Decrease=1]=`Decrease`})(Qn||={});var $n;(function(e){e[e.None=0]=`None`,e[e.Indent=1]=`Indent`,e[e.IndentOutdent=2]=`IndentOutdent`,e[e.Outdent=3]=`Outdent`})($n||={});var er;(function(e){e[e.Both=0]=`Both`,e[e.Right=1]=`Right`,e[e.Left=2]=`Left`,e[e.None=3]=`None`})(er||={});var tr;(function(e){e[e.Type=1]=`Type`,e[e.Parameter=2]=`Parameter`})(tr||={});var nr;(function(e){e[e.Automatic=0]=`Automatic`,e[e.Explicit=1]=`Explicit`})(nr||={});var rr;(function(e){e[e.Invoke=0]=`Invoke`,e[e.Automatic=1]=`Automatic`})(rr||={});var ir;(function(e){e[e.DependsOnKbLayout=-1]=`DependsOnKbLayout`,e[e.Unknown=0]=`Unknown`,e[e.Backspace=1]=`Backspace`,e[e.Tab=2]=`Tab`,e[e.Enter=3]=`Enter`,e[e.Shift=4]=`Shift`,e[e.Ctrl=5]=`Ctrl`,e[e.Alt=6]=`Alt`,e[e.PauseBreak=7]=`PauseBreak`,e[e.CapsLock=8]=`CapsLock`,e[e.Escape=9]=`Escape`,e[e.Space=10]=`Space`,e[e.PageUp=11]=`PageUp`,e[e.PageDown=12]=`PageDown`,e[e.End=13]=`End`,e[e.Home=14]=`Home`,e[e.LeftArrow=15]=`LeftArrow`,e[e.UpArrow=16]=`UpArrow`,e[e.RightArrow=17]=`RightArrow`,e[e.DownArrow=18]=`DownArrow`,e[e.Insert=19]=`Insert`,e[e.Delete=20]=`Delete`,e[e.Digit0=21]=`Digit0`,e[e.Digit1=22]=`Digit1`,e[e.Digit2=23]=`Digit2`,e[e.Digit3=24]=`Digit3`,e[e.Digit4=25]=`Digit4`,e[e.Digit5=26]=`Digit5`,e[e.Digit6=27]=`Digit6`,e[e.Digit7=28]=`Digit7`,e[e.Digit8=29]=`Digit8`,e[e.Digit9=30]=`Digit9`,e[e.KeyA=31]=`KeyA`,e[e.KeyB=32]=`KeyB`,e[e.KeyC=33]=`KeyC`,e[e.KeyD=34]=`KeyD`,e[e.KeyE=35]=`KeyE`,e[e.KeyF=36]=`KeyF`,e[e.KeyG=37]=`KeyG`,e[e.KeyH=38]=`KeyH`,e[e.KeyI=39]=`KeyI`,e[e.KeyJ=40]=`KeyJ`,e[e.KeyK=41]=`KeyK`,e[e.KeyL=42]=`KeyL`,e[e.KeyM=43]=`KeyM`,e[e.KeyN=44]=`KeyN`,e[e.KeyO=45]=`KeyO`,e[e.KeyP=46]=`KeyP`,e[e.KeyQ=47]=`KeyQ`,e[e.KeyR=48]=`KeyR`,e[e.KeyS=49]=`KeyS`,e[e.KeyT=50]=`KeyT`,e[e.KeyU=51]=`KeyU`,e[e.KeyV=52]=`KeyV`,e[e.KeyW=53]=`KeyW`,e[e.KeyX=54]=`KeyX`,e[e.KeyY=55]=`KeyY`,e[e.KeyZ=56]=`KeyZ`,e[e.Meta=57]=`Meta`,e[e.ContextMenu=58]=`ContextMenu`,e[e.F1=59]=`F1`,e[e.F2=60]=`F2`,e[e.F3=61]=`F3`,e[e.F4=62]=`F4`,e[e.F5=63]=`F5`,e[e.F6=64]=`F6`,e[e.F7=65]=`F7`,e[e.F8=66]=`F8`,e[e.F9=67]=`F9`,e[e.F10=68]=`F10`,e[e.F11=69]=`F11`,e[e.F12=70]=`F12`,e[e.F13=71]=`F13`,e[e.F14=72]=`F14`,e[e.F15=73]=`F15`,e[e.F16=74]=`F16`,e[e.F17=75]=`F17`,e[e.F18=76]=`F18`,e[e.F19=77]=`F19`,e[e.F20=78]=`F20`,e[e.F21=79]=`F21`,e[e.F22=80]=`F22`,e[e.F23=81]=`F23`,e[e.F24=82]=`F24`,e[e.NumLock=83]=`NumLock`,e[e.ScrollLock=84]=`ScrollLock`,e[e.Semicolon=85]=`Semicolon`,e[e.Equal=86]=`Equal`,e[e.Comma=87]=`Comma`,e[e.Minus=88]=`Minus`,e[e.Period=89]=`Period`,e[e.Slash=90]=`Slash`,e[e.Backquote=91]=`Backquote`,e[e.BracketLeft=92]=`BracketLeft`,e[e.Backslash=93]=`Backslash`,e[e.BracketRight=94]=`BracketRight`,e[e.Quote=95]=`Quote`,e[e.OEM_8=96]=`OEM_8`,e[e.IntlBackslash=97]=`IntlBackslash`,e[e.Numpad0=98]=`Numpad0`,e[e.Numpad1=99]=`Numpad1`,e[e.Numpad2=100]=`Numpad2`,e[e.Numpad3=101]=`Numpad3`,e[e.Numpad4=102]=`Numpad4`,e[e.Numpad5=103]=`Numpad5`,e[e.Numpad6=104]=`Numpad6`,e[e.Numpad7=105]=`Numpad7`,e[e.Numpad8=106]=`Numpad8`,e[e.Numpad9=107]=`Numpad9`,e[e.NumpadMultiply=108]=`NumpadMultiply`,e[e.NumpadAdd=109]=`NumpadAdd`,e[e.NUMPAD_SEPARATOR=110]=`NUMPAD_SEPARATOR`,e[e.NumpadSubtract=111]=`NumpadSubtract`,e[e.NumpadDecimal=112]=`NumpadDecimal`,e[e.NumpadDivide=113]=`NumpadDivide`,e[e.KEY_IN_COMPOSITION=114]=`KEY_IN_COMPOSITION`,e[e.ABNT_C1=115]=`ABNT_C1`,e[e.ABNT_C2=116]=`ABNT_C2`,e[e.AudioVolumeMute=117]=`AudioVolumeMute`,e[e.AudioVolumeUp=118]=`AudioVolumeUp`,e[e.AudioVolumeDown=119]=`AudioVolumeDown`,e[e.BrowserSearch=120]=`BrowserSearch`,e[e.BrowserHome=121]=`BrowserHome`,e[e.BrowserBack=122]=`BrowserBack`,e[e.BrowserForward=123]=`BrowserForward`,e[e.MediaTrackNext=124]=`MediaTrackNext`,e[e.MediaTrackPrevious=125]=`MediaTrackPrevious`,e[e.MediaStop=126]=`MediaStop`,e[e.MediaPlayPause=127]=`MediaPlayPause`,e[e.LaunchMediaPlayer=128]=`LaunchMediaPlayer`,e[e.LaunchMail=129]=`LaunchMail`,e[e.LaunchApp2=130]=`LaunchApp2`,e[e.Clear=131]=`Clear`,e[e.MAX_VALUE=132]=`MAX_VALUE`})(ir||={});var ar;(function(e){e[e.Hint=1]=`Hint`,e[e.Info=2]=`Info`,e[e.Warning=4]=`Warning`,e[e.Error=8]=`Error`})(ar||={});var or;(function(e){e[e.Unnecessary=1]=`Unnecessary`,e[e.Deprecated=2]=`Deprecated`})(or||={});var sr;(function(e){e[e.Inline=1]=`Inline`,e[e.Gutter=2]=`Gutter`})(sr||={});var cr;(function(e){e[e.Normal=1]=`Normal`,e[e.Underlined=2]=`Underlined`})(cr||={});var lr;(function(e){e[e.UNKNOWN=0]=`UNKNOWN`,e[e.TEXTAREA=1]=`TEXTAREA`,e[e.GUTTER_GLYPH_MARGIN=2]=`GUTTER_GLYPH_MARGIN`,e[e.GUTTER_LINE_NUMBERS=3]=`GUTTER_LINE_NUMBERS`,e[e.GUTTER_LINE_DECORATIONS=4]=`GUTTER_LINE_DECORATIONS`,e[e.GUTTER_VIEW_ZONE=5]=`GUTTER_VIEW_ZONE`,e[e.CONTENT_TEXT=6]=`CONTENT_TEXT`,e[e.CONTENT_EMPTY=7]=`CONTENT_EMPTY`,e[e.CONTENT_VIEW_ZONE=8]=`CONTENT_VIEW_ZONE`,e[e.CONTENT_WIDGET=9]=`CONTENT_WIDGET`,e[e.OVERVIEW_RULER=10]=`OVERVIEW_RULER`,e[e.SCROLLBAR=11]=`SCROLLBAR`,e[e.OVERLAY_WIDGET=12]=`OVERLAY_WIDGET`,e[e.OUTSIDE_EDITOR=13]=`OUTSIDE_EDITOR`})(lr||={});var ur;(function(e){e[e.AIGenerated=1]=`AIGenerated`})(ur||={});var dr;(function(e){e[e.Invoke=0]=`Invoke`,e[e.Automatic=1]=`Automatic`})(dr||={});var fr;(function(e){e[e.TOP_RIGHT_CORNER=0]=`TOP_RIGHT_CORNER`,e[e.BOTTOM_RIGHT_CORNER=1]=`BOTTOM_RIGHT_CORNER`,e[e.TOP_CENTER=2]=`TOP_CENTER`})(fr||={});var pr;(function(e){e[e.Left=1]=`Left`,e[e.Center=2]=`Center`,e[e.Right=4]=`Right`,e[e.Full=7]=`Full`})(pr||={});var mr;(function(e){e[e.Word=0]=`Word`,e[e.Line=1]=`Line`,e[e.Suggest=2]=`Suggest`})(mr||={});var hr;(function(e){e[e.Left=0]=`Left`,e[e.Right=1]=`Right`,e[e.None=2]=`None`,e[e.LeftOfInjectedText=3]=`LeftOfInjectedText`,e[e.RightOfInjectedText=4]=`RightOfInjectedText`})(hr||={});var gr;(function(e){e[e.Off=0]=`Off`,e[e.On=1]=`On`,e[e.Relative=2]=`Relative`,e[e.Interval=3]=`Interval`,e[e.Custom=4]=`Custom`})(gr||={});var _r;(function(e){e[e.None=0]=`None`,e[e.Text=1]=`Text`,e[e.Blocks=2]=`Blocks`})(_r||={});var vr;(function(e){e[e.Smooth=0]=`Smooth`,e[e.Immediate=1]=`Immediate`})(vr||={});var yr;(function(e){e[e.Auto=1]=`Auto`,e[e.Hidden=2]=`Hidden`,e[e.Visible=3]=`Visible`})(yr||={});var br;(function(e){e[e.LTR=0]=`LTR`,e[e.RTL=1]=`RTL`})(br||={});var xr;(function(e){e.Off=`off`,e.OnCode=`onCode`,e.On=`on`})(xr||={});var Sr;(function(e){e[e.Invoke=1]=`Invoke`,e[e.TriggerCharacter=2]=`TriggerCharacter`,e[e.ContentChange=3]=`ContentChange`})(Sr||={});var Cr;(function(e){e[e.File=0]=`File`,e[e.Module=1]=`Module`,e[e.Namespace=2]=`Namespace`,e[e.Package=3]=`Package`,e[e.Class=4]=`Class`,e[e.Method=5]=`Method`,e[e.Property=6]=`Property`,e[e.Field=7]=`Field`,e[e.Constructor=8]=`Constructor`,e[e.Enum=9]=`Enum`,e[e.Interface=10]=`Interface`,e[e.Function=11]=`Function`,e[e.Variable=12]=`Variable`,e[e.Constant=13]=`Constant`,e[e.String=14]=`String`,e[e.Number=15]=`Number`,e[e.Boolean=16]=`Boolean`,e[e.Array=17]=`Array`,e[e.Object=18]=`Object`,e[e.Key=19]=`Key`,e[e.Null=20]=`Null`,e[e.EnumMember=21]=`EnumMember`,e[e.Struct=22]=`Struct`,e[e.Event=23]=`Event`,e[e.Operator=24]=`Operator`,e[e.TypeParameter=25]=`TypeParameter`})(Cr||={});var wr;(function(e){e[e.Deprecated=1]=`Deprecated`})(wr||={});var Tr;(function(e){e[e.Hidden=0]=`Hidden`,e[e.Blink=1]=`Blink`,e[e.Smooth=2]=`Smooth`,e[e.Phase=3]=`Phase`,e[e.Expand=4]=`Expand`,e[e.Solid=5]=`Solid`})(Tr||={});var Er;(function(e){e[e.Line=1]=`Line`,e[e.Block=2]=`Block`,e[e.Underline=3]=`Underline`,e[e.LineThin=4]=`LineThin`,e[e.BlockOutline=5]=`BlockOutline`,e[e.UnderlineThin=6]=`UnderlineThin`})(Er||={});var Dr;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]=`AlwaysGrowsWhenTypingAtEdges`,e[e.NeverGrowsWhenTypingAtEdges=1]=`NeverGrowsWhenTypingAtEdges`,e[e.GrowsOnlyWhenTypingBefore=2]=`GrowsOnlyWhenTypingBefore`,e[e.GrowsOnlyWhenTypingAfter=3]=`GrowsOnlyWhenTypingAfter`})(Dr||={});var Or;(function(e){e[e.None=0]=`None`,e[e.Same=1]=`Same`,e[e.Indent=2]=`Indent`,e[e.DeepIndent=3]=`DeepIndent`})(Or||={});var kr=class{static{this.CtrlCmd=2048}static{this.Shift=1024}static{this.Alt=512}static{this.WinCtrl=256}static chord(e,t){return gn(e,t)}};function Ar(){return{editor:void 0,languages:void 0,CancellationTokenSource:nn,Emitter:E,KeyCode:ir,KeyMod:kr,Position:V,Range:H,Selection:_n,SelectionDirection:br,MarkerSeverity:ar,MarkerTag:or,Uri:R,Token:wn}}var jr=class e{static{this.CHANNEL_NAME=`editorWorkerHost`}static getChannel(t){return t.getChannel(e.CHANNEL_NAME)}static setChannel(t,n){t.setChannel(e.CHANNEL_NAME,n)}},Mr,Nr,Pr=class{constructor(e,t){this.uri=e,this.value=t}};function Fr(e){return Array.isArray(e)}(class e{static{this.defaultToKey=e=>e.toString()}constructor(t,n){if(this[Mr]=`ResourceMap`,t instanceof e)this.map=new Map(t.map),this.toKey=n??e.defaultToKey;else if(Fr(t)){this.map=new Map,this.toKey=n??e.defaultToKey;for(let[e,n]of t)this.set(e,n)}else this.map=new Map,this.toKey=t??e.defaultToKey}set(e,t){return this.map.set(this.toKey(e),new Pr(e,t)),this}get(e){return this.map.get(this.toKey(e))?.value}has(e){return this.map.has(this.toKey(e))}get size(){return this.map.size}clear(){this.map.clear()}delete(e){return this.map.delete(this.toKey(e))}forEach(e,t){t!==void 0&&(e=e.bind(t));for(let[t,n]of this.map)e(n.value,n.uri,this)}*values(){for(let e of this.map.values())yield e.value}*keys(){for(let e of this.map.values())yield e.uri}*entries(){for(let e of this.map.values())yield[e.uri,e.value]}*[(Mr=Symbol.toStringTag,Symbol.iterator)](){for(let[,e]of this.map)yield[e.uri,e.value]}});var Ir=class{constructor(){this[Nr]=`LinkedMap`,this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0,this._state=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0,this._state++}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}get first(){return this._head?.value}get last(){return this._tail?.value}has(e){return this._map.has(e)}get(e,t=0){let n=this._map.get(e);if(n)return t!==0&&this.touch(n,t),n.value}set(e,t,n=0){let r=this._map.get(e);if(r)r.value=t,n!==0&&this.touch(r,n);else{switch(r={key:e,value:t,next:void 0,previous:void 0},n){case 0:this.addItemLast(r);break;case 1:this.addItemFirst(r);break;case 2:this.addItemLast(r);break;default:this.addItemLast(r);break}this._map.set(e,r),this._size++}return this}delete(e){return!!this.remove(e)}remove(e){let t=this._map.get(e);if(t)return this._map.delete(e),this.removeItem(t),this._size--,t.value}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw Error(`Invalid list`);let e=this._head;return this._map.delete(e.key),this.removeItem(e),this._size--,e.value}forEach(e,t){let n=this._state,r=this._head;for(;r;){if(t?e.bind(t)(r.value,r.key,this):e(r.value,r.key,this),this._state!==n)throw Error(`LinkedMap got modified during iteration.`);r=r.next}}keys(){let e=this,t=this._state,n=this._head,r={[Symbol.iterator](){return r},next(){if(e._state!==t)throw Error(`LinkedMap got modified during iteration.`);if(n){let e={value:n.key,done:!1};return n=n.next,e}else return{value:void 0,done:!0}}};return r}values(){let e=this,t=this._state,n=this._head,r={[Symbol.iterator](){return r},next(){if(e._state!==t)throw Error(`LinkedMap got modified during iteration.`);if(n){let e={value:n.value,done:!1};return n=n.next,e}else return{value:void 0,done:!0}}};return r}entries(){let e=this,t=this._state,n=this._head,r={[Symbol.iterator](){return r},next(){if(e._state!==t)throw Error(`LinkedMap got modified during iteration.`);if(n){let e={value:[n.key,n.value],done:!1};return n=n.next,e}else return{value:void 0,done:!0}}};return r}[(Nr=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}trimOld(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this._head,n=this.size;for(;t&&n>e;)this._map.delete(t.key),t=t.next,n--;this._head=t,this._size=n,t&&(t.previous=void 0),this._state++}trimNew(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this._tail,n=this.size;for(;t&&n>e;)this._map.delete(t.key),t=t.previous,n--;this._tail=t,this._size=n,t&&(t.next=void 0),this._state++}addItemFirst(e){if(!this._head&&!this._tail)this._tail=e;else if(this._head)e.next=this._head,this._head.previous=e;else throw Error(`Invalid list`);this._head=e,this._state++}addItemLast(e){if(!this._head&&!this._tail)this._head=e;else if(this._tail)e.previous=this._tail,this._tail.next=e;else throw Error(`Invalid list`);this._tail=e,this._state++}removeItem(e){if(e===this._head&&e===this._tail)this._head=void 0,this._tail=void 0;else if(e===this._head){if(!e.next)throw Error(`Invalid list`);e.next.previous=void 0,this._head=e.next}else if(e===this._tail){if(!e.previous)throw Error(`Invalid list`);e.previous.next=void 0,this._tail=e.previous}else{let t=e.next,n=e.previous;if(!t||!n)throw Error(`Invalid list`);t.previous=n,n.next=t}e.next=void 0,e.previous=void 0,this._state++}touch(e,t){if(!this._head||!this._tail)throw Error(`Invalid list`);if(!(t!==1&&t!==2)){if(t===1){if(e===this._head)return;let t=e.next,n=e.previous;e===this._tail?(n.next=void 0,this._tail=n):(t.previous=n,n.next=t),e.previous=void 0,e.next=this._head,this._head.previous=e,this._head=e,this._state++}else if(t===2){if(e===this._tail)return;let t=e.next,n=e.previous;e===this._head?(t.previous=void 0,this._head=t):(t.previous=n,n.next=t),e.next=void 0,e.previous=this._tail,this._tail.next=e,this._tail=e,this._state++}}}toJSON(){let e=[];return this.forEach((t,n)=>{e.push([n,t])}),e}fromJSON(e){this.clear();for(let[t,n]of e)this.set(t,n)}},Lr=class extends Ir{constructor(e,t=1){super(),this._limit=e,this._ratio=Math.min(Math.max(0,t),1)}get limit(){return this._limit}set limit(e){this._limit=e,this.checkTrim()}get(e,t=2){return super.get(e,t)}peek(e){return super.get(e,0)}set(e,t){return super.set(e,t,2),this}checkTrim(){this.size>this._limit&&this.trim(Math.round(this._limit*this._ratio))}},Rr=class extends Lr{constructor(e,t=1){super(e,t)}trim(e){this.trimOld(e)}set(e,t){return super.set(e,t),this.checkTrim(),this}},zr=class{constructor(){this.map=new Map}add(e,t){let n=this.map.get(e);n||(n=new Set,this.map.set(e,n)),n.add(t)}delete(e,t){let n=this.map.get(e);n&&(n.delete(t),n.size===0&&this.map.delete(e))}forEach(e,t){let n=this.map.get(e);n&&n.forEach(t)}get(e){return this.map.get(e)||new Set}};new Rr(10);function Br(e){let t=[];for(;Object.prototype!==e;)t=t.concat(Object.getOwnPropertyNames(e)),e=Object.getPrototypeOf(e);return t}function Vr(e){let t=[];for(let n of Br(e))typeof e[n]==`function`&&t.push(n);return t}function Hr(e,t){let n=e=>function(){let n=Array.prototype.slice.call(arguments,0);return t(e,n)},r={};for(let t of e)r[t]=n(t);return r}var Ur;(function(e){e[e.Left=1]=`Left`,e[e.Center=2]=`Center`,e[e.Right=4]=`Right`,e[e.Full=7]=`Full`})(Ur||={});var Wr;(function(e){e[e.Left=1]=`Left`,e[e.Center=2]=`Center`,e[e.Right=3]=`Right`})(Wr||={});var Gr;(function(e){e[e.Both=0]=`Both`,e[e.Right=1]=`Right`,e[e.Left=2]=`Left`,e[e.None=3]=`None`})(Gr||={});function Kr(e,t,n,r,i){if(r===0)return!0;let a=t.charCodeAt(r-1);if(e.get(a)!==0||a===13||a===10)return!0;if(i>0){let n=t.charCodeAt(r);if(e.get(n)!==0)return!0}return!1}function qr(e,t,n,r,i){if(r+i===n)return!0;let a=t.charCodeAt(r+i);if(e.get(a)!==0||a===13||a===10)return!0;if(i>0){let n=t.charCodeAt(r+i-1);if(e.get(n)!==0)return!0}return!1}function Jr(e,t,n,r,i){return Kr(e,t,n,r,i)&&qr(e,t,n,r,i)}var Yr=class{constructor(e,t){this._wordSeparators=e,this._searchRegex=t,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(e){this._searchRegex.lastIndex=e,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(e){let t=e.length,n;do{if(this._prevMatchStartIndex+this._prevMatchLength===t||(n=this._searchRegex.exec(e),!n))return null;let r=n.index,i=n[0].length;if(r===this._prevMatchStartIndex&&i===this._prevMatchLength){if(i===0){Ne(e,t,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=r,this._prevMatchLength=i,!this._wordSeparators||Jr(this._wordSeparators,e,t,r,i))return n}while(n);return null}};function Xr(e,t=`Unreachable`){throw Error(t)}function Zr(e){e()||(e(),t(new s(`Assertion Failed`)))}function Qr(e,t){let n=0;for(;n<e.length-1;){let r=e[n],i=e[n+1];if(!t(r,i))return!1;n++}return!0}function $r(e=``){let t=`(-?\\d*\\.\\d\\w*)|([^`;for(let n of`\`~!@#$%^&*()-=+[{]}\\|;:'",.<>/?`){if(e.indexOf(n)>=0)continue;t+=`\\`+n}return t+=`\\s]+)`,new RegExp(t,`g`)}const ei=$r();function ti(e){let t=ei;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n=`g`;e.ignoreCase&&(n+=`i`),e.multiline&&(n+=`m`),e.unicode&&(n+=`u`),t=new RegExp(e.source,n)}return t.lastIndex=0,t}const ni=new y;ni.unshift({maxLen:1e3,windowSize:15,timeBudget:150});function ri(e,t,n,r,i){if(t=ti(t),i||=l.first(ni),n.length>i.maxLen){let a=e-i.maxLen/2;return a<0?a=0:r+=a,n=n.substring(a,e+i.maxLen/2),ri(e,t,n,r,i)}let a=Date.now(),o=e-1-r,s=-1,c=null;for(let e=1;!(Date.now()-a>=i.timeBudget);e++){let r=o-i.windowSize*e;t.lastIndex=Math.max(0,r);let a=ii(t,n,o,s);if(!a&&c||(c=a,r<=0))break;s=r}if(c){let e={word:c[0],startColumn:r+1+c.index,endColumn:r+1+c.index+c[0].length};return t.lastIndex=0,e}return null}function ii(e,t,n,r){let i;for(;i=e.exec(t);){let t=i.index||0;if(t<=n&&e.lastIndex>=n)return i;if(r>0&&t>r)return null}return null}var ai=class{static computeUnicodeHighlights(e,t,n){let r=n?n.startLineNumber:1,i=n?n.endLineNumber:e.getLineCount(),a=new si(t),o=a.getCandidateCodePoints(),s;s=o===`allNonBasicAscii`?RegExp(`[^\\t\\n\\r\\x20-\\x7E]`,`g`):RegExp(`${oi(Array.from(o))}`,`g`);let c=new Yr(null,s),l=[],u=!1,d,f=0,p=0,m=0;forLoop:for(let t=r,n=i;t<=n;t++){let n=e.getLineContent(t),r=n.length;c.reset(0);do if(d=c.next(n),d){let e=d.index,i=d.index+d[0].length;if(e>0){let t=n.charCodeAt(e-1);Ae(t)&&e--}if(i+1<r){let e=n.charCodeAt(i-1);Ae(e)&&i++}let o=n.substring(e,i),s=ri(e+1,ei,n,0);s&&s.endColumn<=e+1&&(s=null);let c=a.shouldHighlightNonBasicASCII(o,s?s.word:null);if(c!==0){if(c===3?f++:c===2?p++:c===1?m++:Xr(c),l.length>=1e3){u=!0;break forLoop}l.push(new H(t,e+1,t,i+1))}}while(d)}return{ranges:l,hasMore:u,ambiguousCharacterCount:f,invisibleCharacterCount:p,nonBasicAsciiCharacterCount:m}}static computeUnicodeHighlightReason(e,t){let n=new si(t);switch(n.shouldHighlightNonBasicASCII(e,null)){case 0:return null;case 2:return{kind:1};case 3:{let r=e.codePointAt(0),i=n.ambiguousCharacters.getPrimaryConfusable(r),a=Le.getLocales().filter(e=>!Le.getInstance(new Set([...t.allowedLocales,e])).isAmbiguous(r));return{kind:0,confusableWith:String.fromCodePoint(i),notAmbiguousInLocales:a}}case 1:return{kind:2}}}};function oi(e,t){return`[${Te(e.map(e=>String.fromCodePoint(e)).join(``))}]`}var si=class{constructor(e){this.options=e,this.allowedCodePoints=new Set(e.allowedCodePoints),this.ambiguousCharacters=Le.getInstance(new Set(e.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return`allNonBasicAscii`;let e=new Set;if(this.options.invisibleCharacters)for(let t of Re.codePoints)ci(String.fromCodePoint(t))||e.add(t);if(this.options.ambiguousCharacters)for(let t of this.ambiguousCharacters.getConfusableCodePoints())e.add(t);for(let t of this.allowedCodePoints)e.delete(t);return e}shouldHighlightNonBasicASCII(e,t){let n=e.codePointAt(0);if(this.allowedCodePoints.has(n))return 0;if(this.options.nonBasicASCII)return 1;let r=!1,i=!1;if(t)for(let e of t){let t=e.codePointAt(0),n=Fe(e);r||=n,!n&&!this.ambiguousCharacters.isAmbiguous(t)&&!Re.isInvisibleCharacter(t)&&(i=!0)}return!r&&i?0:this.options.invisibleCharacters&&!ci(e)&&Re.isInvisibleCharacter(n)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(n)?3:0}};function ci(e){return e===` `||e===`
`||e===`	`}var li=class{constructor(e,t,n){this.changes=e,this.moves=t,this.hitTimeout=n}},ui=class{constructor(e,t){this.lineRangeMapping=e,this.changes=t}},G=class e{static addRange(t,n){let r=0;for(;r<n.length&&n[r].endExclusive<t.start;)r++;let i=r;for(;i<n.length&&n[i].start<=t.endExclusive;)i++;if(r===i)n.splice(r,0,t);else{let a=Math.min(t.start,n[r].start),o=Math.max(t.endExclusive,n[i-1].endExclusive);n.splice(r,i-r,new e(a,o))}}static tryCreate(t,n){if(!(t>n))return new e(t,n)}static ofLength(t){return new e(0,t)}static ofStartAndLength(t,n){return new e(t,t+n)}constructor(e,t){if(this.start=e,this.endExclusive=t,e>t)throw new s(`Invalid range: ${this.toString()}`)}get isEmpty(){return this.start===this.endExclusive}delta(t){return new e(this.start+t,this.endExclusive+t)}deltaStart(t){return new e(this.start+t,this.endExclusive)}deltaEnd(t){return new e(this.start,this.endExclusive+t)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}contains(e){return this.start<=e&&e<this.endExclusive}join(t){return new e(Math.min(this.start,t.start),Math.max(this.endExclusive,t.endExclusive))}intersect(t){let n=Math.max(this.start,t.start),r=Math.min(this.endExclusive,t.endExclusive);if(n<=r)return new e(n,r)}intersects(e){let t=Math.max(this.start,e.start),n=Math.min(this.endExclusive,e.endExclusive);return t<n}isBefore(e){return this.endExclusive<=e.start}isAfter(e){return this.start>=e.endExclusive}slice(e){return e.slice(this.start,this.endExclusive)}substring(e){return e.substring(this.start,this.endExclusive)}clip(e){if(this.isEmpty)throw new s(`Invalid clipping range: ${this.toString()}`);return Math.max(this.start,Math.min(this.endExclusive-1,e))}clipCyclic(e){if(this.isEmpty)throw new s(`Invalid clipping range: ${this.toString()}`);return e<this.start?this.endExclusive-(this.start-e)%this.length:e>=this.endExclusive?this.start+(e-this.start)%this.length:e}forEach(e){for(let t=this.start;t<this.endExclusive;t++)e(t)}};function K(e,t){let n=di(e,t);return n===-1?void 0:e[n]}function di(e,t,n=0,r=e.length){let i=n,a=r;for(;i<a;){let n=Math.floor((i+a)/2);t(e[n])?i=n+1:a=n}return i-1}function fi(e,t){let n=pi(e,t);return n===e.length?void 0:e[n]}function pi(e,t,n=0,r=e.length){let i=n,a=r;for(;i<a;){let n=Math.floor((i+a)/2);t(e[n])?a=n:i=n+1}return i}var mi=class e{static{this.assertInvariants=!1}constructor(e){this._array=e,this._findLastMonotonousLastIdx=0}findLastMonotonous(t){if(e.assertInvariants){if(this._prevFindLastPredicate){for(let e of this._array)if(this._prevFindLastPredicate(e)&&!t(e))throw Error(`MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.`)}this._prevFindLastPredicate=t}let n=di(this._array,t,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=n+1,n===-1?void 0:this._array[n]}},q=class e{static fromRangeInclusive(t){return new e(t.startLineNumber,t.endLineNumber+1)}static joinMany(e){if(e.length===0)return[];let t=new hi(e[0].slice());for(let n=1;n<e.length;n++)t=t.getUnion(new hi(e[n].slice()));return t.ranges}static join(t){if(t.length===0)throw new s(`lineRanges cannot be empty`);let n=t[0].startLineNumber,r=t[0].endLineNumberExclusive;for(let e=1;e<t.length;e++)n=Math.min(n,t[e].startLineNumber),r=Math.max(r,t[e].endLineNumberExclusive);return new e(n,r)}static ofLength(t,n){return new e(t,t+n)}static deserialize(t){return new e(t[0],t[1])}constructor(e,t){if(e>t)throw new s(`startLineNumber ${e} cannot be after endLineNumberExclusive ${t}`);this.startLineNumber=e,this.endLineNumberExclusive=t}contains(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(t){return new e(this.startLineNumber+t,this.endLineNumberExclusive+t)}deltaLength(t){return new e(this.startLineNumber,this.endLineNumberExclusive+t)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(t){return new e(Math.min(this.startLineNumber,t.startLineNumber),Math.max(this.endLineNumberExclusive,t.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}intersect(t){let n=Math.max(this.startLineNumber,t.startLineNumber),r=Math.min(this.endLineNumberExclusive,t.endLineNumberExclusive);if(n<=r)return new e(n,r)}intersectsStrict(e){return this.startLineNumber<e.endLineNumberExclusive&&e.startLineNumber<this.endLineNumberExclusive}overlapOrTouch(e){return this.startLineNumber<=e.endLineNumberExclusive&&e.startLineNumber<=this.endLineNumberExclusive}equals(e){return this.startLineNumber===e.startLineNumber&&this.endLineNumberExclusive===e.endLineNumberExclusive}toInclusiveRange(){return this.isEmpty?null:new H(this.startLineNumber,1,this.endLineNumberExclusive-1,2**53-1)}toExclusiveRange(){return new H(this.startLineNumber,1,this.endLineNumberExclusive,1)}mapToLineArray(e){let t=[];for(let n=this.startLineNumber;n<this.endLineNumberExclusive;n++)t.push(e(n));return t}forEach(e){for(let t=this.startLineNumber;t<this.endLineNumberExclusive;t++)e(t)}serialize(){return[this.startLineNumber,this.endLineNumberExclusive]}includes(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}toOffsetRange(){return new G(this.startLineNumber-1,this.endLineNumberExclusive-1)}},hi=class e{constructor(e=[]){this._normalizedRanges=e}get ranges(){return this._normalizedRanges}addRange(e){if(e.length===0)return;let t=pi(this._normalizedRanges,t=>t.endLineNumberExclusive>=e.startLineNumber),n=di(this._normalizedRanges,t=>t.startLineNumber<=e.endLineNumberExclusive)+1;if(t===n)this._normalizedRanges.splice(t,0,e);else if(t===n-1){let n=this._normalizedRanges[t];this._normalizedRanges[t]=n.join(e)}else{let r=this._normalizedRanges[t].join(this._normalizedRanges[n-1]).join(e);this._normalizedRanges.splice(t,n-t,r)}}contains(e){let t=K(this._normalizedRanges,t=>t.startLineNumber<=e);return!!t&&t.endLineNumberExclusive>e}intersects(e){let t=K(this._normalizedRanges,t=>t.startLineNumber<e.endLineNumberExclusive);return!!t&&t.endLineNumberExclusive>e.startLineNumber}getUnion(t){if(this._normalizedRanges.length===0)return t;if(t._normalizedRanges.length===0)return this;let n=[],r=0,i=0,a=null;for(;r<this._normalizedRanges.length||i<t._normalizedRanges.length;){let e=null;if(r<this._normalizedRanges.length&&i<t._normalizedRanges.length){let n=this._normalizedRanges[r],a=t._normalizedRanges[i];n.startLineNumber<a.startLineNumber?(e=n,r++):(e=a,i++)}else r<this._normalizedRanges.length?(e=this._normalizedRanges[r],r++):(e=t._normalizedRanges[i],i++);a===null?a=e:a.endLineNumberExclusive>=e.startLineNumber?a=new q(a.startLineNumber,Math.max(a.endLineNumberExclusive,e.endLineNumberExclusive)):(n.push(a),a=e)}return a!==null&&n.push(a),new e(n)}subtractFrom(t){let n=pi(this._normalizedRanges,e=>e.endLineNumberExclusive>=t.startLineNumber),r=di(this._normalizedRanges,e=>e.startLineNumber<=t.endLineNumberExclusive)+1;if(n===r)return new e([t]);let i=[],a=t.startLineNumber;for(let e=n;e<r;e++){let t=this._normalizedRanges[e];t.startLineNumber>a&&i.push(new q(a,t.startLineNumber)),a=t.endLineNumberExclusive}return a<t.endLineNumberExclusive&&i.push(new q(a,t.endLineNumberExclusive)),new e(i)}toString(){return this._normalizedRanges.map(e=>e.toString()).join(`, `)}getIntersection(t){let n=[],r=0,i=0;for(;r<this._normalizedRanges.length&&i<t._normalizedRanges.length;){let e=this._normalizedRanges[r],a=t._normalizedRanges[i],o=e.intersect(a);o&&!o.isEmpty&&n.push(o),e.endLineNumberExclusive<a.endLineNumberExclusive?r++:i++}return new e(n)}getWithDelta(t){return new e(this._normalizedRanges.map(e=>e.delta(t)))}};(class e{static{this.zero=new e(0,0)}static betweenPositions(t,n){return t.lineNumber===n.lineNumber?new e(0,n.column-t.column):new e(n.lineNumber-t.lineNumber,n.column-1)}static ofRange(t){return e.betweenPositions(t.getStartPosition(),t.getEndPosition())}static ofText(t){let n=0,r=0;for(let e of t)e===`
`?(n++,r=0):r++;return new e(n,r)}constructor(e,t){this.lineCount=e,this.columnCount=t}isGreaterThanOrEqualTo(e){return this.lineCount===e.lineCount?this.columnCount>=e.columnCount:this.lineCount>e.lineCount}createRange(e){return this.lineCount===0?new H(e.lineNumber,e.column,e.lineNumber,e.column+this.columnCount):new H(e.lineNumber,e.column,e.lineNumber+this.lineCount,this.columnCount+1)}addToPosition(e){return this.lineCount===0?new V(e.lineNumber,e.column+this.columnCount):new V(e.lineNumber+this.lineCount,this.columnCount+1)}toString(){return`${this.lineCount},${this.columnCount}`}});var gi=class{constructor(e,t){this.range=e,this.text=t}toSingleEditOperation(){return{range:this.range,text:this.text}}},_i=class e{static inverse(t,n,r){let i=[],a=1,o=1;for(let n of t){let t=new e(new q(a,n.original.startLineNumber),new q(o,n.modified.startLineNumber));t.modified.isEmpty||i.push(t),a=n.original.endLineNumberExclusive,o=n.modified.endLineNumberExclusive}let s=new e(new q(a,n+1),new q(o,r+1));return s.modified.isEmpty||i.push(s),i}static clip(t,n,r){let i=[];for(let a of t){let t=a.original.intersect(n),o=a.modified.intersect(r);t&&!t.isEmpty&&o&&!o.isEmpty&&i.push(new e(t,o))}return i}constructor(e,t){this.original=e,this.modified=t}toString(){return`{${this.original.toString()}->${this.modified.toString()}}`}flip(){return new e(this.modified,this.original)}join(t){return new e(this.original.join(t.original),this.modified.join(t.modified))}toRangeMapping(){let e=this.original.toInclusiveRange(),t=this.modified.toInclusiveRange();if(e&&t)return new J(e,t);if(this.original.startLineNumber===1||this.modified.startLineNumber===1){if(!(this.modified.startLineNumber===1&&this.original.startLineNumber===1))throw new s(`not a valid diff`);return new J(new H(this.original.startLineNumber,1,this.original.endLineNumberExclusive,1),new H(this.modified.startLineNumber,1,this.modified.endLineNumberExclusive,1))}else return new J(new H(this.original.startLineNumber-1,2**53-1,this.original.endLineNumberExclusive-1,2**53-1),new H(this.modified.startLineNumber-1,2**53-1,this.modified.endLineNumberExclusive-1,2**53-1))}toRangeMapping2(e,t){if(yi(this.original.endLineNumberExclusive,e)&&yi(this.modified.endLineNumberExclusive,t))return new J(new H(this.original.startLineNumber,1,this.original.endLineNumberExclusive,1),new H(this.modified.startLineNumber,1,this.modified.endLineNumberExclusive,1));if(!this.original.isEmpty&&!this.modified.isEmpty)return new J(H.fromPositions(new V(this.original.startLineNumber,1),vi(new V(this.original.endLineNumberExclusive-1,2**53-1),e)),H.fromPositions(new V(this.modified.startLineNumber,1),vi(new V(this.modified.endLineNumberExclusive-1,2**53-1),t)));if(this.original.startLineNumber>1&&this.modified.startLineNumber>1)return new J(H.fromPositions(vi(new V(this.original.startLineNumber-1,2**53-1),e),vi(new V(this.original.endLineNumberExclusive-1,2**53-1),e)),H.fromPositions(vi(new V(this.modified.startLineNumber-1,2**53-1),t),vi(new V(this.modified.endLineNumberExclusive-1,2**53-1),t)));throw new s}};function vi(e,t){if(e.lineNumber<1)return new V(1,1);if(e.lineNumber>t.length)return new V(t.length,t[t.length-1].length+1);let n=t[e.lineNumber-1];return e.column>n.length+1?new V(e.lineNumber,n.length+1):e}function yi(e,t){return e>=1&&e<=t.length}var bi=class e extends _i{static fromRangeMappings(t){let n=q.join(t.map(e=>q.fromRangeInclusive(e.originalRange))),r=q.join(t.map(e=>q.fromRangeInclusive(e.modifiedRange)));return new e(n,r,t)}constructor(e,t,n){super(e,t),this.innerChanges=n}flip(){return new e(this.modified,this.original,this.innerChanges?.map(e=>e.flip()))}withInnerChangesFromLineRanges(){return new e(this.original,this.modified,[this.toRangeMapping()])}},J=class e{static assertSorted(e){for(let t=1;t<e.length;t++){let n=e[t-1],r=e[t];if(!(n.originalRange.getEndPosition().isBeforeOrEqual(r.originalRange.getStartPosition())&&n.modifiedRange.getEndPosition().isBeforeOrEqual(r.modifiedRange.getStartPosition())))throw new s(`Range mappings must be sorted`)}}constructor(e,t){this.originalRange=e,this.modifiedRange=t}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}flip(){return new e(this.modifiedRange,this.originalRange)}toTextEdit(e){let t=e.getValueOfRange(this.modifiedRange);return new gi(this.originalRange,t)}},xi=class{computeDiff(e,t,n){let r=new Oi(e,t,{maxComputationTime:n.maxComputationTimeMs,shouldIgnoreTrimWhitespace:n.ignoreTrimWhitespace,shouldComputeCharChanges:!0,shouldMakePrettyDiff:!0,shouldPostProcessCharChanges:!0}).computeDiff(),i=[],a=null;for(let e of r.changes){let t;t=e.originalEndLineNumber===0?new q(e.originalStartLineNumber+1,e.originalStartLineNumber+1):new q(e.originalStartLineNumber,e.originalEndLineNumber+1);let n;n=e.modifiedEndLineNumber===0?new q(e.modifiedStartLineNumber+1,e.modifiedStartLineNumber+1):new q(e.modifiedStartLineNumber,e.modifiedEndLineNumber+1);let r=new bi(t,n,e.charChanges?.map(e=>new J(new H(e.originalStartLineNumber,e.originalStartColumn,e.originalEndLineNumber,e.originalEndColumn),new H(e.modifiedStartLineNumber,e.modifiedStartColumn,e.modifiedEndLineNumber,e.modifiedEndColumn))));a&&(a.modified.endLineNumberExclusive===r.modified.startLineNumber||a.original.endLineNumberExclusive===r.original.startLineNumber)&&(r=new bi(a.original.join(r.original),a.modified.join(r.modified),a.innerChanges&&r.innerChanges?a.innerChanges.concat(r.innerChanges):void 0),i.pop()),i.push(r),a=r}return Zr(()=>Qr(i,(e,t)=>t.original.startLineNumber-e.original.endLineNumberExclusive===t.modified.startLineNumber-e.modified.endLineNumberExclusive&&e.original.endLineNumberExclusive<t.original.startLineNumber&&e.modified.endLineNumberExclusive<t.modified.startLineNumber)),new li(i,[],r.quitEarly)}};function Si(e,t,n,r){return new Bt(e,t,n).ComputeDiff(r)}var Ci=class{constructor(e){let t=[],n=[];for(let r=0,i=e.length;r<i;r++)t[r]=ki(e[r],1),n[r]=Ai(e[r],1);this.lines=e,this._startColumns=t,this._endColumns=n}getElements(){let e=[];for(let t=0,n=this.lines.length;t<n;t++)e[t]=this.lines[t].substring(this._startColumns[t]-1,this._endColumns[t]-1);return e}getStrictElement(e){return this.lines[e]}getStartLineNumber(e){return e+1}getEndLineNumber(e){return e+1}createCharSequence(e,t,n){let r=[],i=[],a=[],o=0;for(let s=t;s<=n;s++){let t=this.lines[s],c=e?this._startColumns[s]:1,l=e?this._endColumns[s]:t.length+1;for(let e=c;e<l;e++)r[o]=t.charCodeAt(e-1),i[o]=s+1,a[o]=e,o++;!e&&s<n&&(r[o]=10,i[o]=s+1,a[o]=t.length+1,o++)}return new wi(r,i,a)}},wi=class{constructor(e,t,n){this._charCodes=e,this._lineNumbers=t,this._columns=n}toString(){return`[`+this._charCodes.map((e,t)=>(e===10?`\\n`:String.fromCharCode(e))+`-(${this._lineNumbers[t]},${this._columns[t]})`).join(`, `)+`]`}_assertIndex(e,t){if(e<0||e>=t.length)throw Error(`Illegal index`)}getElements(){return this._charCodes}getStartLineNumber(e){return e>0&&e===this._lineNumbers.length?this.getEndLineNumber(e-1):(this._assertIndex(e,this._lineNumbers),this._lineNumbers[e])}getEndLineNumber(e){return e===-1?this.getStartLineNumber(e+1):(this._assertIndex(e,this._lineNumbers),this._charCodes[e]===10?this._lineNumbers[e]+1:this._lineNumbers[e])}getStartColumn(e){return e>0&&e===this._columns.length?this.getEndColumn(e-1):(this._assertIndex(e,this._columns),this._columns[e])}getEndColumn(e){return e===-1?this.getStartColumn(e+1):(this._assertIndex(e,this._columns),this._charCodes[e]===10?1:this._columns[e]+1)}},Ti=class e{constructor(e,t,n,r,i,a,o,s){this.originalStartLineNumber=e,this.originalStartColumn=t,this.originalEndLineNumber=n,this.originalEndColumn=r,this.modifiedStartLineNumber=i,this.modifiedStartColumn=a,this.modifiedEndLineNumber=o,this.modifiedEndColumn=s}static createFromDiffChange(t,n,r){let i=n.getStartLineNumber(t.originalStart),a=n.getStartColumn(t.originalStart),o=n.getEndLineNumber(t.originalStart+t.originalLength-1),s=n.getEndColumn(t.originalStart+t.originalLength-1),c=r.getStartLineNumber(t.modifiedStart),l=r.getStartColumn(t.modifiedStart),u=r.getEndLineNumber(t.modifiedStart+t.modifiedLength-1),d=r.getEndColumn(t.modifiedStart+t.modifiedLength-1);return new e(i,a,o,s,c,l,u,d)}};function Ei(e){if(e.length<=1)return e;let t=[e[0]],n=t[0];for(let r=1,i=e.length;r<i;r++){let i=e[r],a=i.originalStart-(n.originalStart+n.originalLength),o=i.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(a,o)<3?(n.originalLength=i.originalStart+i.originalLength-n.originalStart,n.modifiedLength=i.modifiedStart+i.modifiedLength-n.modifiedStart):(t.push(i),n=i)}return t}var Di=class e{constructor(e,t,n,r,i){this.originalStartLineNumber=e,this.originalEndLineNumber=t,this.modifiedStartLineNumber=n,this.modifiedEndLineNumber=r,this.charChanges=i}static createFromDiffResult(t,n,r,i,a,o,s){let c,l,u,d,f;if(n.originalLength===0?(c=r.getStartLineNumber(n.originalStart)-1,l=0):(c=r.getStartLineNumber(n.originalStart),l=r.getEndLineNumber(n.originalStart+n.originalLength-1)),n.modifiedLength===0?(u=i.getStartLineNumber(n.modifiedStart)-1,d=0):(u=i.getStartLineNumber(n.modifiedStart),d=i.getEndLineNumber(n.modifiedStart+n.modifiedLength-1)),o&&n.originalLength>0&&n.originalLength<20&&n.modifiedLength>0&&n.modifiedLength<20&&a()){let e=r.createCharSequence(t,n.originalStart,n.originalStart+n.originalLength-1),o=i.createCharSequence(t,n.modifiedStart,n.modifiedStart+n.modifiedLength-1);if(e.getElements().length>0&&o.getElements().length>0){let t=Si(e,o,a,!0).changes;s&&(t=Ei(t)),f=[];for(let n=0,r=t.length;n<r;n++)f.push(Ti.createFromDiffChange(t[n],e,o))}}return new e(c,l,u,d,f)}},Oi=class{constructor(e,t,n){this.shouldComputeCharChanges=n.shouldComputeCharChanges,this.shouldPostProcessCharChanges=n.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=n.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=n.shouldMakePrettyDiff,this.originalLines=e,this.modifiedLines=t,this.original=new Ci(e),this.modified=new Ci(t),this.continueLineDiff=ji(n.maxComputationTime),this.continueCharDiff=ji(n.maxComputationTime===0?0:Math.min(n.maxComputationTime,5e3))}computeDiff(){if(this.original.lines.length===1&&this.original.lines[0].length===0)return this.modified.lines.length===1&&this.modified.lines[0].length===0?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:void 0}]};if(this.modified.lines.length===1&&this.modified.lines[0].length===0)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:void 0}]};let e=Si(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),t=e.changes,n=e.quitEarly;if(this.shouldIgnoreTrimWhitespace){let e=[];for(let n=0,r=t.length;n<r;n++)e.push(Di.createFromDiffResult(this.shouldIgnoreTrimWhitespace,t[n],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:n,changes:e}}let r=[],i=0,a=0;for(let e=-1,n=t.length;e<n;e++){let o=e+1<n?t[e+1]:null,s=o?o.originalStart:this.originalLines.length,c=o?o.modifiedStart:this.modifiedLines.length;for(;i<s&&a<c;){let e=this.originalLines[i],t=this.modifiedLines[a];if(e!==t){{let n=ki(e,1),o=ki(t,1);for(;n>1&&o>1;){let r=e.charCodeAt(n-2),i=t.charCodeAt(o-2);if(r!==i)break;n--,o--}(n>1||o>1)&&this._pushTrimWhitespaceCharChange(r,i+1,1,n,a+1,1,o)}{let n=Ai(e,1),o=Ai(t,1),s=e.length+1,c=t.length+1;for(;n<s&&o<c;){let t=e.charCodeAt(n-1),r=e.charCodeAt(o-1);if(t!==r)break;n++,o++}(n<s||o<c)&&this._pushTrimWhitespaceCharChange(r,i+1,n,s,a+1,o,c)}}i++,a++}o&&(r.push(Di.createFromDiffResult(this.shouldIgnoreTrimWhitespace,o,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),i+=o.originalLength,a+=o.modifiedLength)}return{quitEarly:n,changes:r}}_pushTrimWhitespaceCharChange(e,t,n,r,i,a,o){if(this._mergeTrimWhitespaceCharChange(e,t,n,r,i,a,o))return;let s;this.shouldComputeCharChanges&&(s=[new Ti(t,n,t,r,i,a,i,o)]),e.push(new Di(t,t,i,i,s))}_mergeTrimWhitespaceCharChange(e,t,n,r,i,a,o){let s=e.length;if(s===0)return!1;let c=e[s-1];return c.originalEndLineNumber===0||c.modifiedEndLineNumber===0?!1:c.originalEndLineNumber===t&&c.modifiedEndLineNumber===i?(this.shouldComputeCharChanges&&c.charChanges&&c.charChanges.push(new Ti(t,n,t,r,i,a,i,o)),!0):c.originalEndLineNumber+1===t&&c.modifiedEndLineNumber+1===i?(c.originalEndLineNumber=t,c.modifiedEndLineNumber=i,this.shouldComputeCharChanges&&c.charChanges&&c.charChanges.push(new Ti(t,n,t,r,i,a,i,o)),!0):!1}};function ki(e,t){let n=De(e);return n===-1?t:n+1}function Ai(e,t){let n=Oe(e);return n===-1?t:n+2}function ji(e){if(e===0)return()=>!0;let t=Date.now();return()=>Date.now()-t<e}function Mi(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t||e.length!==t.length)return!1;for(let r=0,i=e.length;r<i;r++)if(!n(e[r],t[r]))return!1;return!0}function*Ni(e,t){let n,r;for(let i of e)r!==void 0&&t(r,i)?n.push(i):(n&&(yield n),n=[i]),r=i;n&&(yield n)}function Pi(e,t){for(let n=0;n<=e.length;n++)t(n===0?void 0:e[n-1],n===e.length?void 0:e[n])}function Fi(e,t){for(let n=0;n<e.length;n++)t(n===0?void 0:e[n-1],e[n],n+1===e.length?void 0:e[n+1])}function Ii(e,t){for(let n of t)e.push(n)}var Li;(function(e){function t(e){return e<0}e.isLessThan=t;function n(e){return e<=0}e.isLessThanOrEqual=n;function r(e){return e>0}e.isGreaterThan=r;function i(e){return e===0}e.isNeitherLessOrGreaterThan=i,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0})(Li||={});function Ri(e,t){return(n,r)=>t(e(n),e(r))}const zi=(e,t)=>e-t;function Bi(e){return(t,n)=>-e(t,n)}(class e{static{this.empty=new e(e=>{})}constructor(e){this.iterate=e}toArray(){let e=[];return this.iterate(t=>(e.push(t),!0)),e}filter(t){return new e(e=>this.iterate(n=>t(n)?e(n):!0))}map(t){return new e(e=>this.iterate(n=>e(t(n))))}findLast(e){let t;return this.iterate(n=>(e(n)&&(t=n),!0)),t}findLastMaxBy(e){let t,n=!0;return this.iterate(r=>((n||Li.isGreaterThan(e(r,t)))&&(n=!1,t=r),!0)),t}});var Vi=class e{static trivial(t,n){return new e([new Y(G.ofLength(t.length),G.ofLength(n.length))],!1)}static trivialTimedOut(t,n){return new e([new Y(G.ofLength(t.length),G.ofLength(n.length))],!0)}constructor(e,t){this.diffs=e,this.hitTimeout=t}},Y=class e{static invert(t,n){let r=[];return Pi(t,(t,i)=>{r.push(e.fromOffsetPairs(t?t.getEndExclusives():X.zero,i?i.getStarts():new X(n,(t?t.seq2Range.endExclusive-t.seq1Range.endExclusive:0)+n)))}),r}static fromOffsetPairs(t,n){return new e(new G(t.offset1,n.offset1),new G(t.offset2,n.offset2))}static assertSorted(e){let t;for(let n of e){if(t&&!(t.seq1Range.endExclusive<=n.seq1Range.start&&t.seq2Range.endExclusive<=n.seq2Range.start))throw new s(`Sequence diffs must be sorted`);t=n}}constructor(e,t){this.seq1Range=e,this.seq2Range=t}swap(){return new e(this.seq2Range,this.seq1Range)}toString(){return`${this.seq1Range} <-> ${this.seq2Range}`}join(t){return new e(this.seq1Range.join(t.seq1Range),this.seq2Range.join(t.seq2Range))}delta(t){return t===0?this:new e(this.seq1Range.delta(t),this.seq2Range.delta(t))}deltaStart(t){return t===0?this:new e(this.seq1Range.deltaStart(t),this.seq2Range.deltaStart(t))}deltaEnd(t){return t===0?this:new e(this.seq1Range.deltaEnd(t),this.seq2Range.deltaEnd(t))}intersect(t){let n=this.seq1Range.intersect(t.seq1Range),r=this.seq2Range.intersect(t.seq2Range);if(!(!n||!r))return new e(n,r)}getStarts(){return new X(this.seq1Range.start,this.seq2Range.start)}getEndExclusives(){return new X(this.seq1Range.endExclusive,this.seq2Range.endExclusive)}},X=class e{static{this.zero=new e(0,0)}static{this.max=new e(2**53-1,2**53-1)}constructor(e,t){this.offset1=e,this.offset2=t}toString(){return`${this.offset1} <-> ${this.offset2}`}delta(t){return t===0?this:new e(this.offset1+t,this.offset2+t)}equals(e){return this.offset1===e.offset1&&this.offset2===e.offset2}},Hi=class e{static{this.instance=new e}isValid(){return!0}},Ui=class{constructor(e){if(this.timeout=e,this.startTime=Date.now(),this.valid=!0,e<=0)throw new s(`timeout must be positive`)}isValid(){return!(Date.now()-this.startTime<this.timeout)&&this.valid&&(this.valid=!1),this.valid}},Wi=class{constructor(e,t){this.width=e,this.height=t,this.array=[],this.array=Array(e*t)}get(e,t){return this.array[e+t*this.width]}set(e,t,n){this.array[e+t*this.width]=n}};function Gi(e){return e===32||e===9}var Ki=class e{static{this.chrKeys=new Map}static getKey(e){let t=this.chrKeys.get(e);return t===void 0&&(t=this.chrKeys.size,this.chrKeys.set(e,t)),t}constructor(t,n,r){this.range=t,this.lines=n,this.source=r,this.histogram=[];let i=0;for(let r=t.startLineNumber-1;r<t.endLineNumberExclusive-1;r++){let t=n[r];for(let n=0;n<t.length;n++){i++;let r=t[n],a=e.getKey(r);this.histogram[a]=(this.histogram[a]||0)+1}i++;let a=e.getKey(`
`);this.histogram[a]=(this.histogram[a]||0)+1}this.totalCount=i}computeSimilarity(e){let t=0,n=Math.max(this.histogram.length,e.histogram.length);for(let r=0;r<n;r++)t+=Math.abs((this.histogram[r]??0)-(e.histogram[r]??0));return 1-t/(this.totalCount+e.totalCount)}},qi=class{compute(e,t,n=Hi.instance,r){if(e.length===0||t.length===0)return Vi.trivial(e,t);let i=new Wi(e.length,t.length),a=new Wi(e.length,t.length),o=new Wi(e.length,t.length);for(let s=0;s<e.length;s++)for(let c=0;c<t.length;c++){if(!n.isValid())return Vi.trivialTimedOut(e,t);let l=s===0?0:i.get(s-1,c),u=c===0?0:i.get(s,c-1),d;e.getElement(s)===t.getElement(c)?(d=s===0||c===0?0:i.get(s-1,c-1),s>0&&c>0&&a.get(s-1,c-1)===3&&(d+=o.get(s-1,c-1)),d+=r?r(s,c):1):d=-1;let f=Math.max(l,u,d);if(f===d){let e=s>0&&c>0?o.get(s-1,c-1):0;o.set(s,c,e+1),a.set(s,c,3)}else f===l?(o.set(s,c,0),a.set(s,c,1)):f===u&&(o.set(s,c,0),a.set(s,c,2));i.set(s,c,f)}let s=[],c=e.length,l=t.length;function u(e,t){(e+1!==c||t+1!==l)&&s.push(new Y(new G(e+1,c),new G(t+1,l))),c=e,l=t}let d=e.length-1,f=t.length-1;for(;d>=0&&f>=0;)a.get(d,f)===3?(u(d,f),d--,f--):a.get(d,f)===1?d--:f--;return u(-1,-1),s.reverse(),new Vi(s,!1)}},Ji=class{compute(e,t,n=Hi.instance){if(e.length===0||t.length===0)return Vi.trivial(e,t);let r=e,i=t;function a(e,t){for(;e<r.length&&t<i.length&&r.getElement(e)===i.getElement(t);)e++,t++;return e}let o=0,s=new Xi;s.set(0,a(0,0));let c=new Zi;c.set(0,s.get(0)===0?null:new Yi(null,0,0,s.get(0)));let l=0;loop:for(;;){if(o++,!n.isValid())return Vi.trivialTimedOut(r,i);let e=-Math.min(o,i.length+o%2),t=Math.min(o,r.length+o%2);for(l=e;l<=t;l+=2){let n=0,o=l===t?-1:s.get(l+1),u=l===e?-1:s.get(l-1)+1;n++;let d=Math.min(Math.max(o,u),r.length),f=d-l;if(n++,d>r.length||f>i.length)continue;let p=a(d,f);s.set(l,p);let m=d===o?c.get(l+1):c.get(l-1);if(c.set(l,p===d?m:new Yi(m,d,f,p-d)),s.get(l)===r.length&&s.get(l)-l===i.length)break loop}}let u=c.get(l),d=[],f=r.length,p=i.length;for(;;){let e=u?u.x+u.length:0,t=u?u.y+u.length:0;if((e!==f||t!==p)&&d.push(new Y(new G(e,f),new G(t,p))),!u)break;f=u.x,p=u.y,u=u.prev}return d.reverse(),new Vi(d,!1)}},Yi=class{constructor(e,t,n,r){this.prev=e,this.x=t,this.y=n,this.length=r}},Xi=class{constructor(){this.positiveArr=new Int32Array(10),this.negativeArr=new Int32Array(10)}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,t){if(e<0){if(e=-e-1,e>=this.negativeArr.length){let e=this.negativeArr;this.negativeArr=new Int32Array(e.length*2),this.negativeArr.set(e)}this.negativeArr[e]=t}else{if(e>=this.positiveArr.length){let e=this.positiveArr;this.positiveArr=new Int32Array(e.length*2),this.positiveArr.set(e)}this.positiveArr[e]=t}}},Zi=class{constructor(){this.positiveArr=[],this.negativeArr=[]}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,t){e<0?(e=-e-1,this.negativeArr[e]=t):this.positiveArr[e]=t}},Qi=class{constructor(e,t,n){this.lines=e,this.range=t,this.considerWhitespaceChanges=n,this.elements=[],this.firstElementOffsetByLineIdx=[],this.lineStartOffsets=[],this.trimmedWsLengthsByLineIdx=[],this.firstElementOffsetByLineIdx.push(0);for(let t=this.range.startLineNumber;t<=this.range.endLineNumber;t++){let r=e[t-1],i=0;t===this.range.startLineNumber&&this.range.startColumn>1&&(i=this.range.startColumn-1,r=r.substring(i)),this.lineStartOffsets.push(i);let a=0;if(!n){let e=r.trimStart();a=r.length-e.length,r=e.trimEnd()}this.trimmedWsLengthsByLineIdx.push(a);let o=t===this.range.endLineNumber?Math.min(this.range.endColumn-1-i-a,r.length):r.length;for(let e=0;e<o;e++)this.elements.push(r.charCodeAt(e));t<this.range.endLineNumber&&(this.elements.push(10),this.firstElementOffsetByLineIdx.push(this.elements.length))}}toString(){return`Slice: "${this.text}"`}get text(){return this.getText(new G(0,this.length))}getText(e){return this.elements.slice(e.start,e.endExclusive).map(e=>String.fromCharCode(e)).join(``)}getElement(e){return this.elements[e]}get length(){return this.elements.length}getBoundaryScore(e){let t=na(e>0?this.elements[e-1]:-1),n=na(e<this.elements.length?this.elements[e]:-1);if(t===7&&n===8)return 0;if(t===8)return 150;let r=0;return t!==n&&(r+=10,t===0&&n===1&&(r+=1)),r+=ta(t),r+=ta(n),r}translateOffset(e,t=`right`){let n=di(this.firstElementOffsetByLineIdx,t=>t<=e),r=e-this.firstElementOffsetByLineIdx[n];return new V(this.range.startLineNumber+n,1+this.lineStartOffsets[n]+r+(r===0&&t===`left`?0:this.trimmedWsLengthsByLineIdx[n]))}translateRange(e){let t=this.translateOffset(e.start,`right`),n=this.translateOffset(e.endExclusive,`left`);return n.isBefore(t)?H.fromPositions(n,n):H.fromPositions(t,n)}findWordContaining(e){if(e<0||e>=this.elements.length||!$i(this.elements[e]))return;let t=e;for(;t>0&&$i(this.elements[t-1]);)t--;let n=e;for(;n<this.elements.length&&$i(this.elements[n]);)n++;return new G(t,n)}countLinesIn(e){return this.translateOffset(e.endExclusive).lineNumber-this.translateOffset(e.start).lineNumber}isStronglyEqual(e,t){return this.elements[e]===this.elements[t]}extendToFullLines(e){let t=K(this.firstElementOffsetByLineIdx,t=>t<=e.start)??0,n=fi(this.firstElementOffsetByLineIdx,t=>e.endExclusive<=t)??this.elements.length;return new G(t,n)}};function $i(e){return e>=97&&e<=122||e>=65&&e<=90||e>=48&&e<=57}const ea={0:0,1:0,2:0,3:10,4:2,5:30,6:3,7:10,8:10};function ta(e){return ea[e]}function na(e){return e===10?8:e===13?7:Gi(e)?6:e>=97&&e<=122?0:e>=65&&e<=90?1:e>=48&&e<=57?2:e===-1?3:e===44||e===59?5:4}function ra(e,t,n,r,i,a){let{moves:o,excludedChanges:s}=aa(e,t,n,a);if(!a.isValid())return[];let c=e.filter(e=>!s.has(e)),l=oa(c,r,i,t,n,a);return Ii(o,l),o=ca(o),o=o.filter(e=>{let n=e.original.toOffsetRange().slice(t).map(e=>e.trim());return n.join(`
`).length>=15&&ia(n,e=>e.length>=2)>=2}),o=la(e,o),o}function ia(e,t){let n=0;for(let r of e)t(r)&&n++;return n}function aa(e,t,n,r){let i=[],a=e.filter(e=>e.modified.isEmpty&&e.original.length>=3).map(e=>new Ki(e.original,t,e)),o=new Set(e.filter(e=>e.original.isEmpty&&e.modified.length>=3).map(e=>new Ki(e.modified,n,e))),s=new Set;for(let e of a){let t=-1,n;for(let r of o){let i=e.computeSimilarity(r);i>t&&(t=i,n=r)}if(t>.9&&n&&(o.delete(n),i.push(new _i(e.range,n.range)),s.add(e.source),s.add(n.source)),!r.isValid())return{moves:i,excludedChanges:s}}return{moves:i,excludedChanges:s}}function oa(e,t,n,r,i,a){let o=[],s=new zr;for(let n of e)for(let e=n.original.startLineNumber;e<n.original.endLineNumberExclusive-2;e++){let n=`${t[e-1]}:${t[e+1-1]}:${t[e+2-1]}`;s.add(n,{range:new q(e,e+3)})}let c=[];e.sort(Ri(e=>e.modified.startLineNumber,zi));for(let t of e){let e=[];for(let r=t.modified.startLineNumber;r<t.modified.endLineNumberExclusive-2;r++){let t=`${n[r-1]}:${n[r+1-1]}:${n[r+2-1]}`,i=new q(r,r+3),a=[];s.forEach(t,({range:t})=>{for(let n of e)if(n.originalLineRange.endLineNumberExclusive+1===t.endLineNumberExclusive&&n.modifiedLineRange.endLineNumberExclusive+1===i.endLineNumberExclusive){n.originalLineRange=new q(n.originalLineRange.startLineNumber,t.endLineNumberExclusive),n.modifiedLineRange=new q(n.modifiedLineRange.startLineNumber,i.endLineNumberExclusive),a.push(n);return}let n={modifiedLineRange:i,originalLineRange:t};c.push(n),a.push(n)}),e=a}if(!a.isValid())return[]}c.sort(Bi(Ri(e=>e.modifiedLineRange.length,zi)));let l=new hi,u=new hi;for(let e of c){let t=e.modifiedLineRange.startLineNumber-e.originalLineRange.startLineNumber,n=l.subtractFrom(e.modifiedLineRange),r=u.subtractFrom(e.originalLineRange).getWithDelta(t),i=n.getIntersection(r);for(let e of i.ranges){if(e.length<3)continue;let n=e,r=e.delta(-t);o.push(new _i(r,n)),l.addRange(n),u.addRange(r)}}o.sort(Ri(e=>e.original.startLineNumber,zi));let d=new mi(e);for(let t=0;t<o.length;t++){let n=o[t],s=d.findLastMonotonous(e=>e.original.startLineNumber<=n.original.startLineNumber),c=K(e,e=>e.modified.startLineNumber<=n.modified.startLineNumber),f=Math.max(n.original.startLineNumber-s.original.startLineNumber,n.modified.startLineNumber-c.modified.startLineNumber),p=d.findLastMonotonous(e=>e.original.startLineNumber<n.original.endLineNumberExclusive),m=K(e,e=>e.modified.startLineNumber<n.modified.endLineNumberExclusive),h=Math.max(p.original.endLineNumberExclusive-n.original.endLineNumberExclusive,m.modified.endLineNumberExclusive-n.modified.endLineNumberExclusive),g;for(g=0;g<f;g++){let e=n.original.startLineNumber-g-1,t=n.modified.startLineNumber-g-1;if(e>r.length||t>i.length||l.contains(t)||u.contains(e)||!sa(r[e-1],i[t-1],a))break}g>0&&(u.addRange(new q(n.original.startLineNumber-g,n.original.startLineNumber)),l.addRange(new q(n.modified.startLineNumber-g,n.modified.startLineNumber)));let _;for(_=0;_<h;_++){let e=n.original.endLineNumberExclusive+_,t=n.modified.endLineNumberExclusive+_;if(e>r.length||t>i.length||l.contains(t)||u.contains(e)||!sa(r[e-1],i[t-1],a))break}_>0&&(u.addRange(new q(n.original.endLineNumberExclusive,n.original.endLineNumberExclusive+_)),l.addRange(new q(n.modified.endLineNumberExclusive,n.modified.endLineNumberExclusive+_))),(g>0||_>0)&&(o[t]=new _i(new q(n.original.startLineNumber-g,n.original.endLineNumberExclusive+_),new q(n.modified.startLineNumber-g,n.modified.endLineNumberExclusive+_)))}return o}function sa(e,t,n){if(e.trim()===t.trim())return!0;if(e.length>300&&t.length>300)return!1;let r=new Ji().compute(new Qi([e],new H(1,1,1,e.length),!1),new Qi([t],new H(1,1,1,t.length),!1),n),i=0,a=Y.invert(r.diffs,e.length);for(let t of a)t.seq1Range.forEach(t=>{Gi(e.charCodeAt(t))||i++});function o(t){let n=0;for(let r=0;r<e.length;r++)Gi(t.charCodeAt(r))||n++;return n}let s=o(e.length>t.length?e:t);return i/s>.6&&s>10}function ca(e){if(e.length===0)return e;e.sort(Ri(e=>e.original.startLineNumber,zi));let t=[e[0]];for(let n=1;n<e.length;n++){let r=t[t.length-1],i=e[n],a=i.original.startLineNumber-r.original.endLineNumberExclusive,o=i.modified.startLineNumber-r.modified.endLineNumberExclusive;if(a>=0&&o>=0&&a+o<=2){t[t.length-1]=r.join(i);continue}t.push(i)}return t}function la(e,t){let n=new mi(e);return t=t.filter(t=>{let r=n.findLastMonotonous(e=>e.original.startLineNumber<t.original.endLineNumberExclusive)||new _i(new q(1,1),new q(1,1)),i=K(e,e=>e.modified.startLineNumber<t.modified.endLineNumberExclusive);return r!==i}),t}function ua(e,t,n){let r=n;return r=da(e,t,r),r=da(e,t,r),r=fa(e,t,r),r}function da(e,t,n){if(n.length===0)return n;let r=[];r.push(n[0]);for(let i=1;i<n.length;i++){let a=r[r.length-1],o=n[i];if(o.seq1Range.isEmpty||o.seq2Range.isEmpty){let n=o.seq1Range.start-a.seq1Range.endExclusive,i;for(i=1;i<=n&&!(e.getElement(o.seq1Range.start-i)!==e.getElement(o.seq1Range.endExclusive-i)||t.getElement(o.seq2Range.start-i)!==t.getElement(o.seq2Range.endExclusive-i));i++);if(i--,i===n){r[r.length-1]=new Y(new G(a.seq1Range.start,o.seq1Range.endExclusive-n),new G(a.seq2Range.start,o.seq2Range.endExclusive-n));continue}o=o.delta(-i)}r.push(o)}let i=[];for(let n=0;n<r.length-1;n++){let a=r[n+1],o=r[n];if(o.seq1Range.isEmpty||o.seq2Range.isEmpty){let i=a.seq1Range.start-o.seq1Range.endExclusive,s;for(s=0;s<i&&!(!e.isStronglyEqual(o.seq1Range.start+s,o.seq1Range.endExclusive+s)||!t.isStronglyEqual(o.seq2Range.start+s,o.seq2Range.endExclusive+s));s++);if(s===i){r[n+1]=new Y(new G(o.seq1Range.start+i,a.seq1Range.endExclusive),new G(o.seq2Range.start+i,a.seq2Range.endExclusive));continue}s>0&&(o=o.delta(s))}i.push(o)}return r.length>0&&i.push(r[r.length-1]),i}function fa(e,t,n){if(!e.getBoundaryScore||!t.getBoundaryScore)return n;for(let r=0;r<n.length;r++){let i=r>0?n[r-1]:void 0,a=n[r],o=r+1<n.length?n[r+1]:void 0,s=new G(i?i.seq1Range.endExclusive+1:0,o?o.seq1Range.start-1:e.length),c=new G(i?i.seq2Range.endExclusive+1:0,o?o.seq2Range.start-1:t.length);a.seq1Range.isEmpty?n[r]=pa(a,e,t,s,c):a.seq2Range.isEmpty&&(n[r]=pa(a.swap(),t,e,c,s).swap())}return n}function pa(e,t,n,r,i){let a=1;for(;e.seq1Range.start-a>=r.start&&e.seq2Range.start-a>=i.start&&n.isStronglyEqual(e.seq2Range.start-a,e.seq2Range.endExclusive-a)&&a<100;)a++;a--;let o=0;for(;e.seq1Range.start+o<r.endExclusive&&e.seq2Range.endExclusive+o<i.endExclusive&&n.isStronglyEqual(e.seq2Range.start+o,e.seq2Range.endExclusive+o)&&o<100;)o++;if(a===0&&o===0)return e;let s=0,c=-1;for(let r=-a;r<=o;r++){let i=e.seq2Range.start+r,a=e.seq2Range.endExclusive+r,o=e.seq1Range.start+r,l=t.getBoundaryScore(o)+n.getBoundaryScore(i)+n.getBoundaryScore(a);l>c&&(c=l,s=r)}return e.delta(s)}function ma(e,t,n){let r=[];for(let e of n){let t=r[r.length-1];if(!t){r.push(e);continue}e.seq1Range.start-t.seq1Range.endExclusive<=2||e.seq2Range.start-t.seq2Range.endExclusive<=2?r[r.length-1]=new Y(t.seq1Range.join(e.seq1Range),t.seq2Range.join(e.seq2Range)):r.push(e)}return r}function ha(e,t,n){let r=Y.invert(n,e.length),i=[],a=new X(0,0);function o(n,o){if(n.offset1<a.offset1||n.offset2<a.offset2)return;let s=e.findWordContaining(n.offset1),c=t.findWordContaining(n.offset2);if(!s||!c)return;let l=new Y(s,c),u=l.intersect(o),d=u.seq1Range.length,f=u.seq2Range.length;for(;r.length>0;){let n=r[0];if(!(n.seq1Range.intersects(l.seq1Range)||n.seq2Range.intersects(l.seq2Range)))break;let i=e.findWordContaining(n.seq1Range.start),a=t.findWordContaining(n.seq2Range.start),o=new Y(i,a),s=o.intersect(n);if(d+=s.seq1Range.length,f+=s.seq2Range.length,l=l.join(o),l.seq1Range.endExclusive>=n.seq1Range.endExclusive)r.shift();else break}d+f<(l.seq1Range.length+l.seq2Range.length)*2/3&&i.push(l),a=l.getEndExclusives()}for(;r.length>0;){let e=r.shift();e.seq1Range.isEmpty||(o(e.getStarts(),e),o(e.getEndExclusives().delta(-1),e))}return ga(n,i)}function ga(e,t){let n=[];for(;e.length>0||t.length>0;){let r=e[0],i=t[0],a;a=r&&(!i||r.seq1Range.start<i.seq1Range.start)?e.shift():t.shift(),n.length>0&&n[n.length-1].seq1Range.endExclusive>=a.seq1Range.start?n[n.length-1]=n[n.length-1].join(a):n.push(a)}return n}function _a(e,t,n){let r=n;if(r.length===0)return r;let i=0,a;do{a=!1;let t=[r[0]];for(let n=1;n<r.length;n++){let i=r[n],o=t[t.length-1];function s(t,n){let r=new G(o.seq1Range.endExclusive,i.seq1Range.start);return e.getText(r).replace(/\s/g,``).length<=4&&(t.seq1Range.length+t.seq2Range.length>5||n.seq1Range.length+n.seq2Range.length>5)}s(o,i)?(a=!0,t[t.length-1]=t[t.length-1].join(i)):t.push(i)}r=t}while(i++<10&&a);return r}function va(e,t,n){let r=n;if(r.length===0)return r;let i=0,a;do{a=!1;let n=[r[0]];for(let i=1;i<r.length;i++){let o=r[i],s=n[n.length-1];function c(n,r){let i=new G(s.seq1Range.endExclusive,o.seq1Range.start);if(e.countLinesIn(i)>5||i.length>500)return!1;let a=e.getText(i).trim();if(a.length>20||a.split(/\r\n|\r|\n/).length>1)return!1;let c=e.countLinesIn(n.seq1Range),l=n.seq1Range.length,u=t.countLinesIn(n.seq2Range),d=n.seq2Range.length,f=e.countLinesIn(r.seq1Range),p=r.seq1Range.length,m=t.countLinesIn(r.seq2Range),h=r.seq2Range.length;function g(e){return Math.min(e,130)}return(g(c*40+l)**1.5+g(u*40+d)**1.5)**1.5+(g(f*40+p)**1.5+g(m*40+h)**1.5)**1.5>(130**1.5)**1.5*1.3}c(s,o)?(a=!0,n[n.length-1]=n[n.length-1].join(o)):n.push(o)}r=n}while(i++<10&&a);let o=[];return Fi(r,(t,n,r)=>{let i=n;function a(e){return e.length>0&&e.trim().length<=3&&n.seq1Range.length+n.seq2Range.length>100}let s=e.extendToFullLines(n.seq1Range),c=e.getText(new G(s.start,n.seq1Range.start));a(c)&&(i=i.deltaStart(-c.length));let l=e.getText(new G(n.seq1Range.endExclusive,s.endExclusive));a(l)&&(i=i.deltaEnd(l.length));let u=Y.fromOffsetPairs(t?t.getEndExclusives():X.zero,r?r.getStarts():X.max),d=i.intersect(u);o.length>0&&d.getStarts().equals(o[o.length-1].getEndExclusives())?o[o.length-1]=o[o.length-1].join(d):o.push(d)}),o}var ya=class{constructor(e,t){this.trimmedHash=e,this.lines=t}getElement(e){return this.trimmedHash[e]}get length(){return this.trimmedHash.length}getBoundaryScore(e){let t=e===0?0:ba(this.lines[e-1]),n=e===this.lines.length?0:ba(this.lines[e]);return 1e3-(t+n)}getText(e){return this.lines.slice(e.start,e.endExclusive).join(`
`)}isStronglyEqual(e,t){return this.lines[e]===this.lines[t]}};function ba(e){let t=0;for(;t<e.length&&(e.charCodeAt(t)===32||e.charCodeAt(t)===9);)t++;return t}var xa=class{constructor(){this.dynamicProgrammingDiffing=new qi,this.myersDiffingAlgorithm=new Ji}computeDiff(e,t,n){if(e.length<=1&&Mi(e,t,(e,t)=>e===t))return new li([],[],!1);if(e.length===1&&e[0].length===0||t.length===1&&t[0].length===0)return new li([new bi(new q(1,e.length+1),new q(1,t.length+1),[new J(new H(1,1,e.length,e[e.length-1].length+1),new H(1,1,t.length,t[t.length-1].length+1))])],[],!1);let r=n.maxComputationTimeMs===0?Hi.instance:new Ui(n.maxComputationTimeMs),i=!n.ignoreTrimWhitespace,a=new Map;function o(e){let t=a.get(e);return t===void 0&&(t=a.size,a.set(e,t)),t}let s=e.map(e=>o(e.trim())),c=t.map(e=>o(e.trim())),l=new ya(s,e),u=new ya(c,t),d=(()=>l.length+u.length<1700?this.dynamicProgrammingDiffing.compute(l,u,r,(n,r)=>e[n]===t[r]?t[r].length===0?.1:1+Math.log(1+t[r].length):.99):this.myersDiffingAlgorithm.compute(l,u,r))(),f=d.diffs,p=d.hitTimeout;f=ua(l,u,f),f=_a(l,u,f);let m=[],h=n=>{if(i)for(let a=0;a<n;a++){let n=g+a,o=_+a;if(e[n]!==t[o]){let a=this.refineDiff(e,t,new Y(new G(n,n+1),new G(o,o+1)),r,i);for(let e of a.mappings)m.push(e);a.hitTimeout&&(p=!0)}}},g=0,_=0;for(let n of f){Zr(()=>n.seq1Range.start-g===n.seq2Range.start-_);let a=n.seq1Range.start-g;h(a),g=n.seq1Range.endExclusive,_=n.seq2Range.endExclusive;let o=this.refineDiff(e,t,n,r,i);o.hitTimeout&&(p=!0);for(let e of o.mappings)m.push(e)}h(e.length-g);let v=Sa(m,e,t),y=[];return n.computeMoves&&(y=this.computeMoves(v,e,t,s,c,r,i)),Zr(()=>{function n(e,t){if(e.lineNumber<1||e.lineNumber>t.length)return!1;let n=t[e.lineNumber-1];return!(e.column<1||e.column>n.length+1)}function r(e,t){return!(e.startLineNumber<1||e.startLineNumber>t.length+1||e.endLineNumberExclusive<1||e.endLineNumberExclusive>t.length+1)}for(let i of v){if(!i.innerChanges)return!1;for(let r of i.innerChanges)if(!(n(r.modifiedRange.getStartPosition(),t)&&n(r.modifiedRange.getEndPosition(),t)&&n(r.originalRange.getStartPosition(),e)&&n(r.originalRange.getEndPosition(),e)))return!1;if(!r(i.modified,t)||!r(i.original,e))return!1}return!0}),new li(v,y,p)}computeMoves(e,t,n,r,i,a,o){return ra(e,t,n,r,i,a).map(e=>{let r=this.refineDiff(t,n,new Y(e.original.toOffsetRange(),e.modified.toOffsetRange()),a,o),i=Sa(r.mappings,t,n,!0);return new ui(e,i)})}refineDiff(e,t,n,r,i){let a=wa(n).toRangeMapping2(e,t),o=new Qi(e,a.originalRange,i),s=new Qi(t,a.modifiedRange,i),c=o.length+s.length<500?this.dynamicProgrammingDiffing.compute(o,s,r):this.myersDiffingAlgorithm.compute(o,s,r),l=c.diffs;return l=ua(o,s,l),l=ha(o,s,l),l=ma(o,s,l),l=va(o,s,l),{mappings:l.map(e=>new J(o.translateRange(e.seq1Range),s.translateRange(e.seq2Range))),hitTimeout:c.hitTimeout}}};function Sa(e,t,n,r=!1){let i=[];for(let r of Ni(e.map(e=>Ca(e,t,n)),(e,t)=>e.original.overlapOrTouch(t.original)||e.modified.overlapOrTouch(t.modified))){let e=r[0],t=r[r.length-1];i.push(new bi(e.original.join(t.original),e.modified.join(t.modified),r.map(e=>e.innerChanges[0])))}return Zr(()=>!r&&i.length>0&&(i[0].modified.startLineNumber!==i[0].original.startLineNumber||n.length-i[i.length-1].modified.endLineNumberExclusive!==t.length-i[i.length-1].original.endLineNumberExclusive)?!1:Qr(i,(e,t)=>t.original.startLineNumber-e.original.endLineNumberExclusive===t.modified.startLineNumber-e.modified.endLineNumberExclusive&&e.original.endLineNumberExclusive<t.original.startLineNumber&&e.modified.endLineNumberExclusive<t.modified.startLineNumber)),i}function Ca(e,t,n){let r=0,i=0;e.modifiedRange.endColumn===1&&e.originalRange.endColumn===1&&e.originalRange.startLineNumber+r<=e.originalRange.endLineNumber&&e.modifiedRange.startLineNumber+r<=e.modifiedRange.endLineNumber&&(i=-1),e.modifiedRange.startColumn-1>=n[e.modifiedRange.startLineNumber-1].length&&e.originalRange.startColumn-1>=t[e.originalRange.startLineNumber-1].length&&e.originalRange.startLineNumber<=e.originalRange.endLineNumber+i&&e.modifiedRange.startLineNumber<=e.modifiedRange.endLineNumber+i&&(r=1);let a=new q(e.originalRange.startLineNumber+r,e.originalRange.endLineNumber+1+i),o=new q(e.modifiedRange.startLineNumber+r,e.modifiedRange.endLineNumber+1+i);return new bi(a,o,[e])}function wa(e){return new _i(new q(e.seq1Range.start+1,e.seq1Range.endExclusive+1),new q(e.seq2Range.start+1,e.seq2Range.endExclusive+1))}const Ta={getLegacy:()=>new xi,getDefault:()=>new xa};function Z(e,t){let n=10**t;return Math.round(e*n)/n}var Q=class{constructor(e,t,n,r=1){this._rgbaBrand=void 0,this.r=Math.min(255,Math.max(0,e))|0,this.g=Math.min(255,Math.max(0,t))|0,this.b=Math.min(255,Math.max(0,n))|0,this.a=Z(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.r===t.r&&e.g===t.g&&e.b===t.b&&e.a===t.a}},$=class e{constructor(e,t,n,r){this._hslaBrand=void 0,this.h=Math.max(Math.min(360,e),0)|0,this.s=Z(Math.max(Math.min(1,t),0),3),this.l=Z(Math.max(Math.min(1,n),0),3),this.a=Z(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.h===t.h&&e.s===t.s&&e.l===t.l&&e.a===t.a}static fromRGBA(t){let n=t.r/255,r=t.g/255,i=t.b/255,a=t.a,o=Math.max(n,r,i),s=Math.min(n,r,i),c=0,l=0,u=(s+o)/2,d=o-s;if(d>0){switch(l=Math.min(u<=.5?d/(2*u):d/(2-2*u),1),o){case n:c=(r-i)/d+(r<i?6:0);break;case r:c=(i-n)/d+2;break;case i:c=(n-r)/d+4;break}c*=60,c=Math.round(c)}return new e(c,l,u,a)}static _hue2rgb(e,t,n){return n<0&&(n+=1),n>1&&--n,n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}static toRGBA(t){let n=t.h/360,{s:r,l:i,a}=t,o,s,c;if(r===0)o=s=c=i;else{let t=i<.5?i*(1+r):i+r-i*r,a=2*i-t;o=e._hue2rgb(a,t,n+1/3),s=e._hue2rgb(a,t,n),c=e._hue2rgb(a,t,n-1/3)}return new Q(Math.round(o*255),Math.round(s*255),Math.round(c*255),a)}},Ea=class e{constructor(e,t,n,r){this._hsvaBrand=void 0,this.h=Math.max(Math.min(360,e),0)|0,this.s=Z(Math.max(Math.min(1,t),0),3),this.v=Z(Math.max(Math.min(1,n),0),3),this.a=Z(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.h===t.h&&e.s===t.s&&e.v===t.v&&e.a===t.a}static fromRGBA(t){let n=t.r/255,r=t.g/255,i=t.b/255,a=Math.max(n,r,i),o=a-Math.min(n,r,i),s=a===0?0:o/a,c;return c=o===0?0:a===n?((r-i)/o%6+6)%6:a===r?(i-n)/o+2:(n-r)/o+4,new e(Math.round(c*60),s,a,t.a)}static toRGBA(e){let{h:t,s:n,v:r,a:i}=e,a=r*n,o=a*(1-Math.abs(t/60%2-1)),s=r-a,[c,l,u]=[0,0,0];return t<60?(c=a,l=o):t<120?(c=o,l=a):t<180?(l=a,u=o):t<240?(l=o,u=a):t<300?(c=o,u=a):t<=360&&(c=a,u=o),c=Math.round((c+s)*255),l=Math.round((l+s)*255),u=Math.round((u+s)*255),new Q(c,l,u,i)}},Da=class e{static fromHex(t){return e.Format.CSS.parseHex(t)||e.red}static equals(e,t){return!e&&!t?!0:!e||!t?!1:e.equals(t)}get hsla(){return this._hsla?this._hsla:$.fromRGBA(this.rgba)}get hsva(){return this._hsva?this._hsva:Ea.fromRGBA(this.rgba)}constructor(e){if(e)if(e instanceof Q)this.rgba=e;else if(e instanceof $)this._hsla=e,this.rgba=$.toRGBA(e);else if(e instanceof Ea)this._hsva=e,this.rgba=Ea.toRGBA(e);else throw Error(`Invalid color ctor argument`);else throw Error(`Color needs a value`)}equals(e){return!!e&&Q.equals(this.rgba,e.rgba)&&$.equals(this.hsla,e.hsla)&&Ea.equals(this.hsva,e.hsva)}getRelativeLuminance(){let t=e._relativeLuminanceForComponent(this.rgba.r),n=e._relativeLuminanceForComponent(this.rgba.g),r=e._relativeLuminanceForComponent(this.rgba.b),i=.2126*t+.7152*n+.0722*r;return Z(i,4)}static _relativeLuminanceForComponent(e){let t=e/255;return t<=.03928?t/12.92:((t+.055)/1.055)**2.4}isLighter(){return(this.rgba.r*299+this.rgba.g*587+this.rgba.b*114)/1e3>=128}isLighterThan(e){let t=this.getRelativeLuminance(),n=e.getRelativeLuminance();return t>n}isDarkerThan(e){let t=this.getRelativeLuminance(),n=e.getRelativeLuminance();return t<n}lighten(t){return new e(new $(this.hsla.h,this.hsla.s,this.hsla.l+this.hsla.l*t,this.hsla.a))}darken(t){return new e(new $(this.hsla.h,this.hsla.s,this.hsla.l-this.hsla.l*t,this.hsla.a))}transparent(t){let{r:n,g:r,b:i,a}=this.rgba;return new e(new Q(n,r,i,a*t))}isTransparent(){return this.rgba.a===0}isOpaque(){return this.rgba.a===1}opposite(){return new e(new Q(255-this.rgba.r,255-this.rgba.g,255-this.rgba.b,this.rgba.a))}makeOpaque(t){if(this.isOpaque()||t.rgba.a!==1)return this;let{r:n,g:r,b:i,a}=this.rgba;return new e(new Q(t.rgba.r-a*(t.rgba.r-n),t.rgba.g-a*(t.rgba.g-r),t.rgba.b-a*(t.rgba.b-i),1))}toString(){return this._toString||=e.Format.CSS.format(this),this._toString}static getLighterColor(e,t,n){if(e.isLighterThan(t))return e;n||=.5;let r=e.getRelativeLuminance(),i=t.getRelativeLuminance();return n=n*(i-r)/i,e.lighten(n)}static getDarkerColor(e,t,n){if(e.isDarkerThan(t))return e;n||=.5;let r=e.getRelativeLuminance(),i=t.getRelativeLuminance();return n=n*(r-i)/r,e.darken(n)}static{this.white=new e(new Q(255,255,255,1))}static{this.black=new e(new Q(0,0,0,1))}static{this.red=new e(new Q(255,0,0,1))}static{this.blue=new e(new Q(0,0,255,1))}static{this.green=new e(new Q(0,255,0,1))}static{this.cyan=new e(new Q(0,255,255,1))}static{this.lightgrey=new e(new Q(211,211,211,1))}static{this.transparent=new e(new Q(0,0,0,0))}};(function(e){(function(t){(function(t){function n(t){return t.rgba.a===1?`rgb(${t.rgba.r}, ${t.rgba.g}, ${t.rgba.b})`:e.Format.CSS.formatRGBA(t)}t.formatRGB=n;function r(e){return`rgba(${e.rgba.r}, ${e.rgba.g}, ${e.rgba.b}, ${+e.rgba.a.toFixed(2)})`}t.formatRGBA=r;function i(t){return t.hsla.a===1?`hsl(${t.hsla.h}, ${(t.hsla.s*100).toFixed(2)}%, ${(t.hsla.l*100).toFixed(2)}%)`:e.Format.CSS.formatHSLA(t)}t.formatHSL=i;function a(e){return`hsla(${e.hsla.h}, ${(e.hsla.s*100).toFixed(2)}%, ${(e.hsla.l*100).toFixed(2)}%, ${e.hsla.a.toFixed(2)})`}t.formatHSLA=a;function o(e){let t=e.toString(16);return t.length===2?t:`0`+t}function s(e){return`#${o(e.rgba.r)}${o(e.rgba.g)}${o(e.rgba.b)}`}t.formatHex=s;function c(t,n=!1){return n&&t.rgba.a===1?e.Format.CSS.formatHex(t):`#${o(t.rgba.r)}${o(t.rgba.g)}${o(t.rgba.b)}${o(Math.round(t.rgba.a*255))}`}t.formatHexA=c;function l(t){return t.isOpaque()?e.Format.CSS.formatHex(t):e.Format.CSS.formatRGBA(t)}t.format=l;function u(t){let n=t.length;if(n===0||t.charCodeAt(0)!==35)return null;if(n===7){let n=16*d(t.charCodeAt(1))+d(t.charCodeAt(2)),r=16*d(t.charCodeAt(3))+d(t.charCodeAt(4)),i=16*d(t.charCodeAt(5))+d(t.charCodeAt(6));return new e(new Q(n,r,i,1))}if(n===9){let n=16*d(t.charCodeAt(1))+d(t.charCodeAt(2)),r=16*d(t.charCodeAt(3))+d(t.charCodeAt(4)),i=16*d(t.charCodeAt(5))+d(t.charCodeAt(6)),a=16*d(t.charCodeAt(7))+d(t.charCodeAt(8));return new e(new Q(n,r,i,a/255))}if(n===4){let n=d(t.charCodeAt(1)),r=d(t.charCodeAt(2)),i=d(t.charCodeAt(3));return new e(new Q(16*n+n,16*r+r,16*i+i))}if(n===5){let n=d(t.charCodeAt(1)),r=d(t.charCodeAt(2)),i=d(t.charCodeAt(3)),a=d(t.charCodeAt(4));return new e(new Q(16*n+n,16*r+r,16*i+i,(16*a+a)/255))}return null}t.parseHex=u;function d(e){switch(e){case 48:return 0;case 49:return 1;case 50:return 2;case 51:return 3;case 52:return 4;case 53:return 5;case 54:return 6;case 55:return 7;case 56:return 8;case 57:return 9;case 97:return 10;case 65:return 10;case 98:return 11;case 66:return 11;case 99:return 12;case 67:return 12;case 100:return 13;case 68:return 13;case 101:return 14;case 69:return 14;case 102:return 15;case 70:return 15}return 0}})(t.CSS||={})})(e.Format||={})})(Da||={});function Oa(e){let t=[];for(let n of e){let e=Number(n);(e||e===0&&n.replace(/\s/g,``)!==``)&&t.push(e)}return t}function ka(e,t,n,r){return{red:e/255,blue:n/255,green:t/255,alpha:r}}function Aa(e,t){let n=t.index,r=t[0].length;if(!n)return;let i=e.positionAt(n);return{startLineNumber:i.lineNumber,startColumn:i.column,endLineNumber:i.lineNumber,endColumn:i.column+r}}function ja(e,t){if(!e)return;let n=Da.Format.CSS.parseHex(t);if(n)return{range:e,color:ka(n.rgba.r,n.rgba.g,n.rgba.b,n.rgba.a)}}function Ma(e,t,n){if(!e||t.length!==1)return;let r=t[0].values(),i=Oa(r);return{range:e,color:ka(i[0],i[1],i[2],n?i[3]:1)}}function Na(e,t,n){if(!e||t.length!==1)return;let r=t[0].values(),i=Oa(r),a=new Da(new $(i[0],i[1]/100,i[2]/100,n?i[3]:1));return{range:e,color:ka(a.rgba.r,a.rgba.g,a.rgba.b,a.rgba.a)}}function Pa(e,t){return typeof e==`string`?[...e.matchAll(t)]:e.findMatches(t)}function Fa(e){let t=[],n=Pa(e,/\b(rgb|rgba|hsl|hsla)(\([0-9\s,.\%]*\))|(#)([A-Fa-f0-9]{3})\b|(#)([A-Fa-f0-9]{4})\b|(#)([A-Fa-f0-9]{6})\b|(#)([A-Fa-f0-9]{8})\b/gm);if(n.length>0)for(let r of n){let n=r.filter(e=>e!==void 0),i=n[1],a=n[2];if(!a)continue;let o;i===`rgb`?o=Ma(Aa(e,r),Pa(a,/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*\)$/gm),!1):i===`rgba`?o=Ma(Aa(e,r),Pa(a,/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm),!0):i===`hsl`?o=Na(Aa(e,r),Pa(a,/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*\)$/gm),!1):i===`hsla`?o=Na(Aa(e,r),Pa(a,/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm),!0):i===`#`&&(o=ja(Aa(e,r),i+a)),o&&t.push(o)}return t}function Ia(e){return!e||typeof e.getValue!=`function`||typeof e.positionAt!=`function`?[]:Fa(e)}const La=RegExp(`\\bMARK:\\s*(.*)$`,`d`),Ra=/^-+|-+$/g;function za(e,t){let n=[];if(t.findRegionSectionHeaders&&t.foldingRules?.markers){let r=Ba(e,t);n=n.concat(r)}if(t.findMarkSectionHeaders){let t=Va(e);n=n.concat(t)}return n}function Ba(e,t){let n=[],r=e.getLineCount();for(let i=1;i<=r;i++){let r=e.getLineContent(i),a=r.match(t.foldingRules.markers.start);if(a){let e={startLineNumber:i,startColumn:a[0].length+1,endLineNumber:i,endColumn:r.length+1};if(e.endColumn>e.startColumn){let t={range:e,...Ua(r.substring(a[0].length)),shouldBeInComments:!1};(t.text||t.hasSeparatorLine)&&n.push(t)}}}return n}function Va(e){let t=[],n=e.getLineCount();for(let r=1;r<=n;r++){let n=e.getLineContent(r);Ha(n,r,t)}return t}function Ha(e,t,n){La.lastIndex=0;let r=La.exec(e);if(r){let e=r.indices[1][0]+1,i=r.indices[1][1]+1,a={startLineNumber:t,startColumn:e,endLineNumber:t,endColumn:i};if(a.endColumn>a.startColumn){let e={range:a,...Ua(r[1]),shouldBeInComments:!0};(e.text||e.hasSeparatorLine)&&n.push(e)}}}function Ua(e){e=e.trim();let t=e.startsWith(`-`);return e=e.replace(Ra,``),{text:e,hasSeparatorLine:t}}(function(){typeof globalThis.requestIdleCallback!=`function`||globalThis.cancelIdleCallback})();var Wa;(function(e){async function t(e){let t,n=await Promise.all(e.map(e=>e.then(e=>e,e=>{t||=e})));if(t!==void 0)throw t;return n}e.settled=t;function n(e){return new Promise(async(t,n)=>{try{await e(t,n)}catch(e){n(e)}})}e.withAsyncBody=n})(Wa||={}),class e{static fromArray(t){return new e(e=>{e.emitMany(t)})}static fromPromise(t){return new e(async e=>{e.emitMany(await t)})}static fromPromises(t){return new e(async e=>{await Promise.all(t.map(async t=>e.emitOne(await t)))})}static merge(t){return new e(async e=>{await Promise.all(t.map(async t=>{for await(let n of t)e.emitOne(n)}))})}static{this.EMPTY=e.fromArray([])}constructor(e,t){this._state=0,this._results=[],this._error=null,this._onReturn=t,this._onStateChanged=new E,queueMicrotask(async()=>{let t={emitOne:e=>this.emitOne(e),emitMany:e=>this.emitMany(e),reject:e=>this.reject(e)};try{await Promise.resolve(e(t)),this.resolve()}catch(e){this.reject(e)}finally{t.emitOne=void 0,t.emitMany=void 0,t.reject=void 0}})}[Symbol.asyncIterator](){let e=0;return{next:async()=>{do{if(this._state===2)throw this._error;if(e<this._results.length)return{done:!1,value:this._results[e++]};if(this._state===1)return{done:!0,value:void 0};await S.toPromise(this._onStateChanged.event)}while(!0)},return:async()=>(this._onReturn?.(),{done:!0,value:void 0})}}static map(t,n){return new e(async e=>{for await(let r of t)e.emitOne(n(r))})}map(t){return e.map(this,t)}static filter(t,n){return new e(async e=>{for await(let r of t)n(r)&&e.emitOne(r)})}filter(t){return e.filter(this,t)}static coalesce(t){return e.filter(t,e=>!!e)}coalesce(){return e.coalesce(this)}static async toPromise(e){let t=[];for await(let n of e)t.push(n);return t}toPromise(){return e.toPromise(this)}emitOne(e){this._state===0&&(this._results.push(e),this._onStateChanged.fire())}emitMany(e){this._state===0&&(this._results=this._results.concat(e),this._onStateChanged.fire())}resolve(){this._state===0&&(this._state=1,this._onStateChanged.fire())}reject(e){this._state===0&&(this._state=2,this._error=e,this._onStateChanged.fire())}};var Ga=class{constructor(e){this.values=e,this.prefixSum=new Uint32Array(e.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(e,t){e=Ht(e);let n=this.values,r=this.prefixSum,i=t.length;return i===0?!1:(this.values=new Uint32Array(n.length+i),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e),e+i),this.values.set(t,e),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(e,t){return e=Ht(e),t=Ht(t),this.values[e]===t?!1:(this.values[e]=t,e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),!0)}removeValues(e,t){e=Ht(e),t=Ht(t);let n=this.values,r=this.prefixSum;if(e>=n.length)return!1;let i=n.length-e;return t>=i&&(t=i),t===0?!1:(this.values=new Uint32Array(n.length-t),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e+t),e),this.prefixSum=new Uint32Array(this.values.length),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return this.values.length===0?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(e){return e<0?0:(e=Ht(e),this._getPrefixSum(e))}_getPrefixSum(e){if(e<=this.prefixSumValidIndex[0])return this.prefixSum[e];let t=this.prefixSumValidIndex[0]+1;t===0&&(this.prefixSum[0]=this.values[0],t++),e>=this.values.length&&(e=this.values.length-1);for(let n=t;n<=e;n++)this.prefixSum[n]=this.prefixSum[n-1]+this.values[n];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],e),this.prefixSum[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,n=this.values.length-1,r=0,i=0,a=0;for(;t<=n;)if(r=t+(n-t)/2|0,i=this.prefixSum[r],a=i-this.values[r],e<a)n=r-1;else if(e>=i)t=r+1;else break;return new Ka(r,e-a)}},Ka=class{constructor(e,t){this.index=e,this.remainder=t,this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}},qa=class{constructor(e,t,n,r){this._uri=e,this._lines=t,this._eol=n,this._versionId=r,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return this._cachedTextValue===null&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(e){e.eol&&e.eol!==this._eol&&(this._eol=e.eol,this._lineStarts=null);let t=e.changes;for(let e of t)this._acceptDeleteRange(e.range),this._acceptInsertText(new V(e.range.startLineNumber,e.range.startColumn),e.text);this._versionId=e.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){let e=this._eol.length,t=this._lines.length,n=new Uint32Array(t);for(let r=0;r<t;r++)n[r]=this._lines[r].length+e;this._lineStarts=new Ga(n)}}_setLineText(e,t){this._lines[e]=t,this._lineStarts&&this._lineStarts.setValue(e,this._lines[e].length+this._eol.length)}_acceptDeleteRange(e){if(e.startLineNumber===e.endLineNumber){if(e.startColumn===e.endColumn)return;this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.startLineNumber-1].substring(e.endColumn-1));return}this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.endLineNumber-1].substring(e.endColumn-1)),this._lines.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber)}_acceptInsertText(e,t){if(t.length===0)return;let n=Ee(t);if(n.length===1){this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]+this._lines[e.lineNumber-1].substring(e.column-1));return}n[n.length-1]+=this._lines[e.lineNumber-1].substring(e.column-1),this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]);let r=new Uint32Array(n.length-1);for(let t=1;t<n.length;t++)this._lines.splice(e.lineNumber+t-1,0,n[t]),r[t-1]=n[t].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(e.lineNumber,r)}},Ja=class{constructor(){this._models=Object.create(null)}getModel(e){return this._models[e]}getModels(){let e=[];return Object.keys(this._models).forEach(t=>e.push(this._models[t])),e}$acceptNewModel(e){this._models[e.url]=new Ya(R.parse(e.url),e.lines,e.EOL,e.versionId)}$acceptModelChanged(e,t){this._models[e]&&this._models[e].onEvents(t)}$acceptRemovedModel(e){this._models[e]&&delete this._models[e]}},Ya=class extends qa{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}findMatches(e){let t=[];for(let n=0;n<this._lines.length;n++){let r=this._lines[n],i=this.offsetAt(new V(n+1,1)),a=r.matchAll(e);for(let e of a)(e.index||e.index===0)&&(e.index+=i),t.push(e)}return t}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(e){return this._lines[e-1]}getWordAtPosition(e,t){let n=ri(e.column,ti(t),this._lines[e.lineNumber-1],0);return n?new H(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}words(e){let t=this._lines,n=this._wordenize.bind(this),r=0,i=``,a=0,o=[];return{*[Symbol.iterator](){for(;;)if(a<o.length){let e=i.substring(o[a].start,o[a].end);a+=1,yield e}else if(r<t.length)i=t[r],o=n(i,e),a=0,r+=1;else break}}}getLineWords(e,t){let n=this._lines[e-1],r=this._wordenize(n,t),i=[];for(let e of r)i.push({word:n.substring(e.start,e.end),startColumn:e.start+1,endColumn:e.end+1});return i}_wordenize(e,t){let n=[],r;for(t.lastIndex=0;(r=t.exec(e))&&r[0].length!==0;)n.push({start:r.index,end:r.index+r[0].length});return n}getValueInRange(e){if(e=this._validateRange(e),e.startLineNumber===e.endLineNumber)return this._lines[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);let t=this._eol,n=e.startLineNumber-1,r=e.endLineNumber-1,i=[];i.push(this._lines[n].substring(e.startColumn-1));for(let e=n+1;e<r;e++)i.push(this._lines[e]);return i.push(this._lines[r].substring(0,e.endColumn-1)),i.join(t)}offsetAt(e){return e=this._validatePosition(e),this._ensureLineStarts(),this._lineStarts.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this._ensureLineStarts();let t=this._lineStarts.getIndexOf(e),n=this._lines[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}_validateRange(e){let t=this._validatePosition({lineNumber:e.startLineNumber,column:e.startColumn}),n=this._validatePosition({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}_validatePosition(e){if(!V.isIPosition(e))throw Error(`bad position`);let{lineNumber:t,column:n}=e,r=!1;if(t<1)t=1,n=1,r=!0;else if(t>this._lines.length)t=this._lines.length,n=this._lines[t-1].length+1,r=!0;else{let e=this._lines[t-1].length+1;n<1?(n=1,r=!0):n>e&&(n=e,r=!0)}return r?{lineNumber:t,column:n}:e}},Xa=class{constructor(){this._workerTextModelSyncServer=new Ja}dispose(){}_getModel(e){return this._workerTextModelSyncServer.getModel(e)}_getModels(){return this._workerTextModelSyncServer.getModels()}$acceptNewModel(e){this._workerTextModelSyncServer.$acceptNewModel(e)}$acceptModelChanged(e,t){this._workerTextModelSyncServer.$acceptModelChanged(e,t)}$acceptRemovedModel(e){this._workerTextModelSyncServer.$acceptRemovedModel(e)}async $computeUnicodeHighlights(e,t,n){let r=this._getModel(e);return r?ai.computeUnicodeHighlights(r,t,n):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}}async $findSectionHeaders(e,t){let n=this._getModel(e);return n?za(n,t):[]}async $computeDiff(e,t,n,r){let i=this._getModel(e),a=this._getModel(t);return!i||!a?null:Za.computeDiff(i,a,n,r)}static computeDiff(e,t,n,r){let i=r===`advanced`?Ta.getDefault():Ta.getLegacy(),a=e.getLinesContent(),o=t.getLinesContent(),s=i.computeDiff(a,o,n),c=s.changes.length>0?!1:this._modelsAreIdentical(e,t);function l(e){return e.map(e=>[e.original.startLineNumber,e.original.endLineNumberExclusive,e.modified.startLineNumber,e.modified.endLineNumberExclusive,e.innerChanges?.map(e=>[e.originalRange.startLineNumber,e.originalRange.startColumn,e.originalRange.endLineNumber,e.originalRange.endColumn,e.modifiedRange.startLineNumber,e.modifiedRange.startColumn,e.modifiedRange.endLineNumber,e.modifiedRange.endColumn])])}return{identical:c,quitEarly:s.hitTimeout,changes:l(s.changes),moves:s.moves.map(e=>[e.lineRangeMapping.original.startLineNumber,e.lineRangeMapping.original.endLineNumberExclusive,e.lineRangeMapping.modified.startLineNumber,e.lineRangeMapping.modified.endLineNumberExclusive,l(e.changes)])}}static _modelsAreIdentical(e,t){let n=e.getLineCount(),r=t.getLineCount();if(n!==r)return!1;for(let r=1;r<=n;r++){let n=e.getLineContent(r),i=t.getLineContent(r);if(n!==i)return!1}return!0}static{this._diffLimit=1e5}async $computeMoreMinimalEdits(e,t,n){let r=this._getModel(e);if(!r)return t;let i=[],a;t=t.slice(0).sort((e,t)=>{if(e.range&&t.range)return H.compareRangesUsingStarts(e.range,t.range);let n=e.range?0:1,r=t.range?0:1;return n-r});let o=0;for(let e=1;e<t.length;e++)H.getEndPosition(t[o].range).equals(H.getStartPosition(t[e].range))?(t[o].range=H.fromPositions(H.getStartPosition(t[o].range),H.getEndPosition(t[e].range)),t[o].text+=t[e].text):(o++,t[o]=t[e]);t.length=o+1;for(let{range:e,text:o,eol:s}of t){if(typeof s==`number`&&(a=s),H.isEmpty(e)&&!o)continue;let t=r.getValueInRange(e);if(o=o.replace(/\r\n|\n|\r/g,r.eol),t===o)continue;if(Math.max(o.length,t.length)>Za._diffLimit){i.push({range:e,text:o});continue}let c=It(t,o,n),l=r.offsetAt(H.lift(e).getStartPosition());for(let e of c){let t=r.positionAt(l+e.originalStart),n=r.positionAt(l+e.originalStart+e.originalLength),a={text:o.substr(e.modifiedStart,e.modifiedLength),range:{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}};r.getValueInRange(a.range)!==a.text&&i.push(a)}}return typeof a==`number`&&i.push({eol:a,text:``,range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),i}async $computeLinks(e){let t=this._getModel(e);return t?Zt(t):null}async $computeDefaultDocumentColors(e){let t=this._getModel(e);return t?Ia(t):null}static{this._suggestionsLimit=1e4}async $textualSuggest(e,t,n,r){let i=new x,a=new RegExp(n,r),o=new Set;outer:for(let n of e){let e=this._getModel(n);if(!e)continue;for(let n of e.words(a)){if(n===t||!isNaN(Number(n)))continue;if(o.add(n),o.size>Za._suggestionsLimit)break outer}}return{words:Array.from(o),duration:i.elapsed()}}async $computeWordRanges(e,t,n,r){let i=this._getModel(e);if(!i)return Object.create(null);let a=new RegExp(n,r),o=Object.create(null);for(let e=t.startLineNumber;e<t.endLineNumber;e++){let t=i.getLineWords(e,a);for(let n of t){if(!isNaN(Number(n.word)))continue;let t=o[n.word];t||(t=[],o[n.word]=t),t.push({startLineNumber:e,startColumn:n.startColumn,endLineNumber:e,endColumn:n.endColumn})}}return o}async $navigateValueSet(e,t,n,r,i){let a=this._getModel(e);if(!a)return null;let o=new RegExp(r,i);t.startColumn===t.endColumn&&(t={startLineNumber:t.startLineNumber,startColumn:t.startColumn,endLineNumber:t.endLineNumber,endColumn:t.endColumn+1});let s=a.getValueInRange(t),c=a.getWordAtPosition({lineNumber:t.startLineNumber,column:t.startColumn},o);if(!c)return null;let l=a.getValueInRange(c);return Qt.INSTANCE.navigateValueSet(t,s,c,l,n)}},Za=class extends Xa{constructor(e,t){super(),this._host=e,this._foreignModuleFactory=t,this._foreignModule=null}async $ping(){return`pong`}$loadForeignModule(e,t,n){let r={host:Hr(n,(e,t)=>this._host.$fhr(e,t)),getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(r,t),Promise.resolve(Vr(this._foreignModule))):new Promise((n,i)=>{import(`${_t.asBrowserUri(`${e}.js`).toString(!0)}`).then(e=>{this._foreignModule=e.create(r,t),n(Vr(this._foreignModule))}).catch(i)})}$fmr(e,t){if(!this._foreignModule||typeof this._foreignModule[e]!=`function`)return Promise.reject(Error(`Missing requestHandler or method: `+e));try{return Promise.resolve(this._foreignModule[e].apply(this._foreignModule,t))}catch(e){return Promise.reject(e)}}};typeof importScripts==`function`&&(globalThis.monaco=Ar());let Qa=!1;function $a(e){if(Qa)return;Qa=!0;let t=new Ot(e=>{globalThis.postMessage(e)},t=>new Za(jr.getChannel(t),e));globalThis.onmessage=e=>{t.onmessage(e.data)}}globalThis.onmessage=e=>{Qa||$a(null)};
//# sourceMappingURL=editor.worker-CXHCFDxE.js.map