{"version": 3, "file": "pla-BZStx9Ky.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/pla/pla.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/pla/pla.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"[\", \"]\"],\n    [\"<\", \">\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"<\", close: \">\" },\n    { open: \"(\", close: \")\" }\n  ],\n  surroundingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"<\", close: \">\" },\n    { open: \"(\", close: \")\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".pla\",\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \".i\",\n    \".o\",\n    \".mv\",\n    \".ilb\",\n    \".ob\",\n    \".label\",\n    \".type\",\n    \".phase\",\n    \".pair\",\n    \".symbolic\",\n    \".symbolic-output\",\n    \".kiss\",\n    \".p\",\n    \".e\",\n    \".end\"\n  ],\n  // regular expressions\n  comment: /#.*$/,\n  identifier: /[a-zA-Z]+[a-zA-Z0-9_\\-]*/,\n  plaContent: /[01\\-~\\|]+/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // comments and whitespace\n      { include: \"@whitespace\" },\n      [/@comment/, \"comment\"],\n      // keyword\n      [\n        /\\.([a-zA-Z_\\-]+)/,\n        {\n          cases: {\n            \"@eos\": { token: \"keyword.$1\" },\n            \"@keywords\": {\n              cases: {\n                \".type\": { token: \"keyword.$1\", next: \"@type\" },\n                \"@default\": { token: \"keyword.$1\", next: \"@keywordArg\" }\n              }\n            },\n            \"@default\": { token: \"keyword.$1\" }\n          }\n        }\n      ],\n      // identifiers\n      [/@identifier/, \"identifier\"],\n      // PLA row\n      [/@plaContent/, \"string\"]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"\"]],\n    type: [{ include: \"@whitespace\" }, [/\\w+/, { token: \"type\", next: \"@pop\" }]],\n    keywordArg: [\n      // whitespace\n      [\n        /[ \\t\\r\\n]+/,\n        {\n          cases: {\n            \"@eos\": { token: \"\", next: \"@pop\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // comments\n      [/@comment/, \"comment\", \"@pop\"],\n      // brackets\n      [\n        /[<>()\\[\\]]/,\n        {\n          cases: {\n            \"@eos\": { token: \"@brackets\", next: \"@pop\" },\n            \"@default\": \"@brackets\"\n          }\n        }\n      ],\n      // numbers\n      [\n        /\\-?\\d+/,\n        {\n          cases: {\n            \"@eos\": { token: \"number\", next: \"@pop\" },\n            \"@default\": \"number\"\n          }\n        }\n      ],\n      // identifiers\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@eos\": { token: \"identifier\", next: \"@pop\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // delimiter\n      [\n        /[;=]/,\n        {\n          cases: {\n            \"@eos\": { token: \"delimiter\", next: \"@pop\" },\n            \"@default\": \"delimiter\"\n          }\n        }\n      ]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,IACd,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,OACd,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,mBAAoB,CACpD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CAC1D,CACD,SAAU,CACR,KACA,KACA,MACA,OACA,MACA,SACA,QACA,SACA,QACA,YACA,mBACA,QACA,KACA,KACA,OACD,CAED,QAAS,OACT,WAAY,2BACZ,WAAY,aAEZ,UAAW,CACT,KAAM,CAEJ,CAAE,QAAS,cAAe,CAC1B,CAAC,WAAY,UAAU,CAEvB,CACE,mBACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,aAAc,CAC/B,YAAa,CACX,MAAO,CACL,QAAS,CAAE,MAAO,aAAc,KAAM,QAAS,CAC/C,WAAY,CAAE,MAAO,aAAc,KAAM,cAAe,CACzD,CACF,CACD,WAAY,CAAE,MAAO,aAAc,CACpC,CACF,CACF,CAED,CAAC,cAAe,aAAa,CAE7B,CAAC,cAAe,SAAS,CAC1B,CACD,WAAY,CAAC,CAAC,aAAc,GAAG,CAAC,CAChC,KAAM,CAAC,CAAE,QAAS,cAAe,CAAE,CAAC,MAAO,CAAE,MAAO,OAAQ,KAAM,OAAQ,CAAC,CAAC,CAC5E,WAAY,CAEV,CACE,aACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,GAAI,KAAM,OAAQ,CACnC,WAAY,GACb,CACF,CACF,CAED,CAAC,WAAY,UAAW,OAAO,CAE/B,CACE,aACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,YAAa,KAAM,OAAQ,CAC5C,WAAY,YACb,CACF,CACF,CAED,CACE,SACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,SAAU,KAAM,OAAQ,CACzC,WAAY,SACb,CACF,CACF,CAED,CACE,cACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,aAAc,KAAM,OAAQ,CAC7C,WAAY,aACb,CACF,CACF,CAED,CACE,OACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,YAAa,KAAM,OAAQ,CAC5C,WAAY,YACb,CACF,CACF,CACF,CACF,CACF"}