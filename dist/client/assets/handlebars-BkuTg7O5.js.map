{"version": 3, "file": "handlebars-BkuTg7O5.js", "names": ["monaco_editor_core_star"], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/handlebars/handlebars.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/handlebars/handlebars.ts\nvar EMPTY_ELEMENTS = [\n  \"area\",\n  \"base\",\n  \"br\",\n  \"col\",\n  \"embed\",\n  \"hr\",\n  \"img\",\n  \"input\",\n  \"keygen\",\n  \"link\",\n  \"menuitem\",\n  \"meta\",\n  \"param\",\n  \"source\",\n  \"track\",\n  \"wbr\"\n];\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  comments: {\n    blockComment: [\"{{!--\", \"--}}\"]\n  },\n  brackets: [\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"],\n    [\"{{\", \"}}\"],\n    [\"{\", \"}\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      afterText: /^<\\/(\\w[\\w\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  // ignoreCase: true,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/\\{\\{!--/, \"comment.block.start.handlebars\", \"@commentBlock\"],\n      [/\\{\\{!/, \"comment.start.handlebars\", \"@comment\"],\n      [/\\{\\{/, { token: \"@rematch\", switchTo: \"@handlebarsInSimpleState.root\" }],\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@commentHtml\"],\n      [/(<)(\\w+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)([:\\w]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)(\\w+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/\\{/, \"delimiter.html\"],\n      [/[^<{]+/]\n      // text\n    ],\n    doctype: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.comment\"\n        }\n      ],\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/\\}\\}/, \"comment.end.handlebars\", \"@pop\"],\n      [/./, \"comment.content.handlebars\"]\n    ],\n    commentBlock: [\n      [/--\\}\\}/, \"comment.block.end.handlebars\", \"@pop\"],\n      [/./, \"comment.content.handlebars\"]\n    ],\n    commentHtml: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.comment\"\n        }\n      ],\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.otherTag\"\n        }\n      ],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.script\"\n        }\n      ],\n      [/type/, \"attribute.name\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.scriptAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.scriptAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.scriptWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInEmbeddedState.scriptEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.style\"\n        }\n      ],\n      [/type/, \"attribute.name\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.styleAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.styleAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.styleWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInEmbeddedState.styleEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <style> tags handling\n    handlebarsInSimpleState: [\n      [/\\{\\{\\{?/, \"delimiter.handlebars\"],\n      [/\\}\\}\\}?/, { token: \"delimiter.handlebars\", switchTo: \"@$S2.$S3\" }],\n      { include: \"handlebarsRoot\" }\n    ],\n    handlebarsInEmbeddedState: [\n      [/\\{\\{\\{?/, \"delimiter.handlebars\"],\n      [\n        /\\}\\}\\}?/,\n        {\n          token: \"delimiter.handlebars\",\n          switchTo: \"@$S2.$S3\",\n          nextEmbedded: \"$S3\"\n        }\n      ],\n      { include: \"handlebarsRoot\" }\n    ],\n    handlebarsRoot: [\n      [/\"[^\"]*\"/, \"string.handlebars\"],\n      [/[#/][^\\s}]+/, \"keyword.helper.handlebars\"],\n      [/else\\b/, \"keyword.helper.handlebars\"],\n      [/[\\s]+/],\n      [/[^}]/, \"variable.parameter.handlebars\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;;AAOA,IAAI,EAAY,OAAO,eACnB,EAAmB,OAAO,yBAC1B,EAAoB,OAAO,oBAC3B,EAAe,OAAO,UAAU,eAChC,GAAe,EAAI,EAAM,EAAQ,IAAS,CAC5C,GAAI,GAAQ,OAAO,GAAS,UAAY,OAAO,GAAS,eACjD,IAAI,KAAO,EAAkB,EAAK,CACjC,CAAC,EAAa,KAAK,EAAI,EAAI,EAAI,IAAQ,GACzC,EAAU,EAAI,EAAK,CAAE,QAAW,EAAK,GAAM,WAAY,EAAE,EAAO,EAAiB,EAAM,EAAI,GAAK,EAAK,WAAY,CAAC,CAExH,OAAO,GAEL,GAAc,EAAQ,EAAK,KAAkB,EAAY,EAAQ,EAAK,UAAU,CAAE,GAAgB,EAAY,EAAc,EAAK,UAAU,EAG3I,EAA6B,EAAE,CACnC,EAAW,EAA4BA,EAAwB,CAI/D,IAAI,EAAiB,CACnB,OACA,OACA,KACA,MACA,QACA,KACA,MACA,QACA,SACA,OACA,WACA,OACA,QACA,SACA,QACA,MACD,CACG,EAAO,CACT,YAAa,iFACb,SAAU,CACR,aAAc,CAAC,QAAS,OAAO,CAChC,CACD,SAAU,CACR,CAAC,OAAQ,MAAM,CACf,CAAC,IAAK,IAAI,CACV,CAAC,KAAM,KAAK,CACZ,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,aAAc,CACZ,CACE,WAAgB,OACd,UAAU,EAAe,KAAK,IAAI,CAAC,sCACnC,IACD,CACD,UAAW,wBACX,OAAQ,CACN,aAAc,EAA2B,UAAU,aAAa,cACjE,CACF,CACD,CACE,WAAgB,OACd,UAAU,EAAe,KAAK,IAAI,CAAC,sCACnC,IACD,CACD,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,OAAQ,CACnF,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,GAGd,UAAW,CACT,KAAM,CACJ,CAAC,UAAW,iCAAkC,gBAAgB,CAC9D,CAAC,QAAS,2BAA4B,WAAW,CACjD,CAAC,OAAQ,CAAE,MAAO,WAAY,SAAU,gCAAiC,CAAC,CAC1E,CAAC,YAAa,eAAgB,WAAW,CACzC,CAAC,OAAQ,eAAgB,eAAe,CACxC,CAAC,gBAAiB,CAAC,iBAAkB,WAAY,iBAAiB,CAAC,CACnE,CAAC,cAAe,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,UAAW,CAAC,CAAC,CAC3E,CAAC,aAAc,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,SAAU,CAAC,CAAC,CACzE,CAAC,cAAe,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,YAAa,CAAC,CAAC,CAC7E,CAAC,aAAc,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,YAAa,CAAC,CAAC,CAC5E,CAAC,IAAK,iBAAiB,CACvB,CAAC,KAAM,iBAAiB,CACxB,CAAC,SAAS,CAEX,CACD,QAAS,CACP,CACE,OACA,CACE,MAAO,WACP,SAAU,mCACX,CACF,CACD,CAAC,QAAS,uBAAuB,CACjC,CAAC,IAAK,eAAgB,OAAO,CAC9B,CACD,QAAS,CACP,CAAC,OAAQ,yBAA0B,OAAO,CAC1C,CAAC,IAAK,6BAA6B,CACpC,CACD,aAAc,CACZ,CAAC,SAAU,+BAAgC,OAAO,CAClD,CAAC,IAAK,6BAA6B,CACpC,CACD,YAAa,CACX,CACE,OACA,CACE,MAAO,WACP,SAAU,mCACX,CACF,CACD,CAAC,MAAO,eAAgB,OAAO,CAC/B,CAAC,QAAS,uBAAuB,CACjC,CAAC,IAAK,uBAAuB,CAC9B,CACD,SAAU,CACR,CACE,OACA,CACE,MAAO,WACP,SAAU,oCACX,CACF,CACD,CAAC,OAAQ,iBAAkB,OAAO,CAClC,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CAAC,aAAa,CAEf,CAGD,OAAQ,CACN,CACE,OACA,CACE,MAAO,WACP,SAAU,kCACX,CACF,CACD,CAAC,OAAQ,iBAAkB,mBAAmB,CAC9C,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CACE,IACA,CACE,MAAO,iBACP,KAAM,kCACN,aAAc,kBACf,CACF,CACD,CAAC,aAAa,CAEd,CACE,sBACA,CAAC,iBAAkB,WAAY,CAAE,MAAO,iBAAkB,KAAM,OAAQ,CAAC,CAC1E,CACF,CAED,gBAAiB,CACf,CACE,OACA,CACE,MAAO,WACP,SAAU,2CACX,CACF,CACD,CAAC,IAAK,YAAa,yBAAyB,CAC5C,CACE,IACA,CACE,MAAO,iBACP,KAAM,kCACN,aAAc,kBACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACvD,CAED,sBAAuB,CACrB,CACE,OACA,CACE,MAAO,WACP,SAAU,iDACX,CACF,CACD,CACE,YACA,CACE,MAAO,kBACP,SAAU,2BACX,CACF,CACD,CACE,YACA,CACE,MAAO,kBACP,SAAU,2BACX,CACF,CACD,CACE,IACA,CACE,MAAO,iBACP,KAAM,kCACN,aAAc,kBACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACvD,CAED,qBAAsB,CACpB,CACE,OACA,CACE,MAAO,WACP,SAAU,oDACX,CACF,CACD,CACE,IACA,CACE,MAAO,iBACP,KAAM,sBACN,aAAc,MACf,CACF,CACD,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CAAC,aAAa,CAEd,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACvD,CACD,eAAgB,CACd,CACE,OACA,CACE,MAAO,WACP,SAAU,gDACV,aAAc,OACf,CACF,CACD,CAAC,YAAa,CAAE,MAAO,WAAY,KAAM,OAAQ,aAAc,OAAQ,CAAC,CACzE,CAID,MAAO,CACL,CACE,OACA,CACE,MAAO,WACP,SAAU,iCACX,CACF,CACD,CAAC,OAAQ,iBAAkB,kBAAkB,CAC7C,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CACE,IACA,CACE,MAAO,iBACP,KAAM,0BACN,aAAc,WACf,CACF,CACD,CAAC,aAAa,CAEd,CACE,qBACA,CAAC,iBAAkB,WAAY,CAAE,MAAO,iBAAkB,KAAM,OAAQ,CAAC,CAC1E,CACF,CAED,eAAgB,CACd,CACE,OACA,CACE,MAAO,WACP,SAAU,0CACX,CACF,CACD,CAAC,IAAK,YAAa,wBAAwB,CAC3C,CACE,IACA,CACE,MAAO,iBACP,KAAM,0BACN,aAAc,WACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACtD,CAED,qBAAsB,CACpB,CACE,OACA,CACE,MAAO,WACP,SAAU,gDACX,CACF,CACD,CACE,YACA,CACE,MAAO,kBACP,SAAU,0BACX,CACF,CACD,CACE,YACA,CACE,MAAO,kBACP,SAAU,0BACX,CACF,CACD,CACE,IACA,CACE,MAAO,iBACP,KAAM,0BACN,aAAc,WACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACtD,CAED,oBAAqB,CACnB,CACE,OACA,CACE,MAAO,WACP,SAAU,mDACX,CACF,CACD,CACE,IACA,CACE,MAAO,iBACP,KAAM,qBACN,aAAc,MACf,CACF,CACD,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CAAC,aAAa,CAEd,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACtD,CACD,cAAe,CACb,CACE,OACA,CACE,MAAO,WACP,SAAU,+CACV,aAAc,OACf,CACF,CACD,CAAC,WAAY,CAAE,MAAO,WAAY,KAAM,OAAQ,aAAc,OAAQ,CAAC,CACxE,CAED,wBAAyB,CACvB,CAAC,UAAW,uBAAuB,CACnC,CAAC,UAAW,CAAE,MAAO,uBAAwB,SAAU,WAAY,CAAC,CACpE,CAAE,QAAS,iBAAkB,CAC9B,CACD,0BAA2B,CACzB,CAAC,UAAW,uBAAuB,CACnC,CACE,UACA,CACE,MAAO,uBACP,SAAU,WACV,aAAc,MACf,CACF,CACD,CAAE,QAAS,iBAAkB,CAC9B,CACD,eAAgB,CACd,CAAC,UAAW,oBAAoB,CAChC,CAAC,cAAe,4BAA4B,CAC5C,CAAC,SAAU,4BAA4B,CACvC,CAAC,QAAQ,CACT,CAAC,OAAQ,gCAAgC,CAC1C,CACF,CACF"}