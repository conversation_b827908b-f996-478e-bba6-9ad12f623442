{"version": 3, "file": "mips-CV_xlKv0.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/mips/mips.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/mips/mips.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#%\\^\\&\\*\\(\\)\\=\\$\\-\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    blockComment: [\"###\", \"###\"],\n    lineComment: \"#\"\n  },\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: false,\n  tokenPostfix: \".mips\",\n  regEx: /\\/(?!\\/\\/)(?:[^\\/\\\\]|\\\\.)*\\/[igm]*/,\n  keywords: [\n    \".data\",\n    \".text\",\n    \"syscall\",\n    \"trap\",\n    \"add\",\n    \"addu\",\n    \"addi\",\n    \"addiu\",\n    \"and\",\n    \"andi\",\n    \"div\",\n    \"divu\",\n    \"mult\",\n    \"multu\",\n    \"nor\",\n    \"or\",\n    \"ori\",\n    \"sll\",\n    \"slv\",\n    \"sra\",\n    \"srav\",\n    \"srl\",\n    \"srlv\",\n    \"sub\",\n    \"subu\",\n    \"xor\",\n    \"xori\",\n    \"lhi\",\n    \"lho\",\n    \"lhi\",\n    \"llo\",\n    \"slt\",\n    \"slti\",\n    \"sltu\",\n    \"sltiu\",\n    \"beq\",\n    \"bgtz\",\n    \"blez\",\n    \"bne\",\n    \"j\",\n    \"jal\",\n    \"jalr\",\n    \"jr\",\n    \"lb\",\n    \"lbu\",\n    \"lh\",\n    \"lhu\",\n    \"lw\",\n    \"li\",\n    \"la\",\n    \"sb\",\n    \"sh\",\n    \"sw\",\n    \"mfhi\",\n    \"mflo\",\n    \"mthi\",\n    \"mtlo\",\n    \"move\"\n  ],\n  // we include these common regular expressions\n  symbols: /[\\.,\\:]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"'$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [/\\$[a-zA-Z_]\\w*/, \"variable.predefined\"],\n      [\n        /[.a-zA-Z_]\\w*/,\n        {\n          cases: {\n            this: \"variable.predefined\",\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // whitespace\n      [/[ \\t\\r\\n]+/, \"\"],\n      // Comments\n      [/#.*$/, \"comment\"],\n      // regular expressions\n      [\"///\", { token: \"regexp\", next: \"@hereregexp\" }],\n      [/^(\\s*)(@regEx)/, [\"\", \"regexp\"]],\n      [/(\\,)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\:)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      // delimiters\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d+[eE]([\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d+\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/0[0-7]+(?!\\d)/, \"number.octal\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[,.]/, \"delimiter\"],\n      // strings:\n      [/\"\"\"/, \"string\", '@herestring.\"\"\"'],\n      [/'''/, \"string\", \"@herestring.'''\"],\n      [\n        /\"/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: '@string.\"' }\n          }\n        }\n      ],\n      [\n        /'/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: \"@string.'\" }\n          }\n        }\n      ]\n    ],\n    string: [\n      [/[^\"'\\#\\\\]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\./, \"string.escape.invalid\"],\n      [/\\./, \"string.escape.invalid\"],\n      [\n        /#{/,\n        {\n          cases: {\n            '$S2==\"': {\n              token: \"string\",\n              next: \"root.interpolatedstring\"\n            },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/#/, \"string\"]\n    ],\n    herestring: [\n      [\n        /(\"\"\"|''')/,\n        {\n          cases: {\n            \"$1==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/[^#\\\\'\"]+/, \"string\"],\n      [/['\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\./, \"string.escape.invalid\"],\n      [/#{/, { token: \"string.quote\", next: \"root.interpolatedstring\" }],\n      [/#/, \"string\"]\n    ],\n    comment: [\n      [/[^#]+/, \"comment\"],\n      [/#/, \"comment\"]\n    ],\n    hereregexp: [\n      [/[^\\\\\\/#]+/, \"regexp\"],\n      [/\\\\./, \"regexp\"],\n      [/#.*$/, \"comment\"],\n      [\"///[igm]*\", { token: \"regexp\", next: \"@pop\" }],\n      [/\\//, \"regexp\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,YAAa,wFACb,SAAU,CACR,aAAc,CAAC,MAAO,MAAM,CAC5B,YAAa,IACd,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,kBAAkB,CACpC,IAAS,OAAO,qBAAqB,CACtC,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,WAAY,GACZ,aAAc,QACd,MAAO,qCACP,SAAU,0QA2DT,CAED,QAAS,WACT,QAAS,yEAET,UAAW,CACT,KAAM,CAEJ,CAAC,iBAAkB,sBAAsB,CACzC,CACE,gBACA,CACE,MAAO,CACL,KAAM,sBACN,YAAa,CAAE,MAAO,aAAc,CACpC,WAAY,GACb,CACF,CACF,CAED,CAAC,aAAc,GAAG,CAElB,CAAC,OAAQ,UAAU,CAEnB,CAAC,MAAO,CAAE,MAAO,SAAU,KAAM,cAAe,CAAC,CACjD,CAAC,iBAAkB,CAAC,GAAI,SAAS,CAAC,CAClC,CAAC,oBAAqB,CAAC,YAAa,GAAI,SAAS,CAAC,CAClD,CAAC,oBAAqB,CAAC,YAAa,GAAI,SAAS,CAAC,CAElD,CAAC,WAAY,YAAY,CAEzB,CAAC,sBAAuB,eAAe,CACvC,CAAC,2BAA4B,eAAe,CAC5C,CAAC,oBAAqB,aAAa,CACnC,CAAC,gBAAiB,eAAe,CACjC,CAAC,MAAO,SAAS,CAEjB,CAAC,OAAQ,YAAY,CAErB,CAAC,MAAO,SAAU,kBAAkB,CACpC,CAAC,MAAO,SAAU,kBAAkB,CACpC,CACE,IACA,CACE,MAAO,CACL,OAAQ,SACR,WAAY,CAAE,MAAO,SAAU,KAAM,YAAa,CACnD,CACF,CACF,CACD,CACE,IACA,CACE,MAAO,CACL,OAAQ,SACR,WAAY,CAAE,MAAO,SAAU,KAAM,YAAa,CACnD,CACF,CACF,CACF,CACD,OAAQ,CACN,CAAC,aAAc,SAAS,CACxB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,KAAM,wBAAwB,CAC/B,CAAC,KAAM,wBAAwB,CAC/B,CACE,KACA,CACE,MAAO,CACL,SAAU,CACR,MAAO,SACP,KAAM,0BACP,CACD,WAAY,SACb,CACF,CACF,CACD,CACE,OACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,SAAU,KAAM,OAAQ,CAC5C,WAAY,SACb,CACF,CACF,CACD,CAAC,IAAK,SAAS,CAChB,CACD,WAAY,CACV,CACE,YACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,SAAU,KAAM,OAAQ,CAC5C,WAAY,SACb,CACF,CACF,CACD,CAAC,YAAa,SAAS,CACvB,CAAC,QAAS,SAAS,CACnB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,KAAM,wBAAwB,CAC/B,CAAC,KAAM,CAAE,MAAO,eAAgB,KAAM,0BAA2B,CAAC,CAClE,CAAC,IAAK,SAAS,CAChB,CACD,QAAS,CACP,CAAC,QAAS,UAAU,CACpB,CAAC,IAAK,UAAU,CACjB,CACD,WAAY,CACV,CAAC,YAAa,SAAS,CACvB,CAAC,MAAO,SAAS,CACjB,CAAC,OAAQ,UAAU,CACnB,CAAC,YAAa,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAC,CAChD,CAAC,KAAM,SAAS,CACjB,CACF,CACF"}