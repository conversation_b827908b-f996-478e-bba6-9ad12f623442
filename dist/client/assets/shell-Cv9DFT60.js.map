{"version": 3, "file": "shell-Cv9DFT60.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/shell/shell.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/shell/shell.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: true,\n  tokenPostfix: \".shell\",\n  brackets: [\n    { token: \"delimiter.bracket\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\n    \"if\",\n    \"then\",\n    \"do\",\n    \"else\",\n    \"elif\",\n    \"while\",\n    \"until\",\n    \"for\",\n    \"in\",\n    \"esac\",\n    \"fi\",\n    \"fin\",\n    \"fil\",\n    \"done\",\n    \"exit\",\n    \"set\",\n    \"unset\",\n    \"export\",\n    \"function\"\n  ],\n  builtins: [\n    \"ab\",\n    \"awk\",\n    \"bash\",\n    \"beep\",\n    \"cat\",\n    \"cc\",\n    \"cd\",\n    \"chown\",\n    \"chmod\",\n    \"chroot\",\n    \"clear\",\n    \"cp\",\n    \"curl\",\n    \"cut\",\n    \"diff\",\n    \"echo\",\n    \"find\",\n    \"gawk\",\n    \"gcc\",\n    \"get\",\n    \"git\",\n    \"grep\",\n    \"hg\",\n    \"kill\",\n    \"killall\",\n    \"ln\",\n    \"ls\",\n    \"make\",\n    \"mkdir\",\n    \"openssl\",\n    \"mv\",\n    \"nc\",\n    \"node\",\n    \"npm\",\n    \"ping\",\n    \"ps\",\n    \"restart\",\n    \"rm\",\n    \"rmdir\",\n    \"sed\",\n    \"service\",\n    \"sh\",\n    \"shopt\",\n    \"shred\",\n    \"source\",\n    \"sort\",\n    \"sleep\",\n    \"ssh\",\n    \"start\",\n    \"stop\",\n    \"su\",\n    \"sudo\",\n    \"svn\",\n    \"tee\",\n    \"telnet\",\n    \"top\",\n    \"touch\",\n    \"vi\",\n    \"vim\",\n    \"wall\",\n    \"wc\",\n    \"wget\",\n    \"who\",\n    \"write\",\n    \"yes\",\n    \"zsh\"\n  ],\n  startingWithDash: /\\-+\\w+/,\n  identifiersWithDashes: /[a-zA-Z]\\w+(?:@startingWithDash)+/,\n  // we include these common regular expressions\n  symbols: /[=><!~?&|+\\-*\\/\\^;\\.,]+/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/@identifiersWithDashes/, \"\"],\n      [/(\\s)((?:@startingWithDash)+)/, [\"white\", \"attribute.name\"]],\n      [\n        /[a-zA-Z]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"type.identifier\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      { include: \"@strings\" },\n      { include: \"@parameters\" },\n      { include: \"@heredoc\" },\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      { include: \"@numbers\" },\n      [/[,;]/, \"delimiter\"]\n    ],\n    whitespace: [\n      [/\\s+/, \"white\"],\n      [/(^#!.*$)/, \"metatag\"],\n      [/(^#.*$)/, \"comment\"]\n    ],\n    numbers: [\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]*[0-9a-fA-F]/, \"number.hex\"],\n      [/\\d+/, \"number\"]\n    ],\n    // Recognize strings, including those broken across lines\n    strings: [\n      [/'/, \"string\", \"@stringBody\"],\n      [/\"/, \"string\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [/'/, \"string\", \"@popall\"],\n      [/./, \"string\"]\n    ],\n    dblStringBody: [\n      [/\"/, \"string\", \"@popall\"],\n      [/./, \"string\"]\n    ],\n    heredoc: [\n      [\n        /(<<[-<]?)(\\s*)(['\"`]?)([\\w\\-]+)(['\"`]?)/,\n        [\n          \"constants\",\n          \"white\",\n          \"string.heredoc.delimiter\",\n          \"string.heredoc\",\n          \"string.heredoc.delimiter\"\n        ]\n      ]\n    ],\n    parameters: [\n      [/\\$\\d+/, \"variable.predefined\"],\n      [/\\$\\w+/, \"variable\"],\n      [/\\$[*@#?\\-$!0_]/, \"variable\"],\n      [/\\$'/, \"variable\", \"@parameterBodyQuote\"],\n      [/\\$\"/, \"variable\", \"@parameterBodyDoubleQuote\"],\n      [/\\$\\(/, \"variable\", \"@parameterBodyParen\"],\n      [/\\$\\{/, \"variable\", \"@parameterBodyCurlyBrace\"]\n    ],\n    parameterBodyQuote: [\n      [/[^#:%*@\\-!_']+/, \"variable\"],\n      [/[#:%*@\\-!_]/, \"delimiter\"],\n      [/[']/, \"variable\", \"@pop\"]\n    ],\n    parameterBodyDoubleQuote: [\n      [/[^#:%*@\\-!_\"]+/, \"variable\"],\n      [/[#:%*@\\-!_]/, \"delimiter\"],\n      [/[\"]/, \"variable\", \"@pop\"]\n    ],\n    parameterBodyParen: [\n      [/[^#:%*@\\-!_)]+/, \"variable\"],\n      [/[#:%*@\\-!_]/, \"delimiter\"],\n      [/[)]/, \"variable\", \"@pop\"]\n    ],\n    parameterBodyCurlyBrace: [\n      [/[^#:%*@\\-!_}]+/, \"variable\"],\n      [/[#:%*@\\-!_]/, \"delimiter\"],\n      [/[}]/, \"variable\", \"@pop\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,IACd,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACF,CACG,EAAW,CACb,aAAc,GACd,WAAY,GACZ,aAAc,SACd,SAAU,CACR,CAAE,MAAO,oBAAqB,KAAM,IAAK,MAAO,IAAK,CACrD,CAAE,MAAO,wBAAyB,KAAM,IAAK,MAAO,IAAK,CACzD,CAAE,MAAO,mBAAoB,KAAM,IAAK,MAAO,IAAK,CACrD,CACD,SAAU,CACR,KACA,OACA,KACA,OACA,OACA,QACA,QACA,MACA,KACA,OACA,KACA,MACA,MACA,OACA,OACA,MACA,QACA,SACA,WACD,CACD,SAAU,qUAmET,CACD,iBAAkB,SAClB,sBAAuB,oCAEvB,QAAS,0BAET,UAAW,CACT,KAAM,CACJ,CAAC,yBAA0B,GAAG,CAC9B,CAAC,+BAAgC,CAAC,QAAS,iBAAiB,CAAC,CAC7D,CACE,cACA,CACE,MAAO,CACL,YAAa,UACb,YAAa,kBACb,WAAY,GACb,CACF,CACF,CACD,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,WAAY,CACvB,CAAC,aAAc,YAAY,CAC3B,CAAC,WAAY,YAAY,CACzB,CAAE,QAAS,WAAY,CACvB,CAAC,OAAQ,YAAY,CACtB,CACD,WAAY,CACV,CAAC,MAAO,QAAQ,CAChB,CAAC,WAAY,UAAU,CACvB,CAAC,UAAW,UAAU,CACvB,CACD,QAAS,CACP,CAAC,2BAA4B,eAAe,CAC5C,CAAC,gCAAiC,aAAa,CAC/C,CAAC,MAAO,SAAS,CAClB,CAED,QAAS,CACP,CAAC,IAAK,SAAU,cAAc,CAC9B,CAAC,IAAK,SAAU,iBAAiB,CAClC,CACD,WAAY,CACV,CAAC,IAAK,SAAU,UAAU,CAC1B,CAAC,IAAK,SAAS,CAChB,CACD,cAAe,CACb,CAAC,IAAK,SAAU,UAAU,CAC1B,CAAC,IAAK,SAAS,CAChB,CACD,QAAS,CACP,CACE,0CACA,CACE,YACA,QACA,2BACA,iBACA,2BACD,CACF,CACF,CACD,WAAY,CACV,CAAC,QAAS,sBAAsB,CAChC,CAAC,QAAS,WAAW,CACrB,CAAC,iBAAkB,WAAW,CAC9B,CAAC,MAAO,WAAY,sBAAsB,CAC1C,CAAC,MAAO,WAAY,4BAA4B,CAChD,CAAC,OAAQ,WAAY,sBAAsB,CAC3C,CAAC,OAAQ,WAAY,2BAA2B,CACjD,CACD,mBAAoB,CAClB,CAAC,iBAAkB,WAAW,CAC9B,CAAC,cAAe,YAAY,CAC5B,CAAC,MAAO,WAAY,OAAO,CAC5B,CACD,yBAA0B,CACxB,CAAC,iBAAkB,WAAW,CAC9B,CAAC,cAAe,YAAY,CAC5B,CAAC,MAAO,WAAY,OAAO,CAC5B,CACD,mBAAoB,CAClB,CAAC,iBAAkB,WAAW,CAC9B,CAAC,cAAe,YAAY,CAC5B,CAAC,MAAO,WAAY,OAAO,CAC5B,CACD,wBAAyB,CACvB,CAAC,iBAAkB,WAAW,CAC9B,CAAC,cAAe,YAAY,CAC5B,CAAC,MAAO,WAAY,OAAO,CAC5B,CACF,CACF"}