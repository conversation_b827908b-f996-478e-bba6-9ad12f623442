{"version": 3, "file": "lexon-BBSM84G9.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/lexon/lexon.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/lexon/lexon.ts\nvar conf = {\n  comments: {\n    lineComment: \"COMMENT\"\n    // blockComment: ['COMMENT', '.'],\n  },\n  brackets: [[\"(\", \")\"]],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \":\", close: \".\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"`\", close: \"`\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \":\", close: \".\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*(::\\\\s*|COMMENT\\\\s+)#region\"),\n      end: new RegExp(\"^\\\\s*(::\\\\s*|COMMENT\\\\s+)#endregion\")\n    }\n  }\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  // defaultToken: 'invalid',\n  tokenPostfix: \".lexon\",\n  ignoreCase: true,\n  keywords: [\n    \"lexon\",\n    \"lex\",\n    \"clause\",\n    \"terms\",\n    \"contracts\",\n    \"may\",\n    \"pay\",\n    \"pays\",\n    \"appoints\",\n    \"into\",\n    \"to\"\n  ],\n  typeKeywords: [\"amount\", \"person\", \"key\", \"time\", \"date\", \"asset\", \"text\"],\n  operators: [\n    \"less\",\n    \"greater\",\n    \"equal\",\n    \"le\",\n    \"gt\",\n    \"or\",\n    \"and\",\n    \"add\",\n    \"added\",\n    \"subtract\",\n    \"subtracted\",\n    \"multiply\",\n    \"multiplied\",\n    \"times\",\n    \"divide\",\n    \"divided\",\n    \"is\",\n    \"be\",\n    \"certified\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // comment\n      [/^(\\s*)(comment:?(?:\\s.*|))$/, [\"\", \"comment\"]],\n      // special identifier cases\n      [\n        /\"/,\n        {\n          token: \"identifier.quote\",\n          bracket: \"@open\",\n          next: \"@quoted_identifier\"\n        }\n      ],\n      [\n        \"LEX$\",\n        {\n          token: \"keyword\",\n          bracket: \"@open\",\n          next: \"@identifier_until_period\"\n        }\n      ],\n      [\"LEXON\", { token: \"keyword\", bracket: \"@open\", next: \"@semver\" }],\n      [\n        \":\",\n        {\n          token: \"delimiter\",\n          bracket: \"@open\",\n          next: \"@identifier_until_period\"\n        }\n      ],\n      // identifiers and keywords\n      [\n        /[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@typeKeywords\": \"keyword.type\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d*\\.\\d*\\.\\d*/, \"number.semver\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"]\n    ],\n    quoted_identifier: [\n      [/[^\\\\\"]+/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    space_identifier_until_period: [\n      [\":\", \"delimiter\"],\n      [\" \", { token: \"white\", next: \"@identifier_rest\" }]\n    ],\n    identifier_until_period: [\n      { include: \"@whitespace\" },\n      [\":\", { token: \"delimiter\", next: \"@identifier_rest\" }],\n      [/[^\\\\.]+/, \"identifier\"],\n      [/\\./, { token: \"delimiter\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    identifier_rest: [\n      [/[^\\\\.]+/, \"identifier\"],\n      [/\\./, { token: \"delimiter\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    semver: [\n      { include: \"@whitespace\" },\n      [\":\", \"delimiter\"],\n      [/\\d*\\.\\d*\\.\\d*/, { token: \"number.semver\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"white\"]]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,UAEd,CACD,SAAU,CAAC,CAAC,IAAK,IAAI,CAAC,CACtB,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,mCAAmC,CACrD,IAAS,OAAO,sCAAsC,CACvD,CACF,CACF,CACG,EAAW,CAGb,aAAc,SACd,WAAY,GACZ,SAAU,CACR,QACA,MACA,SACA,QACA,YACA,MACA,MACA,OACA,WACA,OACA,KACD,CACD,aAAc,CAAC,SAAU,SAAU,MAAO,OAAQ,OAAQ,QAAS,OAAO,CAC1E,UAAW,CACT,OACA,UACA,QACA,KACA,KACA,KACA,MACA,MACA,QACA,WACA,aACA,WACA,aACA,QACA,SACA,UACA,KACA,KACA,YACD,CAED,QAAS,wBAET,UAAW,CACT,KAAM,CAEJ,CAAC,8BAA+B,CAAC,GAAI,UAAU,CAAC,CAEhD,CACE,IACA,CACE,MAAO,mBACP,QAAS,QACT,KAAM,qBACP,CACF,CACD,CACE,OACA,CACE,MAAO,UACP,QAAS,QACT,KAAM,2BACP,CACF,CACD,CAAC,QAAS,CAAE,MAAO,UAAW,QAAS,QAAS,KAAM,UAAW,CAAC,CAClE,CACE,IACA,CACE,MAAO,YACP,QAAS,QACT,KAAM,2BACP,CACF,CAED,CACE,gBACA,CACE,MAAO,CACL,aAAc,WACd,gBAAiB,eACjB,YAAa,UACb,WAAY,aACb,CACF,CACF,CAED,CAAE,QAAS,cAAe,CAE1B,CAAC,aAAc,YAAY,CAC3B,CAAC,mBAAoB,YAAY,CACjC,CAAC,WAAY,YAAY,CAEzB,CAAC,gBAAiB,gBAAgB,CAClC,CAAC,2BAA4B,eAAe,CAC5C,CAAC,oBAAqB,aAAa,CACnC,CAAC,MAAO,SAAS,CAEjB,CAAC,QAAS,YAAY,CACvB,CACD,kBAAmB,CACjB,CAAC,UAAW,aAAa,CACzB,CAAC,IAAK,CAAE,MAAO,mBAAoB,QAAS,SAAU,KAAM,OAAQ,CAAC,CACtE,CACD,8BAA+B,CAC7B,CAAC,IAAK,YAAY,CAClB,CAAC,IAAK,CAAE,MAAO,QAAS,KAAM,mBAAoB,CAAC,CACpD,CACD,wBAAyB,CACvB,CAAE,QAAS,cAAe,CAC1B,CAAC,IAAK,CAAE,MAAO,YAAa,KAAM,mBAAoB,CAAC,CACvD,CAAC,UAAW,aAAa,CACzB,CAAC,KAAM,CAAE,MAAO,YAAa,QAAS,SAAU,KAAM,OAAQ,CAAC,CAChE,CACD,gBAAiB,CACf,CAAC,UAAW,aAAa,CACzB,CAAC,KAAM,CAAE,MAAO,YAAa,QAAS,SAAU,KAAM,OAAQ,CAAC,CAChE,CACD,OAAQ,CACN,CAAE,QAAS,cAAe,CAC1B,CAAC,IAAK,YAAY,CAClB,CAAC,gBAAiB,CAAE,MAAO,gBAAiB,QAAS,SAAU,KAAM,OAAQ,CAAC,CAC/E,CACD,WAAY,CAAC,CAAC,aAAc,QAAQ,CAAC,CACtC,CACF"}