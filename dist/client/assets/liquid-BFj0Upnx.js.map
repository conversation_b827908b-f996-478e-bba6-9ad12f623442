{"version": 3, "file": "liquid-BFj0Upnx.js", "names": ["monaco_editor_core_star"], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/liquid/liquid.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/liquid/liquid.ts\nvar EMPTY_ELEMENTS = [\n  \"area\",\n  \"base\",\n  \"br\",\n  \"col\",\n  \"embed\",\n  \"hr\",\n  \"img\",\n  \"input\",\n  \"keygen\",\n  \"link\",\n  \"menuitem\",\n  \"meta\",\n  \"param\",\n  \"source\",\n  \"track\",\n  \"wbr\"\n];\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  brackets: [\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"],\n    [\"{{\", \"}}\"],\n    [\"{%\", \"%}\"],\n    [\"{\", \"}\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"%\", close: \"%\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      afterText: /^<\\/(\\w[\\w\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  builtinTags: [\n    \"if\",\n    \"else\",\n    \"elseif\",\n    \"endif\",\n    \"render\",\n    \"assign\",\n    \"capture\",\n    \"endcapture\",\n    \"case\",\n    \"endcase\",\n    \"comment\",\n    \"endcomment\",\n    \"cycle\",\n    \"decrement\",\n    \"for\",\n    \"endfor\",\n    \"include\",\n    \"increment\",\n    \"layout\",\n    \"raw\",\n    \"endraw\",\n    \"render\",\n    \"tablerow\",\n    \"endtablerow\",\n    \"unless\",\n    \"endunless\"\n  ],\n  builtinFilters: [\n    \"abs\",\n    \"append\",\n    \"at_least\",\n    \"at_most\",\n    \"capitalize\",\n    \"ceil\",\n    \"compact\",\n    \"date\",\n    \"default\",\n    \"divided_by\",\n    \"downcase\",\n    \"escape\",\n    \"escape_once\",\n    \"first\",\n    \"floor\",\n    \"join\",\n    \"json\",\n    \"last\",\n    \"lstrip\",\n    \"map\",\n    \"minus\",\n    \"modulo\",\n    \"newline_to_br\",\n    \"plus\",\n    \"prepend\",\n    \"remove\",\n    \"remove_first\",\n    \"replace\",\n    \"replace_first\",\n    \"reverse\",\n    \"round\",\n    \"rstrip\",\n    \"size\",\n    \"slice\",\n    \"sort\",\n    \"sort_natural\",\n    \"split\",\n    \"strip\",\n    \"strip_html\",\n    \"strip_newlines\",\n    \"times\",\n    \"truncate\",\n    \"truncatewords\",\n    \"uniq\",\n    \"upcase\",\n    \"url_decode\",\n    \"url_encode\",\n    \"where\"\n  ],\n  constants: [\"true\", \"false\"],\n  operators: [\"==\", \"!=\", \">\", \"<\", \">=\", \"<=\"],\n  symbol: /[=><!]+/,\n  identifier: /[a-zA-Z_][\\w]*/,\n  tokenizer: {\n    root: [\n      [/\\{\\%\\s*comment\\s*\\%\\}/, \"comment.start.liquid\", \"@comment\"],\n      [/\\{\\{/, { token: \"@rematch\", switchTo: \"@liquidState.root\" }],\n      [/\\{\\%/, { token: \"@rematch\", switchTo: \"@liquidState.root\" }],\n      [/(<)([\\w\\-]+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)([:\\w]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)([\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/\\{/, \"delimiter.html\"],\n      [/[^<{]+/]\n      // text\n    ],\n    comment: [\n      [/\\{\\%\\s*endcomment\\s*\\%\\}/, \"comment.end.liquid\", \"@pop\"],\n      [/./, \"comment.content.liquid\"]\n    ],\n    otherTag: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@liquidState.otherTag\"\n        }\n      ],\n      [\n        /\\{\\%/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@liquidState.otherTag\"\n        }\n      ],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    liquidState: [\n      [/\\{\\{/, \"delimiter.output.liquid\"],\n      [/\\}\\}/, { token: \"delimiter.output.liquid\", switchTo: \"@$S2.$S3\" }],\n      [/\\{\\%/, \"delimiter.tag.liquid\"],\n      [/raw\\s*\\%\\}/, \"delimiter.tag.liquid\", \"@liquidRaw\"],\n      [/\\%\\}/, { token: \"delimiter.tag.liquid\", switchTo: \"@$S2.$S3\" }],\n      { include: \"liquidRoot\" }\n    ],\n    liquidRaw: [\n      [/^(?!\\{\\%\\s*endraw\\s*\\%\\}).+/],\n      [/\\{\\%/, \"delimiter.tag.liquid\"],\n      [/@identifier/],\n      [/\\%\\}/, { token: \"delimiter.tag.liquid\", next: \"@root\" }]\n    ],\n    liquidRoot: [\n      [/\\d+(\\.\\d+)?/, \"number.liquid\"],\n      [/\"[^\"]*\"/, \"string.liquid\"],\n      [/'[^']*'/, \"string.liquid\"],\n      [/\\s+/],\n      [\n        /@symbol/,\n        {\n          cases: {\n            \"@operators\": \"operator.liquid\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/\\./],\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@constants\": \"keyword.liquid\",\n            \"@builtinFilters\": \"predefined.liquid\",\n            \"@builtinTags\": \"predefined.liquid\",\n            \"@default\": \"variable.liquid\"\n          }\n        }\n      ],\n      [/[^}|%]/, \"variable.liquid\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;;AAOA,IAAI,EAAY,OAAO,eACnB,EAAmB,OAAO,yBAC1B,EAAoB,OAAO,oBAC3B,EAAe,OAAO,UAAU,eAChC,GAAe,EAAI,EAAM,EAAQ,IAAS,CAC5C,GAAI,GAAQ,OAAO,GAAS,UAAY,OAAO,GAAS,eACjD,IAAI,KAAO,EAAkB,EAAK,CACjC,CAAC,EAAa,KAAK,EAAI,EAAI,EAAI,IAAQ,GACzC,EAAU,EAAI,EAAK,CAAE,QAAW,EAAK,GAAM,WAAY,EAAE,EAAO,EAAiB,EAAM,EAAI,GAAK,EAAK,WAAY,CAAC,CAExH,OAAO,GAEL,GAAc,EAAQ,EAAK,KAAkB,EAAY,EAAQ,EAAK,UAAU,CAAE,GAAgB,EAAY,EAAc,EAAK,UAAU,EAG3I,EAA6B,EAAE,CACnC,EAAW,EAA4BA,EAAwB,CAI/D,IAAI,EAAiB,CACnB,OACA,OACA,KACA,MACA,QACA,KACA,MACA,QACA,SACA,OACA,WACA,OACA,QACA,SACA,QACA,MACD,CACG,EAAO,CACT,YAAa,iFACb,SAAU,CACR,CAAC,OAAQ,MAAM,CACf,CAAC,IAAK,IAAI,CACV,CAAC,KAAM,KAAK,CACZ,CAAC,KAAM,KAAK,CACZ,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,aAAc,CACZ,CACE,WAAgB,OACd,UAAU,EAAe,KAAK,IAAI,CAAC,sCACnC,IACD,CACD,UAAW,wBACX,OAAQ,CACN,aAAc,EAA2B,UAAU,aAAa,cACjE,CACF,CACD,CACE,WAAgB,OACd,UAAU,EAAe,KAAK,IAAI,CAAC,sCACnC,IACD,CACD,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,OAAQ,CACnF,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,GACd,YAAa,8MA2BZ,CACD,eAAgB,yYAiDf,CACD,UAAW,CAAC,OAAQ,QAAQ,CAC5B,UAAW,CAAC,KAAM,KAAM,IAAK,IAAK,KAAM,KAAK,CAC7C,OAAQ,UACR,WAAY,iBACZ,UAAW,CACT,KAAM,CACJ,CAAC,wBAAyB,uBAAwB,WAAW,CAC7D,CAAC,OAAQ,CAAE,MAAO,WAAY,SAAU,oBAAqB,CAAC,CAC9D,CAAC,OAAQ,CAAE,MAAO,WAAY,SAAU,oBAAqB,CAAC,CAC9D,CAAC,oBAAqB,CAAC,iBAAkB,WAAY,iBAAiB,CAAC,CACvE,CAAC,cAAe,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,YAAa,CAAC,CAAC,CAC7E,CAAC,iBAAkB,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,YAAa,CAAC,CAAC,CAChF,CAAC,IAAK,iBAAiB,CACvB,CAAC,KAAM,iBAAiB,CACxB,CAAC,SAAS,CAEX,CACD,QAAS,CACP,CAAC,2BAA4B,qBAAsB,OAAO,CAC1D,CAAC,IAAK,yBAAyB,CAChC,CACD,SAAU,CACR,CACE,OACA,CACE,MAAO,WACP,SAAU,wBACX,CACF,CACD,CACE,OACA,CACE,MAAO,WACP,SAAU,wBACX,CACF,CACD,CAAC,OAAQ,iBAAkB,OAAO,CAClC,CAAC,YAAa,kBAAkB,CAChC,CAAC,YAAa,kBAAkB,CAChC,CAAC,UAAW,iBAAiB,CAC7B,CAAC,IAAK,YAAY,CAClB,CAAC,aAAa,CAEf,CACD,YAAa,CACX,CAAC,OAAQ,0BAA0B,CACnC,CAAC,OAAQ,CAAE,MAAO,0BAA2B,SAAU,WAAY,CAAC,CACpE,CAAC,OAAQ,uBAAuB,CAChC,CAAC,aAAc,uBAAwB,aAAa,CACpD,CAAC,OAAQ,CAAE,MAAO,uBAAwB,SAAU,WAAY,CAAC,CACjE,CAAE,QAAS,aAAc,CAC1B,CACD,UAAW,CACT,CAAC,8BAA8B,CAC/B,CAAC,OAAQ,uBAAuB,CAChC,CAAC,cAAc,CACf,CAAC,OAAQ,CAAE,MAAO,uBAAwB,KAAM,QAAS,CAAC,CAC3D,CACD,WAAY,CACV,CAAC,cAAe,gBAAgB,CAChC,CAAC,UAAW,gBAAgB,CAC5B,CAAC,UAAW,gBAAgB,CAC5B,CAAC,MAAM,CACP,CACE,UACA,CACE,MAAO,CACL,aAAc,kBACd,WAAY,GACb,CACF,CACF,CACD,CAAC,KAAK,CACN,CACE,cACA,CACE,MAAO,CACL,aAAc,iBACd,kBAAmB,oBACnB,eAAgB,oBAChB,WAAY,kBACb,CACF,CACF,CACD,CAAC,SAAU,kBAAkB,CAC9B,CACF,CACF"}