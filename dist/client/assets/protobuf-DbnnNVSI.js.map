{"version": 3, "file": "protobuf-DbnnNVSI.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/protobuf/protobuf.ts\nvar namedLiterals = [\"true\", \"false\"];\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\"] }\n  ],\n  autoCloseBefore: \".,=}])>' \\n\t\",\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\"^((?!\\\\/\\\\/).)*(\\\\{[^}\\\"'`]*|\\\\([^)\\\"'`]*|\\\\[[^\\\\]\\\"'`]*)$\"),\n    decreaseIndentPattern: new RegExp(\"^((?!.*?\\\\/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\")\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".proto\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  symbols: /[=><!~?:&|+\\-*/^%]+/,\n  keywords: [\n    \"syntax\",\n    \"import\",\n    \"weak\",\n    \"public\",\n    \"package\",\n    \"option\",\n    \"repeated\",\n    \"oneof\",\n    \"map\",\n    \"reserved\",\n    \"to\",\n    \"max\",\n    \"enum\",\n    \"message\",\n    \"service\",\n    \"rpc\",\n    \"stream\",\n    \"returns\",\n    \"package\",\n    \"optional\",\n    \"true\",\n    \"false\"\n  ],\n  builtinTypes: [\n    \"double\",\n    \"float\",\n    \"int32\",\n    \"int64\",\n    \"uint32\",\n    \"uint64\",\n    \"sint32\",\n    \"sint64\",\n    \"fixed32\",\n    \"fixed64\",\n    \"sfixed32\",\n    \"sfixed64\",\n    \"bool\",\n    \"string\",\n    \"bytes\"\n  ],\n  operators: [\"=\", \"+\", \"-\"],\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|'|\\\\\\${)`,\n  identifier: /[a-zA-Z]\\w*/,\n  fullIdentifier: /@identifier(?:\\s*\\.\\s*@identifier)*/,\n  optionName: /(?:@identifier|\\(\\s*@fullIdentifier\\s*\\))(?:\\s*\\.\\s*@identifier)*/,\n  messageName: /@identifier/,\n  enumName: /@identifier/,\n  messageType: /\\.?\\s*(?:@identifier\\s*\\.\\s*)*@messageName/,\n  enumType: /\\.?\\s*(?:@identifier\\s*\\.\\s*)*@enumName/,\n  floatLit: /[0-9]+\\s*\\.\\s*[0-9]*(?:@exponent)?|[0-9]+@exponent|\\.[0-9]+(?:@exponent)?/,\n  exponent: /[eE]\\s*[+-]?\\s*[0-9]+/,\n  boolLit: /true\\b|false\\b/,\n  decimalLit: /[1-9][0-9]*/,\n  octalLit: /0[0-7]*/,\n  hexLit: /0[xX][0-9a-fA-F]+/,\n  type: /double|float|int32|int64|uint32|uint64|sint32|sint64|fixed32|fixed64|sfixed32|sfixed64|bool|string|bytes|@messageType|@enumType/,\n  keyType: /int32|int64|uint32|uint64|sint32|sint64|fixed32|fixed64|sfixed32|sfixed64|bool|string/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      [/syntax/, \"keyword\"],\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [\n        /(\")(proto3)(\")/,\n        [\"string.quote\", \"string\", { token: \"string.quote\", switchTo: \"@topLevel.proto3\" }]\n      ],\n      [\n        /(\")(proto2)(\")/,\n        [\"string.quote\", \"string\", { token: \"string.quote\", switchTo: \"@topLevel.proto2\" }]\n      ],\n      [\n        // If no `syntax` provided, regarded as proto2\n        /.*?/,\n        { token: \"\", switchTo: \"@topLevel.proto2\" }\n      ]\n    ],\n    topLevel: [\n      // whitespace\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/[;.]/, \"delimiter\"],\n      [\n        /@fullIdentifier/,\n        {\n          cases: {\n            option: { token: \"keyword\", next: \"@option.$S2\" },\n            enum: { token: \"keyword\", next: \"@enumDecl.$S2\" },\n            message: { token: \"keyword\", next: \"@messageDecl.$S2\" },\n            service: { token: \"keyword\", next: \"@serviceDecl.$S2\" },\n            extend: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@extendDecl.$S2\" }\n              }\n            },\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    enumDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@enumBody.$S2\" }]\n    ],\n    enumBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [/option\\b/, \"keyword\", \"@option.$S2\"],\n      [/@identifier/, \"identifier\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    messageDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@messageBody.$S2\" }]\n    ],\n    messageBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [\n        \"(map)(s*)(<)\",\n        [\"keyword\", \"white\", { token: \"@brackets\", bracket: \"@open\", next: \"@map.$S2\" }]\n      ],\n      [\n        /@identifier/,\n        {\n          cases: {\n            option: { token: \"keyword\", next: \"@option.$S2\" },\n            enum: { token: \"keyword\", next: \"@enumDecl.$S2\" },\n            message: { token: \"keyword\", next: \"@messageDecl.$S2\" },\n            oneof: { token: \"keyword\", next: \"@oneofDecl.$S2\" },\n            extensions: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@reserved.$S2\" }\n              }\n            },\n            reserved: { token: \"keyword\", next: \"@reserved.$S2\" },\n            \"(?:repeated|optional)\": { token: \"keyword\", next: \"@field.$S2\" },\n            required: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@field.$S2\" }\n              }\n            },\n            \"$S2==proto3\": { token: \"@rematch\", next: \"@field.$S2\" }\n          }\n        }\n      ],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    extendDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@extendBody.$S2\" }]\n    ],\n    extendBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/(?:repeated|optional|required)/, \"keyword\", \"@field.$S2\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    options: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\"],\n      [/\\]/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    option: [\n      { include: \"@whitespace\" },\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\", \"@pop\"]\n    ],\n    oneofDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@oneofBody.$S2\" }]\n    ],\n    oneofBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/(@identifier)(\\s*)(=)/, [\"identifier\", \"white\", \"delimiter\"]],\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    reserved: [\n      { include: \"@whitespace\" },\n      [/,/, \"delimiter\"],\n      [/;/, \"delimiter\", \"@pop\"],\n      { include: \"@constant\" },\n      [/to\\b|max\\b/, \"keyword\"]\n    ],\n    map: [\n      { include: \"@whitespace\" },\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/,/, \"delimiter\"],\n      [/>/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"identifier\" }]\n    ],\n    field: [\n      { include: \"@whitespace\" },\n      [\n        \"group\",\n        {\n          cases: {\n            \"$S2==proto2\": { token: \"keyword\", switchTo: \"@groupDecl.$S2\" }\n          }\n        }\n      ],\n      [/(@identifier)(\\s*)(=)/, [\"identifier\", \"white\", { token: \"delimiter\", next: \"@pop\" }]],\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ]\n    ],\n    groupDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [\"=\", \"operator\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@messageBody.$S2\" }],\n      { include: \"@constant\" }\n    ],\n    type: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\", \"@pop\"],\n      [/./, \"delimiter\"]\n    ],\n    identifier: [{ include: \"@whitespace\" }, [/@identifier/, \"identifier\", \"@pop\"]],\n    serviceDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@serviceBody.$S2\" }]\n    ],\n    serviceBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/option\\b/, \"keyword\", \"@option.$S2\"],\n      [/rpc\\b/, \"keyword\", \"@rpc.$S2\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    rpc: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/\\(/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@request.$S2\" }],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", next: \"@methodOptions.$S2\" }],\n      [/;/, \"delimiter\", \"@pop\"]\n    ],\n    request: [\n      { include: \"@whitespace\" },\n      [\n        /@messageType/,\n        {\n          cases: {\n            stream: { token: \"keyword\", next: \"@type.$S2\" },\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\)/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"@returns.$S2\" }]\n    ],\n    returns: [\n      { include: \"@whitespace\" },\n      [/returns\\b/, \"keyword\"],\n      [/\\(/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@response.$S2\" }]\n    ],\n    response: [\n      { include: \"@whitespace\" },\n      [\n        /@messageType/,\n        {\n          cases: {\n            stream: { token: \"keyword\", next: \"@type.$S2\" },\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\)/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"@rpc.$S2\" }]\n    ],\n    methodOptions: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [\"option\", \"keyword\"],\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\"],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    stringSingle: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    constant: [\n      [\"@boolLit\", \"keyword.constant\"],\n      [\"@hexLit\", \"number.hex\"],\n      [\"@octalLit\", \"number.octal\"],\n      [\"@decimalLit\", \"number\"],\n      [\"@floatLit\", \"number.float\"],\n      [/(\"([^\"\\\\]|\\\\.)*|'([^'\\\\]|\\\\.)*)$/, \"string.invalid\"],\n      // non-terminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@stringSingle\" }],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", next: \"@prototext\" }],\n      [/identifier/, \"identifier\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    prototext: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/@identifier/, \"identifier\"],\n      [/[:;]/, \"delimiter\"],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAgB,CAAC,OAAQ,QAAQ,CACjC,EAAO,CACT,SAAU,CACR,YAAa,KACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC5C,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC7C,CACD,gBAAiB;GACjB,iBAAkB,CAChB,sBAA2B,OAAO,6DAA6D,CAC/F,sBAA2B,OAAO,yCAAyC,CAC5E,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,SACd,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,mBAAoB,CACpD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CACzD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACpD,CACD,QAAS,sBACT,SAAU,CACR,SACA,SACA,OACA,SACA,UACA,SACA,WACA,QACA,MACA,WACA,KACA,MACA,OACA,UACA,UACA,MACA,SACA,UACA,UACA,WACA,OACA,QACD,CACD,aAAc,CACZ,SACA,QACA,QACA,QACA,SACA,SACA,SACA,SACA,UACA,UACA,WACA,WACA,OACA,SACA,QACD,CACD,UAAW,CAAC,IAAK,IAAK,IAAI,CAC1B,gBACA,QAAS,0CACT,WAAY,cACZ,eAAgB,sCAChB,WAAY,oEACZ,YAAa,cACb,SAAU,cACV,YAAa,6CACb,SAAU,0CACV,SAAU,4EACV,SAAU,wBACV,QAAS,iBACT,WAAY,cACZ,SAAU,UACV,OAAQ,oBACR,KAAM,kIACN,QAAS,wFACT,UAAW,CACT,KAAM,CACJ,CAAE,QAAS,cAAe,CAC1B,CAAC,SAAU,UAAU,CACrB,CAAC,IAAK,YAAY,CAClB,CAAC,IAAK,YAAY,CAClB,CACE,iBACA,CAAC,eAAgB,SAAU,CAAE,MAAO,eAAgB,SAAU,mBAAoB,CAAC,CACpF,CACD,CACE,iBACA,CAAC,eAAgB,SAAU,CAAE,MAAO,eAAgB,SAAU,mBAAoB,CAAC,CACpF,CACD,CAEE,MACA,CAAE,MAAO,GAAI,SAAU,mBAAoB,CAC5C,CACF,CACD,SAAU,CAER,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,YAAa,CACxB,CAAC,IAAK,YAAY,CAClB,CAAC,OAAQ,YAAY,CACrB,CACE,kBACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,UAAW,KAAM,cAAe,CACjD,KAAM,CAAE,MAAO,UAAW,KAAM,gBAAiB,CACjD,QAAS,CAAE,MAAO,UAAW,KAAM,mBAAoB,CACvD,QAAS,CAAE,MAAO,UAAW,KAAM,mBAAoB,CACvD,OAAQ,CACN,MAAO,CACL,cAAe,CAAE,MAAO,UAAW,KAAM,kBAAmB,CAC7D,CACF,CACD,YAAa,UACb,WAAY,aACb,CACF,CACF,CACF,CACD,SAAU,CACR,CAAE,QAAS,cAAe,CAC1B,CAAC,cAAe,kBAAkB,CAClC,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,QAAS,SAAU,gBAAiB,CAAC,CAC3E,CACD,SAAU,CACR,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,YAAa,CACxB,CAAC,IAAK,YAAY,CAClB,CAAC,IAAK,YAAY,CAClB,CAAC,WAAY,UAAW,cAAc,CACtC,CAAC,cAAe,aAAa,CAC7B,CAAC,KAAM,CAAE,MAAO,YAAa,QAAS,QAAS,KAAM,eAAgB,CAAC,CACtE,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,SAAU,KAAM,OAAQ,CAAC,CAC/D,CACD,YAAa,CACX,CAAE,QAAS,cAAe,CAC1B,CAAC,cAAe,kBAAkB,CAClC,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,QAAS,SAAU,mBAAoB,CAAC,CAC9E,CACD,YAAa,CACX,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,YAAa,CACxB,CAAC,IAAK,YAAY,CAClB,CAAC,IAAK,YAAY,CAClB,CACE,eACA,CAAC,UAAW,QAAS,CAAE,MAAO,YAAa,QAAS,QAAS,KAAM,WAAY,CAAC,CACjF,CACD,CACE,cACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,UAAW,KAAM,cAAe,CACjD,KAAM,CAAE,MAAO,UAAW,KAAM,gBAAiB,CACjD,QAAS,CAAE,MAAO,UAAW,KAAM,mBAAoB,CACvD,MAAO,CAAE,MAAO,UAAW,KAAM,iBAAkB,CACnD,WAAY,CACV,MAAO,CACL,cAAe,CAAE,MAAO,UAAW,KAAM,gBAAiB,CAC3D,CACF,CACD,SAAU,CAAE,MAAO,UAAW,KAAM,gBAAiB,CACrD,wBAAyB,CAAE,MAAO,UAAW,KAAM,aAAc,CACjE,SAAU,CACR,MAAO,CACL,cAAe,CAAE,MAAO,UAAW,KAAM,aAAc,CACxD,CACF,CACD,cAAe,CAAE,MAAO,WAAY,KAAM,aAAc,CACzD,CACF,CACF,CACD,CAAC,KAAM,CAAE,MAAO,YAAa,QAAS,QAAS,KAAM,eAAgB,CAAC,CACtE,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,SAAU,KAAM,OAAQ,CAAC,CAC/D,CACD,WAAY,CACV,CAAE,QAAS,cAAe,CAC1B,CAAC,cAAe,kBAAkB,CAClC,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,QAAS,SAAU,kBAAmB,CAAC,CAC7E,CACD,WAAY,CACV,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,YAAa,CACxB,CAAC,IAAK,YAAY,CAClB,CAAC,iCAAkC,UAAW,aAAa,CAC3D,CAAC,KAAM,CAAE,MAAO,YAAa,QAAS,QAAS,KAAM,eAAgB,CAAC,CACtE,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,SAAU,KAAM,OAAQ,CAAC,CAC/D,CACD,QAAS,CACP,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,YAAa,CACxB,CAAC,IAAK,YAAY,CAClB,CAAC,cAAe,aAAa,CAC7B,CAAC,OAAQ,sBAAsB,CAC/B,CAAC,IAAK,WAAW,CACjB,CAAC,KAAM,CAAE,MAAO,YAAa,QAAS,SAAU,KAAM,OAAQ,CAAC,CAChE,CACD,OAAQ,CACN,CAAE,QAAS,cAAe,CAC1B,CAAC,cAAe,aAAa,CAC7B,CAAC,OAAQ,sBAAsB,CAC/B,CAAC,IAAK,WAAY,OAAO,CAC1B,CACD,UAAW,CACT,CAAE,QAAS,cAAe,CAC1B,CAAC,cAAe,aAAa,CAC7B,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,QAAS,SAAU,iBAAkB,CAAC,CAC5E,CACD,UAAW,CACT,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,YAAa,CACxB,CAAC,IAAK,YAAY,CAClB,CAAC,wBAAyB,CAAC,aAAc,QAAS,YAAY,CAAC,CAC/D,CACE,qBACA,CACE,MAAO,CACL,gBAAiB,UACjB,WAAY,kBACb,CACF,CACF,CACD,CAAC,KAAM,CAAE,MAAO,YAAa,QAAS,QAAS,KAAM,eAAgB,CAAC,CACtE,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,SAAU,KAAM,OAAQ,CAAC,CAC/D,CACD,SAAU,CACR,CAAE,QAAS,cAAe,CAC1B,CAAC,IAAK,YAAY,CAClB,CAAC,IAAK,YAAa,OAAO,CAC1B,CAAE,QAAS,YAAa,CACxB,CAAC,aAAc,UAAU,CAC1B,CACD,IAAK,CACH,CAAE,QAAS,cAAe,CAC1B,CACE,qBACA,CACE,MAAO,CACL,gBAAiB,UACjB,WAAY,kBACb,CACF,CACF,CACD,CAAC,IAAK,YAAY,CAClB,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,SAAU,SAAU,aAAc,CAAC,CACzE,CACD,MAAO,CACL,CAAE,QAAS,cAAe,CAC1B,CACE,QACA,CACE,MAAO,CACL,cAAe,CAAE,MAAO,UAAW,SAAU,iBAAkB,CAChE,CACF,CACF,CACD,CAAC,wBAAyB,CAAC,aAAc,QAAS,CAAE,MAAO,YAAa,KAAM,OAAQ,CAAC,CAAC,CACxF,CACE,qBACA,CACE,MAAO,CACL,gBAAiB,UACjB,WAAY,kBACb,CACF,CACF,CACF,CACD,UAAW,CACT,CAAE,QAAS,cAAe,CAC1B,CAAC,cAAe,aAAa,CAC7B,CAAC,IAAK,WAAW,CACjB,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,QAAS,SAAU,mBAAoB,CAAC,CAC7E,CAAE,QAAS,YAAa,CACzB,CACD,KAAM,CACJ,CAAE,QAAS,cAAe,CAC1B,CAAC,cAAe,kBAAmB,OAAO,CAC1C,CAAC,IAAK,YAAY,CACnB,CACD,WAAY,CAAC,CAAE,QAAS,cAAe,CAAE,CAAC,cAAe,aAAc,OAAO,CAAC,CAC/E,YAAa,CACX,CAAE,QAAS,cAAe,CAC1B,CAAC,cAAe,aAAa,CAC7B,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,QAAS,SAAU,mBAAoB,CAAC,CAC9E,CACD,YAAa,CACX,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,YAAa,CACxB,CAAC,IAAK,YAAY,CAClB,CAAC,WAAY,UAAW,cAAc,CACtC,CAAC,QAAS,UAAW,WAAW,CAChC,CAAC,KAAM,CAAE,MAAO,YAAa,QAAS,QAAS,KAAM,eAAgB,CAAC,CACtE,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,SAAU,KAAM,OAAQ,CAAC,CAC/D,CACD,IAAK,CACH,CAAE,QAAS,cAAe,CAC1B,CAAC,cAAe,aAAa,CAC7B,CAAC,KAAM,CAAE,MAAO,YAAa,QAAS,QAAS,SAAU,eAAgB,CAAC,CAC1E,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,QAAS,KAAM,qBAAsB,CAAC,CAC3E,CAAC,IAAK,YAAa,OAAO,CAC3B,CACD,QAAS,CACP,CAAE,QAAS,cAAe,CAC1B,CACE,eACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,UAAW,KAAM,YAAa,CAC/C,WAAY,kBACb,CACF,CACF,CACD,CAAC,KAAM,CAAE,MAAO,YAAa,QAAS,SAAU,SAAU,eAAgB,CAAC,CAC5E,CACD,QAAS,CACP,CAAE,QAAS,cAAe,CAC1B,CAAC,YAAa,UAAU,CACxB,CAAC,KAAM,CAAE,MAAO,YAAa,QAAS,QAAS,SAAU,gBAAiB,CAAC,CAC5E,CACD,SAAU,CACR,CAAE,QAAS,cAAe,CAC1B,CACE,eACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,UAAW,KAAM,YAAa,CAC/C,WAAY,kBACb,CACF,CACF,CACD,CAAC,KAAM,CAAE,MAAO,YAAa,QAAS,SAAU,SAAU,WAAY,CAAC,CACxE,CACD,cAAe,CACb,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,YAAa,CACxB,CAAC,IAAK,YAAY,CAClB,CAAC,SAAU,UAAU,CACrB,CAAC,cAAe,aAAa,CAC7B,CAAC,OAAQ,sBAAsB,CAC/B,CAAC,IAAK,WAAW,CACjB,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,SAAU,KAAM,OAAQ,CAAC,CAC/D,CACD,QAAS,CACP,CAAC,UAAW,UAAU,CACtB,CAAC,OAAQ,UAAW,QAAQ,CAE5B,CAAC,OAAQ,UAAW,OAAO,CAC3B,CAAC,QAAS,UAAU,CACrB,CACD,OAAQ,CACN,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,CAAE,MAAO,eAAgB,QAAS,SAAU,KAAM,OAAQ,CAAC,CAClE,CACD,aAAc,CACZ,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,CAAE,MAAO,eAAgB,QAAS,SAAU,KAAM,OAAQ,CAAC,CAClE,CACD,SAAU,CACR,CAAC,WAAY,mBAAmB,CAChC,CAAC,UAAW,aAAa,CACzB,CAAC,YAAa,eAAe,CAC7B,CAAC,cAAe,SAAS,CACzB,CAAC,YAAa,eAAe,CAC7B,CAAC,mCAAoC,iBAAiB,CAEtD,CAAC,IAAK,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,UAAW,CAAC,CACnE,CAAC,IAAK,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,gBAAiB,CAAC,CACzE,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,QAAS,KAAM,aAAc,CAAC,CACnE,CAAC,aAAc,aAAa,CAC7B,CACD,WAAY,CACV,CAAC,aAAc,QAAQ,CACvB,CAAC,OAAQ,UAAW,WAAW,CAC/B,CAAC,UAAW,UAAU,CACvB,CACD,UAAW,CACT,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,YAAa,CACxB,CAAC,cAAe,aAAa,CAC7B,CAAC,OAAQ,YAAY,CACrB,CAAC,IAAK,CAAE,MAAO,YAAa,QAAS,SAAU,KAAM,OAAQ,CAAC,CAC/D,CACF,CACF"}