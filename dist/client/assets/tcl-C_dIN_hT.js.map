{"version": 3, "file": "tcl-C_dIN_hT.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/tcl/tcl.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/tcl/tcl.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  tokenPostfix: \".tcl\",\n  specialFunctions: [\n    \"set\",\n    \"unset\",\n    \"rename\",\n    \"variable\",\n    \"proc\",\n    \"coroutine\",\n    \"foreach\",\n    \"incr\",\n    \"append\",\n    \"lappend\",\n    \"linsert\",\n    \"lreplace\"\n  ],\n  mainFunctions: [\n    \"if\",\n    \"then\",\n    \"elseif\",\n    \"else\",\n    \"case\",\n    \"switch\",\n    \"while\",\n    \"for\",\n    \"break\",\n    \"continue\",\n    \"return\",\n    \"package\",\n    \"namespace\",\n    \"catch\",\n    \"exit\",\n    \"eval\",\n    \"expr\",\n    \"uplevel\",\n    \"upvar\"\n  ],\n  builtinFunctions: [\n    \"file\",\n    \"info\",\n    \"concat\",\n    \"join\",\n    \"lindex\",\n    \"list\",\n    \"llength\",\n    \"lrange\",\n    \"lsearch\",\n    \"lsort\",\n    \"split\",\n    \"array\",\n    \"parray\",\n    \"binary\",\n    \"format\",\n    \"regexp\",\n    \"regsub\",\n    \"scan\",\n    \"string\",\n    \"subst\",\n    \"dict\",\n    \"cd\",\n    \"clock\",\n    \"exec\",\n    \"glob\",\n    \"pid\",\n    \"pwd\",\n    \"close\",\n    \"eof\",\n    \"fblocked\",\n    \"fconfigure\",\n    \"fcopy\",\n    \"fileevent\",\n    \"flush\",\n    \"gets\",\n    \"open\",\n    \"puts\",\n    \"read\",\n    \"seek\",\n    \"socket\",\n    \"tell\",\n    \"interp\",\n    \"after\",\n    \"auto_execok\",\n    \"auto_load\",\n    \"auto_mkindex\",\n    \"auto_reset\",\n    \"bgerror\",\n    \"error\",\n    \"global\",\n    \"history\",\n    \"load\",\n    \"source\",\n    \"time\",\n    \"trace\",\n    \"unknown\",\n    \"unset\",\n    \"update\",\n    \"vwait\",\n    \"winfo\",\n    \"wm\",\n    \"bind\",\n    \"event\",\n    \"pack\",\n    \"place\",\n    \"grid\",\n    \"font\",\n    \"bell\",\n    \"clipboard\",\n    \"destroy\",\n    \"focus\",\n    \"grab\",\n    \"lower\",\n    \"option\",\n    \"raise\",\n    \"selection\",\n    \"send\",\n    \"tk\",\n    \"tkwait\",\n    \"tk_bisque\",\n    \"tk_focusNext\",\n    \"tk_focusPrev\",\n    \"tk_focusFollowsMouse\",\n    \"tk_popup\",\n    \"tk_setPalette\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  escapes: /\\\\(?:[abfnrtv\\\\\"'\\[\\]\\{\\};\\$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  variables: /(?:\\$+(?:(?:\\:\\:?)?[a-zA-Z_]\\w*)+)/,\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@specialFunctions\": {\n              token: \"keyword.flow\",\n              next: \"@specialFunc\"\n            },\n            \"@mainFunctions\": \"keyword\",\n            \"@builtinFunctions\": \"variable\",\n            \"@default\": \"operator.scss\"\n          }\n        }\n      ],\n      [/\\s+\\-+(?!\\d|\\.)\\w*|{\\*}/, \"metatag\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"operator\"],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/\\.(?!\\d|\\.)[\\w\\-]*/, \"operator.sql\"],\n      // numbers\n      [/\\d+(\\.\\d+)?/, \"number\"],\n      [/\\d+/, \"number\"],\n      // delimiter\n      [/;/, \"delimiter\"],\n      // strings\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@dstring\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstring\" }]\n    ],\n    dstring: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/[^\\\\$\\[\\]\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    sstring: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/[^\\\\$\\[\\]']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/#.*\\\\$/, { token: \"comment\", next: \"@newlineComment\" }],\n      [/#.*(?!\\\\)$/, \"comment\"]\n    ],\n    newlineComment: [\n      [/.*\\\\$/, \"comment\"],\n      [/.*(?!\\\\)$/, { token: \"comment\", next: \"@pop\" }]\n    ],\n    nestedVariable: [\n      [/[^\\{\\}\\$]+/, \"type.identifier\"],\n      [/\\}/, { token: \"identifier\", next: \"@pop\" }]\n    ],\n    nestedCall: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\]/, { token: \"@brackets\", next: \"@pop\" }],\n      { include: \"root\" }\n    ],\n    specialFunc: [\n      [/\"/, { token: \"string\", next: \"@dstring\" }],\n      [/'/, { token: \"string\", next: \"@sstring\" }],\n      [/\\S+/, { token: \"type\", next: \"@pop\" }]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACF,CACG,EAAW,CACb,aAAc,OACd,iBAAkB,CAChB,MACA,QACA,SACA,WACA,OACA,YACA,UACA,OACA,SACA,UACA,UACA,WACD,CACD,cAAe,CACb,KACA,OACA,SACA,OACA,OACA,SACA,QACA,MACA,QACA,WACA,SACA,UACA,YACA,QACA,OACA,OACA,OACA,UACA,QACD,CACD,iBAAkB,slBAsFjB,CACD,QAAS,wBACT,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CACzD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,mBAAoB,CACrD,CACD,QAAS,mFACT,UAAW,qCACX,UAAW,CACT,KAAM,CAEJ,CACE,eACA,CACE,MAAO,CACL,oBAAqB,CACnB,MAAO,eACP,KAAM,eACP,CACD,iBAAkB,UAClB,oBAAqB,WACrB,WAAY,gBACb,CACF,CACF,CACD,CAAC,0BAA2B,UAAU,CAEtC,CAAE,QAAS,cAAe,CAE1B,CAAC,aAAc,YAAY,CAC3B,CAAC,WAAY,WAAW,CACxB,CAAC,iBAAkB,CAAE,MAAO,aAAc,KAAM,kBAAmB,CAAC,CACpE,CAAC,aAAc,kBAAkB,CACjC,CAAC,qBAAsB,eAAe,CAEtC,CAAC,cAAe,SAAS,CACzB,CAAC,MAAO,SAAS,CAEjB,CAAC,IAAK,YAAY,CAElB,CAAC,IAAK,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,WAAY,CAAC,CACpE,CAAC,IAAK,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,WAAY,CAAC,CACrE,CACD,QAAS,CACP,CAAC,KAAM,CAAE,MAAO,YAAa,KAAM,cAAe,CAAC,CACnD,CAAC,iBAAkB,CAAE,MAAO,aAAc,KAAM,kBAAmB,CAAC,CACpE,CAAC,aAAc,kBAAkB,CACjC,CAAC,eAAgB,SAAS,CAC1B,CAAC,WAAY,gBAAgB,CAC7B,CAAC,IAAK,CAAE,MAAO,eAAgB,QAAS,SAAU,KAAM,OAAQ,CAAC,CAClE,CACD,QAAS,CACP,CAAC,KAAM,CAAE,MAAO,YAAa,KAAM,cAAe,CAAC,CACnD,CAAC,iBAAkB,CAAE,MAAO,aAAc,KAAM,kBAAmB,CAAC,CACpE,CAAC,aAAc,kBAAkB,CACjC,CAAC,eAAgB,SAAS,CAC1B,CAAC,WAAY,gBAAgB,CAC7B,CAAC,IAAK,CAAE,MAAO,eAAgB,QAAS,SAAU,KAAM,OAAQ,CAAC,CAClE,CACD,WAAY,CACV,CAAC,aAAc,QAAQ,CACvB,CAAC,SAAU,CAAE,MAAO,UAAW,KAAM,kBAAmB,CAAC,CACzD,CAAC,aAAc,UAAU,CAC1B,CACD,eAAgB,CACd,CAAC,QAAS,UAAU,CACpB,CAAC,YAAa,CAAE,MAAO,UAAW,KAAM,OAAQ,CAAC,CAClD,CACD,eAAgB,CACd,CAAC,aAAc,kBAAkB,CACjC,CAAC,KAAM,CAAE,MAAO,aAAc,KAAM,OAAQ,CAAC,CAC9C,CACD,WAAY,CACV,CAAC,KAAM,CAAE,MAAO,YAAa,KAAM,cAAe,CAAC,CACnD,CAAC,KAAM,CAAE,MAAO,YAAa,KAAM,OAAQ,CAAC,CAC5C,CAAE,QAAS,OAAQ,CACpB,CACD,YAAa,CACX,CAAC,IAAK,CAAE,MAAO,SAAU,KAAM,WAAY,CAAC,CAC5C,CAAC,IAAK,CAAE,MAAO,SAAU,KAAM,WAAY,CAAC,CAC5C,CAAC,MAAO,CAAE,MAAO,OAAQ,KAAM,OAAQ,CAAC,CACzC,CACF,CACF"}