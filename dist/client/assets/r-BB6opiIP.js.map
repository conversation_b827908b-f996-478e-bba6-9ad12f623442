{"version": 3, "file": "r-BB6opiIP.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/r/r.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/r/r.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".r\",\n  roxygen: [\n    \"@alias\",\n    \"@aliases\",\n    \"@assignee\",\n    \"@author\",\n    \"@backref\",\n    \"@callGraph\",\n    \"@callGraphDepth\",\n    \"@callGraphPrimitives\",\n    \"@concept\",\n    \"@describeIn\",\n    \"@description\",\n    \"@details\",\n    \"@docType\",\n    \"@encoding\",\n    \"@evalNamespace\",\n    \"@evalRd\",\n    \"@example\",\n    \"@examples\",\n    \"@export\",\n    \"@exportClass\",\n    \"@exportMethod\",\n    \"@exportPattern\",\n    \"@family\",\n    \"@field\",\n    \"@formals\",\n    \"@format\",\n    \"@import\",\n    \"@importClassesFrom\",\n    \"@importFrom\",\n    \"@importMethodsFrom\",\n    \"@include\",\n    \"@inherit\",\n    \"@inheritDotParams\",\n    \"@inheritParams\",\n    \"@inheritSection\",\n    \"@keywords\",\n    \"@md\",\n    \"@method\",\n    \"@name\",\n    \"@noMd\",\n    \"@noRd\",\n    \"@note\",\n    \"@param\",\n    \"@rawNamespace\",\n    \"@rawRd\",\n    \"@rdname\",\n    \"@references\",\n    \"@return\",\n    \"@S3method\",\n    \"@section\",\n    \"@seealso\",\n    \"@setClass\",\n    \"@slot\",\n    \"@source\",\n    \"@template\",\n    \"@templateVar\",\n    \"@title\",\n    \"@TODO\",\n    \"@usage\",\n    \"@useDynLib\"\n  ],\n  constants: [\n    \"NULL\",\n    \"FALSE\",\n    \"TRUE\",\n    \"NA\",\n    \"Inf\",\n    \"NaN\",\n    \"NA_integer_\",\n    \"NA_real_\",\n    \"NA_complex_\",\n    \"NA_character_\",\n    \"T\",\n    \"F\",\n    \"LETTERS\",\n    \"letters\",\n    \"month.abb\",\n    \"month.name\",\n    \"pi\",\n    \"R.version.string\"\n  ],\n  keywords: [\n    \"break\",\n    \"next\",\n    \"return\",\n    \"if\",\n    \"else\",\n    \"for\",\n    \"in\",\n    \"repeat\",\n    \"while\",\n    \"array\",\n    \"category\",\n    \"character\",\n    \"complex\",\n    \"double\",\n    \"function\",\n    \"integer\",\n    \"list\",\n    \"logical\",\n    \"matrix\",\n    \"numeric\",\n    \"vector\",\n    \"data.frame\",\n    \"factor\",\n    \"library\",\n    \"require\",\n    \"attach\",\n    \"detach\",\n    \"source\"\n  ],\n  special: [\"\\\\n\", \"\\\\r\", \"\\\\t\", \"\\\\b\", \"\\\\a\", \"\\\\f\", \"\\\\v\", \"\\\\'\", '\\\\\"', \"\\\\\\\\\"],\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      { include: \"@operators\" },\n      [/#'$/, \"comment.doc\"],\n      [/#'/, \"comment.doc\", \"@roxygen\"],\n      [/(^#.*$)/, \"comment\"],\n      [/\\s+/, \"white\"],\n      [/[,:;]/, \"delimiter\"],\n      [/@[a-zA-Z]\\w*/, \"tag\"],\n      [\n        /[a-zA-Z]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@constants\": \"constant\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    // Recognize Roxygen comments\n    roxygen: [\n      [\n        /@\\w+/,\n        {\n          cases: {\n            \"@roxygen\": \"tag\",\n            \"@eos\": { token: \"comment.doc\", next: \"@pop\" },\n            \"@default\": \"comment.doc\"\n          }\n        }\n      ],\n      [\n        /\\s+/,\n        {\n          cases: {\n            \"@eos\": { token: \"comment.doc\", next: \"@pop\" },\n            \"@default\": \"comment.doc\"\n          }\n        }\n      ],\n      [/.*/, { token: \"comment.doc\", next: \"@pop\" }]\n    ],\n    // Recognize positives, negatives, decimals, imaginaries, and scientific notation\n    numbers: [\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/-?(\\d*\\.)?\\d+([eE][+\\-]?\\d+)?/, \"number\"]\n    ],\n    // Recognize operators\n    operators: [\n      [/<{1,2}-/, \"operator\"],\n      [/->{1,2}/, \"operator\"],\n      [/%[^%\\s]+%/, \"operator\"],\n      [/\\*\\*/, \"operator\"],\n      [/%%/, \"operator\"],\n      [/&&/, \"operator\"],\n      [/\\|\\|/, \"operator\"],\n      [/<</, \"operator\"],\n      [/>>/, \"operator\"],\n      [/[-+=&|!<>^~*/:$]/, \"operator\"]\n    ],\n    // Recognize strings, including those broken across lines\n    strings: [\n      [/'/, \"string.escape\", \"@stringBody\"],\n      [/\"/, \"string.escape\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@special\": \"string\",\n            \"@default\": \"error-token\"\n          }\n        }\n      ],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/./, \"string\"]\n    ],\n    dblStringBody: [\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@special\": \"string\",\n            \"@default\": \"error-token\"\n          }\n        }\n      ],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/./, \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,IACd,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,KACd,QAAS,8mBA6DR,CACD,UAAW,CACT,OACA,QACA,OACA,KACA,MACA,MACA,cACA,WACA,cACA,gBACA,IACA,IACA,UACA,UACA,YACA,aACA,KACA,mBACD,CACD,SAAU,6MA6BT,CACD,QAAS,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAAO,CAChF,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,oBAAqB,CACrD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CAC1D,CACD,UAAW,CACT,KAAM,CACJ,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,WAAY,CACvB,CAAC,aAAc,YAAY,CAC3B,CAAE,QAAS,aAAc,CACzB,CAAC,MAAO,cAAc,CACtB,CAAC,KAAM,cAAe,WAAW,CACjC,CAAC,UAAW,UAAU,CACtB,CAAC,MAAO,QAAQ,CAChB,CAAC,QAAS,YAAY,CACtB,CAAC,eAAgB,MAAM,CACvB,CACE,cACA,CACE,MAAO,CACL,YAAa,UACb,aAAc,WACd,WAAY,aACb,CACF,CACF,CACF,CAED,QAAS,CACP,CACE,OACA,CACE,MAAO,CACL,WAAY,MACZ,OAAQ,CAAE,MAAO,cAAe,KAAM,OAAQ,CAC9C,WAAY,cACb,CACF,CACF,CACD,CACE,MACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,cAAe,KAAM,OAAQ,CAC9C,WAAY,cACb,CACF,CACF,CACD,CAAC,KAAM,CAAE,MAAO,cAAe,KAAM,OAAQ,CAAC,CAC/C,CAED,QAAS,CACP,CAAC,oBAAqB,aAAa,CACnC,CAAC,gCAAiC,SAAS,CAC5C,CAED,UAAW,CACT,CAAC,UAAW,WAAW,CACvB,CAAC,UAAW,WAAW,CACvB,CAAC,YAAa,WAAW,CACzB,CAAC,OAAQ,WAAW,CACpB,CAAC,KAAM,WAAW,CAClB,CAAC,KAAM,WAAW,CAClB,CAAC,OAAQ,WAAW,CACpB,CAAC,KAAM,WAAW,CAClB,CAAC,KAAM,WAAW,CAClB,CAAC,mBAAoB,WAAW,CACjC,CAED,QAAS,CACP,CAAC,IAAK,gBAAiB,cAAc,CACrC,CAAC,IAAK,gBAAiB,iBAAiB,CACzC,CACD,WAAY,CACV,CACE,MACA,CACE,MAAO,CACL,WAAY,SACZ,WAAY,cACb,CACF,CACF,CACD,CAAC,IAAK,gBAAiB,UAAU,CACjC,CAAC,IAAK,SAAS,CAChB,CACD,cAAe,CACb,CACE,MACA,CACE,MAAO,CACL,WAAY,SACZ,WAAY,cACb,CACF,CACF,CACD,CAAC,IAAK,gBAAiB,UAAU,CACjC,CAAC,IAAK,SAAS,CAChB,CACF,CACF"}