{"version": 3, "file": "scheme-DQn1OzJY.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/scheme/scheme.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scheme/scheme.ts\nvar conf = {\n  comments: {\n    lineComment: \";\",\n    blockComment: [\"#|\", \"|#\"]\n  },\n  brackets: [\n    [\"(\", \")\"],\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: true,\n  tokenPostfix: \".scheme\",\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  keywords: [\n    \"case\",\n    \"do\",\n    \"let\",\n    \"loop\",\n    \"if\",\n    \"else\",\n    \"when\",\n    \"cons\",\n    \"car\",\n    \"cdr\",\n    \"cond\",\n    \"lambda\",\n    \"lambda*\",\n    \"syntax-rules\",\n    \"format\",\n    \"set!\",\n    \"quote\",\n    \"eval\",\n    \"append\",\n    \"list\",\n    \"list?\",\n    \"member?\",\n    \"load\"\n  ],\n  constants: [\"#t\", \"#f\"],\n  operators: [\"eq?\", \"eqv?\", \"equal?\", \"and\", \"or\", \"not\", \"null?\"],\n  tokenizer: {\n    root: [\n      [/#[xXoObB][0-9a-fA-F]+/, \"number.hex\"],\n      [/[+-]?\\d+(?:(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)?/, \"number.float\"],\n      [\n        /(?:\\b(?:(define|define-syntax|define-macro))\\b)(\\s+)((?:\\w|\\-|\\!|\\?)*)/,\n        [\"keyword\", \"white\", \"variable\"]\n      ],\n      { include: \"@whitespace\" },\n      { include: \"@strings\" },\n      [\n        /[a-zA-Z_#][a-zA-Z0-9_\\-\\?\\!\\*]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@constants\": \"constant\",\n            \"@operators\": \"operators\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    comment: [\n      [/[^\\|#]+/, \"comment\"],\n      [/#\\|/, \"comment\", \"@push\"],\n      [/\\|#/, \"comment\", \"@pop\"],\n      [/[\\|#]/, \"comment\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/#\\|/, \"comment\", \"@comment\"],\n      [/;.*$/, \"comment\"]\n    ],\n    strings: [\n      [/\"$/, \"string\", \"@popall\"],\n      [/\"(?=.)/, \"string\", \"@multiLineString\"]\n    ],\n    multiLineString: [\n      [/[^\\\\\"]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/\\\\./, \"string.escape\"],\n      [/\"/, \"string\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,IACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACF,CACG,EAAW,CACb,aAAc,GACd,WAAY,GACZ,aAAc,UACd,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CACzD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,mBAAoB,CACrD,CACD,SAAU,CACR,OACA,KACA,MACA,OACA,KACA,OACA,OACA,OACA,MACA,MACA,OACA,SACA,UACA,eACA,SACA,OACA,QACA,OACA,SACA,OACA,QACA,UACA,OACD,CACD,UAAW,CAAC,KAAM,KAAK,CACvB,UAAW,CAAC,MAAO,OAAQ,SAAU,MAAO,KAAM,MAAO,QAAQ,CACjE,UAAW,CACT,KAAM,CACJ,CAAC,wBAAyB,aAAa,CACvC,CAAC,2CAA4C,eAAe,CAC5D,CACE,yEACA,CAAC,UAAW,QAAS,WAAW,CACjC,CACD,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,WAAY,CACvB,CACE,kCACA,CACE,MAAO,CACL,YAAa,UACb,aAAc,WACd,aAAc,YACd,WAAY,aACb,CACF,CACF,CACF,CACD,QAAS,CACP,CAAC,UAAW,UAAU,CACtB,CAAC,MAAO,UAAW,QAAQ,CAC3B,CAAC,MAAO,UAAW,OAAO,CAC1B,CAAC,QAAS,UAAU,CACrB,CACD,WAAY,CACV,CAAC,aAAc,QAAQ,CACvB,CAAC,MAAO,UAAW,WAAW,CAC9B,CAAC,OAAQ,UAAU,CACpB,CACD,QAAS,CACP,CAAC,KAAM,SAAU,UAAU,CAC3B,CAAC,SAAU,SAAU,mBAAmB,CACzC,CACD,gBAAiB,CACf,CAAC,WAAY,SAAU,UAAU,CACjC,CAAC,UAAW,SAAS,CACrB,CAAC,MAAO,gBAAgB,CACxB,CAAC,IAAK,SAAU,UAAU,CAC1B,CAAC,MAAO,SAAS,CAClB,CACF,CACF"}