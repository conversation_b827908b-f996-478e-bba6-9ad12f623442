{"version": 3, "file": "yaml-DM0-7T5K.js", "names": ["monaco_editor_core_star"], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/yaml/yaml.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/yaml/yaml.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    offSide: true\n  },\n  onEnterRules: [\n    {\n      beforeText: /:\\s*$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.Indent\n      }\n    }\n  ]\n};\nvar language = {\n  tokenPostfix: \".yaml\",\n  brackets: [\n    { token: \"delimiter.bracket\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\"true\", \"True\", \"TRUE\", \"false\", \"False\", \"FALSE\", \"null\", \"Null\", \"Null\", \"~\"],\n  numberInteger: /(?:0|[+-]?[0-9]+)/,\n  numberFloat: /(?:0|[+-]?[0-9]+)(?:\\.[0-9]+)?(?:e[-+][1-9][0-9]*)?/,\n  numberOctal: /0o[0-7]+/,\n  numberHex: /0x[0-9a-fA-F]+/,\n  numberInfinity: /[+-]?\\.(?:inf|Inf|INF)/,\n  numberNaN: /\\.(?:nan|Nan|NAN)/,\n  numberDate: /\\d{4}-\\d\\d-\\d\\d([Tt ]\\d\\d:\\d\\d:\\d\\d(\\.\\d+)?(( ?[+-]\\d\\d?(:\\d\\d)?)|Z)?)?/,\n  escapes: /\\\\(?:[btnfr\\\\\"']|[0-7][0-7]?|[0-3][0-7]{2})/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      // Directive\n      [/%[^ ]+.*$/, \"meta.directive\"],\n      // Document Markers\n      [/---/, \"operators.directivesEnd\"],\n      [/\\.{3}/, \"operators.documentEnd\"],\n      // Block Structure Indicators\n      [/[-?:](?= )/, \"operators\"],\n      { include: \"@anchor\" },\n      { include: \"@tagHandle\" },\n      { include: \"@flowCollections\" },\n      { include: \"@blockStyle\" },\n      // Numbers\n      [/@numberInteger(?![ \\t]*\\S+)/, \"number\"],\n      [/@numberFloat(?![ \\t]*\\S+)/, \"number.float\"],\n      [/@numberOctal(?![ \\t]*\\S+)/, \"number.octal\"],\n      [/@numberHex(?![ \\t]*\\S+)/, \"number.hex\"],\n      [/@numberInfinity(?![ \\t]*\\S+)/, \"number.infinity\"],\n      [/@numberNaN(?![ \\t]*\\S+)/, \"number.nan\"],\n      [/@numberDate(?![ \\t]*\\S+)/, \"number.date\"],\n      // Key:Value pair\n      [/(\".*?\"|'.*?'|[^#'\"]*?)([ \\t]*)(:)( |$)/, [\"type\", \"white\", \"operators\", \"white\"]],\n      { include: \"@flowScalars\" },\n      // String nodes\n      [\n        /.+?(?=(\\s+#|$))/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // Flow Collection: Flow Mapping\n    object: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      // Flow Mapping termination\n      [/\\}/, \"@brackets\", \"@pop\"],\n      // Flow Mapping delimiter\n      [/,/, \"delimiter.comma\"],\n      // Flow Mapping Key:Value delimiter\n      [/:(?= )/, \"operators\"],\n      // Flow Mapping Key:Value key\n      [/(?:\".*?\"|'.*?'|[^,\\{\\[]+?)(?=: )/, \"type\"],\n      // Start Flow Style\n      { include: \"@flowCollections\" },\n      { include: \"@flowScalars\" },\n      // Scalar Data types\n      { include: \"@tagHandle\" },\n      { include: \"@anchor\" },\n      { include: \"@flowNumber\" },\n      // Other value (keyword or string)\n      [\n        /[^\\},]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // Flow Collection: Flow Sequence\n    array: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      // Flow Sequence termination\n      [/\\]/, \"@brackets\", \"@pop\"],\n      // Flow Sequence delimiter\n      [/,/, \"delimiter.comma\"],\n      // Start Flow Style\n      { include: \"@flowCollections\" },\n      { include: \"@flowScalars\" },\n      // Scalar Data types\n      { include: \"@tagHandle\" },\n      { include: \"@anchor\" },\n      { include: \"@flowNumber\" },\n      // Other value (keyword or string)\n      [\n        /[^\\],]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // First line of a Block Style\n    multiString: [[/^( +).+$/, \"string\", \"@multiStringContinued.$1\"]],\n    // Further lines of a Block Style\n    //   Workaround for indentation detection\n    multiStringContinued: [\n      [\n        /^( *).+$/,\n        {\n          cases: {\n            \"$1==$S2\": \"string\",\n            \"@default\": { token: \"@rematch\", next: \"@popall\" }\n          }\n        }\n      ]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"white\"]],\n    // Only line comments\n    comment: [[/#.*$/, \"comment\"]],\n    // Start Flow Collections\n    flowCollections: [\n      [/\\[/, \"@brackets\", \"@array\"],\n      [/\\{/, \"@brackets\", \"@object\"]\n    ],\n    // Start Flow Scalars (quoted strings)\n    flowScalars: [\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/'[^']*'/, \"string\"],\n      [/\"/, \"string\", \"@doubleQuotedString\"]\n    ],\n    doubleQuotedString: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    // Start Block Scalar\n    blockStyle: [[/[>|][0-9]*[+-]?$/, \"operators\", \"@multiString\"]],\n    // Numbers in Flow Collections (terminate with ,]})\n    flowNumber: [\n      [/@numberInteger(?=[ \\t]*[,\\]\\}])/, \"number\"],\n      [/@numberFloat(?=[ \\t]*[,\\]\\}])/, \"number.float\"],\n      [/@numberOctal(?=[ \\t]*[,\\]\\}])/, \"number.octal\"],\n      [/@numberHex(?=[ \\t]*[,\\]\\}])/, \"number.hex\"],\n      [/@numberInfinity(?=[ \\t]*[,\\]\\}])/, \"number.infinity\"],\n      [/@numberNaN(?=[ \\t]*[,\\]\\}])/, \"number.nan\"],\n      [/@numberDate(?=[ \\t]*[,\\]\\}])/, \"number.date\"]\n    ],\n    tagHandle: [[/\\![^ ]*/, \"tag\"]],\n    anchor: [[/[&*][^ ]+/, \"namespace\"]]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;;AAOA,IAAI,EAAY,OAAO,eACnB,EAAmB,OAAO,yBAC1B,EAAoB,OAAO,oBAC3B,EAAe,OAAO,UAAU,eAChC,GAAe,EAAI,EAAM,EAAQ,IAAS,CAC5C,GAAI,GAAQ,OAAO,GAAS,UAAY,OAAO,GAAS,eACjD,IAAI,KAAO,EAAkB,EAAK,CACjC,CAAC,EAAa,KAAK,EAAI,EAAI,EAAI,IAAQ,GACzC,EAAU,EAAI,EAAK,CAAE,QAAW,EAAK,GAAM,WAAY,EAAE,EAAO,EAAiB,EAAM,EAAI,GAAK,EAAK,WAAY,CAAC,CAExH,OAAO,GAEL,GAAc,EAAQ,EAAK,KAAkB,EAAY,EAAQ,EAAK,UAAU,CAAE,GAAgB,EAAY,EAAc,EAAK,UAAU,EAG3I,EAA6B,EAAE,CACnC,EAAW,EAA4BA,EAAwB,CAI/D,IAAI,EAAO,CACT,SAAU,CACR,YAAa,IACd,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,GACV,CACD,aAAc,CACZ,CACE,WAAY,QACZ,OAAQ,CACN,aAAc,EAA2B,UAAU,aAAa,OACjE,CACF,CACF,CACF,CACG,EAAW,CACb,aAAc,QACd,SAAU,CACR,CAAE,MAAO,oBAAqB,KAAM,IAAK,MAAO,IAAK,CACrD,CAAE,MAAO,mBAAoB,KAAM,IAAK,MAAO,IAAK,CACrD,CACD,SAAU,CAAC,OAAQ,OAAQ,OAAQ,QAAS,QAAS,QAAS,OAAQ,OAAQ,OAAQ,IAAI,CAC1F,cAAe,oBACf,YAAa,sDACb,YAAa,WACb,UAAW,iBACX,eAAgB,yBAChB,UAAW,oBACX,WAAY,0EACZ,QAAS,8CACT,UAAW,CACT,KAAM,CACJ,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,WAAY,CAEvB,CAAC,YAAa,iBAAiB,CAE/B,CAAC,MAAO,0BAA0B,CAClC,CAAC,QAAS,wBAAwB,CAElC,CAAC,aAAc,YAAY,CAC3B,CAAE,QAAS,UAAW,CACtB,CAAE,QAAS,aAAc,CACzB,CAAE,QAAS,mBAAoB,CAC/B,CAAE,QAAS,cAAe,CAE1B,CAAC,8BAA+B,SAAS,CACzC,CAAC,4BAA6B,eAAe,CAC7C,CAAC,4BAA6B,eAAe,CAC7C,CAAC,0BAA2B,aAAa,CACzC,CAAC,+BAAgC,kBAAkB,CACnD,CAAC,0BAA2B,aAAa,CACzC,CAAC,2BAA4B,cAAc,CAE3C,CAAC,yCAA0C,CAAC,OAAQ,QAAS,YAAa,QAAQ,CAAC,CACnF,CAAE,QAAS,eAAgB,CAE3B,CACE,kBACA,CACE,MAAO,CACL,YAAa,UACb,WAAY,SACb,CACF,CACF,CACF,CAED,OAAQ,CACN,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,WAAY,CAEvB,CAAC,KAAM,YAAa,OAAO,CAE3B,CAAC,IAAK,kBAAkB,CAExB,CAAC,SAAU,YAAY,CAEvB,CAAC,mCAAoC,OAAO,CAE5C,CAAE,QAAS,mBAAoB,CAC/B,CAAE,QAAS,eAAgB,CAE3B,CAAE,QAAS,aAAc,CACzB,CAAE,QAAS,UAAW,CACtB,CAAE,QAAS,cAAe,CAE1B,CACE,UACA,CACE,MAAO,CACL,YAAa,UACb,WAAY,SACb,CACF,CACF,CACF,CAED,MAAO,CACL,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,WAAY,CAEvB,CAAC,KAAM,YAAa,OAAO,CAE3B,CAAC,IAAK,kBAAkB,CAExB,CAAE,QAAS,mBAAoB,CAC/B,CAAE,QAAS,eAAgB,CAE3B,CAAE,QAAS,aAAc,CACzB,CAAE,QAAS,UAAW,CACtB,CAAE,QAAS,cAAe,CAE1B,CACE,UACA,CACE,MAAO,CACL,YAAa,UACb,WAAY,SACb,CACF,CACF,CACF,CAED,YAAa,CAAC,CAAC,WAAY,SAAU,2BAA2B,CAAC,CAGjE,qBAAsB,CACpB,CACE,WACA,CACE,MAAO,CACL,UAAW,SACX,WAAY,CAAE,MAAO,WAAY,KAAM,UAAW,CACnD,CACF,CACF,CACF,CACD,WAAY,CAAC,CAAC,aAAc,QAAQ,CAAC,CAErC,QAAS,CAAC,CAAC,OAAQ,UAAU,CAAC,CAE9B,gBAAiB,CACf,CAAC,KAAM,YAAa,SAAS,CAC7B,CAAC,KAAM,YAAa,UAAU,CAC/B,CAED,YAAa,CACX,CAAC,kBAAmB,iBAAiB,CACrC,CAAC,kBAAmB,iBAAiB,CACrC,CAAC,UAAW,SAAS,CACrB,CAAC,IAAK,SAAU,sBAAsB,CACvC,CACD,mBAAoB,CAClB,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,SAAU,OAAO,CACxB,CAED,WAAY,CAAC,CAAC,mBAAoB,YAAa,eAAe,CAAC,CAE/D,WAAY,CACV,CAAC,kCAAmC,SAAS,CAC7C,CAAC,gCAAiC,eAAe,CACjD,CAAC,gCAAiC,eAAe,CACjD,CAAC,8BAA+B,aAAa,CAC7C,CAAC,mCAAoC,kBAAkB,CACvD,CAAC,8BAA+B,aAAa,CAC7C,CAAC,+BAAgC,cAAc,CAChD,CACD,UAAW,CAAC,CAAC,UAAW,MAAM,CAAC,CAC/B,OAAQ,CAAC,CAAC,YAAa,YAAY,CAAC,CACrC,CACF"}