{"version": 3, "file": "css-DrbxvN-C.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/css/css.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/css/css.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|((::|[@#.!:])?[\\w-?]+%?)|::|[@#.!:]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".css\",\n  ws: \"[ \t\\n\\r\\f]*\",\n  // whitespaces (referenced in several rules)\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.bracket\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [{ include: \"@selector\" }],\n    selector: [\n      { include: \"@comments\" },\n      { include: \"@import\" },\n      { include: \"@strings\" },\n      [\n        \"[@](keyframes|-webkit-keyframes|-moz-keyframes|-o-keyframes)\",\n        { token: \"keyword\", next: \"@keyframedeclaration\" }\n      ],\n      [\"[@](page|content|font-face|-moz-document)\", { token: \"keyword\" }],\n      [\"[@](charset|namespace)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\n        \"(url-prefix)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      [\n        \"(url)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      { include: \"@selectorname\" },\n      [\"[\\\\*]\", \"tag\"],\n      // selector symbols\n      [\"[>\\\\+,]\", \"delimiter\"],\n      // selector operators\n      [\"\\\\[\", { token: \"delimiter.bracket\", next: \"@selectorattribute\" }],\n      [\"{\", { token: \"delimiter.bracket\", next: \"@selectorbody\" }]\n    ],\n    selectorbody: [\n      { include: \"@comments\" },\n      [\"[*_]?@identifier@ws:(?=(\\\\s|\\\\d|[^{;}]*[;}]))\", \"attribute.name\", \"@rulevalue\"],\n      // rule definition: to distinguish from a nested selector check for whitespace, number or a semicolon\n      [\"}\", { token: \"delimiter.bracket\", next: \"@pop\" }]\n    ],\n    selectorname: [\n      [\"(\\\\.|#(?=[^{])|%|(@identifier)|:)+\", \"tag\"]\n      // selector (.foo, div, ...)\n    ],\n    selectorattribute: [{ include: \"@term\" }, [\"]\", { token: \"delimiter.bracket\", next: \"@pop\" }]],\n    term: [\n      { include: \"@comments\" },\n      [\n        \"(url-prefix)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      [\n        \"(url)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      { include: \"@functioninvocation\" },\n      { include: \"@numbers\" },\n      { include: \"@name\" },\n      { include: \"@strings\" },\n      [\"([<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,])\", \"delimiter\"],\n      [\",\", \"delimiter\"]\n    ],\n    rulevalue: [\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      { include: \"@term\" },\n      [\"!important\", \"keyword\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    warndebug: [[\"[@](warn|debug)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    import: [[\"[@](import)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    parenthizedterm: [\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    declarationbody: [\n      { include: \"@term\" },\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [/[^*/]+/, \"comment\"],\n      [/./, \"comment\"]\n    ],\n    name: [[\"@identifier\", \"attribute.value\"]],\n    numbers: [\n      [\"-?(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"attribute.value.number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"attribute.value.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"attribute.value.unit\",\n        \"@pop\"\n      ]\n    ],\n    keyframedeclaration: [\n      [\"@identifier\", \"attribute.value\"],\n      [\"{\", { token: \"delimiter.bracket\", switchTo: \"@keyframebody\" }]\n    ],\n    keyframebody: [\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.bracket\", next: \"@selectorbody\" }],\n      [\"}\", { token: \"delimiter.bracket\", next: \"@pop\" }]\n    ],\n    functioninvocation: [\n      [\"@identifier\\\\(\", { token: \"attribute.value\", next: \"@functionarguments\" }]\n    ],\n    functionarguments: [\n      [\"\\\\$@identifier@ws:\", \"attribute.name\"],\n      [\"[,]\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"attribute.value\", next: \"@pop\" }]\n    ],\n    strings: [\n      ['~?\"', { token: \"string\", next: \"@stringenddoublequote\" }],\n      [\"~?'\", { token: \"string\", next: \"@stringendquote\" }]\n    ],\n    stringenddoublequote: [\n      [\"\\\\\\\\.\", \"string\"],\n      ['\"', { token: \"string\", next: \"@pop\" }],\n      [/[^\\\\\"]+/, \"string\"],\n      [\".\", \"string\"]\n    ],\n    stringendquote: [\n      [\"\\\\\\\\.\", \"string\"],\n      [\"'\", { token: \"string\", next: \"@pop\" }],\n      [/[^\\\\']+/, \"string\"],\n      [\".\", \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,YAAa,0DACb,SAAU,CACR,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACxD,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,+CAA+C,CACjE,IAAS,OAAO,uCAAuC,CACxD,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,OACd,GAAI;QAEJ,WAAY,sHACZ,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,oBAAqB,CACrD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,oBAAqB,CACrD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CACzD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACpD,CACD,UAAW,CACT,KAAM,CAAC,CAAE,QAAS,YAAa,CAAC,CAChC,SAAU,CACR,CAAE,QAAS,YAAa,CACxB,CAAE,QAAS,UAAW,CACtB,CAAE,QAAS,WAAY,CACvB,CACE,+DACA,CAAE,MAAO,UAAW,KAAM,uBAAwB,CACnD,CACD,CAAC,4CAA6C,CAAE,MAAO,UAAW,CAAC,CACnE,CAAC,yBAA0B,CAAE,MAAO,UAAW,KAAM,mBAAoB,CAAC,CAC1E,CACE,oBACA,CAAC,kBAAmB,CAAE,MAAO,wBAAyB,KAAM,kBAAmB,CAAC,CACjF,CACD,CACE,aACA,CAAC,kBAAmB,CAAE,MAAO,wBAAyB,KAAM,kBAAmB,CAAC,CACjF,CACD,CAAE,QAAS,gBAAiB,CAC5B,CAAC,QAAS,MAAM,CAEhB,CAAC,UAAW,YAAY,CAExB,CAAC,MAAO,CAAE,MAAO,oBAAqB,KAAM,qBAAsB,CAAC,CACnE,CAAC,IAAK,CAAE,MAAO,oBAAqB,KAAM,gBAAiB,CAAC,CAC7D,CACD,aAAc,CACZ,CAAE,QAAS,YAAa,CACxB,CAAC,gDAAiD,iBAAkB,aAAa,CAEjF,CAAC,IAAK,CAAE,MAAO,oBAAqB,KAAM,OAAQ,CAAC,CACpD,CACD,aAAc,CACZ,CAAC,qCAAsC,MAAM,CAE9C,CACD,kBAAmB,CAAC,CAAE,QAAS,QAAS,CAAE,CAAC,IAAK,CAAE,MAAO,oBAAqB,KAAM,OAAQ,CAAC,CAAC,CAC9F,KAAM,CACJ,CAAE,QAAS,YAAa,CACxB,CACE,oBACA,CAAC,kBAAmB,CAAE,MAAO,wBAAyB,KAAM,kBAAmB,CAAC,CACjF,CACD,CACE,aACA,CAAC,kBAAmB,CAAE,MAAO,wBAAyB,KAAM,kBAAmB,CAAC,CACjF,CACD,CAAE,QAAS,sBAAuB,CAClC,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,QAAS,CACpB,CAAE,QAAS,WAAY,CACvB,CAAC,gCAAiC,YAAY,CAC9C,CAAC,IAAK,YAAY,CACnB,CACD,UAAW,CACT,CAAE,QAAS,YAAa,CACxB,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,QAAS,CACpB,CAAC,aAAc,UAAU,CACzB,CAAC,IAAK,YAAa,OAAO,CAC1B,CAAC,QAAS,CAAE,MAAO,GAAI,KAAM,OAAQ,CAAC,CAEvC,CACD,UAAW,CAAC,CAAC,kBAAmB,CAAE,MAAO,UAAW,KAAM,mBAAoB,CAAC,CAAC,CAChF,OAAQ,CAAC,CAAC,cAAe,CAAE,MAAO,UAAW,KAAM,mBAAoB,CAAC,CAAC,CACzE,eAAgB,CACd,CAAE,QAAS,WAAY,CACvB,CAAC;IAAa,SAAS,CACvB,CAAC,MAAO,CAAE,MAAO,wBAAyB,KAAM,OAAQ,CAAC,CAC1D,CACD,gBAAiB,CACf,CAAE,QAAS,QAAS,CACpB,CAAC,MAAO,CAAE,MAAO,wBAAyB,KAAM,OAAQ,CAAC,CAC1D,CACD,gBAAiB,CACf,CAAE,QAAS,QAAS,CACpB,CAAC,IAAK,YAAa,OAAO,CAC1B,CAAC,QAAS,CAAE,MAAO,GAAI,KAAM,OAAQ,CAAC,CAEvC,CACD,SAAU,CACR,CAAC,SAAU,UAAW,WAAW,CACjC,CAAC,YAAa,UAAU,CACzB,CACD,QAAS,CACP,CAAC,SAAU,UAAW,OAAO,CAC7B,CAAC,SAAU,UAAU,CACrB,CAAC,IAAK,UAAU,CACjB,CACD,KAAM,CAAC,CAAC,cAAe,kBAAkB,CAAC,CAC1C,QAAS,CACP,CAAC,qCAAsC,CAAE,MAAO,yBAA0B,KAAM,SAAU,CAAC,CAC3F,CAAC,wBAAyB,sBAAsB,CACjD,CACD,MAAO,CACL,CACE,0FACA,uBACA,OACD,CACF,CACD,oBAAqB,CACnB,CAAC,cAAe,kBAAkB,CAClC,CAAC,IAAK,CAAE,MAAO,oBAAqB,SAAU,gBAAiB,CAAC,CACjE,CACD,aAAc,CACZ,CAAE,QAAS,QAAS,CACpB,CAAC,IAAK,CAAE,MAAO,oBAAqB,KAAM,gBAAiB,CAAC,CAC5D,CAAC,IAAK,CAAE,MAAO,oBAAqB,KAAM,OAAQ,CAAC,CACpD,CACD,mBAAoB,CAClB,CAAC,iBAAkB,CAAE,MAAO,kBAAmB,KAAM,qBAAsB,CAAC,CAC7E,CACD,kBAAmB,CACjB,CAAC,qBAAsB,iBAAiB,CACxC,CAAC,MAAO,YAAY,CACpB,CAAE,QAAS,QAAS,CACpB,CAAC,MAAO,CAAE,MAAO,kBAAmB,KAAM,OAAQ,CAAC,CACpD,CACD,QAAS,CACP,CAAC,MAAO,CAAE,MAAO,SAAU,KAAM,wBAAyB,CAAC,CAC3D,CAAC,MAAO,CAAE,MAAO,SAAU,KAAM,kBAAmB,CAAC,CACtD,CACD,qBAAsB,CACpB,CAAC,QAAS,SAAS,CACnB,CAAC,IAAK,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAC,CACxC,CAAC,UAAW,SAAS,CACrB,CAAC,IAAK,SAAS,CAChB,CACD,eAAgB,CACd,CAAC,QAAS,SAAS,CACnB,CAAC,IAAK,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAC,CACxC,CAAC,UAAW,SAAS,CACrB,CAAC,IAAK,SAAS,CAChB,CACF,CACF"}