{"version": 3, "file": "twig-BAQPn0SF.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/twig/twig.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/twig/twig.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  comments: {\n    blockComment: [\"{#\", \"#}\"]\n  },\n  brackets: [\n    [\"{#\", \"#}\"],\n    [\"{%\", \"%}\"],\n    [\"{{\", \"}}\"],\n    [\"(\", \")\"],\n    [\"[\", \"]\"],\n    // HTML\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{# \", close: \" #}\" },\n    { open: \"{% \", close: \" %}\" },\n    { open: \"{{ \", close: \" }}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    // HTML\n    { open: \"<\", close: \">\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  ignoreCase: true,\n  keywords: [\n    // (opening) tags\n    \"apply\",\n    \"autoescape\",\n    \"block\",\n    \"deprecated\",\n    \"do\",\n    \"embed\",\n    \"extends\",\n    \"flush\",\n    \"for\",\n    \"from\",\n    \"if\",\n    \"import\",\n    \"include\",\n    \"macro\",\n    \"sandbox\",\n    \"set\",\n    \"use\",\n    \"verbatim\",\n    \"with\",\n    // closing tags\n    \"endapply\",\n    \"endautoescape\",\n    \"endblock\",\n    \"endembed\",\n    \"endfor\",\n    \"endif\",\n    \"endmacro\",\n    \"endsandbox\",\n    \"endset\",\n    \"endwith\",\n    // literals\n    \"true\",\n    \"false\"\n  ],\n  tokenizer: {\n    root: [\n      // whitespace\n      [/\\s+/],\n      // Twig Tag Delimiters\n      [/{#/, \"comment.twig\", \"@commentState\"],\n      [/{%[-~]?/, \"delimiter.twig\", \"@blockState\"],\n      [/{{[-~]?/, \"delimiter.twig\", \"@variableState\"],\n      // HTML\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@comment\"],\n      [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)(\\s*)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)((?:[\\w\\-]+:)?[\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/[^<{]+/]\n      // text\n    ],\n    /**\n     * Comment Tag Handling\n     */\n    commentState: [\n      [/#}/, \"comment.twig\", \"@pop\"],\n      [/./, \"comment.twig\"]\n    ],\n    /**\n     * Block Tag Handling\n     */\n    blockState: [\n      [/[-~]?%}/, \"delimiter.twig\", \"@pop\"],\n      // whitespace\n      [/\\s+/],\n      // verbatim\n      // Unlike other blocks, verbatim ehas its own state\n      // transition to ensure we mark its contents as strings.\n      [\n        /(verbatim)(\\s*)([-~]?%})/,\n        [\"keyword.twig\", \"\", { token: \"delimiter.twig\", next: \"@rawDataState\" }]\n      ],\n      { include: \"expression\" }\n    ],\n    rawDataState: [\n      // endverbatim\n      [\n        /({%[-~]?)(\\s*)(endverbatim)(\\s*)([-~]?%})/,\n        [\"delimiter.twig\", \"\", \"keyword.twig\", \"\", { token: \"delimiter.twig\", next: \"@popall\" }]\n      ],\n      [/./, \"string.twig\"]\n    ],\n    /**\n     * Variable Tag Handling\n     */\n    variableState: [[/[-~]?}}/, \"delimiter.twig\", \"@pop\"], { include: \"expression\" }],\n    stringState: [\n      // closing double quoted string\n      [/\"/, \"string.twig\", \"@pop\"],\n      // interpolation start\n      [/#{\\s*/, \"string.twig\", \"@interpolationState\"],\n      // string part\n      [/[^#\"\\\\]*(?:(?:\\\\.|#(?!\\{))[^#\"\\\\]*)*/, \"string.twig\"]\n    ],\n    interpolationState: [\n      // interpolation end\n      [/}/, \"string.twig\", \"@pop\"],\n      { include: \"expression\" }\n    ],\n    /**\n     * Expression Handling\n     */\n    expression: [\n      // whitespace\n      [/\\s+/],\n      // operators - math\n      [/\\+|-|\\/{1,2}|%|\\*{1,2}/, \"operators.twig\"],\n      // operators - logic\n      [/(and|or|not|b-and|b-xor|b-or)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - comparison (symbols)\n      [/==|!=|<|>|>=|<=/, \"operators.twig\"],\n      // operators - comparison (words)\n      [/(starts with|ends with|matches)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - containment\n      [/(in)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - test\n      [/(is)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - misc\n      [/\\||~|:|\\.{1,2}|\\?{1,2}/, \"operators.twig\"],\n      // names\n      [\n        /[^\\W\\d][\\w]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword.twig\",\n            \"@default\": \"variable.twig\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d+(\\.\\d+)?/, \"number.twig\"],\n      // punctuation\n      [/\\(|\\)|\\[|\\]|{|}|,/, \"delimiter.twig\"],\n      // strings\n      [/\"([^#\"\\\\]*(?:\\\\.[^#\"\\\\]*)*)\"|\\'([^\\'\\\\]*(?:\\\\.[^\\'\\\\]*)*)\\'/, \"string.twig\"],\n      // opening double quoted string\n      [/\"/, \"string.twig\", \"@stringState\"],\n      // misc syntactic constructs\n      // These are not operators per se, but for the purposes of lexical analysis we\n      // can treat them as such.\n      // arrow functions\n      [/=>/, \"operators.twig\"],\n      // assignment\n      [/=/, \"operators.twig\"]\n    ],\n    /**\n     * HTML\n     */\n    doctype: [\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [/type/, \"attribute.name.html\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [/=/, \"delimiter.html\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^<]+/, \"\"]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [/type/, \"attribute.name.html\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [/=/, \"delimiter.html\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^<]+/, \"\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,YAAa,iFACb,SAAU,CACR,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,KAAM,KAAK,CACZ,CAAC,KAAM,KAAK,CACZ,CAAC,KAAM,KAAK,CACZ,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CAEV,CAAC,OAAQ,MAAM,CACf,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,MAAO,MAAO,MAAO,CAC7B,CAAE,KAAM,MAAO,MAAO,MAAO,CAC7B,CAAE,KAAM,MAAO,MAAO,MAAO,CAC7B,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAEzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,GACd,WAAY,GACZ,SAAU,wOAmCT,CACD,UAAW,CACT,KAAM,CAEJ,CAAC,MAAM,CAEP,CAAC,KAAM,eAAgB,gBAAgB,CACvC,CAAC,UAAW,iBAAkB,cAAc,CAC5C,CAAC,UAAW,iBAAkB,iBAAiB,CAE/C,CAAC,YAAa,eAAgB,WAAW,CACzC,CAAC,OAAQ,eAAgB,WAAW,CACpC,CAAC,sCAAuC,CAAC,iBAAkB,WAAY,GAAI,iBAAiB,CAAC,CAC7F,CAAC,cAAe,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,UAAW,CAAC,CAAC,CAC3E,CAAC,aAAc,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,SAAU,CAAC,CAAC,CACzE,CAAC,4BAA6B,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,YAAa,CAAC,CAAC,CAC3F,CAAC,8BAA+B,CAAC,iBAAkB,CAAE,MAAO,WAAY,KAAM,YAAa,CAAC,CAAC,CAC7F,CAAC,IAAK,iBAAiB,CACvB,CAAC,SAAS,CAEX,CAID,aAAc,CACZ,CAAC,KAAM,eAAgB,OAAO,CAC9B,CAAC,IAAK,eAAe,CACtB,CAID,WAAY,CACV,CAAC,UAAW,iBAAkB,OAAO,CAErC,CAAC,MAAM,CAIP,CACE,2BACA,CAAC,eAAgB,GAAI,CAAE,MAAO,iBAAkB,KAAM,gBAAiB,CAAC,CACzE,CACD,CAAE,QAAS,aAAc,CAC1B,CACD,aAAc,CAEZ,CACE,4CACA,CAAC,iBAAkB,GAAI,eAAgB,GAAI,CAAE,MAAO,iBAAkB,KAAM,UAAW,CAAC,CACzF,CACD,CAAC,IAAK,cAAc,CACrB,CAID,cAAe,CAAC,CAAC,UAAW,iBAAkB,OAAO,CAAE,CAAE,QAAS,aAAc,CAAC,CACjF,YAAa,CAEX,CAAC,IAAK,cAAe,OAAO,CAE5B,CAAC,QAAS,cAAe,sBAAsB,CAE/C,CAAC,uCAAwC,cAAc,CACxD,CACD,mBAAoB,CAElB,CAAC,IAAK,cAAe,OAAO,CAC5B,CAAE,QAAS,aAAc,CAC1B,CAID,WAAY,CAEV,CAAC,MAAM,CAEP,CAAC,yBAA0B,iBAAiB,CAE5C,CAAC,qCAAsC,CAAC,iBAAkB,GAAG,CAAC,CAE9D,CAAC,kBAAmB,iBAAiB,CAErC,CAAC,uCAAwC,CAAC,iBAAkB,GAAG,CAAC,CAEhE,CAAC,YAAa,CAAC,iBAAkB,GAAG,CAAC,CAErC,CAAC,YAAa,CAAC,iBAAkB,GAAG,CAAC,CAErC,CAAC,yBAA0B,iBAAiB,CAE5C,CACE,eACA,CACE,MAAO,CACL,YAAa,eACb,WAAY,gBACb,CACF,CACF,CAED,CAAC,cAAe,cAAc,CAE9B,CAAC,oBAAqB,iBAAiB,CAEvC,CAAC,8DAA+D,cAAc,CAE9E,CAAC,IAAK,cAAe,eAAe,CAKpC,CAAC,KAAM,iBAAiB,CAExB,CAAC,IAAK,iBAAiB,CACxB,CAID,QAAS,CACP,CAAC,QAAS,uBAAuB,CACjC,CAAC,IAAK,eAAgB,OAAO,CAC9B,CACD,QAAS,CACP,CAAC,MAAO,eAAgB,OAAO,CAC/B,CAAC,QAAS,uBAAuB,CACjC,CAAC,IAAK,uBAAuB,CAC9B,CACD,SAAU,CACR,CAAC,OAAQ,iBAAkB,OAAO,CAClC,CAAC,YAAa,uBAAuB,CACrC,CAAC,YAAa,uBAAuB,CACrC,CAAC,UAAW,sBAAsB,CAClC,CAAC,IAAK,iBAAiB,CACvB,CAAC,aAAa,CAEf,CAGD,OAAQ,CACN,CAAC,OAAQ,sBAAuB,mBAAmB,CACnD,CAAC,YAAa,uBAAuB,CACrC,CAAC,YAAa,uBAAuB,CACrC,CAAC,UAAW,sBAAsB,CAClC,CAAC,IAAK,iBAAiB,CACvB,CACE,IACA,CACE,MAAO,iBACP,KAAM,kBACN,aAAc,kBACf,CACF,CACD,CAAC,aAAa,CAEd,CACE,sBACA,CAAC,iBAAkB,WAAY,CAAE,MAAO,iBAAkB,KAAM,OAAQ,CAAC,CAC1E,CACF,CAED,gBAAiB,CACf,CAAC,IAAK,iBAAkB,yBAAyB,CACjD,CACE,IACA,CACE,MAAO,iBACP,KAAM,kBACN,aAAc,kBACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACvD,CAED,sBAAuB,CACrB,CACE,YACA,CACE,MAAO,uBACP,SAAU,2BACX,CACF,CACD,CACE,YACA,CACE,MAAO,uBACP,SAAU,2BACX,CACF,CACD,CACE,IACA,CACE,MAAO,iBACP,KAAM,kBACN,aAAc,kBACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACvD,CAED,qBAAsB,CACpB,CACE,IACA,CACE,MAAO,iBACP,KAAM,sBACN,aAAc,MACf,CACF,CACD,CAAC,YAAa,uBAAuB,CACrC,CAAC,YAAa,uBAAuB,CACrC,CAAC,UAAW,sBAAsB,CAClC,CAAC,IAAK,iBAAiB,CACvB,CAAC,aAAa,CAEd,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACvD,CACD,eAAgB,CACd,CAAC,YAAa,CAAE,MAAO,WAAY,KAAM,OAAQ,aAAc,OAAQ,CAAC,CACxE,CAAC,QAAS,GAAG,CACd,CAID,MAAO,CACL,CAAC,OAAQ,sBAAuB,kBAAkB,CAClD,CAAC,YAAa,uBAAuB,CACrC,CAAC,YAAa,uBAAuB,CACrC,CAAC,UAAW,sBAAsB,CAClC,CAAC,IAAK,iBAAiB,CACvB,CACE,IACA,CACE,MAAO,iBACP,KAAM,iBACN,aAAc,WACf,CACF,CACD,CAAC,aAAa,CAEd,CACE,qBACA,CAAC,iBAAkB,WAAY,CAAE,MAAO,iBAAkB,KAAM,OAAQ,CAAC,CAC1E,CACF,CAED,eAAgB,CACd,CAAC,IAAK,iBAAkB,wBAAwB,CAChD,CACE,IACA,CACE,MAAO,iBACP,KAAM,iBACN,aAAc,WACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACtD,CAED,qBAAsB,CACpB,CACE,YACA,CACE,MAAO,uBACP,SAAU,0BACX,CACF,CACD,CACE,YACA,CACE,MAAO,uBACP,SAAU,0BACX,CACF,CACD,CACE,IACA,CACE,MAAO,iBACP,KAAM,iBACN,aAAc,WACf,CACF,CAED,CAAC,aAAa,CAEd,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACtD,CAED,oBAAqB,CACnB,CACE,IACA,CACE,MAAO,iBACP,KAAM,qBACN,aAAc,MACf,CACF,CACD,CAAC,YAAa,uBAAuB,CACrC,CAAC,YAAa,uBAAuB,CACrC,CAAC,UAAW,sBAAsB,CAClC,CAAC,IAAK,iBAAiB,CACvB,CAAC,aAAa,CAEd,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACtD,CACD,cAAe,CACb,CAAC,WAAY,CAAE,MAAO,WAAY,KAAM,OAAQ,aAAc,OAAQ,CAAC,CACvE,CAAC,QAAS,GAAG,CACd,CACF,CACF"}