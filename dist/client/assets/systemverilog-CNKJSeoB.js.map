{"version": 3, "file": "systemverilog-CNKJSeoB.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/systemverilog/systemverilog.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"begin\", \"end\"],\n    [\"case\", \"endcase\"],\n    [\"casex\", \"endcase\"],\n    [\"casez\", \"endcase\"],\n    [\"checker\", \"endchecker\"],\n    [\"class\", \"endclass\"],\n    [\"clocking\", \"endclocking\"],\n    [\"config\", \"endconfig\"],\n    [\"function\", \"endfunction\"],\n    [\"generate\", \"endgenerate\"],\n    [\"group\", \"endgroup\"],\n    [\"interface\", \"endinterface\"],\n    [\"module\", \"endmodule\"],\n    [\"package\", \"endpackage\"],\n    [\"primitive\", \"endprimitive\"],\n    [\"program\", \"endprogram\"],\n    [\"property\", \"endproperty\"],\n    [\"specify\", \"endspecify\"],\n    [\"sequence\", \"endsequence\"],\n    [\"table\", \"endtable\"],\n    [\"task\", \"endtask\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    offSide: false,\n    markers: {\n      start: new RegExp(\n        \"^(?:\\\\s*|.*(?!\\\\/[\\\\/\\\\*])[^\\\\w])(?:begin|case(x|z)?|class|clocking|config|covergroup|function|generate|interface|module|package|primitive|property|program|sequence|specify|table|task)\\\\b\"\n      ),\n      end: new RegExp(\n        \"^(?:\\\\s*|.*(?!\\\\/[\\\\/\\\\*])[^\\\\w])(?:end|endcase|endclass|endclocking|endconfig|endgroup|endfunction|endgenerate|endinterface|endmodule|endpackage|endprimitive|endproperty|endprogram|endsequence|endspecify|endtable|endtask)\\\\b\"\n      )\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sv\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.angle\", open: \"<\", close: \">\" }\n  ],\n  keywords: [\n    \"accept_on\",\n    \"alias\",\n    \"always\",\n    \"always_comb\",\n    \"always_ff\",\n    \"always_latch\",\n    \"and\",\n    \"assert\",\n    \"assign\",\n    \"assume\",\n    \"automatic\",\n    \"before\",\n    \"begin\",\n    \"bind\",\n    \"bins\",\n    \"binsof\",\n    \"bit\",\n    \"break\",\n    \"buf\",\n    \"bufif0\",\n    \"bufif1\",\n    \"byte\",\n    \"case\",\n    \"casex\",\n    \"casez\",\n    \"cell\",\n    \"chandle\",\n    \"checker\",\n    \"class\",\n    \"clocking\",\n    \"cmos\",\n    \"config\",\n    \"const\",\n    \"constraint\",\n    \"context\",\n    \"continue\",\n    \"cover\",\n    \"covergroup\",\n    \"coverpoint\",\n    \"cross\",\n    \"deassign\",\n    \"default\",\n    \"defparam\",\n    \"design\",\n    \"disable\",\n    \"dist\",\n    \"do\",\n    \"edge\",\n    \"else\",\n    \"end\",\n    \"endcase\",\n    \"endchecker\",\n    \"endclass\",\n    \"endclocking\",\n    \"endconfig\",\n    \"endfunction\",\n    \"endgenerate\",\n    \"endgroup\",\n    \"endinterface\",\n    \"endmodule\",\n    \"endpackage\",\n    \"endprimitive\",\n    \"endprogram\",\n    \"endproperty\",\n    \"endspecify\",\n    \"endsequence\",\n    \"endtable\",\n    \"endtask\",\n    \"enum\",\n    \"event\",\n    \"eventually\",\n    \"expect\",\n    \"export\",\n    \"extends\",\n    \"extern\",\n    \"final\",\n    \"first_match\",\n    \"for\",\n    \"force\",\n    \"foreach\",\n    \"forever\",\n    \"fork\",\n    \"forkjoin\",\n    \"function\",\n    \"generate\",\n    \"genvar\",\n    \"global\",\n    \"highz0\",\n    \"highz1\",\n    \"if\",\n    \"iff\",\n    \"ifnone\",\n    \"ignore_bins\",\n    \"illegal_bins\",\n    \"implements\",\n    \"implies\",\n    \"import\",\n    \"incdir\",\n    \"include\",\n    \"initial\",\n    \"inout\",\n    \"input\",\n    \"inside\",\n    \"instance\",\n    \"int\",\n    \"integer\",\n    \"interconnect\",\n    \"interface\",\n    \"intersect\",\n    \"join\",\n    \"join_any\",\n    \"join_none\",\n    \"large\",\n    \"let\",\n    \"liblist\",\n    \"library\",\n    \"local\",\n    \"localparam\",\n    \"logic\",\n    \"longint\",\n    \"macromodule\",\n    \"matches\",\n    \"medium\",\n    \"modport\",\n    \"module\",\n    \"nand\",\n    \"negedge\",\n    \"nettype\",\n    \"new\",\n    \"nexttime\",\n    \"nmos\",\n    \"nor\",\n    \"noshowcancelled\",\n    \"not\",\n    \"notif0\",\n    \"notif1\",\n    \"null\",\n    \"or\",\n    \"output\",\n    \"package\",\n    \"packed\",\n    \"parameter\",\n    \"pmos\",\n    \"posedge\",\n    \"primitive\",\n    \"priority\",\n    \"program\",\n    \"property\",\n    \"protected\",\n    \"pull0\",\n    \"pull1\",\n    \"pulldown\",\n    \"pullup\",\n    \"pulsestyle_ondetect\",\n    \"pulsestyle_onevent\",\n    \"pure\",\n    \"rand\",\n    \"randc\",\n    \"randcase\",\n    \"randsequence\",\n    \"rcmos\",\n    \"real\",\n    \"realtime\",\n    \"ref\",\n    \"reg\",\n    \"reject_on\",\n    \"release\",\n    \"repeat\",\n    \"restrict\",\n    \"return\",\n    \"rnmos\",\n    \"rpmos\",\n    \"rtran\",\n    \"rtranif0\",\n    \"rtranif1\",\n    \"s_always\",\n    \"s_eventually\",\n    \"s_nexttime\",\n    \"s_until\",\n    \"s_until_with\",\n    \"scalared\",\n    \"sequence\",\n    \"shortint\",\n    \"shortreal\",\n    \"showcancelled\",\n    \"signed\",\n    \"small\",\n    \"soft\",\n    \"solve\",\n    \"specify\",\n    \"specparam\",\n    \"static\",\n    \"string\",\n    \"strong\",\n    \"strong0\",\n    \"strong1\",\n    \"struct\",\n    \"super\",\n    \"supply0\",\n    \"supply1\",\n    \"sync_accept_on\",\n    \"sync_reject_on\",\n    \"table\",\n    \"tagged\",\n    \"task\",\n    \"this\",\n    \"throughout\",\n    \"time\",\n    \"timeprecision\",\n    \"timeunit\",\n    \"tran\",\n    \"tranif0\",\n    \"tranif1\",\n    \"tri\",\n    \"tri0\",\n    \"tri1\",\n    \"triand\",\n    \"trior\",\n    \"trireg\",\n    \"type\",\n    \"typedef\",\n    \"union\",\n    \"unique\",\n    \"unique0\",\n    \"unsigned\",\n    \"until\",\n    \"until_with\",\n    \"untyped\",\n    \"use\",\n    \"uwire\",\n    \"var\",\n    \"vectored\",\n    \"virtual\",\n    \"void\",\n    \"wait\",\n    \"wait_order\",\n    \"wand\",\n    \"weak\",\n    \"weak0\",\n    \"weak1\",\n    \"while\",\n    \"wildcard\",\n    \"wire\",\n    \"with\",\n    \"within\",\n    \"wor\",\n    \"xnor\",\n    \"xor\"\n  ],\n  builtin_gates: [\n    \"and\",\n    \"nand\",\n    \"nor\",\n    \"or\",\n    \"xor\",\n    \"xnor\",\n    \"buf\",\n    \"not\",\n    \"bufif0\",\n    \"bufif1\",\n    \"notif1\",\n    \"notif0\",\n    \"cmos\",\n    \"nmos\",\n    \"pmos\",\n    \"rcmos\",\n    \"rnmos\",\n    \"rpmos\",\n    \"tran\",\n    \"tranif1\",\n    \"tranif0\",\n    \"rtran\",\n    \"rtranif1\",\n    \"rtranif0\"\n  ],\n  operators: [\n    // assignment operators\n    \"=\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"%=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"<<=\",\n    \">>+\",\n    \"<<<=\",\n    \">>>=\",\n    // conditional expression\n    \"?\",\n    \":\",\n    // Unary operators\n    \"+\",\n    \"-\",\n    \"!\",\n    \"~\",\n    \"&\",\n    \"~&\",\n    \"|\",\n    \"~|\",\n    \"^\",\n    \"~^\",\n    \"^~\",\n    //binary operators\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"==\",\n    \"!=\",\n    \"===\",\n    \"!==\",\n    \"==?\",\n    \"!=?\",\n    \"&&\",\n    \"||\",\n    \"**\",\n    \"<\",\n    \"<=\",\n    \">\",\n    \">=\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \">>\",\n    \"<<\",\n    \">>>\",\n    \"<<<\",\n    // increment or decrement operator\n    \"++\",\n    \"--\",\n    //binary logical operator\n    \"->\",\n    \"<->\",\n    // binary set membership operator\n    \"inside\",\n    // binary distrubution operator\n    \"dist\",\n    \"::\",\n    \"+:\",\n    \"-:\",\n    \"*>\",\n    \"&&&\",\n    \"|->\",\n    \"|=>\",\n    \"#=#\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%#]+/,\n  escapes: /%%|\\\\(?:[antvf\\\\\"']|x[0-9A-Fa-f]{1,2}|[0-7]{1,3})/,\n  identifier: /(?:[a-zA-Z_][a-zA-Z0-9_$\\.]*|\\\\\\S+ )/,\n  systemcall: /[$][a-zA-Z0-9_]+/,\n  timeunits: /s|ms|us|ns|ps|fs/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // module instances\n      [\n        /^(\\s*)(@identifier)/,\n        [\n          \"\",\n          {\n            cases: {\n              \"@builtin_gates\": {\n                token: \"keyword.$2\",\n                next: \"@module_instance\"\n              },\n              table: {\n                token: \"keyword.$2\",\n                next: \"@table\"\n              },\n              \"@keywords\": { token: \"keyword.$2\" },\n              \"@default\": {\n                token: \"identifier\",\n                next: \"@module_instance\"\n              }\n            }\n          }\n        ]\n      ],\n      // include statements\n      [/^\\s*`include/, { token: \"keyword.directive.include\", next: \"@include\" }],\n      // Preprocessor directives\n      [/^\\s*`\\s*\\w+/, \"keyword\"],\n      // identifiers and keywords\n      { include: \"@identifier_or_keyword\" },\n      // whitespace and comments\n      { include: \"@whitespace\" },\n      // (* attributes *).\n      [/\\(\\*.*\\*\\)/, \"annotation\"],\n      // Systemcall\n      [/@systemcall/, \"variable.predefined\"],\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      { include: \"@numbers\" },\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      { include: \"@strings\" }\n    ],\n    identifier_or_keyword: [\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/\\d+?[\\d_]*(?:\\.[\\d_]+)?[eE][\\-+]?\\d+/, \"number.float\"],\n      [/\\d+?[\\d_]*\\.[\\d_]+(?:\\s*@timeunits)?/, \"number.float\"],\n      [/(?:\\d+?[\\d_]*\\s*)?'[sS]?[dD]\\s*[0-9xXzZ?]+?[0-9xXzZ?_]*/, \"number\"],\n      [/(?:\\d+?[\\d_]*\\s*)?'[sS]?[bB]\\s*[0-1xXzZ?]+?[0-1xXzZ?_]*/, \"number.binary\"],\n      [/(?:\\d+?[\\d_]*\\s*)?'[sS]?[oO]\\s*[0-7xXzZ?]+?[0-7xXzZ?_]*/, \"number.octal\"],\n      [/(?:\\d+?[\\d_]*\\s*)?'[sS]?[hH]\\s*[0-9a-fA-FxXzZ?]+?[0-9a-fA-FxXzZ?_]*/, \"number.hex\"],\n      [/1step/, \"number\"],\n      [/[\\dxXzZ]+?[\\dxXzZ_]*(?:\\s*@timeunits)?/, \"number\"],\n      [/'[01xXzZ]+/, \"number\"]\n    ],\n    module_instance: [\n      { include: \"@whitespace\" },\n      [/(#?)(\\()/, [\"\", { token: \"@brackets\", next: \"@port_connection\" }]],\n      [/@identifier\\s*[;={}\\[\\],]/, { token: \"@rematch\", next: \"@pop\" }],\n      [/@symbols|[;={}\\[\\],]/, { token: \"@rematch\", next: \"@pop\" }],\n      [/@identifier/, \"type\"],\n      [/;/, \"delimiter\", \"@pop\"]\n    ],\n    port_connection: [\n      { include: \"@identifier_or_keyword\" },\n      { include: \"@whitespace\" },\n      [/@systemcall/, \"variable.predefined\"],\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[,]/, \"delimiter\"],\n      [/\\(/, \"@brackets\", \"@port_connection\"],\n      [/\\)/, \"@brackets\", \"@pop\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    strings: [\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    include: [\n      [\n        /(\\s*)(\")([\\w*\\/*]*)(.\\w*)(\")/,\n        [\n          \"\",\n          \"string.include.identifier\",\n          \"string.include.identifier\",\n          \"string.include.identifier\",\n          { token: \"string.include.identifier\", next: \"@pop\" }\n        ]\n      ],\n      [\n        /(\\s*)(<)([\\w*\\/*]*)(.\\w*)(>)/,\n        [\n          \"\",\n          \"string.include.identifier\",\n          \"string.include.identifier\",\n          \"string.include.identifier\",\n          { token: \"string.include.identifier\", next: \"@pop\" }\n        ]\n      ]\n    ],\n    table: [\n      { include: \"@whitespace\" },\n      [/[()]/, \"@brackets\"],\n      [/[:;]/, \"delimiter\"],\n      [/[01\\-*?xXbBrRfFpPnN]/, \"variable.predefined\"],\n      [\"endtable\", \"keyword.endtable\", \"@pop\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,KACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,QAAS,MAAM,CAChB,CAAC,OAAQ,UAAU,CACnB,CAAC,QAAS,UAAU,CACpB,CAAC,QAAS,UAAU,CACpB,CAAC,UAAW,aAAa,CACzB,CAAC,QAAS,WAAW,CACrB,CAAC,WAAY,cAAc,CAC3B,CAAC,SAAU,YAAY,CACvB,CAAC,WAAY,cAAc,CAC3B,CAAC,WAAY,cAAc,CAC3B,CAAC,QAAS,WAAW,CACrB,CAAC,YAAa,eAAe,CAC7B,CAAC,SAAU,YAAY,CACvB,CAAC,UAAW,aAAa,CACzB,CAAC,YAAa,eAAe,CAC7B,CAAC,UAAW,aAAa,CACzB,CAAC,WAAY,cAAc,CAC3B,CAAC,UAAW,aAAa,CACzB,CAAC,WAAY,cAAc,CAC3B,CAAC,QAAS,WAAW,CACrB,CAAC,OAAQ,UAAU,CACpB,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC7C,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,GACT,QAAS,CACP,MAAW,OACT,8LACD,CACD,IAAS,OACP,oOACD,CACF,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,MACd,SAAU,CACR,CAAE,MAAO,kBAAmB,KAAM,IAAK,MAAO,IAAK,CACnD,CAAE,MAAO,wBAAyB,KAAM,IAAK,MAAO,IAAK,CACzD,CAAE,MAAO,mBAAoB,KAAM,IAAK,MAAO,IAAK,CACpD,CAAE,MAAO,kBAAmB,KAAM,IAAK,MAAO,IAAK,CACpD,CACD,SAAU,y4DAyPT,CACD,cAAe,CACb,MACA,OACA,MACA,KACA,MACA,OACA,MACA,MACA,SACA,SACA,SACA,SACA,OACA,OACA,OACA,QACA,QACA,QACA,OACA,UACA,UACA,QACA,WACA,WACD,CACD,UAAW,kNA0EV,CAED,QAAS,yBACT,QAAS,oDACT,WAAY,uCACZ,WAAY,mBACZ,UAAW,mBAEX,UAAW,CACT,KAAM,CAEJ,CACE,sBACA,CACE,GACA,CACE,MAAO,CACL,iBAAkB,CAChB,MAAO,aACP,KAAM,mBACP,CACD,MAAO,CACL,MAAO,aACP,KAAM,SACP,CACD,YAAa,CAAE,MAAO,aAAc,CACpC,WAAY,CACV,MAAO,aACP,KAAM,mBACP,CACF,CACF,CACF,CACF,CAED,CAAC,eAAgB,CAAE,MAAO,4BAA6B,KAAM,WAAY,CAAC,CAE1E,CAAC,cAAe,UAAU,CAE1B,CAAE,QAAS,yBAA0B,CAErC,CAAE,QAAS,cAAe,CAE1B,CAAC,aAAc,aAAa,CAE5B,CAAC,cAAe,sBAAsB,CAEtC,CAAC,aAAc,YAAY,CAC3B,CAAC,mBAAoB,YAAY,CACjC,CACE,WACA,CACE,MAAO,CACL,aAAc,YACd,WAAY,GACb,CACF,CACF,CAED,CAAE,QAAS,WAAY,CAEvB,CAAC,QAAS,YAAY,CAEtB,CAAE,QAAS,WAAY,CACxB,CACD,sBAAuB,CACrB,CACE,cACA,CACE,MAAO,CACL,YAAa,CAAE,MAAO,aAAc,CACpC,WAAY,aACb,CACF,CACF,CACF,CACD,QAAS,CACP,CAAC,uCAAwC,eAAe,CACxD,CAAC,uCAAwC,eAAe,CACxD,CAAC,0DAA2D,SAAS,CACrE,CAAC,0DAA2D,gBAAgB,CAC5E,CAAC,0DAA2D,eAAe,CAC3E,CAAC,sEAAuE,aAAa,CACrF,CAAC,QAAS,SAAS,CACnB,CAAC,yCAA0C,SAAS,CACpD,CAAC,aAAc,SAAS,CACzB,CACD,gBAAiB,CACf,CAAE,QAAS,cAAe,CAC1B,CAAC,WAAY,CAAC,GAAI,CAAE,MAAO,YAAa,KAAM,mBAAoB,CAAC,CAAC,CACpE,CAAC,4BAA6B,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CAClE,CAAC,uBAAwB,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CAC7D,CAAC,cAAe,OAAO,CACvB,CAAC,IAAK,YAAa,OAAO,CAC3B,CACD,gBAAiB,CACf,CAAE,QAAS,yBAA0B,CACrC,CAAE,QAAS,cAAe,CAC1B,CAAC,cAAe,sBAAsB,CACtC,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,WAAY,CACvB,CAAC,MAAO,YAAY,CACpB,CAAC,KAAM,YAAa,mBAAmB,CACvC,CAAC,KAAM,YAAa,OAAO,CAC5B,CACD,WAAY,CACV,CAAC,aAAc,GAAG,CAClB,CAAC,OAAQ,UAAW,WAAW,CAC/B,CAAC,UAAW,UAAU,CACvB,CACD,QAAS,CACP,CAAC,UAAW,UAAU,CACtB,CAAC,OAAQ,UAAW,OAAO,CAC3B,CAAC,QAAS,UAAU,CACrB,CACD,QAAS,CACP,CAAC,kBAAmB,iBAAiB,CAErC,CAAC,IAAK,SAAU,UAAU,CAC3B,CACD,OAAQ,CACN,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,SAAU,OAAO,CACxB,CACD,QAAS,CACP,CACE,+BACA,CACE,GACA,4BACA,4BACA,4BACA,CAAE,MAAO,4BAA6B,KAAM,OAAQ,CACrD,CACF,CACD,CACE,+BACA,CACE,GACA,4BACA,4BACA,4BACA,CAAE,MAAO,4BAA6B,KAAM,OAAQ,CACrD,CACF,CACF,CACD,MAAO,CACL,CAAE,QAAS,cAAe,CAC1B,CAAC,OAAQ,YAAY,CACrB,CAAC,OAAQ,YAAY,CACrB,CAAC,uBAAwB,sBAAsB,CAC/C,CAAC,WAAY,mBAAoB,OAAO,CACzC,CACF,CACF"}