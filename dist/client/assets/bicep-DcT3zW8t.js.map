{"version": 3, "file": "bicep-DcT3zW8t.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/bicep/bicep.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/bicep/bicep.ts\nvar bounded = (text) => `\\\\b${text}\\\\b`;\nvar identifierStart = \"[_a-zA-Z]\";\nvar identifierContinue = \"[_a-zA-Z0-9]\";\nvar identifier = bounded(`${identifierStart}${identifierContinue}*`);\nvar keywords = [\n  \"targetScope\",\n  \"resource\",\n  \"module\",\n  \"param\",\n  \"var\",\n  \"output\",\n  \"for\",\n  \"in\",\n  \"if\",\n  \"existing\"\n];\nvar namedLiterals = [\"true\", \"false\", \"null\"];\nvar nonCommentWs = `[ \\\\t\\\\r\\\\n]`;\nvar numericLiteral = `[0-9]+`;\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\" },\n    { open: \"'''\", close: \"'''\" }\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"'''\", close: \"'''\", notIn: [\"string\", \"comment\"] }\n  ],\n  autoCloseBefore: \":.,=}])' \\n\t\",\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\"^((?!\\\\/\\\\/).)*(\\\\{[^}\\\"'`]*|\\\\([^)\\\"'`]*|\\\\[[^\\\\]\\\"'`]*)$\"),\n    decreaseIndentPattern: new RegExp(\"^((?!.*?\\\\/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\")\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".bicep\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  symbols: /[=><!~?:&|+\\-*/^%]+/,\n  keywords,\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|'|\\\\\\${)`,\n  tokenizer: {\n    root: [{ include: \"@expression\" }, { include: \"@whitespace\" }],\n    stringVerbatim: [\n      { regex: `(|'|'')[^']`, action: { token: \"string\" } },\n      { regex: `'''`, action: { token: \"string.quote\", next: \"@pop\" } }\n    ],\n    stringLiteral: [\n      { regex: `\\\\\\${`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `[^\\\\\\\\'$]+`, action: { token: \"string\" } },\n      { regex: \"@escapes\", action: { token: \"string.escape\" } },\n      { regex: `\\\\\\\\.`, action: { token: \"string.escape.invalid\" } },\n      { regex: `'`, action: { token: \"string\", next: \"@pop\" } }\n    ],\n    bracketCounting: [\n      { regex: `{`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `}`, action: { token: \"delimiter.bracket\", next: \"@pop\" } },\n      { include: \"expression\" }\n    ],\n    comment: [\n      { regex: `[^\\\\*]+`, action: { token: \"comment\" } },\n      { regex: `\\\\*\\\\/`, action: { token: \"comment\", next: \"@pop\" } },\n      { regex: `[\\\\/*]`, action: { token: \"comment\" } }\n    ],\n    whitespace: [\n      { regex: nonCommentWs },\n      { regex: `\\\\/\\\\*`, action: { token: \"comment\", next: \"@comment\" } },\n      { regex: `\\\\/\\\\/.*$`, action: { token: \"comment\" } }\n    ],\n    expression: [\n      { regex: `'''`, action: { token: \"string.quote\", next: \"@stringVerbatim\" } },\n      { regex: `'`, action: { token: \"string.quote\", next: \"@stringLiteral\" } },\n      { regex: numericLiteral, action: { token: \"number\" } },\n      {\n        regex: identifier,\n        action: {\n          cases: {\n            \"@keywords\": { token: \"keyword\" },\n            \"@namedLiterals\": { token: \"keyword\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": "AAYA,IAAI,GAHW,GAAS,MAAM,EAAK,MAGV,yBAA2C,CAChE,EAAW,CACb,cACA,WACA,SACA,QACA,MACA,SACA,MACA,KACA,KACA,WACD,CACG,EAAgB,CAAC,OAAQ,QAAS,OAAO,CACzC,EAAe,eACf,EAAiB,SACjB,EAAO,CACT,SAAU,CACR,YAAa,KACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,MAAO,MAAO,MAAO,CAC9B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,MAAO,MAAO,MAAO,MAAO,CAAC,SAAU,UAAU,CAAE,CAC5D,CACD,gBAAiB;GACjB,iBAAkB,CAChB,sBAA2B,OAAO,6DAA6D,CAC/F,sBAA2B,OAAO,yCAAyC,CAC5E,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,SACd,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,mBAAoB,CACpD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CAC1D,CACD,QAAS,sBACT,WACA,gBACA,QAAS,0CACT,UAAW,CACT,KAAM,CAAC,CAAE,QAAS,cAAe,CAAE,CAAE,QAAS,cAAe,CAAC,CAC9D,eAAgB,CACd,CAAE,MAAO,cAAe,OAAQ,CAAE,MAAO,SAAU,CAAE,CACrD,CAAE,MAAO,MAAO,OAAQ,CAAE,MAAO,eAAgB,KAAM,OAAQ,CAAE,CAClE,CACD,cAAe,CACb,CAAE,MAAO,OAAS,OAAQ,CAAE,MAAO,oBAAqB,KAAM,mBAAoB,CAAE,CACpF,CAAE,MAAO,aAAc,OAAQ,CAAE,MAAO,SAAU,CAAE,CACpD,CAAE,MAAO,WAAY,OAAQ,CAAE,MAAO,gBAAiB,CAAE,CACzD,CAAE,MAAO,QAAS,OAAQ,CAAE,MAAO,wBAAyB,CAAE,CAC9D,CAAE,MAAO,IAAK,OAAQ,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAE,CAC1D,CACD,gBAAiB,CACf,CAAE,MAAO,IAAK,OAAQ,CAAE,MAAO,oBAAqB,KAAM,mBAAoB,CAAE,CAChF,CAAE,MAAO,IAAK,OAAQ,CAAE,MAAO,oBAAqB,KAAM,OAAQ,CAAE,CACpE,CAAE,QAAS,aAAc,CAC1B,CACD,QAAS,CACP,CAAE,MAAO,UAAW,OAAQ,CAAE,MAAO,UAAW,CAAE,CAClD,CAAE,MAAO,SAAU,OAAQ,CAAE,MAAO,UAAW,KAAM,OAAQ,CAAE,CAC/D,CAAE,MAAO,SAAU,OAAQ,CAAE,MAAO,UAAW,CAAE,CAClD,CACD,WAAY,CACV,CAAE,MAAO,EAAc,CACvB,CAAE,MAAO,SAAU,OAAQ,CAAE,MAAO,UAAW,KAAM,WAAY,CAAE,CACnE,CAAE,MAAO,YAAa,OAAQ,CAAE,MAAO,UAAW,CAAE,CACrD,CACD,WAAY,CACV,CAAE,MAAO,MAAO,OAAQ,CAAE,MAAO,eAAgB,KAAM,kBAAmB,CAAE,CAC5E,CAAE,MAAO,IAAK,OAAQ,CAAE,MAAO,eAAgB,KAAM,iBAAkB,CAAE,CACzE,CAAE,MAAO,EAAgB,OAAQ,CAAE,MAAO,SAAU,CAAE,CACtD,CACE,MAAO,EACP,OAAQ,CACN,MAAO,CACL,YAAa,CAAE,MAAO,UAAW,CACjC,iBAAkB,CAAE,MAAO,UAAW,CACtC,WAAY,CAAE,MAAO,aAAc,CACpC,CACF,CACF,CACF,CACF,CACF"}