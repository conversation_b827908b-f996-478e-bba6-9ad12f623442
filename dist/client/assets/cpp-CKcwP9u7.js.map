{"version": 3, "file": "cpp-CKcwP9u7.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/cpp/cpp.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/cpp/cpp.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#pragma\\\\s+region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#pragma\\\\s+endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".cpp\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.angle\", open: \"<\", close: \">\" }\n  ],\n  keywords: [\n    \"abstract\",\n    \"amp\",\n    \"array\",\n    \"auto\",\n    \"bool\",\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"char\",\n    \"class\",\n    \"const\",\n    \"constexpr\",\n    \"const_cast\",\n    \"continue\",\n    \"cpu\",\n    \"decltype\",\n    \"default\",\n    \"delegate\",\n    \"delete\",\n    \"do\",\n    \"double\",\n    \"dynamic_cast\",\n    \"each\",\n    \"else\",\n    \"enum\",\n    \"event\",\n    \"explicit\",\n    \"export\",\n    \"extern\",\n    \"false\",\n    \"final\",\n    \"finally\",\n    \"float\",\n    \"for\",\n    \"friend\",\n    \"gcnew\",\n    \"generic\",\n    \"goto\",\n    \"if\",\n    \"in\",\n    \"initonly\",\n    \"inline\",\n    \"int\",\n    \"interface\",\n    \"interior_ptr\",\n    \"internal\",\n    \"literal\",\n    \"long\",\n    \"mutable\",\n    \"namespace\",\n    \"new\",\n    \"noexcept\",\n    \"nullptr\",\n    \"__nullptr\",\n    \"operator\",\n    \"override\",\n    \"partial\",\n    \"pascal\",\n    \"pin_ptr\",\n    \"private\",\n    \"property\",\n    \"protected\",\n    \"public\",\n    \"ref\",\n    \"register\",\n    \"reinterpret_cast\",\n    \"restrict\",\n    \"return\",\n    \"safe_cast\",\n    \"sealed\",\n    \"short\",\n    \"signed\",\n    \"sizeof\",\n    \"static\",\n    \"static_assert\",\n    \"static_cast\",\n    \"struct\",\n    \"switch\",\n    \"template\",\n    \"this\",\n    \"thread_local\",\n    \"throw\",\n    \"tile_static\",\n    \"true\",\n    \"try\",\n    \"typedef\",\n    \"typeid\",\n    \"typename\",\n    \"union\",\n    \"unsigned\",\n    \"using\",\n    \"virtual\",\n    \"void\",\n    \"volatile\",\n    \"wchar_t\",\n    \"where\",\n    \"while\",\n    \"_asm\",\n    // reserved word with one underscores\n    \"_based\",\n    \"_cdecl\",\n    \"_declspec\",\n    \"_fastcall\",\n    \"_if_exists\",\n    \"_if_not_exists\",\n    \"_inline\",\n    \"_multiple_inheritance\",\n    \"_pascal\",\n    \"_single_inheritance\",\n    \"_stdcall\",\n    \"_virtual_inheritance\",\n    \"_w64\",\n    \"__abstract\",\n    // reserved word with two underscores\n    \"__alignof\",\n    \"__asm\",\n    \"__assume\",\n    \"__based\",\n    \"__box\",\n    \"__builtin_alignof\",\n    \"__cdecl\",\n    \"__clrcall\",\n    \"__declspec\",\n    \"__delegate\",\n    \"__event\",\n    \"__except\",\n    \"__fastcall\",\n    \"__finally\",\n    \"__forceinline\",\n    \"__gc\",\n    \"__hook\",\n    \"__identifier\",\n    \"__if_exists\",\n    \"__if_not_exists\",\n    \"__inline\",\n    \"__int128\",\n    \"__int16\",\n    \"__int32\",\n    \"__int64\",\n    \"__int8\",\n    \"__interface\",\n    \"__leave\",\n    \"__m128\",\n    \"__m128d\",\n    \"__m128i\",\n    \"__m256\",\n    \"__m256d\",\n    \"__m256i\",\n    \"__m512\",\n    \"__m512d\",\n    \"__m512i\",\n    \"__m64\",\n    \"__multiple_inheritance\",\n    \"__newslot\",\n    \"__nogc\",\n    \"__noop\",\n    \"__nounwind\",\n    \"__novtordisp\",\n    \"__pascal\",\n    \"__pin\",\n    \"__pragma\",\n    \"__property\",\n    \"__ptr32\",\n    \"__ptr64\",\n    \"__raise\",\n    \"__restrict\",\n    \"__resume\",\n    \"__sealed\",\n    \"__single_inheritance\",\n    \"__stdcall\",\n    \"__super\",\n    \"__thiscall\",\n    \"__try\",\n    \"__try_cast\",\n    \"__typeof\",\n    \"__unaligned\",\n    \"__unhook\",\n    \"__uuidof\",\n    \"__value\",\n    \"__virtual_inheritance\",\n    \"__w64\",\n    \"__wchar_t\"\n  ],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \"!\",\n    \"~\",\n    \"?\",\n    \":\",\n    \"==\",\n    \"<=\",\n    \">=\",\n    \"!=\",\n    \"&&\",\n    \"||\",\n    \"++\",\n    \"--\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"%\",\n    \"<<\",\n    \">>\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[0abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  integersuffix: /([uU](ll|LL|l|L)|(ll|LL|l|L)?[uU]?)/,\n  floatsuffix: /[fFlL]?/,\n  encoding: /u|u8|U|L/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // C++ 11 Raw String\n      [/@encoding?R\\\"(?:([^ ()\\\\\\t]*))\\(/, { token: \"string.raw.begin\", next: \"@raw.$1\" }],\n      // identifiers and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // The preprocessor checks must be before whitespace as they check /^\\s*#/ which\n      // otherwise fails to match later after other whitespace has been removed.\n      // Inclusion\n      [/^\\s*#\\s*include/, { token: \"keyword.directive.include\", next: \"@include\" }],\n      // Preprocessor directive\n      [/^\\s*#\\s*\\w+/, \"keyword.directive\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // [[ attributes ]].\n      [/\\[\\s*\\[/, { token: \"annotation\", next: \"@annotation\" }],\n      // delimiters and operators\n      [/[{}()<>\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/0[xX][0-9a-fA-F']*[0-9a-fA-F](@integersuffix)/, \"number.hex\"],\n      [/0[0-7']*[0-7](@integersuffix)/, \"number.octal\"],\n      [/0[bB][0-1']*[0-1](@integersuffix)/, \"number.binary\"],\n      [/\\d[\\d']*\\d(@integersuffix)/, \"number\"],\n      [/\\d(@integersuffix)/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string\"],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@doccomment\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*\\\\$/, \"comment\", \"@linecomment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    //For use with continuous line comments\n    linecomment: [\n      [/.*[^\\\\]$/, \"comment\", \"@pop\"],\n      [/[^]+/, \"comment\"]\n    ],\n    //Identical copy of comment above, except for the addition of .doc\n    doccomment: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    raw: [\n      [/[^)]+/, \"string.raw\"],\n      [/\\)$S2\\\"/, { token: \"string.raw.end\", next: \"@pop\" }],\n      [/\\)/, \"string.raw\"]\n    ],\n    annotation: [\n      { include: \"@whitespace\" },\n      [/using|alignas/, \"keyword\"],\n      [/[a-zA-Z0-9_]+/, \"annotation\"],\n      [/[,:]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [/\\]\\s*\\]/, { token: \"annotation\", next: \"@pop\" }]\n    ],\n    include: [\n      [\n        /(\\s*)(<)([^<>]*)(>)/,\n        [\n          \"\",\n          \"keyword.directive.include.begin\",\n          \"string.include.identifier\",\n          { token: \"keyword.directive.include.end\", next: \"@pop\" }\n        ]\n      ],\n      [\n        /(\\s*)(\")([^\"]*)(\")/,\n        [\n          \"\",\n          \"keyword.directive.include.begin\",\n          \"string.include.identifier\",\n          { token: \"keyword.directive.include.end\", next: \"@pop\" }\n        ]\n      ]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,KACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC7C,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,4BAA4B,CAC9C,IAAS,OAAO,+BAA+B,CAChD,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,OACd,SAAU,CACR,CAAE,MAAO,kBAAmB,KAAM,IAAK,MAAO,IAAK,CACnD,CAAE,MAAO,wBAAyB,KAAM,IAAK,MAAO,IAAK,CACzD,CAAE,MAAO,mBAAoB,KAAM,IAAK,MAAO,IAAK,CACpD,CAAE,MAAO,kBAAmB,KAAM,IAAK,MAAO,IAAK,CACpD,CACD,SAAU,qhDAuLT,CACD,UAAW,wGAoCV,CAED,QAAS,wBACT,QAAS,yEACT,cAAe,sCACf,YAAa,UACb,SAAU,WAEV,UAAW,CACT,KAAM,CAEJ,CAAC,mCAAoC,CAAE,MAAO,mBAAoB,KAAM,UAAW,CAAC,CAEpF,CACE,eACA,CACE,MAAO,CACL,YAAa,CAAE,MAAO,aAAc,CACpC,WAAY,aACb,CACF,CACF,CAID,CAAC,kBAAmB,CAAE,MAAO,4BAA6B,KAAM,WAAY,CAAC,CAE7E,CAAC,cAAe,oBAAoB,CAEpC,CAAE,QAAS,cAAe,CAE1B,CAAC,UAAW,CAAE,MAAO,aAAc,KAAM,cAAe,CAAC,CAEzD,CAAC,eAAgB,YAAY,CAC7B,CACE,WACA,CACE,MAAO,CACL,aAAc,YACd,WAAY,GACb,CACF,CACF,CAED,CAAC,uCAAwC,eAAe,CACxD,CAAC,yCAA0C,eAAe,CAC1D,CAAC,gDAAiD,aAAa,CAC/D,CAAC,gCAAiC,eAAe,CACjD,CAAC,oCAAqC,gBAAgB,CACtD,CAAC,6BAA8B,SAAS,CACxC,CAAC,qBAAsB,SAAS,CAEhC,CAAC,QAAS,YAAY,CAEtB,CAAC,kBAAmB,iBAAiB,CAErC,CAAC,IAAK,SAAU,UAAU,CAE1B,CAAC,WAAY,SAAS,CACtB,CAAC,mBAAoB,CAAC,SAAU,gBAAiB,SAAS,CAAC,CAC3D,CAAC,IAAK,iBAAiB,CACxB,CACD,WAAY,CACV,CAAC,aAAc,GAAG,CAClB,CAAC,eAAgB,cAAe,cAAc,CAC9C,CAAC,OAAQ,UAAW,WAAW,CAC/B,CAAC,YAAa,UAAW,eAAe,CACxC,CAAC,UAAW,UAAU,CACvB,CACD,QAAS,CACP,CAAC,UAAW,UAAU,CACtB,CAAC,OAAQ,UAAW,OAAO,CAC3B,CAAC,QAAS,UAAU,CACrB,CAED,YAAa,CACX,CAAC,WAAY,UAAW,OAAO,CAC/B,CAAC,OAAQ,UAAU,CACpB,CAED,WAAY,CACV,CAAC,UAAW,cAAc,CAC1B,CAAC,OAAQ,cAAe,OAAO,CAC/B,CAAC,QAAS,cAAc,CACzB,CACD,OAAQ,CACN,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,IAAK,SAAU,OAAO,CACxB,CACD,IAAK,CACH,CAAC,QAAS,aAAa,CACvB,CAAC,UAAW,CAAE,MAAO,iBAAkB,KAAM,OAAQ,CAAC,CACtD,CAAC,KAAM,aAAa,CACrB,CACD,WAAY,CACV,CAAE,QAAS,cAAe,CAC1B,CAAC,gBAAiB,UAAU,CAC5B,CAAC,gBAAiB,aAAa,CAC/B,CAAC,OAAQ,YAAY,CACrB,CAAC,OAAQ,YAAY,CACrB,CAAC,UAAW,CAAE,MAAO,aAAc,KAAM,OAAQ,CAAC,CACnD,CACD,QAAS,CACP,CACE,sBACA,CACE,GACA,kCACA,4BACA,CAAE,MAAO,gCAAiC,KAAM,OAAQ,CACzD,CACF,CACD,CACE,qBACA,CACE,GACA,kCACA,4BACA,CAAE,MAAO,gCAAiC,KAAM,OAAQ,CACzD,CACF,CACF,CACF,CACF"}