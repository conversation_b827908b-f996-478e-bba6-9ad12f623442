{"version": 3, "file": "azcli-C-qqIyZJ.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/azcli/azcli.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/azcli/azcli.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  }\n};\nvar language = {\n  defaultToken: \"keyword\",\n  ignoreCase: true,\n  tokenPostfix: \".azcli\",\n  str: /[^#\\s]/,\n  tokenizer: {\n    root: [\n      { include: \"@comment\" },\n      [\n        /\\s-+@str*\\s*/,\n        {\n          cases: {\n            \"@eos\": { token: \"key.identifier\", next: \"@popall\" },\n            \"@default\": { token: \"key.identifier\", next: \"@type\" }\n          }\n        }\n      ],\n      [\n        /^-+@str*\\s*/,\n        {\n          cases: {\n            \"@eos\": { token: \"key.identifier\", next: \"@popall\" },\n            \"@default\": { token: \"key.identifier\", next: \"@type\" }\n          }\n        }\n      ]\n    ],\n    type: [\n      { include: \"@comment\" },\n      [\n        /-+@str*\\s*/,\n        {\n          cases: {\n            \"@eos\": { token: \"key.identifier\", next: \"@popall\" },\n            \"@default\": \"key.identifier\"\n          }\n        }\n      ],\n      [\n        /@str+\\s*/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    comment: [\n      [\n        /#.*$/,\n        {\n          cases: {\n            \"@eos\": { token: \"comment\", next: \"@popall\" }\n          }\n        }\n      ]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,IACd,CACF,CACG,EAAW,CACb,aAAc,UACd,WAAY,GACZ,aAAc,SACd,IAAK,SACL,UAAW,CACT,KAAM,CACJ,CAAE,QAAS,WAAY,CACvB,CACE,eACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,iBAAkB,KAAM,UAAW,CACpD,WAAY,CAAE,MAAO,iBAAkB,KAAM,QAAS,CACvD,CACF,CACF,CACD,CACE,cACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,iBAAkB,KAAM,UAAW,CACpD,WAAY,CAAE,MAAO,iBAAkB,KAAM,QAAS,CACvD,CACF,CACF,CACF,CACD,KAAM,CACJ,CAAE,QAAS,WAAY,CACvB,CACE,aACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,iBAAkB,KAAM,UAAW,CACpD,WAAY,iBACb,CACF,CACF,CACD,CACE,WACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,SAAU,KAAM,UAAW,CAC5C,WAAY,SACb,CACF,CACF,CACF,CACD,QAAS,CACP,CACE,OACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,UAAW,KAAM,UAAW,CAC9C,CACF,CACF,CACF,CACF,CACF"}