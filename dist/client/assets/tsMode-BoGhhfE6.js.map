{"version": 3, "file": "tsMode-BoGhhfE6.js", "names": ["monaco_editor_core_star"], "sources": ["../../../node_modules/monaco-editor/esm/vs/language/typescript/tsMode.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/language/typescript/workerManager.ts\nvar WorkerManager = class {\n  constructor(_modeId, _defaults) {\n    this._modeId = _modeId;\n    this._defaults = _defaults;\n    this._worker = null;\n    this._client = null;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n    this._updateExtraLibsToken = 0;\n    this._extraLibsChangeListener = this._defaults.onDidExtraLibsChange(\n      () => this._updateExtraLibs()\n    );\n  }\n  dispose() {\n    this._configChangeListener.dispose();\n    this._extraLibsChangeListener.dispose();\n    this._stopWorker();\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  async _updateExtraLibs() {\n    if (!this._worker) {\n      return;\n    }\n    const myToken = ++this._updateExtraLibsToken;\n    const proxy = await this._worker.getProxy();\n    if (this._updateExtraLibsToken !== myToken) {\n      return;\n    }\n    proxy.updateExtraLibs(this._defaults.getExtraLibs());\n  }\n  _getClient() {\n    if (!this._client) {\n      this._client = (async () => {\n        this._worker = monaco_editor_core_exports.editor.createWebWorker({\n          // module that exports the create() method and returns a `TypeScriptWorker` instance\n          moduleId: \"vs/language/typescript/tsWorker\",\n          label: this._modeId,\n          keepIdleModels: true,\n          // passed in to the create() method\n          createData: {\n            compilerOptions: this._defaults.getCompilerOptions(),\n            extraLibs: this._defaults.getExtraLibs(),\n            customWorkerPath: this._defaults.workerOptions.customWorkerPath,\n            inlayHintsOptions: this._defaults.inlayHintsOptions\n          }\n        });\n        if (this._defaults.getEagerModelSync()) {\n          return await this._worker.withSyncedResources(\n            monaco_editor_core_exports.editor.getModels().filter((model) => model.getLanguageId() === this._modeId).map((model) => model.uri)\n          );\n        }\n        return await this._worker.getProxy();\n      })();\n    }\n    return this._client;\n  }\n  async getLanguageServiceWorker(...resources) {\n    const client = await this._getClient();\n    if (this._worker) {\n      await this._worker.withSyncedResources(resources);\n    }\n    return client;\n  }\n};\n\n// src/language/typescript/languageFeatures.ts\nimport {\n  typescriptDefaults\n} from \"./monaco.contribution.js\";\n\n// src/language/typescript/lib/lib.index.ts\nvar libFileSet = {};\nlibFileSet[\"lib.d.ts\"] = true;\nlibFileSet[\"lib.decorators.d.ts\"] = true;\nlibFileSet[\"lib.decorators.legacy.d.ts\"] = true;\nlibFileSet[\"lib.dom.asynciterable.d.ts\"] = true;\nlibFileSet[\"lib.dom.d.ts\"] = true;\nlibFileSet[\"lib.dom.iterable.d.ts\"] = true;\nlibFileSet[\"lib.es2015.collection.d.ts\"] = true;\nlibFileSet[\"lib.es2015.core.d.ts\"] = true;\nlibFileSet[\"lib.es2015.d.ts\"] = true;\nlibFileSet[\"lib.es2015.generator.d.ts\"] = true;\nlibFileSet[\"lib.es2015.iterable.d.ts\"] = true;\nlibFileSet[\"lib.es2015.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2015.proxy.d.ts\"] = true;\nlibFileSet[\"lib.es2015.reflect.d.ts\"] = true;\nlibFileSet[\"lib.es2015.symbol.d.ts\"] = true;\nlibFileSet[\"lib.es2015.symbol.wellknown.d.ts\"] = true;\nlibFileSet[\"lib.es2016.array.include.d.ts\"] = true;\nlibFileSet[\"lib.es2016.d.ts\"] = true;\nlibFileSet[\"lib.es2016.full.d.ts\"] = true;\nlibFileSet[\"lib.es2016.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2017.d.ts\"] = true;\nlibFileSet[\"lib.es2017.date.d.ts\"] = true;\nlibFileSet[\"lib.es2017.full.d.ts\"] = true;\nlibFileSet[\"lib.es2017.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2017.object.d.ts\"] = true;\nlibFileSet[\"lib.es2017.sharedmemory.d.ts\"] = true;\nlibFileSet[\"lib.es2017.string.d.ts\"] = true;\nlibFileSet[\"lib.es2017.typedarrays.d.ts\"] = true;\nlibFileSet[\"lib.es2018.asyncgenerator.d.ts\"] = true;\nlibFileSet[\"lib.es2018.asynciterable.d.ts\"] = true;\nlibFileSet[\"lib.es2018.d.ts\"] = true;\nlibFileSet[\"lib.es2018.full.d.ts\"] = true;\nlibFileSet[\"lib.es2018.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2018.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2018.regexp.d.ts\"] = true;\nlibFileSet[\"lib.es2019.array.d.ts\"] = true;\nlibFileSet[\"lib.es2019.d.ts\"] = true;\nlibFileSet[\"lib.es2019.full.d.ts\"] = true;\nlibFileSet[\"lib.es2019.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2019.object.d.ts\"] = true;\nlibFileSet[\"lib.es2019.string.d.ts\"] = true;\nlibFileSet[\"lib.es2019.symbol.d.ts\"] = true;\nlibFileSet[\"lib.es2020.bigint.d.ts\"] = true;\nlibFileSet[\"lib.es2020.d.ts\"] = true;\nlibFileSet[\"lib.es2020.date.d.ts\"] = true;\nlibFileSet[\"lib.es2020.full.d.ts\"] = true;\nlibFileSet[\"lib.es2020.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2020.number.d.ts\"] = true;\nlibFileSet[\"lib.es2020.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2020.sharedmemory.d.ts\"] = true;\nlibFileSet[\"lib.es2020.string.d.ts\"] = true;\nlibFileSet[\"lib.es2020.symbol.wellknown.d.ts\"] = true;\nlibFileSet[\"lib.es2021.d.ts\"] = true;\nlibFileSet[\"lib.es2021.full.d.ts\"] = true;\nlibFileSet[\"lib.es2021.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2021.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2021.string.d.ts\"] = true;\nlibFileSet[\"lib.es2021.weakref.d.ts\"] = true;\nlibFileSet[\"lib.es2022.array.d.ts\"] = true;\nlibFileSet[\"lib.es2022.d.ts\"] = true;\nlibFileSet[\"lib.es2022.error.d.ts\"] = true;\nlibFileSet[\"lib.es2022.full.d.ts\"] = true;\nlibFileSet[\"lib.es2022.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2022.object.d.ts\"] = true;\nlibFileSet[\"lib.es2022.regexp.d.ts\"] = true;\nlibFileSet[\"lib.es2022.sharedmemory.d.ts\"] = true;\nlibFileSet[\"lib.es2022.string.d.ts\"] = true;\nlibFileSet[\"lib.es2023.array.d.ts\"] = true;\nlibFileSet[\"lib.es2023.collection.d.ts\"] = true;\nlibFileSet[\"lib.es2023.d.ts\"] = true;\nlibFileSet[\"lib.es2023.full.d.ts\"] = true;\nlibFileSet[\"lib.es5.d.ts\"] = true;\nlibFileSet[\"lib.es6.d.ts\"] = true;\nlibFileSet[\"lib.esnext.collection.d.ts\"] = true;\nlibFileSet[\"lib.esnext.d.ts\"] = true;\nlibFileSet[\"lib.esnext.decorators.d.ts\"] = true;\nlibFileSet[\"lib.esnext.disposable.d.ts\"] = true;\nlibFileSet[\"lib.esnext.full.d.ts\"] = true;\nlibFileSet[\"lib.esnext.intl.d.ts\"] = true;\nlibFileSet[\"lib.esnext.object.d.ts\"] = true;\nlibFileSet[\"lib.esnext.promise.d.ts\"] = true;\nlibFileSet[\"lib.scripthost.d.ts\"] = true;\nlibFileSet[\"lib.webworker.asynciterable.d.ts\"] = true;\nlibFileSet[\"lib.webworker.d.ts\"] = true;\nlibFileSet[\"lib.webworker.importscripts.d.ts\"] = true;\nlibFileSet[\"lib.webworker.iterable.d.ts\"] = true;\n\n// src/language/typescript/languageFeatures.ts\nfunction flattenDiagnosticMessageText(diag, newLine, indent = 0) {\n  if (typeof diag === \"string\") {\n    return diag;\n  } else if (diag === void 0) {\n    return \"\";\n  }\n  let result = \"\";\n  if (indent) {\n    result += newLine;\n    for (let i = 0; i < indent; i++) {\n      result += \"  \";\n    }\n  }\n  result += diag.messageText;\n  indent++;\n  if (diag.next) {\n    for (const kid of diag.next) {\n      result += flattenDiagnosticMessageText(kid, newLine, indent);\n    }\n  }\n  return result;\n}\nfunction displayPartsToString(displayParts) {\n  if (displayParts) {\n    return displayParts.map((displayPart) => displayPart.text).join(\"\");\n  }\n  return \"\";\n}\nvar Adapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  // protected _positionToOffset(model: editor.ITextModel, position: monaco.IPosition): number {\n  // \treturn model.getOffsetAt(position);\n  // }\n  // protected _offsetToPosition(model: editor.ITextModel, offset: number): monaco.IPosition {\n  // \treturn model.getPositionAt(offset);\n  // }\n  _textSpanToRange(model, span) {\n    let p1 = model.getPositionAt(span.start);\n    let p2 = model.getPositionAt(span.start + span.length);\n    let { lineNumber: startLineNumber, column: startColumn } = p1;\n    let { lineNumber: endLineNumber, column: endColumn } = p2;\n    return { startLineNumber, startColumn, endLineNumber, endColumn };\n  }\n};\nvar LibFiles = class {\n  constructor(_worker) {\n    this._worker = _worker;\n    this._libFiles = {};\n    this._hasFetchedLibFiles = false;\n    this._fetchLibFilesPromise = null;\n  }\n  isLibFile(uri) {\n    if (!uri) {\n      return false;\n    }\n    if (uri.path.indexOf(\"/lib.\") === 0) {\n      return !!libFileSet[uri.path.slice(1)];\n    }\n    return false;\n  }\n  getOrCreateModel(fileName) {\n    const uri = monaco_editor_core_exports.Uri.parse(fileName);\n    const model = monaco_editor_core_exports.editor.getModel(uri);\n    if (model) {\n      return model;\n    }\n    if (this.isLibFile(uri) && this._hasFetchedLibFiles) {\n      return monaco_editor_core_exports.editor.createModel(this._libFiles[uri.path.slice(1)], \"typescript\", uri);\n    }\n    const matchedLibFile = typescriptDefaults.getExtraLibs()[fileName];\n    if (matchedLibFile) {\n      return monaco_editor_core_exports.editor.createModel(matchedLibFile.content, \"typescript\", uri);\n    }\n    return null;\n  }\n  _containsLibFile(uris) {\n    for (let uri of uris) {\n      if (this.isLibFile(uri)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  async fetchLibFilesIfNecessary(uris) {\n    if (!this._containsLibFile(uris)) {\n      return;\n    }\n    await this._fetchLibFiles();\n  }\n  _fetchLibFiles() {\n    if (!this._fetchLibFilesPromise) {\n      this._fetchLibFilesPromise = this._worker().then((w) => w.getLibFiles()).then((libFiles) => {\n        this._hasFetchedLibFiles = true;\n        this._libFiles = libFiles;\n      });\n    }\n    return this._fetchLibFilesPromise;\n  }\n};\nvar DiagnosticsAdapter = class extends Adapter {\n  constructor(_libFiles, _defaults, _selector, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n    this._defaults = _defaults;\n    this._selector = _selector;\n    this._disposables = [];\n    this._listener = /* @__PURE__ */ Object.create(null);\n    const onModelAdd = (model) => {\n      if (model.getLanguageId() !== _selector) {\n        return;\n      }\n      const maybeValidate = () => {\n        const { onlyVisible } = this._defaults.getDiagnosticsOptions();\n        if (onlyVisible) {\n          if (model.isAttachedToEditor()) {\n            this._doValidate(model);\n          }\n        } else {\n          this._doValidate(model);\n        }\n      };\n      let handle;\n      const changeSubscription = model.onDidChangeContent(() => {\n        clearTimeout(handle);\n        handle = window.setTimeout(maybeValidate, 500);\n      });\n      const visibleSubscription = model.onDidChangeAttached(() => {\n        const { onlyVisible } = this._defaults.getDiagnosticsOptions();\n        if (onlyVisible) {\n          if (model.isAttachedToEditor()) {\n            maybeValidate();\n          } else {\n            monaco_editor_core_exports.editor.setModelMarkers(model, this._selector, []);\n          }\n        }\n      });\n      this._listener[model.uri.toString()] = {\n        dispose() {\n          changeSubscription.dispose();\n          visibleSubscription.dispose();\n          clearTimeout(handle);\n        }\n      };\n      maybeValidate();\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._selector, []);\n      const key = model.uri.toString();\n      if (this._listener[key]) {\n        this._listener[key].dispose();\n        delete this._listener[key];\n      }\n    };\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidCreateModel((model) => onModelAdd(model))\n    );\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        onModelRemoved(event.model);\n        onModelAdd(event.model);\n      })\n    );\n    this._disposables.push({\n      dispose() {\n        for (const model of monaco_editor_core_exports.editor.getModels()) {\n          onModelRemoved(model);\n        }\n      }\n    });\n    const recomputeDiagostics = () => {\n      for (const model of monaco_editor_core_exports.editor.getModels()) {\n        onModelRemoved(model);\n        onModelAdd(model);\n      }\n    };\n    this._disposables.push(this._defaults.onDidChange(recomputeDiagostics));\n    this._disposables.push(this._defaults.onDidExtraLibsChange(recomputeDiagostics));\n    monaco_editor_core_exports.editor.getModels().forEach((model) => onModelAdd(model));\n  }\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables = [];\n  }\n  async _doValidate(model) {\n    const worker = await this._worker(model.uri);\n    if (model.isDisposed()) {\n      return;\n    }\n    const promises = [];\n    const { noSyntaxValidation, noSemanticValidation, noSuggestionDiagnostics } = this._defaults.getDiagnosticsOptions();\n    if (!noSyntaxValidation) {\n      promises.push(worker.getSyntacticDiagnostics(model.uri.toString()));\n    }\n    if (!noSemanticValidation) {\n      promises.push(worker.getSemanticDiagnostics(model.uri.toString()));\n    }\n    if (!noSuggestionDiagnostics) {\n      promises.push(worker.getSuggestionDiagnostics(model.uri.toString()));\n    }\n    const allDiagnostics = await Promise.all(promises);\n    if (!allDiagnostics || model.isDisposed()) {\n      return;\n    }\n    const diagnostics = allDiagnostics.reduce((p, c) => c.concat(p), []).filter(\n      (d) => (this._defaults.getDiagnosticsOptions().diagnosticCodesToIgnore || []).indexOf(d.code) === -1\n    );\n    const relatedUris = diagnostics.map((d) => d.relatedInformation || []).reduce((p, c) => c.concat(p), []).map(\n      (relatedInformation) => relatedInformation.file ? monaco_editor_core_exports.Uri.parse(relatedInformation.file.fileName) : null\n    );\n    await this._libFiles.fetchLibFilesIfNecessary(relatedUris);\n    if (model.isDisposed()) {\n      return;\n    }\n    monaco_editor_core_exports.editor.setModelMarkers(\n      model,\n      this._selector,\n      diagnostics.map((d) => this._convertDiagnostics(model, d))\n    );\n  }\n  _convertDiagnostics(model, diag) {\n    const diagStart = diag.start || 0;\n    const diagLength = diag.length || 1;\n    const { lineNumber: startLineNumber, column: startColumn } = model.getPositionAt(diagStart);\n    const { lineNumber: endLineNumber, column: endColumn } = model.getPositionAt(\n      diagStart + diagLength\n    );\n    const tags = [];\n    if (diag.reportsUnnecessary) {\n      tags.push(monaco_editor_core_exports.MarkerTag.Unnecessary);\n    }\n    if (diag.reportsDeprecated) {\n      tags.push(monaco_editor_core_exports.MarkerTag.Deprecated);\n    }\n    return {\n      severity: this._tsDiagnosticCategoryToMarkerSeverity(diag.category),\n      startLineNumber,\n      startColumn,\n      endLineNumber,\n      endColumn,\n      message: flattenDiagnosticMessageText(diag.messageText, \"\\n\"),\n      code: diag.code.toString(),\n      tags,\n      relatedInformation: this._convertRelatedInformation(model, diag.relatedInformation)\n    };\n  }\n  _convertRelatedInformation(model, relatedInformation) {\n    if (!relatedInformation) {\n      return [];\n    }\n    const result = [];\n    relatedInformation.forEach((info) => {\n      let relatedResource = model;\n      if (info.file) {\n        relatedResource = this._libFiles.getOrCreateModel(info.file.fileName);\n      }\n      if (!relatedResource) {\n        return;\n      }\n      const infoStart = info.start || 0;\n      const infoLength = info.length || 1;\n      const { lineNumber: startLineNumber, column: startColumn } = relatedResource.getPositionAt(infoStart);\n      const { lineNumber: endLineNumber, column: endColumn } = relatedResource.getPositionAt(\n        infoStart + infoLength\n      );\n      result.push({\n        resource: relatedResource.uri,\n        startLineNumber,\n        startColumn,\n        endLineNumber,\n        endColumn,\n        message: flattenDiagnosticMessageText(info.messageText, \"\\n\")\n      });\n    });\n    return result;\n  }\n  _tsDiagnosticCategoryToMarkerSeverity(category) {\n    switch (category) {\n      case 1 /* Error */:\n        return monaco_editor_core_exports.MarkerSeverity.Error;\n      case 3 /* Message */:\n        return monaco_editor_core_exports.MarkerSeverity.Info;\n      case 0 /* Warning */:\n        return monaco_editor_core_exports.MarkerSeverity.Warning;\n      case 2 /* Suggestion */:\n        return monaco_editor_core_exports.MarkerSeverity.Hint;\n    }\n    return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n};\nvar SuggestAdapter = class _SuggestAdapter extends Adapter {\n  get triggerCharacters() {\n    return [\".\"];\n  }\n  async provideCompletionItems(model, position, _context, token) {\n    const wordInfo = model.getWordUntilPosition(position);\n    const wordRange = new monaco_editor_core_exports.Range(\n      position.lineNumber,\n      wordInfo.startColumn,\n      position.lineNumber,\n      wordInfo.endColumn\n    );\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const info = await worker.getCompletionsAtPosition(resource.toString(), offset);\n    if (!info || model.isDisposed()) {\n      return;\n    }\n    const suggestions = info.entries.map((entry) => {\n      let range = wordRange;\n      if (entry.replacementSpan) {\n        const p1 = model.getPositionAt(entry.replacementSpan.start);\n        const p2 = model.getPositionAt(entry.replacementSpan.start + entry.replacementSpan.length);\n        range = new monaco_editor_core_exports.Range(p1.lineNumber, p1.column, p2.lineNumber, p2.column);\n      }\n      const tags = [];\n      if (entry.kindModifiers !== void 0 && entry.kindModifiers.indexOf(\"deprecated\") !== -1) {\n        tags.push(monaco_editor_core_exports.languages.CompletionItemTag.Deprecated);\n      }\n      return {\n        uri: resource,\n        position,\n        offset,\n        range,\n        label: entry.name,\n        insertText: entry.name,\n        sortText: entry.sortText,\n        kind: _SuggestAdapter.convertKind(entry.kind),\n        tags\n      };\n    });\n    return {\n      suggestions\n    };\n  }\n  async resolveCompletionItem(item, token) {\n    const myItem = item;\n    const resource = myItem.uri;\n    const position = myItem.position;\n    const offset = myItem.offset;\n    const worker = await this._worker(resource);\n    const details = await worker.getCompletionEntryDetails(\n      resource.toString(),\n      offset,\n      myItem.label\n    );\n    if (!details) {\n      return myItem;\n    }\n    return {\n      uri: resource,\n      position,\n      label: details.name,\n      kind: _SuggestAdapter.convertKind(details.kind),\n      detail: displayPartsToString(details.displayParts),\n      documentation: {\n        value: _SuggestAdapter.createDocumentationString(details)\n      }\n    };\n  }\n  static convertKind(kind) {\n    switch (kind) {\n      case Kind.primitiveType:\n      case Kind.keyword:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Keyword;\n      case Kind.variable:\n      case Kind.localVariable:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Variable;\n      case Kind.memberVariable:\n      case Kind.memberGetAccessor:\n      case Kind.memberSetAccessor:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Field;\n      case Kind.function:\n      case Kind.memberFunction:\n      case Kind.constructSignature:\n      case Kind.callSignature:\n      case Kind.indexSignature:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Function;\n      case Kind.enum:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Enum;\n      case Kind.module:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Module;\n      case Kind.class:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Class;\n      case Kind.interface:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Interface;\n      case Kind.warning:\n        return monaco_editor_core_exports.languages.CompletionItemKind.File;\n    }\n    return monaco_editor_core_exports.languages.CompletionItemKind.Property;\n  }\n  static createDocumentationString(details) {\n    let documentationString = displayPartsToString(details.documentation);\n    if (details.tags) {\n      for (const tag of details.tags) {\n        documentationString += `\n\n${tagToString(tag)}`;\n      }\n    }\n    return documentationString;\n  }\n};\nfunction tagToString(tag) {\n  let tagLabel = `*@${tag.name}*`;\n  if (tag.name === \"param\" && tag.text) {\n    const [paramName, ...rest] = tag.text;\n    tagLabel += `\\`${paramName.text}\\``;\n    if (rest.length > 0)\n      tagLabel += ` \\u2014 ${rest.map((r) => r.text).join(\" \")}`;\n  } else if (Array.isArray(tag.text)) {\n    tagLabel += ` \\u2014 ${tag.text.map((r) => r.text).join(\" \")}`;\n  } else if (tag.text) {\n    tagLabel += ` \\u2014 ${tag.text}`;\n  }\n  return tagLabel;\n}\nvar SignatureHelpAdapter = class _SignatureHelpAdapter extends Adapter {\n  constructor() {\n    super(...arguments);\n    this.signatureHelpTriggerCharacters = [\"(\", \",\"];\n  }\n  static _toSignatureHelpTriggerReason(context) {\n    switch (context.triggerKind) {\n      case monaco_editor_core_exports.languages.SignatureHelpTriggerKind.TriggerCharacter:\n        if (context.triggerCharacter) {\n          if (context.isRetrigger) {\n            return { kind: \"retrigger\", triggerCharacter: context.triggerCharacter };\n          } else {\n            return { kind: \"characterTyped\", triggerCharacter: context.triggerCharacter };\n          }\n        } else {\n          return { kind: \"invoked\" };\n        }\n      case monaco_editor_core_exports.languages.SignatureHelpTriggerKind.ContentChange:\n        return context.isRetrigger ? { kind: \"retrigger\" } : { kind: \"invoked\" };\n      case monaco_editor_core_exports.languages.SignatureHelpTriggerKind.Invoke:\n      default:\n        return { kind: \"invoked\" };\n    }\n  }\n  async provideSignatureHelp(model, position, token, context) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const info = await worker.getSignatureHelpItems(resource.toString(), offset, {\n      triggerReason: _SignatureHelpAdapter._toSignatureHelpTriggerReason(context)\n    });\n    if (!info || model.isDisposed()) {\n      return;\n    }\n    const ret = {\n      activeSignature: info.selectedItemIndex,\n      activeParameter: info.argumentIndex,\n      signatures: []\n    };\n    info.items.forEach((item) => {\n      const signature = {\n        label: \"\",\n        parameters: []\n      };\n      signature.documentation = {\n        value: displayPartsToString(item.documentation)\n      };\n      signature.label += displayPartsToString(item.prefixDisplayParts);\n      item.parameters.forEach((p, i, a) => {\n        const label = displayPartsToString(p.displayParts);\n        const parameter = {\n          label,\n          documentation: {\n            value: displayPartsToString(p.documentation)\n          }\n        };\n        signature.label += label;\n        signature.parameters.push(parameter);\n        if (i < a.length - 1) {\n          signature.label += displayPartsToString(item.separatorDisplayParts);\n        }\n      });\n      signature.label += displayPartsToString(item.suffixDisplayParts);\n      ret.signatures.push(signature);\n    });\n    return {\n      value: ret,\n      dispose() {\n      }\n    };\n  }\n};\nvar QuickInfoAdapter = class extends Adapter {\n  async provideHover(model, position, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const info = await worker.getQuickInfoAtPosition(resource.toString(), offset);\n    if (!info || model.isDisposed()) {\n      return;\n    }\n    const documentation = displayPartsToString(info.documentation);\n    const tags = info.tags ? info.tags.map((tag) => tagToString(tag)).join(\"  \\n\\n\") : \"\";\n    const contents = displayPartsToString(info.displayParts);\n    return {\n      range: this._textSpanToRange(model, info.textSpan),\n      contents: [\n        {\n          value: \"```typescript\\n\" + contents + \"\\n```\\n\"\n        },\n        {\n          value: documentation + (tags ? \"\\n\\n\" + tags : \"\")\n        }\n      ]\n    };\n  }\n};\nvar DocumentHighlightAdapter = class extends Adapter {\n  async provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const entries = await worker.getDocumentHighlights(resource.toString(), offset, [\n      resource.toString()\n    ]);\n    if (!entries || model.isDisposed()) {\n      return;\n    }\n    return entries.flatMap((entry) => {\n      return entry.highlightSpans.map((highlightSpans) => {\n        return {\n          range: this._textSpanToRange(model, highlightSpans.textSpan),\n          kind: highlightSpans.kind === \"writtenReference\" ? monaco_editor_core_exports.languages.DocumentHighlightKind.Write : monaco_editor_core_exports.languages.DocumentHighlightKind.Text\n        };\n      });\n    });\n  }\n};\nvar DefinitionAdapter = class extends Adapter {\n  constructor(_libFiles, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n  }\n  async provideDefinition(model, position, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const entries = await worker.getDefinitionAtPosition(resource.toString(), offset);\n    if (!entries || model.isDisposed()) {\n      return;\n    }\n    await this._libFiles.fetchLibFilesIfNecessary(\n      entries.map((entry) => monaco_editor_core_exports.Uri.parse(entry.fileName))\n    );\n    if (model.isDisposed()) {\n      return;\n    }\n    const result = [];\n    for (let entry of entries) {\n      const refModel = this._libFiles.getOrCreateModel(entry.fileName);\n      if (refModel) {\n        result.push({\n          uri: refModel.uri,\n          range: this._textSpanToRange(refModel, entry.textSpan)\n        });\n      }\n    }\n    return result;\n  }\n};\nvar ReferenceAdapter = class extends Adapter {\n  constructor(_libFiles, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n  }\n  async provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const entries = await worker.getReferencesAtPosition(resource.toString(), offset);\n    if (!entries || model.isDisposed()) {\n      return;\n    }\n    await this._libFiles.fetchLibFilesIfNecessary(\n      entries.map((entry) => monaco_editor_core_exports.Uri.parse(entry.fileName))\n    );\n    if (model.isDisposed()) {\n      return;\n    }\n    const result = [];\n    for (let entry of entries) {\n      const refModel = this._libFiles.getOrCreateModel(entry.fileName);\n      if (refModel) {\n        result.push({\n          uri: refModel.uri,\n          range: this._textSpanToRange(refModel, entry.textSpan)\n        });\n      }\n    }\n    return result;\n  }\n};\nvar OutlineAdapter = class extends Adapter {\n  async provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const root = await worker.getNavigationTree(resource.toString());\n    if (!root || model.isDisposed()) {\n      return;\n    }\n    const convert = (item, containerLabel) => {\n      const result2 = {\n        name: item.text,\n        detail: \"\",\n        kind: outlineTypeTable[item.kind] || monaco_editor_core_exports.languages.SymbolKind.Variable,\n        range: this._textSpanToRange(model, item.spans[0]),\n        selectionRange: this._textSpanToRange(model, item.spans[0]),\n        tags: [],\n        children: item.childItems?.map((child) => convert(child, item.text)),\n        containerName: containerLabel\n      };\n      return result2;\n    };\n    const result = root.childItems ? root.childItems.map((item) => convert(item)) : [];\n    return result;\n  }\n};\nvar Kind = class {\n  static {\n    this.unknown = \"\";\n  }\n  static {\n    this.keyword = \"keyword\";\n  }\n  static {\n    this.script = \"script\";\n  }\n  static {\n    this.module = \"module\";\n  }\n  static {\n    this.class = \"class\";\n  }\n  static {\n    this.interface = \"interface\";\n  }\n  static {\n    this.type = \"type\";\n  }\n  static {\n    this.enum = \"enum\";\n  }\n  static {\n    this.variable = \"var\";\n  }\n  static {\n    this.localVariable = \"local var\";\n  }\n  static {\n    this.function = \"function\";\n  }\n  static {\n    this.localFunction = \"local function\";\n  }\n  static {\n    this.memberFunction = \"method\";\n  }\n  static {\n    this.memberGetAccessor = \"getter\";\n  }\n  static {\n    this.memberSetAccessor = \"setter\";\n  }\n  static {\n    this.memberVariable = \"property\";\n  }\n  static {\n    this.constructorImplementation = \"constructor\";\n  }\n  static {\n    this.callSignature = \"call\";\n  }\n  static {\n    this.indexSignature = \"index\";\n  }\n  static {\n    this.constructSignature = \"construct\";\n  }\n  static {\n    this.parameter = \"parameter\";\n  }\n  static {\n    this.typeParameter = \"type parameter\";\n  }\n  static {\n    this.primitiveType = \"primitive type\";\n  }\n  static {\n    this.label = \"label\";\n  }\n  static {\n    this.alias = \"alias\";\n  }\n  static {\n    this.const = \"const\";\n  }\n  static {\n    this.let = \"let\";\n  }\n  static {\n    this.warning = \"warning\";\n  }\n};\nvar outlineTypeTable = /* @__PURE__ */ Object.create(null);\noutlineTypeTable[Kind.module] = monaco_editor_core_exports.languages.SymbolKind.Module;\noutlineTypeTable[Kind.class] = monaco_editor_core_exports.languages.SymbolKind.Class;\noutlineTypeTable[Kind.enum] = monaco_editor_core_exports.languages.SymbolKind.Enum;\noutlineTypeTable[Kind.interface] = monaco_editor_core_exports.languages.SymbolKind.Interface;\noutlineTypeTable[Kind.memberFunction] = monaco_editor_core_exports.languages.SymbolKind.Method;\noutlineTypeTable[Kind.memberVariable] = monaco_editor_core_exports.languages.SymbolKind.Property;\noutlineTypeTable[Kind.memberGetAccessor] = monaco_editor_core_exports.languages.SymbolKind.Property;\noutlineTypeTable[Kind.memberSetAccessor] = monaco_editor_core_exports.languages.SymbolKind.Property;\noutlineTypeTable[Kind.variable] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.const] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.localVariable] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.variable] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.function] = monaco_editor_core_exports.languages.SymbolKind.Function;\noutlineTypeTable[Kind.localFunction] = monaco_editor_core_exports.languages.SymbolKind.Function;\nvar FormatHelper = class extends Adapter {\n  static _convertOptions(options) {\n    return {\n      ConvertTabsToSpaces: options.insertSpaces,\n      TabSize: options.tabSize,\n      IndentSize: options.tabSize,\n      IndentStyle: 2 /* Smart */,\n      NewLineCharacter: \"\\n\",\n      InsertSpaceAfterCommaDelimiter: true,\n      InsertSpaceAfterSemicolonInForStatements: true,\n      InsertSpaceBeforeAndAfterBinaryOperators: true,\n      InsertSpaceAfterKeywordsInControlFlowStatements: true,\n      InsertSpaceAfterFunctionKeywordForAnonymousFunctions: true,\n      InsertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis: false,\n      InsertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets: false,\n      InsertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces: false,\n      PlaceOpenBraceOnNewLineForControlBlocks: false,\n      PlaceOpenBraceOnNewLineForFunctions: false\n    };\n  }\n  _convertTextChanges(model, change) {\n    return {\n      text: change.newText,\n      range: this._textSpanToRange(model, change.span)\n    };\n  }\n};\nvar FormatAdapter = class extends FormatHelper {\n  constructor() {\n    super(...arguments);\n    this.canFormatMultipleRanges = false;\n  }\n  async provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    const startOffset = model.getOffsetAt({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const endOffset = model.getOffsetAt({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const edits = await worker.getFormattingEditsForRange(\n      resource.toString(),\n      startOffset,\n      endOffset,\n      FormatHelper._convertOptions(options)\n    );\n    if (!edits || model.isDisposed()) {\n      return;\n    }\n    return edits.map((edit) => this._convertTextChanges(model, edit));\n  }\n};\nvar FormatOnTypeAdapter = class extends FormatHelper {\n  get autoFormatTriggerCharacters() {\n    return [\";\", \"}\", \"\\n\"];\n  }\n  async provideOnTypeFormattingEdits(model, position, ch, options, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const edits = await worker.getFormattingEditsAfterKeystroke(\n      resource.toString(),\n      offset,\n      ch,\n      FormatHelper._convertOptions(options)\n    );\n    if (!edits || model.isDisposed()) {\n      return;\n    }\n    return edits.map((edit) => this._convertTextChanges(model, edit));\n  }\n};\nvar CodeActionAdaptor = class extends FormatHelper {\n  async provideCodeActions(model, range, context, token) {\n    const resource = model.uri;\n    const start = model.getOffsetAt({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const end = model.getOffsetAt({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    const formatOptions = FormatHelper._convertOptions(model.getOptions());\n    const errorCodes = context.markers.filter((m) => m.code).map((m) => m.code).map(Number);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const codeFixes = await worker.getCodeFixesAtPosition(\n      resource.toString(),\n      start,\n      end,\n      errorCodes,\n      formatOptions\n    );\n    if (!codeFixes || model.isDisposed()) {\n      return { actions: [], dispose: () => {\n      } };\n    }\n    const actions = codeFixes.filter((fix) => {\n      return fix.changes.filter((change) => change.isNewFile).length === 0;\n    }).map((fix) => {\n      return this._tsCodeFixActionToMonacoCodeAction(model, context, fix);\n    });\n    return {\n      actions,\n      dispose: () => {\n      }\n    };\n  }\n  _tsCodeFixActionToMonacoCodeAction(model, context, codeFix) {\n    const edits = [];\n    for (const change of codeFix.changes) {\n      for (const textChange of change.textChanges) {\n        edits.push({\n          resource: model.uri,\n          versionId: void 0,\n          textEdit: {\n            range: this._textSpanToRange(model, textChange.span),\n            text: textChange.newText\n          }\n        });\n      }\n    }\n    const action = {\n      title: codeFix.description,\n      edit: { edits },\n      diagnostics: context.markers,\n      kind: \"quickfix\"\n    };\n    return action;\n  }\n};\nvar RenameAdapter = class extends Adapter {\n  constructor(_libFiles, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n  }\n  async provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    const fileName = resource.toString();\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const renameInfo = await worker.getRenameInfo(fileName, offset, {\n      allowRenameOfImportPath: false\n    });\n    if (renameInfo.canRename === false) {\n      return {\n        edits: [],\n        rejectReason: renameInfo.localizedErrorMessage\n      };\n    }\n    if (renameInfo.fileToRename !== void 0) {\n      throw new Error(\"Renaming files is not supported.\");\n    }\n    const renameLocations = await worker.findRenameLocations(\n      fileName,\n      offset,\n      /*strings*/\n      false,\n      /*comments*/\n      false,\n      /*prefixAndSuffix*/\n      false\n    );\n    if (!renameLocations || model.isDisposed()) {\n      return;\n    }\n    const edits = [];\n    for (const renameLocation of renameLocations) {\n      const model2 = this._libFiles.getOrCreateModel(renameLocation.fileName);\n      if (model2) {\n        edits.push({\n          resource: model2.uri,\n          versionId: void 0,\n          textEdit: {\n            range: this._textSpanToRange(model2, renameLocation.textSpan),\n            text: newName\n          }\n        });\n      } else {\n        throw new Error(`Unknown file ${renameLocation.fileName}.`);\n      }\n    }\n    return { edits };\n  }\n};\nvar InlayHintsAdapter = class extends Adapter {\n  async provideInlayHints(model, range, token) {\n    const resource = model.uri;\n    const fileName = resource.toString();\n    const start = model.getOffsetAt({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const end = model.getOffsetAt({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return null;\n    }\n    const tsHints = await worker.provideInlayHints(fileName, start, end);\n    const hints = tsHints.map((hint) => {\n      return {\n        ...hint,\n        label: hint.text,\n        position: model.getPositionAt(hint.position),\n        kind: this._convertHintKind(hint.kind)\n      };\n    });\n    return { hints, dispose: () => {\n    } };\n  }\n  _convertHintKind(kind) {\n    switch (kind) {\n      case \"Parameter\":\n        return monaco_editor_core_exports.languages.InlayHintKind.Parameter;\n      case \"Type\":\n        return monaco_editor_core_exports.languages.InlayHintKind.Type;\n      default:\n        return monaco_editor_core_exports.languages.InlayHintKind.Type;\n    }\n  }\n};\n\n// src/language/typescript/tsMode.ts\nvar javaScriptWorker;\nvar typeScriptWorker;\nfunction setupTypeScript(defaults) {\n  typeScriptWorker = setupMode(defaults, \"typescript\");\n}\nfunction setupJavaScript(defaults) {\n  javaScriptWorker = setupMode(defaults, \"javascript\");\n}\nfunction getJavaScriptWorker() {\n  return new Promise((resolve, reject) => {\n    if (!javaScriptWorker) {\n      return reject(\"JavaScript not registered!\");\n    }\n    resolve(javaScriptWorker);\n  });\n}\nfunction getTypeScriptWorker() {\n  return new Promise((resolve, reject) => {\n    if (!typeScriptWorker) {\n      return reject(\"TypeScript not registered!\");\n    }\n    resolve(typeScriptWorker);\n  });\n}\nfunction setupMode(defaults, modeId) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(modeId, defaults);\n  disposables.push(client);\n  const worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  const libFiles = new LibFiles(worker);\n  function registerProviders() {\n    const { modeConfiguration } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration.completionItems) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCompletionItemProvider(\n          modeId,\n          new SuggestAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.signatureHelp) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerSignatureHelpProvider(\n          modeId,\n          new SignatureHelpAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.hovers) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerHoverProvider(modeId, new QuickInfoAdapter(worker))\n      );\n    }\n    if (modeConfiguration.documentHighlights) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentHighlightProvider(\n          modeId,\n          new DocumentHighlightAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.definitions) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDefinitionProvider(\n          modeId,\n          new DefinitionAdapter(libFiles, worker)\n        )\n      );\n    }\n    if (modeConfiguration.references) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerReferenceProvider(\n          modeId,\n          new ReferenceAdapter(libFiles, worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentSymbols) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n          modeId,\n          new OutlineAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.rename) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerRenameProvider(\n          modeId,\n          new RenameAdapter(libFiles, worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentRangeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n          modeId,\n          new FormatAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.onTypeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerOnTypeFormattingEditProvider(\n          modeId,\n          new FormatOnTypeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.codeActions) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCodeActionProvider(modeId, new CodeActionAdaptor(worker))\n      );\n    }\n    if (modeConfiguration.inlayHints) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerInlayHintsProvider(modeId, new InlayHintsAdapter(worker))\n      );\n    }\n    if (modeConfiguration.diagnostics) {\n      providers.push(new DiagnosticsAdapter(libFiles, defaults, modeId, worker));\n    }\n  }\n  registerProviders();\n  disposables.push(asDisposable(providers));\n  return worker;\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\nexport {\n  Adapter,\n  CodeActionAdaptor,\n  DefinitionAdapter,\n  DiagnosticsAdapter,\n  DocumentHighlightAdapter,\n  FormatAdapter,\n  FormatHelper,\n  FormatOnTypeAdapter,\n  InlayHintsAdapter,\n  Kind,\n  LibFiles,\n  OutlineAdapter,\n  QuickInfoAdapter,\n  ReferenceAdapter,\n  RenameAdapter,\n  SignatureHelpAdapter,\n  SuggestAdapter,\n  WorkerManager,\n  flattenDiagnosticMessageText,\n  getJavaScriptWorker,\n  getTypeScriptWorker,\n  setupJavaScript,\n  setupTypeScript\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;;AAOA,IAAI,EAAY,OAAO,eACnB,EAAmB,OAAO,yBAC1B,EAAoB,OAAO,oBAC3B,EAAe,OAAO,UAAU,eAChC,GAAe,EAAI,EAAM,EAAQ,IAAS,CAC5C,GAAI,GAAQ,OAAO,GAAS,UAAY,OAAO,GAAS,eACjD,IAAI,KAAO,EAAkB,EAAK,CACjC,CAAC,EAAa,KAAK,EAAI,EAAI,EAAI,IAAQ,GACzC,EAAU,EAAI,EAAK,CAAE,QAAW,EAAK,GAAM,WAAY,EAAE,EAAO,EAAiB,EAAM,EAAI,GAAK,EAAK,WAAY,CAAC,CAExH,OAAO,GAEL,GAAc,EAAQ,EAAK,KAAkB,EAAY,EAAQ,EAAK,UAAU,CAAE,GAAgB,EAAY,EAAc,EAAK,UAAU,EAG3I,EAA6B,EAAE,CACnC,EAAW,EAA4BA,EAAwB,CAI/D,IAAI,EAAgB,KAAM,CACxB,YAAY,EAAS,EAAW,CAC9B,KAAK,QAAU,EACf,KAAK,UAAY,EACjB,KAAK,QAAU,KACf,KAAK,QAAU,KACf,KAAK,sBAAwB,KAAK,UAAU,gBAAkB,KAAK,aAAa,CAAC,CACjF,KAAK,sBAAwB,EAC7B,KAAK,yBAA2B,KAAK,UAAU,yBACvC,KAAK,kBAAkB,CAC9B,CAEH,SAAU,CACR,KAAK,sBAAsB,SAAS,CACpC,KAAK,yBAAyB,SAAS,CACvC,KAAK,aAAa,CAEpB,aAAc,CACZ,AAEE,KAAK,WADL,KAAK,QAAQ,SAAS,CACP,MAEjB,KAAK,QAAU,KAEjB,MAAM,kBAAmB,CACvB,GAAI,CAAC,KAAK,QACR,OAEF,IAAM,EAAU,EAAE,KAAK,sBACjB,EAAQ,MAAM,KAAK,QAAQ,UAAU,CACvC,KAAK,wBAA0B,GAGnC,EAAM,gBAAgB,KAAK,UAAU,cAAc,CAAC,CAEtD,YAAa,CAwBX,MAvBA,CACE,KAAK,WAAW,UACd,KAAK,QAAU,EAA2B,OAAO,gBAAgB,CAE/D,SAAU,kCACV,MAAO,KAAK,QACZ,eAAgB,GAEhB,WAAY,CACV,gBAAiB,KAAK,UAAU,oBAAoB,CACpD,UAAW,KAAK,UAAU,cAAc,CACxC,iBAAkB,KAAK,UAAU,cAAc,iBAC/C,kBAAmB,KAAK,UAAU,kBACnC,CACF,CAAC,CACE,KAAK,UAAU,mBAAmB,CAC7B,MAAM,KAAK,QAAQ,oBACxB,EAA2B,OAAO,WAAW,CAAC,OAAQ,GAAU,EAAM,eAAe,GAAK,KAAK,QAAQ,CAAC,IAAK,GAAU,EAAM,IAAI,CAClI,CAEI,MAAM,KAAK,QAAQ,UAAU,IAClC,CAEC,KAAK,QAEd,MAAM,yBAAyB,GAAG,EAAW,CAC3C,IAAM,EAAS,MAAM,KAAK,YAAY,CAItC,OAHI,KAAK,SACP,MAAM,KAAK,QAAQ,oBAAoB,EAAU,CAE5C,IAUP,EAAa,EAAE,CACnB,EAAW,YAAc,GACzB,EAAW,uBAAyB,GACpC,EAAW,8BAAgC,GAC3C,EAAW,8BAAgC,GAC3C,EAAW,gBAAkB,GAC7B,EAAW,yBAA2B,GACtC,EAAW,8BAAgC,GAC3C,EAAW,wBAA0B,GACrC,EAAW,mBAAqB,GAChC,EAAW,6BAA+B,GAC1C,EAAW,4BAA8B,GACzC,EAAW,2BAA6B,GACxC,EAAW,yBAA2B,GACtC,EAAW,2BAA6B,GACxC,EAAW,0BAA4B,GACvC,EAAW,oCAAsC,GACjD,EAAW,iCAAmC,GAC9C,EAAW,mBAAqB,GAChC,EAAW,wBAA0B,GACrC,EAAW,wBAA0B,GACrC,EAAW,mBAAqB,GAChC,EAAW,wBAA0B,GACrC,EAAW,wBAA0B,GACrC,EAAW,wBAA0B,GACrC,EAAW,0BAA4B,GACvC,EAAW,gCAAkC,GAC7C,EAAW,0BAA4B,GACvC,EAAW,+BAAiC,GAC5C,EAAW,kCAAoC,GAC/C,EAAW,iCAAmC,GAC9C,EAAW,mBAAqB,GAChC,EAAW,wBAA0B,GACrC,EAAW,wBAA0B,GACrC,EAAW,2BAA6B,GACxC,EAAW,0BAA4B,GACvC,EAAW,yBAA2B,GACtC,EAAW,mBAAqB,GAChC,EAAW,wBAA0B,GACrC,EAAW,wBAA0B,GACrC,EAAW,0BAA4B,GACvC,EAAW,0BAA4B,GACvC,EAAW,0BAA4B,GACvC,EAAW,0BAA4B,GACvC,EAAW,mBAAqB,GAChC,EAAW,wBAA0B,GACrC,EAAW,wBAA0B,GACrC,EAAW,wBAA0B,GACrC,EAAW,0BAA4B,GACvC,EAAW,2BAA6B,GACxC,EAAW,gCAAkC,GAC7C,EAAW,0BAA4B,GACvC,EAAW,oCAAsC,GACjD,EAAW,mBAAqB,GAChC,EAAW,wBAA0B,GACrC,EAAW,wBAA0B,GACrC,EAAW,2BAA6B,GACxC,EAAW,0BAA4B,GACvC,EAAW,2BAA6B,GACxC,EAAW,yBAA2B,GACtC,EAAW,mBAAqB,GAChC,EAAW,yBAA2B,GACtC,EAAW,wBAA0B,GACrC,EAAW,wBAA0B,GACrC,EAAW,0BAA4B,GACvC,EAAW,0BAA4B,GACvC,EAAW,gCAAkC,GAC7C,EAAW,0BAA4B,GACvC,EAAW,yBAA2B,GACtC,EAAW,8BAAgC,GAC3C,EAAW,mBAAqB,GAChC,EAAW,wBAA0B,GACrC,EAAW,gBAAkB,GAC7B,EAAW,gBAAkB,GAC7B,EAAW,8BAAgC,GAC3C,EAAW,mBAAqB,GAChC,EAAW,8BAAgC,GAC3C,EAAW,8BAAgC,GAC3C,EAAW,wBAA0B,GACrC,EAAW,wBAA0B,GACrC,EAAW,0BAA4B,GACvC,EAAW,2BAA6B,GACxC,EAAW,uBAAyB,GACpC,EAAW,oCAAsC,GACjD,EAAW,sBAAwB,GACnC,EAAW,oCAAsC,GACjD,EAAW,+BAAiC,GAG5C,SAAS,EAA6B,EAAM,EAAS,EAAS,EAAG,CAC/D,GAAI,OAAO,GAAS,SAClB,OAAO,KACE,IAAS,IAAK,GACvB,MAAO,GAET,IAAI,EAAS,GACb,GAAI,EAAQ,CACV,GAAU,EACV,IAAK,IAAI,EAAI,EAAG,EAAI,EAAQ,IAC1B,GAAU,KAKd,GAFA,GAAU,EAAK,YACf,IACI,EAAK,KACP,IAAK,IAAM,KAAO,EAAK,KACrB,GAAU,EAA6B,EAAK,EAAS,EAAO,CAGhE,OAAO,EAET,SAAS,EAAqB,EAAc,CAI1C,OAHI,EACK,EAAa,IAAK,GAAgB,EAAY,KAAK,CAAC,KAAK,GAAG,CAE9D,GAET,IAAI,EAAU,KAAM,CAClB,YAAY,EAAS,CACnB,KAAK,QAAU,EAQjB,iBAAiB,EAAO,EAAM,CAC5B,IAAI,EAAK,EAAM,cAAc,EAAK,MAAM,CACpC,EAAK,EAAM,cAAc,EAAK,MAAQ,EAAK,OAAO,CAClD,CAAE,WAAY,EAAiB,OAAQ,GAAgB,EACvD,CAAE,WAAY,EAAe,OAAQ,GAAc,EACvD,MAAO,CAAE,kBAAiB,cAAa,gBAAe,YAAW,GAGjE,EAAW,KAAM,CACnB,YAAY,EAAS,CACnB,KAAK,QAAU,EACf,KAAK,UAAY,EAAE,CACnB,KAAK,oBAAsB,GAC3B,KAAK,sBAAwB,KAE/B,UAAU,EAAK,CAOb,OANK,GAGD,EAAI,KAAK,QAAQ,QAAQ,GAAK,EACzB,CAAC,CAAC,EAAW,EAAI,KAAK,MAAM,EAAE,EAEhC,GAET,iBAAiB,EAAU,CACzB,IAAM,EAAM,EAA2B,IAAI,MAAM,EAAS,CACpD,EAAQ,EAA2B,OAAO,SAAS,EAAI,CAC7D,GAAI,EACF,OAAO,EAET,GAAI,KAAK,UAAU,EAAI,EAAI,KAAK,oBAC9B,OAAO,EAA2B,OAAO,YAAY,KAAK,UAAU,EAAI,KAAK,MAAM,EAAE,EAAG,aAAc,EAAI,CAE5G,IAAM,EAAiB,EAAmB,cAAc,CAAC,GAIzD,OAHI,EACK,EAA2B,OAAO,YAAY,EAAe,QAAS,aAAc,EAAI,CAE1F,KAET,iBAAiB,EAAM,CACrB,IAAK,IAAI,KAAO,EACd,GAAI,KAAK,UAAU,EAAI,CACrB,MAAO,GAGX,MAAO,GAET,MAAM,yBAAyB,EAAM,CAC9B,KAAK,iBAAiB,EAAK,EAGhC,MAAM,KAAK,gBAAgB,CAE7B,gBAAiB,CAOf,MANA,CACE,KAAK,wBAAwB,KAAK,SAAS,CAAC,KAAM,GAAM,EAAE,aAAa,CAAC,CAAC,KAAM,GAAa,CAC1F,KAAK,oBAAsB,GAC3B,KAAK,UAAY,GACjB,CAEG,KAAK,wBAGZ,EAAqB,cAAc,CAAQ,CAC7C,YAAY,EAAW,EAAW,EAAW,EAAQ,CACnD,MAAM,EAAO,CACb,KAAK,UAAY,EACjB,KAAK,UAAY,EACjB,KAAK,UAAY,EACjB,KAAK,aAAe,EAAE,CACtB,KAAK,UAA4B,OAAO,OAAO,KAAK,CACpD,IAAM,EAAc,GAAU,CAC5B,GAAI,EAAM,eAAe,GAAK,EAC5B,OAEF,IAAM,MAAsB,CAC1B,GAAM,CAAE,eAAgB,KAAK,UAAU,uBAAuB,CAC1D,EACE,EAAM,oBAAoB,EAC5B,KAAK,YAAY,EAAM,CAGzB,KAAK,YAAY,EAAM,EAGvB,EACE,EAAqB,EAAM,uBAAyB,CACxD,aAAa,EAAO,CACpB,EAAS,OAAO,WAAW,EAAe,IAAI,EAC9C,CACI,EAAsB,EAAM,wBAA0B,CAC1D,GAAM,CAAE,eAAgB,KAAK,UAAU,uBAAuB,CAC1D,IACE,EAAM,oBAAoB,CAC5B,GAAe,CAEf,EAA2B,OAAO,gBAAgB,EAAO,KAAK,UAAW,EAAE,CAAC,GAGhF,CACF,KAAK,UAAU,EAAM,IAAI,UAAU,EAAI,CACrC,SAAU,CACR,EAAmB,SAAS,CAC5B,EAAoB,SAAS,CAC7B,aAAa,EAAO,EAEvB,CACD,GAAe,EAEX,EAAkB,GAAU,CAChC,EAA2B,OAAO,gBAAgB,EAAO,KAAK,UAAW,EAAE,CAAC,CAC5E,IAAM,EAAM,EAAM,IAAI,UAAU,CAC5B,KAAK,UAAU,KACjB,KAAK,UAAU,GAAK,SAAS,CAC7B,OAAO,KAAK,UAAU,KAG1B,KAAK,aAAa,KAChB,EAA2B,OAAO,iBAAkB,GAAU,EAAW,EAAM,CAAC,CACjF,CACD,KAAK,aAAa,KAAK,EAA2B,OAAO,mBAAmB,EAAe,CAAC,CAC5F,KAAK,aAAa,KAChB,EAA2B,OAAO,yBAA0B,GAAU,CACpE,EAAe,EAAM,MAAM,CAC3B,EAAW,EAAM,MAAM,EACvB,CACH,CACD,KAAK,aAAa,KAAK,CACrB,SAAU,CACR,IAAK,IAAM,KAAS,EAA2B,OAAO,WAAW,CAC/D,EAAe,EAAM,EAG1B,CAAC,CACF,IAAM,MAA4B,CAChC,IAAK,IAAM,KAAS,EAA2B,OAAO,WAAW,CAC/D,EAAe,EAAM,CACrB,EAAW,EAAM,EAGrB,KAAK,aAAa,KAAK,KAAK,UAAU,YAAY,EAAoB,CAAC,CACvE,KAAK,aAAa,KAAK,KAAK,UAAU,qBAAqB,EAAoB,CAAC,CAChF,EAA2B,OAAO,WAAW,CAAC,QAAS,GAAU,EAAW,EAAM,CAAC,CAErF,SAAU,CACR,KAAK,aAAa,QAAS,GAAM,GAAK,EAAE,SAAS,CAAC,CAClD,KAAK,aAAe,EAAE,CAExB,MAAM,YAAY,EAAO,CACvB,IAAM,EAAS,MAAM,KAAK,QAAQ,EAAM,IAAI,CAC5C,GAAI,EAAM,YAAY,CACpB,OAEF,IAAM,EAAW,EAAE,CACb,CAAE,qBAAoB,uBAAsB,2BAA4B,KAAK,UAAU,uBAAuB,CAC/G,GACH,EAAS,KAAK,EAAO,wBAAwB,EAAM,IAAI,UAAU,CAAC,CAAC,CAEhE,GACH,EAAS,KAAK,EAAO,uBAAuB,EAAM,IAAI,UAAU,CAAC,CAAC,CAE/D,GACH,EAAS,KAAK,EAAO,yBAAyB,EAAM,IAAI,UAAU,CAAC,CAAC,CAEtE,IAAM,EAAiB,MAAM,QAAQ,IAAI,EAAS,CAClD,GAAI,CAAC,GAAkB,EAAM,YAAY,CACvC,OAEF,IAAM,EAAc,EAAe,QAAQ,EAAG,IAAM,EAAE,OAAO,EAAE,CAAE,EAAE,CAAC,CAAC,OAClE,IAAO,KAAK,UAAU,uBAAuB,CAAC,yBAA2B,EAAE,EAAE,QAAQ,EAAE,KAAK,GAAK,GACnG,CACK,EAAc,EAAY,IAAK,GAAM,EAAE,oBAAsB,EAAE,CAAC,CAAC,QAAQ,EAAG,IAAM,EAAE,OAAO,EAAE,CAAE,EAAE,CAAC,CAAC,IACtG,GAAuB,EAAmB,KAAO,EAA2B,IAAI,MAAM,EAAmB,KAAK,SAAS,CAAG,KAC5H,CACD,MAAM,KAAK,UAAU,yBAAyB,EAAY,CACtD,GAAM,YAAY,EAGtB,EAA2B,OAAO,gBAChC,EACA,KAAK,UACL,EAAY,IAAK,GAAM,KAAK,oBAAoB,EAAO,EAAE,CAAC,CAC3D,CAEH,oBAAoB,EAAO,EAAM,CAC/B,IAAM,EAAY,EAAK,OAAS,EAC1B,EAAa,EAAK,QAAU,EAC5B,CAAE,WAAY,EAAiB,OAAQ,GAAgB,EAAM,cAAc,EAAU,CACrF,CAAE,WAAY,EAAe,OAAQ,GAAc,EAAM,cAC7D,EAAY,EACb,CACK,EAAO,EAAE,CAOf,OANI,EAAK,oBACP,EAAK,KAAK,EAA2B,UAAU,YAAY,CAEzD,EAAK,mBACP,EAAK,KAAK,EAA2B,UAAU,WAAW,CAErD,CACL,SAAU,KAAK,sCAAsC,EAAK,SAAS,CACnE,kBACA,cACA,gBACA,YACA,QAAS,EAA6B,EAAK,YAAa;EAAK,CAC7D,KAAM,EAAK,KAAK,UAAU,CAC1B,OACA,mBAAoB,KAAK,2BAA2B,EAAO,EAAK,mBAAmB,CACpF,CAEH,2BAA2B,EAAO,EAAoB,CACpD,GAAI,CAAC,EACH,MAAO,EAAE,CAEX,IAAM,EAAS,EAAE,CAwBjB,OAvBA,EAAmB,QAAS,GAAS,CACnC,IAAI,EAAkB,EAItB,GAHI,EAAK,OACP,EAAkB,KAAK,UAAU,iBAAiB,EAAK,KAAK,SAAS,EAEnE,CAAC,EACH,OAEF,IAAM,EAAY,EAAK,OAAS,EAC1B,EAAa,EAAK,QAAU,EAC5B,CAAE,WAAY,EAAiB,OAAQ,GAAgB,EAAgB,cAAc,EAAU,CAC/F,CAAE,WAAY,EAAe,OAAQ,GAAc,EAAgB,cACvE,EAAY,EACb,CACD,EAAO,KAAK,CACV,SAAU,EAAgB,IAC1B,kBACA,cACA,gBACA,YACA,QAAS,EAA6B,EAAK,YAAa;EAAK,CAC9D,CAAC,EACF,CACK,EAET,sCAAsC,EAAU,CAC9C,OAAQ,EAAR,CACE,IAAK,GACH,OAAO,EAA2B,eAAe,MACnD,IAAK,GACH,OAAO,EAA2B,eAAe,KACnD,IAAK,GACH,OAAO,EAA2B,eAAe,QACnD,IAAK,GACH,OAAO,EAA2B,eAAe,KAErD,OAAO,EAA2B,eAAe,OAGjD,EAAiB,MAAM,UAAwB,CAAQ,CACzD,IAAI,mBAAoB,CACtB,MAAO,CAAC,IAAI,CAEd,MAAM,uBAAuB,EAAO,EAAU,EAAU,EAAO,CAC7D,IAAM,EAAW,EAAM,qBAAqB,EAAS,CAC/C,EAAY,IAAI,EAA2B,MAC/C,EAAS,WACT,EAAS,YACT,EAAS,WACT,EAAS,UACV,CACK,EAAW,EAAM,IACjB,EAAS,EAAM,YAAY,EAAS,CACpC,EAAS,MAAM,KAAK,QAAQ,EAAS,CAC3C,GAAI,EAAM,YAAY,CACpB,OAEF,IAAM,EAAO,MAAM,EAAO,yBAAyB,EAAS,UAAU,CAAE,EAAO,CAC3E,MAAC,GAAQ,EAAM,YAAY,EA0B/B,MAAO,CACL,YAxBkB,EAAK,QAAQ,IAAK,GAAU,CAC9C,IAAI,EAAQ,EACZ,GAAI,EAAM,gBAAiB,CACzB,IAAM,EAAK,EAAM,cAAc,EAAM,gBAAgB,MAAM,CACrD,EAAK,EAAM,cAAc,EAAM,gBAAgB,MAAQ,EAAM,gBAAgB,OAAO,CAC1F,EAAQ,IAAI,EAA2B,MAAM,EAAG,WAAY,EAAG,OAAQ,EAAG,WAAY,EAAG,OAAO,CAElG,IAAM,EAAO,EAAE,CAIf,OAHI,EAAM,gBAAkB,IAAK,IAAK,EAAM,cAAc,QAAQ,aAAa,GAAK,IAClF,EAAK,KAAK,EAA2B,UAAU,kBAAkB,WAAW,CAEvE,CACL,IAAK,EACL,WACA,SACA,QACA,MAAO,EAAM,KACb,WAAY,EAAM,KAClB,SAAU,EAAM,SAChB,KAAM,EAAgB,YAAY,EAAM,KAAK,CAC7C,OACD,EACD,CAGD,CAEH,MAAM,sBAAsB,EAAM,EAAO,CACvC,IAAM,EAAS,EACT,EAAW,EAAO,IAClB,EAAW,EAAO,SAClB,EAAS,EAAO,OAEhB,EAAU,MADD,MAAM,KAAK,QAAQ,EAAS,EACd,0BAC3B,EAAS,UAAU,CACnB,EACA,EAAO,MACR,CAID,OAHK,EAGE,CACL,IAAK,EACL,WACA,MAAO,EAAQ,KACf,KAAM,EAAgB,YAAY,EAAQ,KAAK,CAC/C,OAAQ,EAAqB,EAAQ,aAAa,CAClD,cAAe,CACb,MAAO,EAAgB,0BAA0B,EAAQ,CAC1D,CACF,CAXQ,EAaX,OAAO,YAAY,EAAM,CACvB,OAAQ,EAAR,CACE,KAAK,EAAK,cACV,KAAK,EAAK,QACR,OAAO,EAA2B,UAAU,mBAAmB,QACjE,KAAK,EAAK,SACV,KAAK,EAAK,cACR,OAAO,EAA2B,UAAU,mBAAmB,SACjE,KAAK,EAAK,eACV,KAAK,EAAK,kBACV,KAAK,EAAK,kBACR,OAAO,EAA2B,UAAU,mBAAmB,MACjE,KAAK,EAAK,SACV,KAAK,EAAK,eACV,KAAK,EAAK,mBACV,KAAK,EAAK,cACV,KAAK,EAAK,eACR,OAAO,EAA2B,UAAU,mBAAmB,SACjE,KAAK,EAAK,KACR,OAAO,EAA2B,UAAU,mBAAmB,KACjE,KAAK,EAAK,OACR,OAAO,EAA2B,UAAU,mBAAmB,OACjE,KAAK,EAAK,MACR,OAAO,EAA2B,UAAU,mBAAmB,MACjE,KAAK,EAAK,UACR,OAAO,EAA2B,UAAU,mBAAmB,UACjE,KAAK,EAAK,QACR,OAAO,EAA2B,UAAU,mBAAmB,KAEnE,OAAO,EAA2B,UAAU,mBAAmB,SAEjE,OAAO,0BAA0B,EAAS,CACxC,IAAI,EAAsB,EAAqB,EAAQ,cAAc,CACrE,GAAI,EAAQ,KACV,IAAK,IAAM,KAAO,EAAQ,KACxB,GAAuB;;EAE7B,EAAY,EAAI,GAGd,OAAO,IAGX,SAAS,EAAY,EAAK,CACxB,IAAI,EAAW,KAAK,EAAI,KAAK,GAC7B,GAAI,EAAI,OAAS,SAAW,EAAI,KAAM,CACpC,GAAM,CAAC,EAAW,GAAG,GAAQ,EAAI,KACjC,GAAY,KAAK,EAAU,KAAK,IAC5B,EAAK,OAAS,IAChB,GAAY,WAAW,EAAK,IAAK,GAAM,EAAE,KAAK,CAAC,KAAK,IAAI,SACjD,MAAM,QAAQ,EAAI,KAAK,CAChC,GAAY,WAAW,EAAI,KAAK,IAAK,GAAM,EAAE,KAAK,CAAC,KAAK,IAAI,GACnD,EAAI,OACb,GAAY,WAAW,EAAI,QAE7B,OAAO,EAET,IAAI,EAAuB,MAAM,UAA8B,CAAQ,CACrE,aAAc,CACZ,MAAM,GAAG,UAAU,CACnB,KAAK,+BAAiC,CAAC,IAAK,IAAI,CAElD,OAAO,8BAA8B,EAAS,CAC5C,OAAQ,EAAQ,YAAhB,CACE,KAAK,EAA2B,UAAU,yBAAyB,iBAQ/D,OAPE,EAAQ,iBACN,EAAQ,YACH,CAAE,KAAM,YAAa,iBAAkB,EAAQ,iBAAkB,CAEjE,CAAE,KAAM,iBAAkB,iBAAkB,EAAQ,iBAAkB,CAGxE,CAAE,KAAM,UAAW,CAE9B,KAAK,EAA2B,UAAU,yBAAyB,cACjE,OAAO,EAAQ,YAAc,CAAE,KAAM,YAAa,CAAG,CAAE,KAAM,UAAW,CAC1E,KAAK,EAA2B,UAAU,yBAAyB,OACnE,QACE,MAAO,CAAE,KAAM,UAAW,EAGhC,MAAM,qBAAqB,EAAO,EAAU,EAAO,EAAS,CAC1D,IAAM,EAAW,EAAM,IACjB,EAAS,EAAM,YAAY,EAAS,CACpC,EAAS,MAAM,KAAK,QAAQ,EAAS,CAC3C,GAAI,EAAM,YAAY,CACpB,OAEF,IAAM,EAAO,MAAM,EAAO,sBAAsB,EAAS,UAAU,CAAE,EAAQ,CAC3E,cAAe,EAAsB,8BAA8B,EAAQ,CAC5E,CAAC,CACF,GAAI,CAAC,GAAQ,EAAM,YAAY,CAC7B,OAEF,IAAM,EAAM,CACV,gBAAiB,EAAK,kBACtB,gBAAiB,EAAK,cACtB,WAAY,EAAE,CACf,CA2BD,OA1BA,EAAK,MAAM,QAAS,GAAS,CAC3B,IAAM,EAAY,CAChB,MAAO,GACP,WAAY,EAAE,CACf,CACD,EAAU,cAAgB,CACxB,MAAO,EAAqB,EAAK,cAAc,CAChD,CACD,EAAU,OAAS,EAAqB,EAAK,mBAAmB,CAChE,EAAK,WAAW,SAAS,EAAG,EAAG,IAAM,CACnC,IAAM,EAAQ,EAAqB,EAAE,aAAa,CAC5C,EAAY,CAChB,QACA,cAAe,CACb,MAAO,EAAqB,EAAE,cAAc,CAC7C,CACF,CACD,EAAU,OAAS,EACnB,EAAU,WAAW,KAAK,EAAU,CAChC,EAAI,EAAE,OAAS,IACjB,EAAU,OAAS,EAAqB,EAAK,sBAAsB,GAErE,CACF,EAAU,OAAS,EAAqB,EAAK,mBAAmB,CAChE,EAAI,WAAW,KAAK,EAAU,EAC9B,CACK,CACL,MAAO,EACP,SAAU,GAEX,GAGD,EAAmB,cAAc,CAAQ,CAC3C,MAAM,aAAa,EAAO,EAAU,EAAO,CACzC,IAAM,EAAW,EAAM,IACjB,EAAS,EAAM,YAAY,EAAS,CACpC,EAAS,MAAM,KAAK,QAAQ,EAAS,CAC3C,GAAI,EAAM,YAAY,CACpB,OAEF,IAAM,EAAO,MAAM,EAAO,uBAAuB,EAAS,UAAU,CAAE,EAAO,CAC7E,GAAI,CAAC,GAAQ,EAAM,YAAY,CAC7B,OAEF,IAAM,EAAgB,EAAqB,EAAK,cAAc,CACxD,EAAO,EAAK,KAAO,EAAK,KAAK,IAAK,GAAQ,EAAY,EAAI,CAAC,CAAC,KAAK;;EAAS,CAAG,GAC7E,EAAW,EAAqB,EAAK,aAAa,CACxD,MAAO,CACL,MAAO,KAAK,iBAAiB,EAAO,EAAK,SAAS,CAClD,SAAU,CACR,CACE,MAAO,kBAAoB,EAAW,UACvC,CACD,CACE,MAAO,GAAiB,EAAO;;EAAS,EAAO,IAChD,CACF,CACF,GAGD,EAA2B,cAAc,CAAQ,CACnD,MAAM,0BAA0B,EAAO,EAAU,EAAO,CACtD,IAAM,EAAW,EAAM,IACjB,EAAS,EAAM,YAAY,EAAS,CACpC,EAAS,MAAM,KAAK,QAAQ,EAAS,CAC3C,GAAI,EAAM,YAAY,CACpB,OAEF,IAAM,EAAU,MAAM,EAAO,sBAAsB,EAAS,UAAU,CAAE,EAAQ,CAC9E,EAAS,UAAU,CACpB,CAAC,CACE,MAAC,GAAW,EAAM,YAAY,EAGlC,OAAO,EAAQ,QAAS,GACf,EAAM,eAAe,IAAK,IACxB,CACL,MAAO,KAAK,iBAAiB,EAAO,EAAe,SAAS,CAC5D,KAAM,EAAe,OAAS,mBAAqB,EAA2B,UAAU,sBAAsB,MAAQ,EAA2B,UAAU,sBAAsB,KAClL,EACD,CACF,GAGF,EAAoB,cAAc,CAAQ,CAC5C,YAAY,EAAW,EAAQ,CAC7B,MAAM,EAAO,CACb,KAAK,UAAY,EAEnB,MAAM,kBAAkB,EAAO,EAAU,EAAO,CAC9C,IAAM,EAAW,EAAM,IACjB,EAAS,EAAM,YAAY,EAAS,CACpC,EAAS,MAAM,KAAK,QAAQ,EAAS,CAC3C,GAAI,EAAM,YAAY,CACpB,OAEF,IAAM,EAAU,MAAM,EAAO,wBAAwB,EAAS,UAAU,CAAE,EAAO,CAOjF,GANI,CAAC,GAAW,EAAM,YAAY,GAGlC,MAAM,KAAK,UAAU,yBACnB,EAAQ,IAAK,GAAU,EAA2B,IAAI,MAAM,EAAM,SAAS,CAAC,CAC7E,CACG,EAAM,YAAY,EACpB,OAEF,IAAM,EAAS,EAAE,CACjB,IAAK,IAAI,KAAS,EAAS,CACzB,IAAM,EAAW,KAAK,UAAU,iBAAiB,EAAM,SAAS,CAC5D,GACF,EAAO,KAAK,CACV,IAAK,EAAS,IACd,MAAO,KAAK,iBAAiB,EAAU,EAAM,SAAS,CACvD,CAAC,CAGN,OAAO,IAGP,EAAmB,cAAc,CAAQ,CAC3C,YAAY,EAAW,EAAQ,CAC7B,MAAM,EAAO,CACb,KAAK,UAAY,EAEnB,MAAM,kBAAkB,EAAO,EAAU,EAAS,EAAO,CACvD,IAAM,EAAW,EAAM,IACjB,EAAS,EAAM,YAAY,EAAS,CACpC,EAAS,MAAM,KAAK,QAAQ,EAAS,CAC3C,GAAI,EAAM,YAAY,CACpB,OAEF,IAAM,EAAU,MAAM,EAAO,wBAAwB,EAAS,UAAU,CAAE,EAAO,CAOjF,GANI,CAAC,GAAW,EAAM,YAAY,GAGlC,MAAM,KAAK,UAAU,yBACnB,EAAQ,IAAK,GAAU,EAA2B,IAAI,MAAM,EAAM,SAAS,CAAC,CAC7E,CACG,EAAM,YAAY,EACpB,OAEF,IAAM,EAAS,EAAE,CACjB,IAAK,IAAI,KAAS,EAAS,CACzB,IAAM,EAAW,KAAK,UAAU,iBAAiB,EAAM,SAAS,CAC5D,GACF,EAAO,KAAK,CACV,IAAK,EAAS,IACd,MAAO,KAAK,iBAAiB,EAAU,EAAM,SAAS,CACvD,CAAC,CAGN,OAAO,IAGP,EAAiB,cAAc,CAAQ,CACzC,MAAM,uBAAuB,EAAO,EAAO,CACzC,IAAM,EAAW,EAAM,IACjB,EAAS,MAAM,KAAK,QAAQ,EAAS,CAC3C,GAAI,EAAM,YAAY,CACpB,OAEF,IAAM,EAAO,MAAM,EAAO,kBAAkB,EAAS,UAAU,CAAC,CAChE,GAAI,CAAC,GAAQ,EAAM,YAAY,CAC7B,OAEF,IAAM,GAAW,EAAM,KACL,CACd,KAAM,EAAK,KACX,OAAQ,GACR,KAAM,EAAiB,EAAK,OAAS,EAA2B,UAAU,WAAW,SACrF,MAAO,KAAK,iBAAiB,EAAO,EAAK,MAAM,GAAG,CAClD,eAAgB,KAAK,iBAAiB,EAAO,EAAK,MAAM,GAAG,CAC3D,KAAM,EAAE,CACR,SAAU,EAAK,YAAY,IAAK,GAAU,EAAQ,EAAO,EAAK,KAAK,CAAC,CACpE,cAAe,EAChB,EAIH,OADe,EAAK,WAAa,EAAK,WAAW,IAAK,GAAS,EAAQ,EAAK,CAAC,CAAG,EAAE,GAIlF,EAAO,KAAM,CACf,OACE,KAAK,QAAU,GAEjB,OACE,KAAK,QAAU,UAEjB,OACE,KAAK,OAAS,SAEhB,OACE,KAAK,OAAS,SAEhB,OACE,KAAK,MAAQ,QAEf,OACE,KAAK,UAAY,YAEnB,OACE,KAAK,KAAO,OAEd,OACE,KAAK,KAAO,OAEd,OACE,KAAK,SAAW,MAElB,OACE,KAAK,cAAgB,YAEvB,OACE,KAAK,SAAW,WAElB,OACE,KAAK,cAAgB,iBAEvB,OACE,KAAK,eAAiB,SAExB,OACE,KAAK,kBAAoB,SAE3B,OACE,KAAK,kBAAoB,SAE3B,OACE,KAAK,eAAiB,WAExB,OACE,KAAK,0BAA4B,cAEnC,OACE,KAAK,cAAgB,OAEvB,OACE,KAAK,eAAiB,QAExB,OACE,KAAK,mBAAqB,YAE5B,OACE,KAAK,UAAY,YAEnB,OACE,KAAK,cAAgB,iBAEvB,OACE,KAAK,cAAgB,iBAEvB,OACE,KAAK,MAAQ,QAEf,OACE,KAAK,MAAQ,QAEf,OACE,KAAK,MAAQ,QAEf,OACE,KAAK,IAAM,MAEb,OACE,KAAK,QAAU,YAGf,EAAmC,OAAO,OAAO,KAAK,CAC1D,EAAiB,EAAK,QAAU,EAA2B,UAAU,WAAW,OAChF,EAAiB,EAAK,OAAS,EAA2B,UAAU,WAAW,MAC/E,EAAiB,EAAK,MAAQ,EAA2B,UAAU,WAAW,KAC9E,EAAiB,EAAK,WAAa,EAA2B,UAAU,WAAW,UACnF,EAAiB,EAAK,gBAAkB,EAA2B,UAAU,WAAW,OACxF,EAAiB,EAAK,gBAAkB,EAA2B,UAAU,WAAW,SACxF,EAAiB,EAAK,mBAAqB,EAA2B,UAAU,WAAW,SAC3F,EAAiB,EAAK,mBAAqB,EAA2B,UAAU,WAAW,SAC3F,EAAiB,EAAK,UAAY,EAA2B,UAAU,WAAW,SAClF,EAAiB,EAAK,OAAS,EAA2B,UAAU,WAAW,SAC/E,EAAiB,EAAK,eAAiB,EAA2B,UAAU,WAAW,SACvF,EAAiB,EAAK,UAAY,EAA2B,UAAU,WAAW,SAClF,EAAiB,EAAK,UAAY,EAA2B,UAAU,WAAW,SAClF,EAAiB,EAAK,eAAiB,EAA2B,UAAU,WAAW,SACvF,IAAI,EAAe,cAAc,CAAQ,CACvC,OAAO,gBAAgB,EAAS,CAC9B,MAAO,CACL,oBAAqB,EAAQ,aAC7B,QAAS,EAAQ,QACjB,WAAY,EAAQ,QACpB,YAAa,EACb,iBAAkB;EAClB,+BAAgC,GAChC,yCAA0C,GAC1C,yCAA0C,GAC1C,gDAAiD,GACjD,qDAAsD,GACtD,2DAA4D,GAC5D,wDAAyD,GACzD,4DAA6D,GAC7D,wCAAyC,GACzC,oCAAqC,GACtC,CAEH,oBAAoB,EAAO,EAAQ,CACjC,MAAO,CACL,KAAM,EAAO,QACb,MAAO,KAAK,iBAAiB,EAAO,EAAO,KAAK,CACjD,GAGD,EAAgB,cAAc,CAAa,CAC7C,aAAc,CACZ,MAAM,GAAG,UAAU,CACnB,KAAK,wBAA0B,GAEjC,MAAM,oCAAoC,EAAO,EAAO,EAAS,EAAO,CACtE,IAAM,EAAW,EAAM,IACjB,EAAc,EAAM,YAAY,CACpC,WAAY,EAAM,gBAClB,OAAQ,EAAM,YACf,CAAC,CACI,EAAY,EAAM,YAAY,CAClC,WAAY,EAAM,cAClB,OAAQ,EAAM,UACf,CAAC,CACI,EAAS,MAAM,KAAK,QAAQ,EAAS,CAC3C,GAAI,EAAM,YAAY,CACpB,OAEF,IAAM,EAAQ,MAAM,EAAO,2BACzB,EAAS,UAAU,CACnB,EACA,EACA,EAAa,gBAAgB,EAAQ,CACtC,CACG,MAAC,GAAS,EAAM,YAAY,EAGhC,OAAO,EAAM,IAAK,GAAS,KAAK,oBAAoB,EAAO,EAAK,CAAC,GAGjE,EAAsB,cAAc,CAAa,CACnD,IAAI,6BAA8B,CAChC,MAAO,CAAC,IAAK,IAAK;EAAK,CAEzB,MAAM,6BAA6B,EAAO,EAAU,EAAI,EAAS,EAAO,CACtE,IAAM,EAAW,EAAM,IACjB,EAAS,EAAM,YAAY,EAAS,CACpC,EAAS,MAAM,KAAK,QAAQ,EAAS,CAC3C,GAAI,EAAM,YAAY,CACpB,OAEF,IAAM,EAAQ,MAAM,EAAO,iCACzB,EAAS,UAAU,CACnB,EACA,EACA,EAAa,gBAAgB,EAAQ,CACtC,CACG,MAAC,GAAS,EAAM,YAAY,EAGhC,OAAO,EAAM,IAAK,GAAS,KAAK,oBAAoB,EAAO,EAAK,CAAC,GAGjE,EAAoB,cAAc,CAAa,CACjD,MAAM,mBAAmB,EAAO,EAAO,EAAS,EAAO,CACrD,IAAM,EAAW,EAAM,IACjB,EAAQ,EAAM,YAAY,CAC9B,WAAY,EAAM,gBAClB,OAAQ,EAAM,YACf,CAAC,CACI,EAAM,EAAM,YAAY,CAC5B,WAAY,EAAM,cAClB,OAAQ,EAAM,UACf,CAAC,CACI,EAAgB,EAAa,gBAAgB,EAAM,YAAY,CAAC,CAChE,EAAa,EAAQ,QAAQ,OAAQ,GAAM,EAAE,KAAK,CAAC,IAAK,GAAM,EAAE,KAAK,CAAC,IAAI,OAAO,CACjF,EAAS,MAAM,KAAK,QAAQ,EAAS,CAC3C,GAAI,EAAM,YAAY,CACpB,OAEF,IAAM,EAAY,MAAM,EAAO,uBAC7B,EAAS,UAAU,CACnB,EACA,EACA,EACA,EACD,CAUD,MATI,CAAC,GAAa,EAAM,YAAY,CAC3B,CAAE,QAAS,EAAE,CAAE,YAAe,GAClC,CAOE,CACL,QANc,EAAU,OAAQ,GACzB,EAAI,QAAQ,OAAQ,GAAW,EAAO,UAAU,CAAC,SAAW,EACnE,CAAC,IAAK,GACC,KAAK,mCAAmC,EAAO,EAAS,EAAI,CACnE,CAGA,YAAe,GAEhB,CAEH,mCAAmC,EAAO,EAAS,EAAS,CAC1D,IAAM,EAAQ,EAAE,CAChB,IAAK,IAAM,KAAU,EAAQ,QAC3B,IAAK,IAAM,KAAc,EAAO,YAC9B,EAAM,KAAK,CACT,SAAU,EAAM,IAChB,UAAW,IAAK,GAChB,SAAU,CACR,MAAO,KAAK,iBAAiB,EAAO,EAAW,KAAK,CACpD,KAAM,EAAW,QAClB,CACF,CAAC,CASN,MANe,CACb,MAAO,EAAQ,YACf,KAAM,CAAE,QAAO,CACf,YAAa,EAAQ,QACrB,KAAM,WACP,GAID,EAAgB,cAAc,CAAQ,CACxC,YAAY,EAAW,EAAQ,CAC7B,MAAM,EAAO,CACb,KAAK,UAAY,EAEnB,MAAM,mBAAmB,EAAO,EAAU,EAAS,EAAO,CACxD,IAAM,EAAW,EAAM,IACjB,EAAW,EAAS,UAAU,CAC9B,EAAS,EAAM,YAAY,EAAS,CACpC,EAAS,MAAM,KAAK,QAAQ,EAAS,CAC3C,GAAI,EAAM,YAAY,CACpB,OAEF,IAAM,EAAa,MAAM,EAAO,cAAc,EAAU,EAAQ,CAC9D,wBAAyB,GAC1B,CAAC,CACF,GAAI,EAAW,YAAc,GAC3B,MAAO,CACL,MAAO,EAAE,CACT,aAAc,EAAW,sBAC1B,CAEH,GAAI,EAAW,eAAiB,IAAK,GACnC,MAAU,MAAM,mCAAmC,CAErD,IAAM,EAAkB,MAAM,EAAO,oBACnC,EACA,EAEA,GAEA,GAEA,GACD,CACD,GAAI,CAAC,GAAmB,EAAM,YAAY,CACxC,OAEF,IAAM,EAAQ,EAAE,CAChB,IAAK,IAAM,KAAkB,EAAiB,CAC5C,IAAM,EAAS,KAAK,UAAU,iBAAiB,EAAe,SAAS,CACvE,GAAI,EACF,EAAM,KAAK,CACT,SAAU,EAAO,IACjB,UAAW,IAAK,GAChB,SAAU,CACR,MAAO,KAAK,iBAAiB,EAAQ,EAAe,SAAS,CAC7D,KAAM,EACP,CACF,CAAC,MAEF,MAAU,MAAM,gBAAgB,EAAe,SAAS,GAAG,CAG/D,MAAO,CAAE,QAAO,GAGhB,EAAoB,cAAc,CAAQ,CAC5C,MAAM,kBAAkB,EAAO,EAAO,EAAO,CAC3C,IAAM,EAAW,EAAM,IACjB,EAAW,EAAS,UAAU,CAC9B,EAAQ,EAAM,YAAY,CAC9B,WAAY,EAAM,gBAClB,OAAQ,EAAM,YACf,CAAC,CACI,EAAM,EAAM,YAAY,CAC5B,WAAY,EAAM,cAClB,OAAQ,EAAM,UACf,CAAC,CACI,EAAS,MAAM,KAAK,QAAQ,EAAS,CAa3C,OAZI,EAAM,YAAY,CACb,KAWF,CAAE,OATO,MAAM,EAAO,kBAAkB,EAAU,EAAO,EAAI,EAC9C,IAAK,IAClB,CACL,GAAG,EACH,MAAO,EAAK,KACZ,SAAU,EAAM,cAAc,EAAK,SAAS,CAC5C,KAAM,KAAK,iBAAiB,EAAK,KAAK,CACvC,EACD,CACc,YAAe,GAC5B,CAEL,iBAAiB,EAAM,CACrB,OAAQ,EAAR,CACE,IAAK,YACH,OAAO,EAA2B,UAAU,cAAc,UAC5D,IAAK,OACH,OAAO,EAA2B,UAAU,cAAc,KAC5D,QACE,OAAO,EAA2B,UAAU,cAAc,QAM9D,EACA,EACJ,SAAS,EAAgB,EAAU,CACjC,EAAmB,EAAU,EAAU,aAAa,CAEtD,SAAS,EAAgB,EAAU,CACjC,EAAmB,EAAU,EAAU,aAAa,CAEtD,SAAS,GAAsB,CAC7B,OAAO,IAAI,SAAS,EAAS,IAAW,CACtC,GAAI,CAAC,EACH,OAAO,EAAO,6BAA6B,CAE7C,EAAQ,EAAiB,EACzB,CAEJ,SAAS,GAAsB,CAC7B,OAAO,IAAI,SAAS,EAAS,IAAW,CACtC,GAAI,CAAC,EACH,OAAO,EAAO,6BAA6B,CAE7C,EAAQ,EAAiB,EACzB,CAEJ,SAAS,EAAU,EAAU,EAAQ,CACnC,IAAM,EAAc,EAAE,CAChB,EAAY,EAAE,CACd,EAAS,IAAI,EAAc,EAAQ,EAAS,CAClD,EAAY,KAAK,EAAO,CACxB,IAAM,GAAU,GAAG,IACV,EAAO,yBAAyB,GAAG,EAAK,CAE3C,EAAW,IAAI,EAAS,EAAO,CACrC,SAAS,GAAoB,CAC3B,GAAM,CAAE,qBAAsB,EAC9B,EAAW,EAAU,CACjB,EAAkB,iBACpB,EAAU,KACR,EAA2B,UAAU,+BACnC,EACA,IAAI,EAAe,EAAO,CAC3B,CACF,CAEC,EAAkB,eACpB,EAAU,KACR,EAA2B,UAAU,8BACnC,EACA,IAAI,EAAqB,EAAO,CACjC,CACF,CAEC,EAAkB,QACpB,EAAU,KACR,EAA2B,UAAU,sBAAsB,EAAQ,IAAI,EAAiB,EAAO,CAAC,CACjG,CAEC,EAAkB,oBACpB,EAAU,KACR,EAA2B,UAAU,kCACnC,EACA,IAAI,EAAyB,EAAO,CACrC,CACF,CAEC,EAAkB,aACpB,EAAU,KACR,EAA2B,UAAU,2BACnC,EACA,IAAI,EAAkB,EAAU,EAAO,CACxC,CACF,CAEC,EAAkB,YACpB,EAAU,KACR,EAA2B,UAAU,0BACnC,EACA,IAAI,EAAiB,EAAU,EAAO,CACvC,CACF,CAEC,EAAkB,iBACpB,EAAU,KACR,EAA2B,UAAU,+BACnC,EACA,IAAI,EAAe,EAAO,CAC3B,CACF,CAEC,EAAkB,QACpB,EAAU,KACR,EAA2B,UAAU,uBACnC,EACA,IAAI,EAAc,EAAU,EAAO,CACpC,CACF,CAEC,EAAkB,8BACpB,EAAU,KACR,EAA2B,UAAU,4CACnC,EACA,IAAI,EAAc,EAAO,CAC1B,CACF,CAEC,EAAkB,uBACpB,EAAU,KACR,EAA2B,UAAU,qCACnC,EACA,IAAI,EAAoB,EAAO,CAChC,CACF,CAEC,EAAkB,aACpB,EAAU,KACR,EAA2B,UAAU,2BAA2B,EAAQ,IAAI,EAAkB,EAAO,CAAC,CACvG,CAEC,EAAkB,YACpB,EAAU,KACR,EAA2B,UAAU,2BAA2B,EAAQ,IAAI,EAAkB,EAAO,CAAC,CACvG,CAEC,EAAkB,aACpB,EAAU,KAAK,IAAI,EAAmB,EAAU,EAAU,EAAQ,EAAO,CAAC,CAK9E,OAFA,GAAmB,CACnB,EAAY,KAAK,EAAa,EAAU,CAAC,CAClC,EAET,SAAS,EAAa,EAAa,CACjC,MAAO,CAAE,YAAe,EAAW,EAAY,CAAE,CAEnD,SAAS,EAAW,EAAa,CAC/B,KAAO,EAAY,QACjB,EAAY,KAAK,CAAC,SAAS"}