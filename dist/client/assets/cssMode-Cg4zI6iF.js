import{n as e}from"./index-BtNCOo_D.js";
/*!-----------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation. All rights reserved.
* Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
* Released under the MIT license
* https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
*-----------------------------------------------------------------------------*/
var t=Object.defineProperty,n=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a=(e,a,o,s)=>{if(a&&typeof a==`object`||typeof a==`function`)for(let c of r(a))!i.call(e,c)&&c!==o&&t(e,c,{get:()=>a[c],enumerable:!(s=n(a,c))||s.enumerable});return e},o=(e,t,n)=>(a(e,t,`default`),n&&a(n,t,`default`)),s={};o(s,e);var c=class{constructor(e){this._defaults=e,this._worker=null,this._client=null,this._idleCheckInterval=window.setInterval(()=>this._checkIfIdle(),30*1e3),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker())}_stopWorker(){this._worker&&=(this._worker.dispose(),null),this._client=null}dispose(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()}_checkIfIdle(){this._worker&&Date.now()-this._lastUsedTime>12e4&&this._stopWorker()}_getClient(){return this._lastUsedTime=Date.now(),this._client||=(this._worker=s.editor.createWebWorker({moduleId:`vs/language/css/cssWorker`,label:this._defaults.languageId,createData:{options:this._defaults.options,languageId:this._defaults.languageId}}),this._worker.getProxy()),this._client}getLanguageServiceWorker(...e){let t;return this._getClient().then(e=>{t=e}).then(t=>{if(this._worker)return this._worker.withSyncedResources(e)}).then(e=>t)}},l;(function(e){function t(e){return typeof e==`string`}e.is=t})(l||={});var u;(function(e){function t(e){return typeof e==`string`}e.is=t})(u||={});var d;(function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647;function t(t){return typeof t==`number`&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}e.is=t})(d||={});var f;(function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647;function t(t){return typeof t==`number`&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}e.is=t})(f||={});var p;(function(e){function t(e,t){return e===Number.MAX_VALUE&&(e=f.MAX_VALUE),t===Number.MAX_VALUE&&(t=f.MAX_VALUE),{line:e,character:t}}e.create=t;function n(e){let t=e;return B.objectLiteral(t)&&B.uinteger(t.line)&&B.uinteger(t.character)}e.is=n})(p||={});var m;(function(e){function t(e,t,n,r){if(B.uinteger(e)&&B.uinteger(t)&&B.uinteger(n)&&B.uinteger(r))return{start:p.create(e,t),end:p.create(n,r)};if(p.is(e)&&p.is(t))return{start:e,end:t};throw Error(`Range#create called with invalid arguments[${e}, ${t}, ${n}, ${r}]`)}e.create=t;function n(e){let t=e;return B.objectLiteral(t)&&p.is(t.start)&&p.is(t.end)}e.is=n})(m||={});var h;(function(e){function t(e,t){return{uri:e,range:t}}e.create=t;function n(e){let t=e;return B.objectLiteral(t)&&m.is(t.range)&&(B.string(t.uri)||B.undefined(t.uri))}e.is=n})(h||={});var ee;(function(e){function t(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}}e.create=t;function n(e){let t=e;return B.objectLiteral(t)&&m.is(t.targetRange)&&B.string(t.targetUri)&&m.is(t.targetSelectionRange)&&(m.is(t.originSelectionRange)||B.undefined(t.originSelectionRange))}e.is=n})(ee||={});var g;(function(e){function t(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}}e.create=t;function n(e){let t=e;return B.objectLiteral(t)&&B.numberRange(t.red,0,1)&&B.numberRange(t.green,0,1)&&B.numberRange(t.blue,0,1)&&B.numberRange(t.alpha,0,1)}e.is=n})(g||={});var te;(function(e){function t(e,t){return{range:e,color:t}}e.create=t;function n(e){let t=e;return B.objectLiteral(t)&&m.is(t.range)&&g.is(t.color)}e.is=n})(te||={});var ne;(function(e){function t(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}}e.create=t;function n(e){let t=e;return B.objectLiteral(t)&&B.string(t.label)&&(B.undefined(t.textEdit)||S.is(t))&&(B.undefined(t.additionalTextEdits)||B.typedArray(t.additionalTextEdits,S.is))}e.is=n})(ne||={});var _;(function(e){e.Comment=`comment`,e.Imports=`imports`,e.Region=`region`})(_||={});var re;(function(e){function t(e,t,n,r,i,a){let o={startLine:e,endLine:t};return B.defined(n)&&(o.startCharacter=n),B.defined(r)&&(o.endCharacter=r),B.defined(i)&&(o.kind=i),B.defined(a)&&(o.collapsedText=a),o}e.create=t;function n(e){let t=e;return B.objectLiteral(t)&&B.uinteger(t.startLine)&&B.uinteger(t.startLine)&&(B.undefined(t.startCharacter)||B.uinteger(t.startCharacter))&&(B.undefined(t.endCharacter)||B.uinteger(t.endCharacter))&&(B.undefined(t.kind)||B.string(t.kind))}e.is=n})(re||={});var v;(function(e){function t(e,t){return{location:e,message:t}}e.create=t;function n(e){let t=e;return B.defined(t)&&h.is(t.location)&&B.string(t.message)}e.is=n})(v||={});var y;(function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4})(y||={});var ie;(function(e){e.Unnecessary=1,e.Deprecated=2})(ie||={});var ae;(function(e){function t(e){let t=e;return B.objectLiteral(t)&&B.string(t.href)}e.is=t})(ae||={});var b;(function(e){function t(e,t,n,r,i,a){let o={range:e,message:t};return B.defined(n)&&(o.severity=n),B.defined(r)&&(o.code=r),B.defined(i)&&(o.source=i),B.defined(a)&&(o.relatedInformation=a),o}e.create=t;function n(e){let t=e;return B.defined(t)&&m.is(t.range)&&B.string(t.message)&&(B.number(t.severity)||B.undefined(t.severity))&&(B.integer(t.code)||B.string(t.code)||B.undefined(t.code))&&(B.undefined(t.codeDescription)||B.string(t.codeDescription?.href))&&(B.string(t.source)||B.undefined(t.source))&&(B.undefined(t.relatedInformation)||B.typedArray(t.relatedInformation,v.is))}e.is=n})(b||={});var x;(function(e){function t(e,t,...n){let r={title:e,command:t};return B.defined(n)&&n.length>0&&(r.arguments=n),r}e.create=t;function n(e){let t=e;return B.defined(t)&&B.string(t.title)&&B.string(t.command)}e.is=n})(x||={});var S;(function(e){function t(e,t){return{range:e,newText:t}}e.replace=t;function n(e,t){return{range:{start:e,end:e},newText:t}}e.insert=n;function r(e){return{range:e,newText:``}}e.del=r;function i(e){let t=e;return B.objectLiteral(t)&&B.string(t.newText)&&m.is(t.range)}e.is=i})(S||={});var C;(function(e){function t(e,t,n){let r={label:e};return t!==void 0&&(r.needsConfirmation=t),n!==void 0&&(r.description=n),r}e.create=t;function n(e){let t=e;return B.objectLiteral(t)&&B.string(t.label)&&(B.boolean(t.needsConfirmation)||t.needsConfirmation===void 0)&&(B.string(t.description)||t.description===void 0)}e.is=n})(C||={});var w;(function(e){function t(e){let t=e;return B.string(t)}e.is=t})(w||={});var oe;(function(e){function t(e,t,n){return{range:e,newText:t,annotationId:n}}e.replace=t;function n(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}}e.insert=n;function r(e,t){return{range:e,newText:``,annotationId:t}}e.del=r;function i(e){let t=e;return S.is(t)&&(C.is(t.annotationId)||w.is(t.annotationId))}e.is=i})(oe||={});var se;(function(e){function t(e,t){return{textDocument:e,edits:t}}e.create=t;function n(e){let t=e;return B.defined(t)&&k.is(t.textDocument)&&Array.isArray(t.edits)}e.is=n})(se||={});var T;(function(e){function t(e,t,n){let r={kind:`create`,uri:e};return t!==void 0&&(t.overwrite!==void 0||t.ignoreIfExists!==void 0)&&(r.options=t),n!==void 0&&(r.annotationId=n),r}e.create=t;function n(e){let t=e;return t&&t.kind===`create`&&B.string(t.uri)&&(t.options===void 0||(t.options.overwrite===void 0||B.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===void 0||B.boolean(t.options.ignoreIfExists)))&&(t.annotationId===void 0||w.is(t.annotationId))}e.is=n})(T||={});var E;(function(e){function t(e,t,n,r){let i={kind:`rename`,oldUri:e,newUri:t};return n!==void 0&&(n.overwrite!==void 0||n.ignoreIfExists!==void 0)&&(i.options=n),r!==void 0&&(i.annotationId=r),i}e.create=t;function n(e){let t=e;return t&&t.kind===`rename`&&B.string(t.oldUri)&&B.string(t.newUri)&&(t.options===void 0||(t.options.overwrite===void 0||B.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===void 0||B.boolean(t.options.ignoreIfExists)))&&(t.annotationId===void 0||w.is(t.annotationId))}e.is=n})(E||={});var D;(function(e){function t(e,t,n){let r={kind:`delete`,uri:e};return t!==void 0&&(t.recursive!==void 0||t.ignoreIfNotExists!==void 0)&&(r.options=t),n!==void 0&&(r.annotationId=n),r}e.create=t;function n(e){let t=e;return t&&t.kind===`delete`&&B.string(t.uri)&&(t.options===void 0||(t.options.recursive===void 0||B.boolean(t.options.recursive))&&(t.options.ignoreIfNotExists===void 0||B.boolean(t.options.ignoreIfNotExists)))&&(t.annotationId===void 0||w.is(t.annotationId))}e.is=n})(D||={});var O;(function(e){function t(e){let t=e;return t&&(t.changes!==void 0||t.documentChanges!==void 0)&&(t.documentChanges===void 0||t.documentChanges.every(e=>B.string(e.kind)?T.is(e)||E.is(e)||D.is(e):se.is(e)))}e.is=t})(O||={});var ce;(function(e){function t(e){return{uri:e}}e.create=t;function n(e){let t=e;return B.defined(t)&&B.string(t.uri)}e.is=n})(ce||={});var le;(function(e){function t(e,t){return{uri:e,version:t}}e.create=t;function n(e){let t=e;return B.defined(t)&&B.string(t.uri)&&B.integer(t.version)}e.is=n})(le||={});var k;(function(e){function t(e,t){return{uri:e,version:t}}e.create=t;function n(e){let t=e;return B.defined(t)&&B.string(t.uri)&&(t.version===null||B.integer(t.version))}e.is=n})(k||={});var ue;(function(e){function t(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}}e.create=t;function n(e){let t=e;return B.defined(t)&&B.string(t.uri)&&B.string(t.languageId)&&B.integer(t.version)&&B.string(t.text)}e.is=n})(ue||={});var A;(function(e){e.PlainText=`plaintext`,e.Markdown=`markdown`;function t(t){let n=t;return n===e.PlainText||n===e.Markdown}e.is=t})(A||={});var j;(function(e){function t(e){let t=e;return B.objectLiteral(e)&&A.is(t.kind)&&B.string(t.value)}e.is=t})(j||={});var M;(function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25})(M||={});var N;(function(e){e.PlainText=1,e.Snippet=2})(N||={});var de;(function(e){e.Deprecated=1})(de||={});var fe;(function(e){function t(e,t,n){return{newText:e,insert:t,replace:n}}e.create=t;function n(e){let t=e;return t&&B.string(t.newText)&&m.is(t.insert)&&m.is(t.replace)}e.is=n})(fe||={});var pe;(function(e){e.asIs=1,e.adjustIndentation=2})(pe||={});var me;(function(e){function t(e){let t=e;return t&&(B.string(t.detail)||t.detail===void 0)&&(B.string(t.description)||t.description===void 0)}e.is=t})(me||={});var he;(function(e){function t(e){return{label:e}}e.create=t})(he||={});var ge;(function(e){function t(e,t){return{items:e||[],isIncomplete:!!t}}e.create=t})(ge||={});var P;(function(e){function t(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,`\\$&`)}e.fromPlainText=t;function n(e){let t=e;return B.string(t)||B.objectLiteral(t)&&B.string(t.language)&&B.string(t.value)}e.is=n})(P||={});var _e;(function(e){function t(e){let t=e;return!!t&&B.objectLiteral(t)&&(j.is(t.contents)||P.is(t.contents)||B.typedArray(t.contents,P.is))&&(e.range===void 0||m.is(e.range))}e.is=t})(_e||={});var ve;(function(e){function t(e,t){return t?{label:e,documentation:t}:{label:e}}e.create=t})(ve||={});var ye;(function(e){function t(e,t,...n){let r={label:e};return B.defined(t)&&(r.documentation=t),B.defined(n)?r.parameters=n:r.parameters=[],r}e.create=t})(ye||={});var F;(function(e){e.Text=1,e.Read=2,e.Write=3})(F||={});var be;(function(e){function t(e,t){let n={range:e};return B.number(t)&&(n.kind=t),n}e.create=t})(be||={});var I;(function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26})(I||={});var xe;(function(e){e.Deprecated=1})(xe||={});var Se;(function(e){function t(e,t,n,r,i){let a={name:e,kind:t,location:{uri:r,range:n}};return i&&(a.containerName=i),a}e.create=t})(Se||={});var Ce;(function(e){function t(e,t,n,r){return r===void 0?{name:e,kind:t,location:{uri:n}}:{name:e,kind:t,location:{uri:n,range:r}}}e.create=t})(Ce||={});var we;(function(e){function t(e,t,n,r,i,a){let o={name:e,detail:t,kind:n,range:r,selectionRange:i};return a!==void 0&&(o.children=a),o}e.create=t;function n(e){let t=e;return t&&B.string(t.name)&&B.number(t.kind)&&m.is(t.range)&&m.is(t.selectionRange)&&(t.detail===void 0||B.string(t.detail))&&(t.deprecated===void 0||B.boolean(t.deprecated))&&(t.children===void 0||Array.isArray(t.children))&&(t.tags===void 0||Array.isArray(t.tags))}e.is=n})(we||={});var Te;(function(e){e.Empty=``,e.QuickFix=`quickfix`,e.Refactor=`refactor`,e.RefactorExtract=`refactor.extract`,e.RefactorInline=`refactor.inline`,e.RefactorRewrite=`refactor.rewrite`,e.Source=`source`,e.SourceOrganizeImports=`source.organizeImports`,e.SourceFixAll=`source.fixAll`})(Te||={});var L;(function(e){e.Invoked=1,e.Automatic=2})(L||={});var Ee;(function(e){function t(e,t,n){let r={diagnostics:e};return t!=null&&(r.only=t),n!=null&&(r.triggerKind=n),r}e.create=t;function n(e){let t=e;return B.defined(t)&&B.typedArray(t.diagnostics,b.is)&&(t.only===void 0||B.typedArray(t.only,B.string))&&(t.triggerKind===void 0||t.triggerKind===L.Invoked||t.triggerKind===L.Automatic)}e.is=n})(Ee||={});var De;(function(e){function t(e,t,n){let r={title:e},i=!0;return typeof t==`string`?(i=!1,r.kind=t):x.is(t)?r.command=t:r.edit=t,i&&n!==void 0&&(r.kind=n),r}e.create=t;function n(e){let t=e;return t&&B.string(t.title)&&(t.diagnostics===void 0||B.typedArray(t.diagnostics,b.is))&&(t.kind===void 0||B.string(t.kind))&&(t.edit!==void 0||t.command!==void 0)&&(t.command===void 0||x.is(t.command))&&(t.isPreferred===void 0||B.boolean(t.isPreferred))&&(t.edit===void 0||O.is(t.edit))}e.is=n})(De||={});var Oe;(function(e){function t(e,t){let n={range:e};return B.defined(t)&&(n.data=t),n}e.create=t;function n(e){let t=e;return B.defined(t)&&m.is(t.range)&&(B.undefined(t.command)||x.is(t.command))}e.is=n})(Oe||={});var ke;(function(e){function t(e,t){return{tabSize:e,insertSpaces:t}}e.create=t;function n(e){let t=e;return B.defined(t)&&B.uinteger(t.tabSize)&&B.boolean(t.insertSpaces)}e.is=n})(ke||={});var Ae;(function(e){function t(e,t,n){return{range:e,target:t,data:n}}e.create=t;function n(e){let t=e;return B.defined(t)&&m.is(t.range)&&(B.undefined(t.target)||B.string(t.target))}e.is=n})(Ae||={});var je;(function(e){function t(e,t){return{range:e,parent:t}}e.create=t;function n(t){let n=t;return B.objectLiteral(n)&&m.is(n.range)&&(n.parent===void 0||e.is(n.parent))}e.is=n})(je||={});var Me;(function(e){e.namespace=`namespace`,e.type=`type`,e.class=`class`,e.enum=`enum`,e.interface=`interface`,e.struct=`struct`,e.typeParameter=`typeParameter`,e.parameter=`parameter`,e.variable=`variable`,e.property=`property`,e.enumMember=`enumMember`,e.event=`event`,e.function=`function`,e.method=`method`,e.macro=`macro`,e.keyword=`keyword`,e.modifier=`modifier`,e.comment=`comment`,e.string=`string`,e.number=`number`,e.regexp=`regexp`,e.operator=`operator`,e.decorator=`decorator`})(Me||={});var Ne;(function(e){e.declaration=`declaration`,e.definition=`definition`,e.readonly=`readonly`,e.static=`static`,e.deprecated=`deprecated`,e.abstract=`abstract`,e.async=`async`,e.modification=`modification`,e.documentation=`documentation`,e.defaultLibrary=`defaultLibrary`})(Ne||={});var Pe;(function(e){function t(e){let t=e;return B.objectLiteral(t)&&(t.resultId===void 0||typeof t.resultId==`string`)&&Array.isArray(t.data)&&(t.data.length===0||typeof t.data[0]==`number`)}e.is=t})(Pe||={});var Fe;(function(e){function t(e,t){return{range:e,text:t}}e.create=t;function n(e){let t=e;return t!=null&&m.is(t.range)&&B.string(t.text)}e.is=n})(Fe||={});var Ie;(function(e){function t(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}}e.create=t;function n(e){let t=e;return t!=null&&m.is(t.range)&&B.boolean(t.caseSensitiveLookup)&&(B.string(t.variableName)||t.variableName===void 0)}e.is=n})(Ie||={});var Le;(function(e){function t(e,t){return{range:e,expression:t}}e.create=t;function n(e){let t=e;return t!=null&&m.is(t.range)&&(B.string(t.expression)||t.expression===void 0)}e.is=n})(Le||={});var Re;(function(e){function t(e,t){return{frameId:e,stoppedLocation:t}}e.create=t;function n(e){let t=e;return B.defined(t)&&m.is(e.stoppedLocation)}e.is=n})(Re||={});var R;(function(e){e.Type=1,e.Parameter=2;function t(e){return e===1||e===2}e.is=t})(R||={});var z;(function(e){function t(e){return{value:e}}e.create=t;function n(e){let t=e;return B.objectLiteral(t)&&(t.tooltip===void 0||B.string(t.tooltip)||j.is(t.tooltip))&&(t.location===void 0||h.is(t.location))&&(t.command===void 0||x.is(t.command))}e.is=n})(z||={});var ze;(function(e){function t(e,t,n){let r={position:e,label:t};return n!==void 0&&(r.kind=n),r}e.create=t;function n(e){let t=e;return B.objectLiteral(t)&&p.is(t.position)&&(B.string(t.label)||B.typedArray(t.label,z.is))&&(t.kind===void 0||R.is(t.kind))&&t.textEdits===void 0||B.typedArray(t.textEdits,S.is)&&(t.tooltip===void 0||B.string(t.tooltip)||j.is(t.tooltip))&&(t.paddingLeft===void 0||B.boolean(t.paddingLeft))&&(t.paddingRight===void 0||B.boolean(t.paddingRight))}e.is=n})(ze||={});var Be;(function(e){function t(e){return{kind:`snippet`,value:e}}e.createSnippet=t})(Be||={});var Ve;(function(e){function t(e,t,n,r){return{insertText:e,filterText:t,range:n,command:r}}e.create=t})(Ve||={});var He;(function(e){function t(e){return{items:e}}e.create=t})(He||={});var Ue;(function(e){e.Invoked=0,e.Automatic=1})(Ue||={});var We;(function(e){function t(e,t){return{range:e,text:t}}e.create=t})(We||={});var Ge;(function(e){function t(e,t){return{triggerKind:e,selectedCompletionInfo:t}}e.create=t})(Ge||={});var Ke;(function(e){function t(e){let t=e;return B.objectLiteral(t)&&u.is(t.uri)&&B.string(t.name)}e.is=t})(Ke||={});var qe;(function(e){function t(e,t,n,r){return new Je(e,t,n,r)}e.create=t;function n(e){let t=e;return!!(B.defined(t)&&B.string(t.uri)&&(B.undefined(t.languageId)||B.string(t.languageId))&&B.uinteger(t.lineCount)&&B.func(t.getText)&&B.func(t.positionAt)&&B.func(t.offsetAt))}e.is=n;function r(e,t){let n=e.getText(),r=i(t,(e,t)=>{let n=e.range.start.line-t.range.start.line;return n===0?e.range.start.character-t.range.start.character:n}),a=n.length;for(let t=r.length-1;t>=0;t--){let i=r[t],o=e.offsetAt(i.range.start),s=e.offsetAt(i.range.end);if(s<=a)n=n.substring(0,o)+i.newText+n.substring(s,n.length);else throw Error(`Overlapping edit`);a=o}return n}e.applyEdits=r;function i(e,t){if(e.length<=1)return e;let n=e.length/2|0,r=e.slice(0,n),a=e.slice(n);i(r,t),i(a,t);let o=0,s=0,c=0;for(;o<r.length&&s<a.length;)t(r[o],a[s])<=0?e[c++]=r[o++]:e[c++]=a[s++];for(;o<r.length;)e[c++]=r[o++];for(;s<a.length;)e[c++]=a[s++];return e}})(qe||={});var Je=class{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],t=this._content,n=!0;for(let r=0;r<t.length;r++){n&&=(e.push(r),!1);let i=t.charAt(r);n=i===`\r`||i===`
`,i===`\r`&&r+1<t.length&&t.charAt(r+1)===`
`&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(r===0)return p.create(0,e);for(;n<r;){let i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}let i=n-1;return p.create(i,e-t[i])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}},B;(function(e){let t=Object.prototype.toString;function n(e){return e!==void 0}e.defined=n;function r(e){return e===void 0}e.undefined=r;function i(e){return e===!0||e===!1}e.boolean=i;function a(e){return t.call(e)===`[object String]`}e.string=a;function o(e){return t.call(e)===`[object Number]`}e.number=o;function s(e,n,r){return t.call(e)===`[object Number]`&&n<=e&&e<=r}e.numberRange=s;function c(e){return t.call(e)===`[object Number]`&&-2147483648<=e&&e<=2147483647}e.integer=c;function l(e){return t.call(e)===`[object Number]`&&0<=e&&e<=2147483647}e.uinteger=l;function u(e){return t.call(e)===`[object Function]`}e.func=u;function d(e){return typeof e==`object`&&!!e}e.objectLiteral=d;function f(e,t){return Array.isArray(e)&&e.every(t)}e.typedArray=f})(B||={});var V=class{constructor(e,t,n){this._languageId=e,this._worker=t,this._disposables=[],this._listener=Object.create(null);let r=e=>{let t=e.getLanguageId();if(t!==this._languageId)return;let n;this._listener[e.uri.toString()]=e.onDidChangeContent(()=>{window.clearTimeout(n),n=window.setTimeout(()=>this._doValidate(e.uri,t),500)}),this._doValidate(e.uri,t)},i=e=>{s.editor.setModelMarkers(e,this._languageId,[]);let t=e.uri.toString(),n=this._listener[t];n&&(n.dispose(),delete this._listener[t])};this._disposables.push(s.editor.onDidCreateModel(r)),this._disposables.push(s.editor.onWillDisposeModel(i)),this._disposables.push(s.editor.onDidChangeModelLanguage(e=>{i(e.model),r(e.model)})),this._disposables.push(n(e=>{s.editor.getModels().forEach(e=>{e.getLanguageId()===this._languageId&&(i(e),r(e))})})),this._disposables.push({dispose:()=>{for(let e in s.editor.getModels().forEach(i),this._listener)this._listener[e].dispose()}}),s.editor.getModels().forEach(r)}dispose(){this._disposables.forEach(e=>e&&e.dispose()),this._disposables.length=0}_doValidate(e,t){this._worker(e).then(t=>t.doValidation(e.toString())).then(n=>{let r=n.map(t=>Xe(e,t)),i=s.editor.getModel(e);i&&i.getLanguageId()===t&&s.editor.setModelMarkers(i,t,r)}).then(void 0,e=>{console.error(e)})}};function Ye(e){switch(e){case y.Error:return s.MarkerSeverity.Error;case y.Warning:return s.MarkerSeverity.Warning;case y.Information:return s.MarkerSeverity.Info;case y.Hint:return s.MarkerSeverity.Hint;default:return s.MarkerSeverity.Info}}function Xe(e,t){let n=typeof t.code==`number`?String(t.code):t.code;return{severity:Ye(t.severity),startLineNumber:t.range.start.line+1,startColumn:t.range.start.character+1,endLineNumber:t.range.end.line+1,endColumn:t.range.end.character+1,message:t.message,code:n,source:t.source}}var H=class{constructor(e,t){this._worker=e,this._triggerCharacters=t}get triggerCharacters(){return this._triggerCharacters}provideCompletionItems(e,t,n,r){let i=e.uri;return this._worker(i).then(e=>e.doComplete(i.toString(),U(t))).then(n=>{if(!n)return;let r=e.getWordUntilPosition(t),i=new s.Range(t.lineNumber,r.startColumn,t.lineNumber,r.endColumn),a=n.items.map(e=>{let t={label:e.label,insertText:e.insertText||e.label,sortText:e.sortText,filterText:e.filterText,documentation:e.documentation,detail:e.detail,command:$e(e.command),range:i,kind:Qe(e.kind)};return e.textEdit&&(Ze(e.textEdit)?t.range={insert:G(e.textEdit.insert),replace:G(e.textEdit.replace)}:t.range=G(e.textEdit.range),t.insertText=e.textEdit.newText),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(K)),e.insertTextFormat===N.Snippet&&(t.insertTextRules=s.languages.CompletionItemInsertTextRule.InsertAsSnippet),t});return{isIncomplete:n.isIncomplete,suggestions:a}})}};function U(e){if(e)return{character:e.column-1,line:e.lineNumber-1}}function W(e){if(e)return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function G(e){if(e)return new s.Range(e.start.line+1,e.start.character+1,e.end.line+1,e.end.character+1)}function Ze(e){return e.insert!==void 0&&e.replace!==void 0}function Qe(e){let t=s.languages.CompletionItemKind;switch(e){case M.Text:return t.Text;case M.Method:return t.Method;case M.Function:return t.Function;case M.Constructor:return t.Constructor;case M.Field:return t.Field;case M.Variable:return t.Variable;case M.Class:return t.Class;case M.Interface:return t.Interface;case M.Module:return t.Module;case M.Property:return t.Property;case M.Unit:return t.Unit;case M.Value:return t.Value;case M.Enum:return t.Enum;case M.Keyword:return t.Keyword;case M.Snippet:return t.Snippet;case M.Color:return t.Color;case M.File:return t.File;case M.Reference:return t.Reference}return t.Property}function K(e){if(e)return{range:G(e.range),text:e.newText}}function $e(e){return e&&e.command===`editor.action.triggerSuggest`?{id:e.command,title:e.title,arguments:e.arguments}:void 0}var et=class{constructor(e){this._worker=e}provideHover(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.doHover(r.toString(),U(t))).then(e=>{if(e)return{range:G(e.range),contents:nt(e.contents)}})}};function tt(e){return e&&typeof e==`object`&&typeof e.kind==`string`}function q(e){return typeof e==`string`?{value:e}:tt(e)?e.kind===`plaintext`?{value:e.value.replace(/[\\`*_{}[\]()#+\-.!]/g,`\\$&`)}:{value:e.value}:{value:"```"+e.language+`
`+e.value+"\n```\n"}}function nt(e){if(e)return Array.isArray(e)?e.map(q):[q(e)]}var J=class{constructor(e){this._worker=e}provideDocumentHighlights(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.findDocumentHighlights(r.toString(),U(t))).then(e=>{if(e)return e.map(e=>({range:G(e.range),kind:rt(e.kind)}))})}};function rt(e){switch(e){case F.Read:return s.languages.DocumentHighlightKind.Read;case F.Write:return s.languages.DocumentHighlightKind.Write;case F.Text:return s.languages.DocumentHighlightKind.Text}return s.languages.DocumentHighlightKind.Text}var Y=class{constructor(e){this._worker=e}provideDefinition(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.findDefinition(r.toString(),U(t))).then(e=>{if(e)return[X(e)]})}};function X(e){return{uri:s.Uri.parse(e.uri),range:G(e.range)}}var Z=class{constructor(e){this._worker=e}provideReferences(e,t,n,r){let i=e.uri;return this._worker(i).then(e=>e.findReferences(i.toString(),U(t))).then(e=>{if(e)return e.map(X)})}},Q=class{constructor(e){this._worker=e}provideRenameEdits(e,t,n,r){let i=e.uri;return this._worker(i).then(e=>e.doRename(i.toString(),U(t),n)).then(e=>it(e))}};function it(e){if(!e||!e.changes)return;let t=[];for(let n in e.changes){let r=s.Uri.parse(n);for(let i of e.changes[n])t.push({resource:r,versionId:void 0,textEdit:{range:G(i.range),text:i.newText}})}return{edits:t}}var $=class{constructor(e){this._worker=e}provideDocumentSymbols(e,t){let n=e.uri;return this._worker(n).then(e=>e.findDocumentSymbols(n.toString())).then(e=>{if(e)return e.map(e=>at(e)?ot(e):{name:e.name,detail:``,containerName:e.containerName,kind:st(e.kind),range:G(e.location.range),selectionRange:G(e.location.range),tags:[]})})}};function at(e){return`children`in e}function ot(e){return{name:e.name,detail:e.detail??``,kind:st(e.kind),range:G(e.range),selectionRange:G(e.selectionRange),tags:e.tags??[],children:(e.children??[]).map(e=>ot(e))}}function st(e){let t=s.languages.SymbolKind;switch(e){case I.File:return t.File;case I.Module:return t.Module;case I.Namespace:return t.Namespace;case I.Package:return t.Package;case I.Class:return t.Class;case I.Method:return t.Method;case I.Property:return t.Property;case I.Field:return t.Field;case I.Constructor:return t.Constructor;case I.Enum:return t.Enum;case I.Interface:return t.Interface;case I.Function:return t.Function;case I.Variable:return t.Variable;case I.Constant:return t.Constant;case I.String:return t.String;case I.Number:return t.Number;case I.Boolean:return t.Boolean;case I.Array:return t.Array}return t.Function}var ct=class{constructor(e){this._worker=e}provideLinks(e,t){let n=e.uri;return this._worker(n).then(e=>e.findDocumentLinks(n.toString())).then(e=>{if(e)return{links:e.map(e=>({range:G(e.range),url:e.target}))}})}},lt=class{constructor(e){this._worker=e}provideDocumentFormattingEdits(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.format(r.toString(),null,dt(t)).then(e=>{if(!(!e||e.length===0))return e.map(K)}))}},ut=class{constructor(e){this._worker=e,this.canFormatMultipleRanges=!1}provideDocumentRangeFormattingEdits(e,t,n,r){let i=e.uri;return this._worker(i).then(e=>e.format(i.toString(),W(t),dt(n)).then(e=>{if(!(!e||e.length===0))return e.map(K)}))}};function dt(e){return{tabSize:e.tabSize,insertSpaces:e.insertSpaces}}var ft=class{constructor(e){this._worker=e}provideDocumentColors(e,t){let n=e.uri;return this._worker(n).then(e=>e.findDocumentColors(n.toString())).then(e=>{if(e)return e.map(e=>({color:e.color,range:G(e.range)}))})}provideColorPresentations(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.getColorPresentations(r.toString(),t.color,W(t.range))).then(e=>{if(e)return e.map(e=>{let t={label:e.label};return e.textEdit&&(t.textEdit=K(e.textEdit)),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(K)),t})})}},pt=class{constructor(e){this._worker=e}provideFoldingRanges(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.getFoldingRanges(r.toString(),t)).then(e=>{if(e)return e.map(e=>{let t={start:e.startLine+1,end:e.endLine+1};return e.kind!==void 0&&(t.kind=mt(e.kind)),t})})}};function mt(e){switch(e){case _.Comment:return s.languages.FoldingRangeKind.Comment;case _.Imports:return s.languages.FoldingRangeKind.Imports;case _.Region:return s.languages.FoldingRangeKind.Region}}var ht=class{constructor(e){this._worker=e}provideSelectionRanges(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.getSelectionRanges(r.toString(),t.map(U))).then(e=>{if(e)return e.map(e=>{let t=[];for(;e;)t.push({range:G(e.range)}),e=e.parent;return t})})}};function gt(e){let t=[],n=[],r=new c(e);t.push(r);let i=(...e)=>r.getLanguageServiceWorker(...e);function a(){let{languageId:t,modeConfiguration:r}=e;vt(n),r.completionItems&&n.push(s.languages.registerCompletionItemProvider(t,new H(i,[`/`,`-`,`:`]))),r.hovers&&n.push(s.languages.registerHoverProvider(t,new et(i))),r.documentHighlights&&n.push(s.languages.registerDocumentHighlightProvider(t,new J(i))),r.definitions&&n.push(s.languages.registerDefinitionProvider(t,new Y(i))),r.references&&n.push(s.languages.registerReferenceProvider(t,new Z(i))),r.documentSymbols&&n.push(s.languages.registerDocumentSymbolProvider(t,new $(i))),r.rename&&n.push(s.languages.registerRenameProvider(t,new Q(i))),r.colors&&n.push(s.languages.registerColorProvider(t,new ft(i))),r.foldingRanges&&n.push(s.languages.registerFoldingRangeProvider(t,new pt(i))),r.diagnostics&&n.push(new V(t,i,e.onDidChange)),r.selectionRanges&&n.push(s.languages.registerSelectionRangeProvider(t,new ht(i))),r.documentFormattingEdits&&n.push(s.languages.registerDocumentFormattingEditProvider(t,new lt(i))),r.documentRangeFormattingEdits&&n.push(s.languages.registerDocumentRangeFormattingEditProvider(t,new ut(i)))}return a(),t.push(_t(n)),_t(t)}function _t(e){return{dispose:()=>vt(e)}}function vt(e){for(;e.length;)e.pop().dispose()}export{H as CompletionAdapter,Y as DefinitionAdapter,V as DiagnosticsAdapter,ft as DocumentColorAdapter,lt as DocumentFormattingEditProvider,J as DocumentHighlightAdapter,ct as DocumentLinkAdapter,ut as DocumentRangeFormattingEditProvider,$ as DocumentSymbolAdapter,pt as FoldingRangeAdapter,et as HoverAdapter,Z as ReferenceAdapter,Q as RenameAdapter,ht as SelectionRangeAdapter,c as WorkerManager,U as fromPosition,W as fromRange,gt as setupMode,G as toRange,K as toTextEdit};
//# sourceMappingURL=cssMode-Cg4zI6iF.js.map