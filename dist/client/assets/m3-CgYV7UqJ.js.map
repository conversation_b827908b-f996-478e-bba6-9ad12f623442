{"version": 3, "file": "m3-CgYV7UqJ.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/m3/m3.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/m3/m3.ts\nvar conf = {\n  comments: {\n    blockComment: [\"(*\", \"*)\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"(*\", close: \"*)\" },\n    { open: \"<*\", close: \"*>\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".m3\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\n    \"AND\",\n    \"ANY\",\n    \"ARRAY\",\n    \"AS\",\n    \"BEGIN\",\n    \"BITS\",\n    \"BRANDED\",\n    \"BY\",\n    \"CASE\",\n    \"CONST\",\n    \"DIV\",\n    \"DO\",\n    \"ELSE\",\n    \"ELSIF\",\n    \"END\",\n    \"EVAL\",\n    \"EXCEPT\",\n    \"EXCEPTION\",\n    \"EXIT\",\n    \"EXPORTS\",\n    \"FINALLY\",\n    \"FOR\",\n    \"FROM\",\n    \"GENERIC\",\n    \"IF\",\n    \"IMPORT\",\n    \"IN\",\n    \"INTERFACE\",\n    \"LOCK\",\n    \"LOOP\",\n    \"METHODS\",\n    \"MOD\",\n    \"MODULE\",\n    \"NOT\",\n    \"OBJECT\",\n    \"OF\",\n    \"OR\",\n    \"OVERRIDES\",\n    \"PROCEDURE\",\n    \"RAISE\",\n    \"RAISES\",\n    \"READONLY\",\n    \"RECORD\",\n    \"REF\",\n    \"REPEAT\",\n    \"RETURN\",\n    \"REVEAL\",\n    \"SET\",\n    \"THEN\",\n    \"TO\",\n    \"TRY\",\n    \"TYPE\",\n    \"TYPECASE\",\n    \"UNSAFE\",\n    \"UNTIL\",\n    \"UNTRACED\",\n    \"VALUE\",\n    \"VAR\",\n    \"WHILE\",\n    \"WITH\"\n  ],\n  reservedConstNames: [\n    \"ABS\",\n    \"ADR\",\n    \"ADRSIZE\",\n    \"BITSIZE\",\n    \"BYTESIZE\",\n    \"CEILING\",\n    \"DEC\",\n    \"DISPOSE\",\n    \"FALSE\",\n    \"FIRST\",\n    \"FLOAT\",\n    \"FLOOR\",\n    \"INC\",\n    \"ISTYPE\",\n    \"LAST\",\n    \"LOOPHOLE\",\n    \"MAX\",\n    \"MIN\",\n    \"NARROW\",\n    \"NEW\",\n    \"NIL\",\n    \"NUMBER\",\n    \"ORD\",\n    \"ROUND\",\n    \"SUBARRAY\",\n    \"TRUE\",\n    \"TRUNC\",\n    \"TYPECODE\",\n    \"VAL\"\n  ],\n  reservedTypeNames: [\n    \"ADDRESS\",\n    \"ANY\",\n    \"BOOLEAN\",\n    \"CARDINAL\",\n    \"CHAR\",\n    \"EXTENDED\",\n    \"INTEGER\",\n    \"LONGCARD\",\n    \"LONGINT\",\n    \"LONGREAL\",\n    \"MUTEX\",\n    \"NULL\",\n    \"REAL\",\n    \"REFANY\",\n    \"ROOT\",\n    \"TEXT\"\n  ],\n  operators: [\"+\", \"-\", \"*\", \"/\", \"&\", \"^\", \".\"],\n  relations: [\"=\", \"#\", \"<\", \"<=\", \">\", \">=\", \"<:\", \":\"],\n  delimiters: [\"|\", \"..\", \"=>\", \",\", \";\", \":=\"],\n  symbols: /[>=<#.,:;+\\-*/&^]+/,\n  escapes: /\\\\(?:[\\\\fnrt\"']|[0-7]{3})/,\n  tokenizer: {\n    root: [\n      // Identifiers and keywords\n      [/_\\w*/, \"invalid\"],\n      [\n        /[a-zA-Z][a-zA-Z0-9_]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@reservedConstNames\": { token: \"constant.reserved.$0\" },\n            \"@reservedTypeNames\": { token: \"type.reserved.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Whitespace\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      // Integer- and real literals\n      [/[0-9]+\\.[0-9]+(?:[DdEeXx][\\+\\-]?[0-9]+)?/, \"number.float\"],\n      [/[0-9]+(?:\\_[0-9a-fA-F]+)?L?/, \"number\"],\n      // Operators, relations, and delimiters\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operators\",\n            \"@relations\": \"operators\",\n            \"@delimiters\": \"delimiter\",\n            \"@default\": \"invalid\"\n          }\n        }\n      ],\n      // Character literals\n      [/'[^\\\\']'/, \"string.char\"],\n      [/(')(@escapes)(')/, [\"string.char\", \"string.escape\", \"string.char\"]],\n      [/'/, \"invalid\"],\n      // Text literals\n      [/\"([^\"\\\\]|\\\\.)*$/, \"invalid\"],\n      [/\"/, \"string.text\", \"@text\"]\n    ],\n    text: [\n      [/[^\\\\\"]+/, \"string.text\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"invalid\"],\n      [/\"/, \"string.text\", \"@pop\"]\n    ],\n    comment: [\n      [/\\(\\*/, \"comment\", \"@push\"],\n      [/\\*\\)/, \"comment\", \"@pop\"],\n      [/./, \"comment\"]\n    ],\n    pragma: [\n      [/<\\*/, \"keyword.pragma\", \"@push\"],\n      [/\\*>/, \"keyword.pragma\", \"@pop\"],\n      [/./, \"keyword.pragma\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\(\\*/, \"comment\", \"@comment\"],\n      [/<\\*/, \"keyword.pragma\", \"@pragma\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,KAAM,MAAO,KAAM,CAC3B,CAAE,KAAM,KAAM,MAAO,KAAM,CAC3B,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACvD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAU,UAAU,CAAE,CACxD,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,MACd,SAAU,CACR,CAAE,MAAO,kBAAmB,KAAM,IAAK,MAAO,IAAK,CACnD,CAAE,MAAO,wBAAyB,KAAM,IAAK,MAAO,IAAK,CACzD,CAAE,MAAO,mBAAoB,KAAM,IAAK,MAAO,IAAK,CACrD,CACD,SAAU,wWA6DT,CACD,mBAAoB,2LA8BnB,CACD,kBAAmB,CACjB,UACA,MACA,UACA,WACA,OACA,WACA,UACA,WACA,UACA,WACA,QACA,OACA,OACA,SACA,OACA,OACD,CACD,UAAW,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CAC9C,UAAW,CAAC,IAAK,IAAK,IAAK,KAAM,IAAK,KAAM,KAAM,IAAI,CACtD,WAAY,CAAC,IAAK,KAAM,KAAM,IAAK,IAAK,KAAK,CAC7C,QAAS,qBACT,QAAS,4BACT,UAAW,CACT,KAAM,CAEJ,CAAC,OAAQ,UAAU,CACnB,CACE,wBACA,CACE,MAAO,CACL,YAAa,CAAE,MAAO,aAAc,CACpC,sBAAuB,CAAE,MAAO,uBAAwB,CACxD,qBAAsB,CAAE,MAAO,mBAAoB,CACnD,WAAY,aACb,CACF,CACF,CAED,CAAE,QAAS,cAAe,CAC1B,CAAC,aAAc,YAAY,CAE3B,CAAC,2CAA4C,eAAe,CAC5D,CAAC,8BAA+B,SAAS,CAEzC,CACE,WACA,CACE,MAAO,CACL,aAAc,YACd,aAAc,YACd,cAAe,YACf,WAAY,UACb,CACF,CACF,CAED,CAAC,WAAY,cAAc,CAC3B,CAAC,mBAAoB,CAAC,cAAe,gBAAiB,cAAc,CAAC,CACrE,CAAC,IAAK,UAAU,CAEhB,CAAC,kBAAmB,UAAU,CAC9B,CAAC,IAAK,cAAe,QAAQ,CAC9B,CACD,KAAM,CACJ,CAAC,UAAW,cAAc,CAC1B,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,UAAU,CAClB,CAAC,IAAK,cAAe,OAAO,CAC7B,CACD,QAAS,CACP,CAAC,OAAQ,UAAW,QAAQ,CAC5B,CAAC,OAAQ,UAAW,OAAO,CAC3B,CAAC,IAAK,UAAU,CACjB,CACD,OAAQ,CACN,CAAC,MAAO,iBAAkB,QAAQ,CAClC,CAAC,MAAO,iBAAkB,OAAO,CACjC,CAAC,IAAK,iBAAiB,CACxB,CACD,WAAY,CACV,CAAC,aAAc,QAAQ,CACvB,CAAC,OAAQ,UAAW,WAAW,CAC/B,CAAC,MAAO,iBAAkB,UAAU,CACrC,CACF,CACF"}