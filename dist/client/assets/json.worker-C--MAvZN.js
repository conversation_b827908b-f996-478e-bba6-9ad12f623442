const e=new class{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout(()=>{throw e.stack?o.isErrorNoTelemetry(e)?new o(e.message+`

`+e.stack):Error(e.message+`

`+e.stack):e},0)}}emit(e){this.listeners.forEach(t=>{t(e)})}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}};function t(t){i(t)||e.onUnexpectedError(t)}function n(e){if(e instanceof Error){let{name:t,message:n}=e,r=e.stacktrace||e.stack;return{$isError:!0,name:t,message:n,stack:r,noTelemetry:o.isErrorNoTelemetry(e)}}return e}const r=`Canceled`;function i(e){return e instanceof a?!0:e instanceof Error&&e.name===r&&e.message===r}var a=class extends Error{constructor(){super(r),this.name=this.message}},o=class e extends Error{constructor(e){super(e),this.name=`CodeExpectedError`}static fromError(t){if(t instanceof e)return t;let n=new e;return n.message=t.message,n.stack=t.stack,n}static isErrorNoTelemetry(e){return e.name===`CodeExpectedError`}},s=class e extends Error{constructor(t){super(t||`An unexpected bug occurred.`),Object.setPrototypeOf(this,e.prototype)}};function c(e,t){let n=this,r=!1,i;return function(){if(r)return i;if(r=!0,t)try{i=e.apply(n,arguments)}finally{t()}else i=e.apply(n,arguments);return i}}var l;(function(e){function t(e){return e&&typeof e==`object`&&typeof e[Symbol.iterator]==`function`}e.is=t;let n=Object.freeze([]);function r(){return n}e.empty=r;function*i(e){yield e}e.single=i;function a(e){return t(e)?e:i(e)}e.wrap=a;function o(e){return e||n}e.from=o;function*s(e){for(let t=e.length-1;t>=0;t--)yield e[t]}e.reverse=s;function c(e){return!e||e[Symbol.iterator]().next().done===!0}e.isEmpty=c;function l(e){return e[Symbol.iterator]().next().value}e.first=l;function u(e,t){let n=0;for(let r of e)if(t(r,n++))return!0;return!1}e.some=u;function d(e,t){for(let n of e)if(t(n))return n}e.find=d;function*f(e,t){for(let n of e)t(n)&&(yield n)}e.filter=f;function*p(e,t){let n=0;for(let r of e)yield t(r,n++)}e.map=p;function*m(e,t){let n=0;for(let r of e)yield*t(r,n++)}e.flatMap=m;function*h(...e){for(let t of e)yield*t}e.concat=h;function g(e,t,n){let r=n;for(let n of e)r=t(r,n);return r}e.reduce=g;function*_(e,t,n=e.length){for(t<0&&(t+=e.length),n<0?n+=e.length:n>e.length&&(n=e.length);t<n;t++)yield e[t]}e.slice=_;function v(t,n=1/0){let r=[];if(n===0)return[r,t];let i=t[Symbol.iterator]();for(let t=0;t<n;t++){let t=i.next();if(t.done)return[r,e.empty()];r.push(t.value)}return[r,{[Symbol.iterator](){return i}}]}e.consume=v;async function y(e){let t=[];for await(let n of e)t.push(n);return Promise.resolve(t)}e.asyncToArray=y})(l||={});function u(e){return null?.trackDisposable(e),e}function d(e){null?.markAsDisposed(e)}function f(e,t){null?.setParent(e,t)}function p(e){if(l.is(e)){let t=[];for(let n of e)if(n)try{n.dispose()}catch(e){t.push(e)}if(t.length===1)throw t[0];if(t.length>1)throw AggregateError(t,`Encountered errors while disposing of store`);return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function m(...e){return h(()=>p(e))}function h(e){let t=u({dispose:c(()=>{d(t),e()})});return t}var g=class e{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this._toDispose=new Set,this._isDisposed=!1,u(this)}dispose(){this._isDisposed||(d(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(this._toDispose.size!==0)try{p(this._toDispose)}finally{this._toDispose.clear()}}add(t){if(!t)return t;if(t===this)throw Error(`Cannot register a disposable on itself!`);return f(t,this),this._isDisposed?e.DISABLE_DISPOSED_WARNING||console.warn(Error(`Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!`).stack):this._toDispose.add(t),t}deleteAndLeak(e){e&&this._toDispose.has(e)&&(this._toDispose.delete(e),f(e,null))}},_=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this._store=new g,u(this),f(this._store,this)}dispose(){d(this),this._store.dispose()}_register(e){if(e===this)throw Error(`Cannot register a disposable on itself!`);return this._store.add(e)}},v=class e{static{this.Undefined=new e(void 0)}constructor(t){this.element=t,this.next=e.Undefined,this.prev=e.Undefined}},y=class{constructor(){this._first=v.Undefined,this._last=v.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===v.Undefined}clear(){let e=this._first;for(;e!==v.Undefined;){let t=e.next;e.prev=v.Undefined,e.next=v.Undefined,e=t}this._first=v.Undefined,this._last=v.Undefined,this._size=0}unshift(e){return this._insert(e,!1)}push(e){return this._insert(e,!0)}_insert(e,t){let n=new v(e);if(this._first===v.Undefined)this._first=n,this._last=n;else if(t){let e=this._last;this._last=n,n.prev=e,e.next=n}else{let e=this._first;this._first=n,n.next=e,e.prev=n}this._size+=1;let r=!1;return()=>{r||(r=!0,this._remove(n))}}shift(){if(this._first!==v.Undefined){let e=this._first.element;return this._remove(this._first),e}}pop(){if(this._last!==v.Undefined){let e=this._last.element;return this._remove(this._last),e}}_remove(e){if(e.prev!==v.Undefined&&e.next!==v.Undefined){let t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===v.Undefined&&e.next===v.Undefined?(this._first=v.Undefined,this._last=v.Undefined):e.next===v.Undefined?(this._last=this._last.prev,this._last.next=v.Undefined):e.prev===v.Undefined&&(this._first=this._first.next,this._first.prev=v.Undefined);--this._size}*[Symbol.iterator](){let e=this._first;for(;e!==v.Undefined;)yield e.element,e=e.next}};const b=globalThis.performance&&typeof globalThis.performance.now==`function`;var x=class e{static create(t){return new e(t)}constructor(e){this._now=b&&e===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}reset(){this._startTime=this._now(),this._stopTime=-1}elapsed(){return this._stopTime===-1?this._now()-this._startTime:this._stopTime-this._startTime}},S;(function(e){e.None=()=>_.None;function t(e,t){return f(e,()=>void 0,0,void 0,!0,void 0,t)}e.defer=t;function n(e){return(t,n=null,r)=>{let i=!1,a;return a=e(e=>{if(!i)return a?a.dispose():i=!0,t.call(n,e)},null,r),i&&a.dispose(),a}}e.once=n;function r(t,n){return e.once(e.filter(t,n))}e.onceIf=r;function i(e,t,n){return u((n,r=null,i)=>e(e=>n.call(r,t(e)),null,i),n)}e.map=i;function a(e,t,n){return u((n,r=null,i)=>e(e=>{t(e),n.call(r,e)},null,i),n)}e.forEach=a;function o(e,t,n){return u((n,r=null,i)=>e(e=>t(e)&&n.call(r,e),null,i),n)}e.filter=o;function s(e){return e}e.signal=s;function c(...e){return(t,n=null,r)=>{let i=m(...e.map(e=>e(e=>t.call(n,e))));return d(i,r)}}e.any=c;function l(e,t,n,r){let a=n;return i(e,e=>(a=t(a,e),a),r)}e.reduce=l;function u(e,t){let n,r={onWillAddFirstListener(){n=e(i.fire,i)},onDidRemoveLastListener(){n?.dispose()}},i=new T(r);return t?.add(i),i.event}function d(e,t){return t instanceof Array?t.push(e):t&&t.add(e),e}function f(e,t,n=100,r=!1,i=!1,a,o){let s,c,l,u=0,d,f={leakWarningThreshold:a,onWillAddFirstListener(){s=e(e=>{u++,c=t(c,e),r&&!l&&(p.fire(c),c=void 0),d=()=>{let e=c;c=void 0,l=void 0,(!r||u>1)&&p.fire(e),u=0},typeof n==`number`?(clearTimeout(l),l=setTimeout(d,n)):l===void 0&&(l=0,queueMicrotask(d))})},onWillRemoveListener(){i&&u>0&&d?.()},onDidRemoveLastListener(){d=void 0,s.dispose()}},p=new T(f);return o?.add(p),p.event}e.debounce=f;function p(t,n=0,r){return e.debounce(t,(e,t)=>e?(e.push(t),e):[t],n,void 0,!0,void 0,r)}e.accumulate=p;function h(e,t=(e,t)=>e===t,n){let r=!0,i;return o(e,e=>{let n=r||!t(e,i);return r=!1,i=e,n},n)}e.latch=h;function v(t,n,r){return[e.filter(t,n,r),e.filter(t,e=>!n(e),r)]}e.split=v;function y(e,t=!1,n=[],r){let i=n.slice(),a=e(e=>{i?i.push(e):s.fire(e)});r&&r.add(a);let o=()=>{i?.forEach(e=>s.fire(e)),i=null},s=new T({onWillAddFirstListener(){a||(a=e(e=>s.fire(e)),r&&r.add(a))},onDidAddFirstListener(){i&&(t?setTimeout(o):o())},onDidRemoveLastListener(){a&&a.dispose(),a=null}});return r&&r.add(s),s.event}e.buffer=y;function b(e,t){return(n,r,i)=>{let a=t(new S);return e(function(e){let t=a.evaluate(e);t!==x&&n.call(r,t)},void 0,i)}}e.chain=b;let x=Symbol(`HaltChainable`);class S{constructor(){this.steps=[]}map(e){return this.steps.push(e),this}forEach(e){return this.steps.push(t=>(e(t),t)),this}filter(e){return this.steps.push(t=>e(t)?t:x),this}reduce(e,t){let n=t;return this.steps.push(t=>(n=e(n,t),n)),this}latch(e=(e,t)=>e===t){let t=!0,n;return this.steps.push(r=>{let i=t||!e(r,n);return t=!1,n=r,i?r:x}),this}evaluate(e){for(let t of this.steps)if(e=t(e),e===x)break;return e}}function C(e,t,n=e=>e){let r=(...e)=>i.fire(n(...e)),i=new T({onWillAddFirstListener:()=>e.on(t,r),onDidRemoveLastListener:()=>e.removeListener(t,r)});return i.event}e.fromNodeEventEmitter=C;function w(e,t,n=e=>e){let r=(...e)=>i.fire(n(...e)),i=new T({onWillAddFirstListener:()=>e.addEventListener(t,r),onDidRemoveLastListener:()=>e.removeEventListener(t,r)});return i.event}e.fromDOMEventEmitter=w;function ee(e){return new Promise(t=>n(e)(t))}e.toPromise=ee;function te(e){let t=new T;return e.then(e=>{t.fire(e)},()=>{t.fire(void 0)}).finally(()=>{t.dispose()}),t.event}e.fromPromise=te;function ne(e,t){return e(e=>t.fire(e))}e.forward=ne;function re(e,t,n){return t(n),e(e=>t(e))}e.runAndSubscribe=re;class ie{constructor(e,t){this._observable=e,this._counter=0,this._hasChanged=!1,this.emitter=new T({onWillAddFirstListener:()=>{e.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{e.removeObserver(this)}}),t&&t.add(this.emitter)}beginUpdate(e){this._counter++}handlePossibleChange(e){}handleChange(e,t){this._hasChanged=!0}endUpdate(e){this._counter--,this._counter===0&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}function ae(e,t){return new ie(e,t).emitter.event}e.fromObservable=ae;function oe(e){return(t,n,r)=>{let i=0,a=!1,o={beginUpdate(){i++},endUpdate(){i--,i===0&&(e.reportChanges(),a&&(a=!1,t.call(n)))},handlePossibleChange(){},handleChange(){a=!0}};e.addObserver(o),e.reportChanges();let s={dispose(){e.removeObserver(o)}};return r instanceof g?r.add(s):Array.isArray(r)&&r.push(s),s}}e.fromObservableLight=oe})(S||={});var C=class e{static{this.all=new Set}static{this._idPool=0}constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${e._idPool++}`,e.all.add(this)}start(e){this._stopWatch=new x,this.listenerCount=e}stop(){if(this._stopWatch){let e=this._stopWatch.elapsed();this.durations.push(e),this.elapsedOverall+=e,this.invocationCount+=1,this._stopWatch=void 0}}},w=class e{static{this._idPool=1}constructor(t,n,r=(e._idPool++).toString(16).padStart(3,`0`)){this._errorHandler=t,this.threshold=n,this.name=r,this._warnCountdown=0}dispose(){this._stacks?.clear()}check(e,t){let n=this.threshold;if(n<=0||t<n)return;this._stacks||=new Map;let r=this._stacks.get(e.value)||0;if(this._stacks.set(e.value,r+1),--this._warnCountdown,this._warnCountdown<=0){this._warnCountdown=n*.5;let[e,r]=this.getMostFrequentStack(),i=`[${this.name}] potential listener LEAK detected, having ${t} listeners already. MOST frequent listener (${r}):`;console.warn(i),console.warn(e);let a=new te(i,e);this._errorHandler(a)}return()=>{let t=this._stacks.get(e.value)||0;this._stacks.set(e.value,t-1)}}getMostFrequentStack(){if(!this._stacks)return;let e,t=0;for(let[n,r]of this._stacks)(!e||t<r)&&(e=[n,r],t=r);return e}},ee=class e{static create(){return new e(Error().stack??``)}constructor(e){this.value=e}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},te=class extends Error{constructor(e,t){super(e),this.name=`ListenerLeakError`,this.stack=t}},ne=class extends Error{constructor(e,t){super(e),this.name=`ListenerRefusalError`,this.stack=t}},re=class{constructor(e){this.value=e}},T=class{constructor(e){this._size=0,this._options=e,this._leakageMon=this._options?.leakWarningThreshold?new w(e?.onListenerError??t,this._options?.leakWarningThreshold??-1):void 0,this._perfMon=this._options?._profName?new C(this._options._profName):void 0,this._deliveryQueue=this._options?.deliveryQueue}dispose(){this._disposed||(this._disposed=!0,this._deliveryQueue?.current===this&&this._deliveryQueue.reset(),this._listeners&&(this._listeners=void 0,this._size=0),this._options?.onDidRemoveLastListener?.(),this._leakageMon?.dispose())}get event(){return this._event??=(e,n,r)=>{if(this._leakageMon&&this._size>this._leakageMon.threshold**2){let e=`[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this._size} vs ${this._leakageMon.threshold})`;console.warn(e);let n=this._leakageMon.getMostFrequentStack()??[`UNKNOWN stack`,-1],r=new ne(`${e}. HINT: Stack shows most frequent listener (${n[1]}-times)`,n[0]);return(this._options?.onListenerError||t)(r),_.None}if(this._disposed)return _.None;n&&(e=e.bind(n));let i=new re(e),a;this._leakageMon&&this._size>=Math.ceil(this._leakageMon.threshold*.2)&&(i.stack=ee.create(),a=this._leakageMon.check(i.stack,this._size+1)),this._listeners?this._listeners instanceof re?(this._deliveryQueue??=new ie,this._listeners=[this._listeners,i]):this._listeners.push(i):(this._options?.onWillAddFirstListener?.(this),this._listeners=i,this._options?.onDidAddFirstListener?.(this)),this._size++;let o=h(()=>{(void 0)?.unregister(o),a?.(),this._removeListener(i)});return r instanceof g?r.add(o):Array.isArray(r)&&r.push(o),o},this._event}_removeListener(e){if(this._options?.onWillRemoveListener?.(this),!this._listeners)return;if(this._size===1){this._listeners=void 0,this._options?.onDidRemoveLastListener?.(this),this._size=0;return}let t=this._listeners,n=t.indexOf(e);if(n===-1)throw console.log(`disposed?`,this._disposed),console.log(`size?`,this._size),console.log(`arr?`,JSON.stringify(this._listeners)),Error(`Attempted to dispose unknown listener`);this._size--,t[n]=void 0;let r=this._deliveryQueue.current===this;if(this._size*2<=t.length){let e=0;for(let n=0;n<t.length;n++)t[n]?t[e++]=t[n]:r&&(this._deliveryQueue.end--,e<this._deliveryQueue.i&&this._deliveryQueue.i--);t.length=e}}_deliver(e,n){if(!e)return;let r=this._options?.onListenerError||t;if(!r){e.value(n);return}try{e.value(n)}catch(e){r(e)}}_deliverQueue(e){let t=e.current._listeners;for(;e.i<e.end;)this._deliver(t[e.i++],e.value);e.reset()}fire(e){if(this._deliveryQueue?.current&&(this._deliverQueue(this._deliveryQueue),this._perfMon?.stop()),this._perfMon?.start(this._size),this._listeners)if(this._listeners instanceof re)this._deliver(this._listeners,e);else{let t=this._deliveryQueue;t.enqueue(this,e,this._listeners.length),this._deliverQueue(t)}this._perfMon?.stop()}hasListeners(){return this._size>0}},ie=class{constructor(){this.i=-1,this.end=0}enqueue(e,t,n){this.i=0,this.end=n,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}};function ae(){return globalThis._VSCODE_NLS_MESSAGES}function oe(){return globalThis._VSCODE_NLS_LANGUAGE}const se=oe()===`pseudo`||typeof document<`u`&&document.location&&document.location.hash.indexOf(`pseudo=true`)>=0;function ce(e,t){let n;return n=t.length===0?e:e.replace(/\{(\d+)\}/g,(e,n)=>{let r=n[0],i=t[r],a=e;return typeof i==`string`?a=i:(typeof i==`number`||typeof i==`boolean`||i==null)&&(a=String(i)),a}),se&&(n=`［`+n.replace(/[aouei]/g,`$&$&`)+`］`),n}function E(e,t,...n){return ce(typeof e==`number`?le(e,t):t,n)}function le(e,t){let n=ae()?.[e];if(typeof n!=`string`){if(typeof t==`string`)return t;throw Error(`!!! NLS MISSING: ${e} !!!`)}return n}let ue=!1,de=!1,fe=!1,pe=!1,me=!1,he;const ge=globalThis;let D;ge.vscode!==void 0&&ge.vscode.process!==void 0?D=ge.vscode.process:typeof process<`u`&&typeof process?.versions?.node==`string`&&(D=process);const _e=typeof D?.versions?.electron==`string`&&D?.type===`renderer`;if(typeof D==`object`){ue=D.platform===`win32`,de=D.platform===`darwin`,fe=D.platform===`linux`,fe&&D.env.SNAP&&D.env.SNAP_REVISION,D.env.CI||D.env.BUILD_ARTIFACTSTAGINGDIRECTORY;let e=D.env.VSCODE_NLS_CONFIG;if(e)try{let t=JSON.parse(e);t.userLocale,t.osLocale,t.resolvedLanguage,t.languagePack?.translationsConfigFile}catch{}pe=!0}else typeof navigator==`object`&&!_e?(he=navigator.userAgent,ue=he.indexOf(`Windows`)>=0,de=he.indexOf(`Macintosh`)>=0,(he.indexOf(`Macintosh`)>=0||he.indexOf(`iPad`)>=0||he.indexOf(`iPhone`)>=0)&&navigator.maxTouchPoints&&navigator.maxTouchPoints,fe=he.indexOf(`Linux`)>=0,he?.indexOf(`Mobi`),me=!0,oe(),navigator.language.toLowerCase()):console.error(`Unable to resolve platform.`);const ve=ue,ye=de,be=pe,xe=me,Se=me&&typeof ge.importScripts==`function`?ge.origin:void 0,Ce=he,we=typeof ge.postMessage==`function`&&!ge.importScripts;(()=>{if(we){let e=[];ge.addEventListener(`message`,t=>{if(t.data&&t.data.vscodeScheduleAsyncWork)for(let n=0,r=e.length;n<r;n++){let r=e[n];if(r.id===t.data.vscodeScheduleAsyncWork){e.splice(n,1),r.callback();return}}});let t=0;return n=>{let r=++t;e.push({id:r,callback:n}),ge.postMessage({vscodeScheduleAsyncWork:r},`*`)}}return e=>setTimeout(e)})();const Te=!!(Ce&&Ce.indexOf(`Chrome`)>=0);Ce&&Ce.indexOf(`Firefox`),!Te&&Ce&&Ce.indexOf(`Safari`),Ce&&Ce.indexOf(`Edg/`),Ce&&Ce.indexOf(`Android`);function Ee(e){return e}var De=class{constructor(e,t){this.lastCache=void 0,this.lastArgKey=void 0,typeof e==`function`?(this._fn=e,this._computeKey=Ee):(this._fn=t,this._computeKey=e.getCacheKey)}get(e){let t=this._computeKey(e);return this.lastArgKey!==t&&(this.lastArgKey=t,this.lastCache=this._fn(e)),this.lastCache}},Oe=class{constructor(e){this.executor=e,this._didRun=!1}get value(){if(!this._didRun)try{this._value=this.executor()}catch(e){this._error=e}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}};function ke(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,`\\$&`)}function Ae(e){return e.split(/\r\n|\r|\n/)}function je(e){for(let t=0,n=e.length;t<n;t++){let n=e.charCodeAt(t);if(n!==32&&n!==9)return t}return-1}function Me(e,t=e.length-1){for(let n=t;n>=0;n--){let t=e.charCodeAt(n);if(t!==32&&t!==9)return n}return-1}function Ne(e){return e>=65&&e<=90}function Pe(e){return 55296<=e&&e<=56319}function Fe(e){return 56320<=e&&e<=57343}function Ie(e,t){return(e-55296<<10)+(t-56320)+65536}function Le(e,t,n){let r=e.charCodeAt(n);if(Pe(r)&&n+1<t){let t=e.charCodeAt(n+1);if(Fe(t))return Ie(r,t)}return r}const Re=/^[\t\n\r\x20-\x7E]*$/;function ze(e){return Re.test(e)}(class e{static{this._INSTANCE=null}static getInstance(){return e._INSTANCE||=new e,e._INSTANCE}constructor(){this._data=Be()}getGraphemeBreakType(e){if(e<32)return e===10?3:e===13?2:4;if(e<127)return 0;let t=this._data,n=t.length/3,r=1;for(;r<=n;)if(e<t[3*r])r=2*r;else if(e>t[3*r+1])r=2*r+1;else return t[3*r+2];return 0}});function Be(){return JSON.parse(`[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]`)}var Ve=class e{static{this.ambiguousCharacterData=new Oe(()=>JSON.parse(`{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}`))}static{this.cache=new De({getCacheKey:JSON.stringify},t=>{function n(e){let t=new Map;for(let n=0;n<e.length;n+=2)t.set(e[n],e[n+1]);return t}function r(e,t){let n=new Map(e);for(let[e,r]of t)n.set(e,r);return n}function i(e,t){if(!e)return t;let n=new Map;for(let[r,i]of e)t.has(r)&&n.set(r,i);return n}let a=this.ambiguousCharacterData.value,o=t.filter(e=>!e.startsWith(`_`)&&e in a);o.length===0&&(o=[`_default`]);let s;for(let e of o){let t=n(a[e]);s=i(s,t)}let c=n(a._common),l=r(c,s);return new e(l)})}static getInstance(t){return e.cache.get(Array.from(t))}static{this._locales=new Oe(()=>Object.keys(e.ambiguousCharacterData.value).filter(e=>!e.startsWith(`_`)))}static getLocales(){return e._locales.value}constructor(e){this.confusableDictionary=e}isAmbiguous(e){return this.confusableDictionary.has(e)}getPrimaryConfusable(e){return this.confusableDictionary.get(e)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}},He=class e{static getRawData(){return JSON.parse(`[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]`)}static{this._data=void 0}static getData(){return this._data||=new Set(e.getRawData()),this._data}static isInvisibleCharacter(t){return e.getData().has(t)}static get codePoints(){return e.getData()}};let Ue;const We=globalThis.vscode;if(We!==void 0&&We.process!==void 0){let e=We.process;Ue={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else Ue=typeof process<`u`&&typeof process?.versions?.node==`string`?{get platform(){return process.platform},get arch(){return process.arch},get env(){return{}},cwd(){return{}.VSCODE_CWD||process.cwd()}}:{get platform(){return ve?`win32`:ye?`darwin`:`linux`},get arch(){},get env(){return{}},cwd(){return`/`}};const Ge=Ue.cwd,Ke=Ue.env,qe=Ue.platform;var Je=class extends Error{constructor(e,t,n){let r;typeof t==`string`&&t.indexOf(`not `)===0?(r=`must not be`,t=t.replace(/^not /,``)):r=`must be`;let i=e.indexOf(`.`)===-1?`argument`:`property`,a=`The "${e}" ${i} ${r} of type ${t}`;a+=`. Received type ${typeof n}`,super(a),this.code=`ERR_INVALID_ARG_TYPE`}};function Ye(e,t){if(typeof e!=`object`||!e)throw new Je(t,`Object`,e)}function O(e,t){if(typeof e!=`string`)throw new Je(t,`string`,e)}const Xe=qe===`win32`;function k(e){return e===47||e===92}function Ze(e){return e===47}function Qe(e){return e>=65&&e<=90||e>=97&&e<=122}function $e(e,t,n,r){let i=``,a=0,o=-1,s=0,c=0;for(let l=0;l<=e.length;++l){if(l<e.length)c=e.charCodeAt(l);else if(r(c))break;else c=47;if(r(c)){if(!(o===l-1||s===1))if(s===2){if(i.length<2||a!==2||i.charCodeAt(i.length-1)!==46||i.charCodeAt(i.length-2)!==46){if(i.length>2){let e=i.lastIndexOf(n);e===-1?(i=``,a=0):(i=i.slice(0,e),a=i.length-1-i.lastIndexOf(n)),o=l,s=0;continue}else if(i.length!==0){i=``,a=0,o=l,s=0;continue}}t&&(i+=i.length>0?`${n}..`:`..`,a=2)}else i.length>0?i+=`${n}${e.slice(o+1,l)}`:i=e.slice(o+1,l),a=l-o-1;o=l,s=0}else c===46&&s!==-1?++s:s=-1}return i}function et(e){return e?`${e[0]===`.`?``:`.`}${e}`:``}function tt(e,t){Ye(t,`pathObject`);let n=t.dir||t.root,r=t.base||`${t.name||``}${et(t.ext)}`;return n?n===t.root?`${n}${r}`:`${n}${e}${r}`:r}const A={resolve(...e){let t=``,n=``,r=!1;for(let i=e.length-1;i>=-1;i--){let a;if(i>=0){if(a=e[i],O(a,`paths[${i}]`),a.length===0)continue}else t.length===0?a=Ge():(a=Ke[`=${t}`]||Ge(),(a===void 0||a.slice(0,2).toLowerCase()!==t.toLowerCase()&&a.charCodeAt(2)===92)&&(a=`${t}\\`));let o=a.length,s=0,c=``,l=!1,u=a.charCodeAt(0);if(o===1)k(u)&&(s=1,l=!0);else if(k(u))if(l=!0,k(a.charCodeAt(1))){let e=2,t=e;for(;e<o&&!k(a.charCodeAt(e));)e++;if(e<o&&e!==t){let n=a.slice(t,e);for(t=e;e<o&&k(a.charCodeAt(e));)e++;if(e<o&&e!==t){for(t=e;e<o&&!k(a.charCodeAt(e));)e++;(e===o||e!==t)&&(c=`\\\\${n}\\${a.slice(t,e)}`,s=e)}}}else s=1;else Qe(u)&&a.charCodeAt(1)===58&&(c=a.slice(0,2),s=2,o>2&&k(a.charCodeAt(2))&&(l=!0,s=3));if(c.length>0)if(t.length>0){if(c.toLowerCase()!==t.toLowerCase())continue}else t=c;if(r){if(t.length>0)break}else if(n=`${a.slice(s)}\\${n}`,r=l,l&&t.length>0)break}return n=$e(n,!r,`\\`,k),r?`${t}\\${n}`:`${t}${n}`||`.`},normalize(e){O(e,`path`);let t=e.length;if(t===0)return`.`;let n=0,r,i=!1,a=e.charCodeAt(0);if(t===1)return Ze(a)?`\\`:e;if(k(a))if(i=!0,k(e.charCodeAt(1))){let i=2,a=i;for(;i<t&&!k(e.charCodeAt(i));)i++;if(i<t&&i!==a){let o=e.slice(a,i);for(a=i;i<t&&k(e.charCodeAt(i));)i++;if(i<t&&i!==a){for(a=i;i<t&&!k(e.charCodeAt(i));)i++;if(i===t)return`\\\\${o}\\${e.slice(a)}\\`;i!==a&&(r=`\\\\${o}\\${e.slice(a,i)}`,n=i)}}}else n=1;else Qe(a)&&e.charCodeAt(1)===58&&(r=e.slice(0,2),n=2,t>2&&k(e.charCodeAt(2))&&(i=!0,n=3));let o=n<t?$e(e.slice(n),!i,`\\`,k):``;return o.length===0&&!i&&(o=`.`),o.length>0&&k(e.charCodeAt(t-1))&&(o+=`\\`),r===void 0?i?`\\${o}`:o:i?`${r}\\${o}`:`${r}${o}`},isAbsolute(e){O(e,`path`);let t=e.length;if(t===0)return!1;let n=e.charCodeAt(0);return k(n)||t>2&&Qe(n)&&e.charCodeAt(1)===58&&k(e.charCodeAt(2))},join(...e){if(e.length===0)return`.`;let t,n;for(let r=0;r<e.length;++r){let i=e[r];O(i,`path`),i.length>0&&(t===void 0?t=n=i:t+=`\\${i}`)}if(t===void 0)return`.`;let r=!0,i=0;if(typeof n==`string`&&k(n.charCodeAt(0))){++i;let e=n.length;e>1&&k(n.charCodeAt(1))&&(++i,e>2&&(k(n.charCodeAt(2))?++i:r=!1))}if(r){for(;i<t.length&&k(t.charCodeAt(i));)i++;i>=2&&(t=`\\${t.slice(i)}`)}return A.normalize(t)},relative(e,t){if(O(e,`from`),O(t,`to`),e===t)return``;let n=A.resolve(e),r=A.resolve(t);if(n===r||(e=n.toLowerCase(),t=r.toLowerCase(),e===t))return``;let i=0;for(;i<e.length&&e.charCodeAt(i)===92;)i++;let a=e.length;for(;a-1>i&&e.charCodeAt(a-1)===92;)a--;let o=a-i,s=0;for(;s<t.length&&t.charCodeAt(s)===92;)s++;let c=t.length;for(;c-1>s&&t.charCodeAt(c-1)===92;)c--;let l=c-s,u=o<l?o:l,d=-1,f=0;for(;f<u;f++){let n=e.charCodeAt(i+f);if(n!==t.charCodeAt(s+f))break;n===92&&(d=f)}if(f!==u){if(d===-1)return r}else{if(l>u){if(t.charCodeAt(s+f)===92)return r.slice(s+f+1);if(f===2)return r.slice(s+f)}o>u&&(e.charCodeAt(i+f)===92?d=f:f===2&&(d=3)),d===-1&&(d=0)}let p=``;for(f=i+d+1;f<=a;++f)(f===a||e.charCodeAt(f)===92)&&(p+=p.length===0?`..`:`\\..`);return s+=d,p.length>0?`${p}${r.slice(s,c)}`:(r.charCodeAt(s)===92&&++s,r.slice(s,c))},toNamespacedPath(e){if(typeof e!=`string`||e.length===0)return e;let t=A.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===92){if(t.charCodeAt(1)===92){let e=t.charCodeAt(2);if(e!==63&&e!==46)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(Qe(t.charCodeAt(0))&&t.charCodeAt(1)===58&&t.charCodeAt(2)===92)return`\\\\?\\${t}`;return e},dirname(e){O(e,`path`);let t=e.length;if(t===0)return`.`;let n=-1,r=0,i=e.charCodeAt(0);if(t===1)return k(i)?e:`.`;if(k(i)){if(n=r=1,k(e.charCodeAt(1))){let i=2,a=i;for(;i<t&&!k(e.charCodeAt(i));)i++;if(i<t&&i!==a){for(a=i;i<t&&k(e.charCodeAt(i));)i++;if(i<t&&i!==a){for(a=i;i<t&&!k(e.charCodeAt(i));)i++;if(i===t)return e;i!==a&&(n=r=i+1)}}}}else Qe(i)&&e.charCodeAt(1)===58&&(n=t>2&&k(e.charCodeAt(2))?3:2,r=n);let a=-1,o=!0;for(let n=t-1;n>=r;--n)if(k(e.charCodeAt(n))){if(!o){a=n;break}}else o=!1;if(a===-1){if(n===-1)return`.`;a=n}return e.slice(0,a)},basename(e,t){t!==void 0&&O(t,`suffix`),O(e,`path`);let n=0,r=-1,i=!0,a;if(e.length>=2&&Qe(e.charCodeAt(0))&&e.charCodeAt(1)===58&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return``;let o=t.length-1,s=-1;for(a=e.length-1;a>=n;--a){let c=e.charCodeAt(a);if(k(c)){if(!i){n=a+1;break}}else s===-1&&(i=!1,s=a+1),o>=0&&(c===t.charCodeAt(o)?--o===-1&&(r=a):(o=-1,r=s))}return n===r?r=s:r===-1&&(r=e.length),e.slice(n,r)}for(a=e.length-1;a>=n;--a)if(k(e.charCodeAt(a))){if(!i){n=a+1;break}}else r===-1&&(i=!1,r=a+1);return r===-1?``:e.slice(n,r)},extname(e){O(e,`path`);let t=0,n=-1,r=0,i=-1,a=!0,o=0;e.length>=2&&e.charCodeAt(1)===58&&Qe(e.charCodeAt(0))&&(t=r=2);for(let s=e.length-1;s>=t;--s){let t=e.charCodeAt(s);if(k(t)){if(!a){r=s+1;break}continue}i===-1&&(a=!1,i=s+1),t===46?n===-1?n=s:o!==1&&(o=1):n!==-1&&(o=-1)}return n===-1||i===-1||o===0||o===1&&n===i-1&&n===r+1?``:e.slice(n,i)},format:tt.bind(null,`\\`),parse(e){O(e,`path`);let t={root:``,dir:``,base:``,ext:``,name:``};if(e.length===0)return t;let n=e.length,r=0,i=e.charCodeAt(0);if(n===1)return k(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(k(i)){if(r=1,k(e.charCodeAt(1))){let t=2,i=t;for(;t<n&&!k(e.charCodeAt(t));)t++;if(t<n&&t!==i){for(i=t;t<n&&k(e.charCodeAt(t));)t++;if(t<n&&t!==i){for(i=t;t<n&&!k(e.charCodeAt(t));)t++;t===n?r=t:t!==i&&(r=t+1)}}}}else if(Qe(i)&&e.charCodeAt(1)===58){if(n<=2)return t.root=t.dir=e,t;if(r=2,k(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let a=-1,o=r,s=-1,c=!0,l=e.length-1,u=0;for(;l>=r;--l){if(i=e.charCodeAt(l),k(i)){if(!c){o=l+1;break}continue}s===-1&&(c=!1,s=l+1),i===46?a===-1?a=l:u!==1&&(u=1):a!==-1&&(u=-1)}return s!==-1&&(a===-1||u===0||u===1&&a===s-1&&a===o+1?t.base=t.name=e.slice(o,s):(t.name=e.slice(o,a),t.base=e.slice(o,s),t.ext=e.slice(a,s))),o>0&&o!==r?t.dir=e.slice(0,o-1):t.dir=t.root,t},sep:`\\`,delimiter:`;`,win32:null,posix:null},nt=(()=>{if(Xe){let e=/\\/g;return()=>{let t=Ge().replace(e,`/`);return t.slice(t.indexOf(`/`))}}return()=>Ge()})(),j={resolve(...e){let t=``,n=!1;for(let r=e.length-1;r>=-1&&!n;r--){let i=r>=0?e[r]:nt();O(i,`paths[${r}]`),i.length!==0&&(t=`${i}/${t}`,n=i.charCodeAt(0)===47)}return t=$e(t,!n,`/`,Ze),n?`/${t}`:t.length>0?t:`.`},normalize(e){if(O(e,`path`),e.length===0)return`.`;let t=e.charCodeAt(0)===47,n=e.charCodeAt(e.length-1)===47;return e=$e(e,!t,`/`,Ze),e.length===0?t?`/`:n?`./`:`.`:(n&&(e+=`/`),t?`/${e}`:e)},isAbsolute(e){return O(e,`path`),e.length>0&&e.charCodeAt(0)===47},join(...e){if(e.length===0)return`.`;let t;for(let n=0;n<e.length;++n){let r=e[n];O(r,`path`),r.length>0&&(t===void 0?t=r:t+=`/${r}`)}return t===void 0?`.`:j.normalize(t)},relative(e,t){if(O(e,`from`),O(t,`to`),e===t||(e=j.resolve(e),t=j.resolve(t),e===t))return``;let n=e.length,r=n-1,i=t.length-1,a=r<i?r:i,o=-1,s=0;for(;s<a;s++){let n=e.charCodeAt(1+s);if(n!==t.charCodeAt(1+s))break;n===47&&(o=s)}if(s===a)if(i>a){if(t.charCodeAt(1+s)===47)return t.slice(1+s+1);if(s===0)return t.slice(1+s)}else r>a&&(e.charCodeAt(1+s)===47?o=s:s===0&&(o=0));let c=``;for(s=1+o+1;s<=n;++s)(s===n||e.charCodeAt(s)===47)&&(c+=c.length===0?`..`:`/..`);return`${c}${t.slice(1+o)}`},toNamespacedPath(e){return e},dirname(e){if(O(e,`path`),e.length===0)return`.`;let t=e.charCodeAt(0)===47,n=-1,r=!0;for(let t=e.length-1;t>=1;--t)if(e.charCodeAt(t)===47){if(!r){n=t;break}}else r=!1;return n===-1?t?`/`:`.`:t&&n===1?`//`:e.slice(0,n)},basename(e,t){t!==void 0&&O(t,`ext`),O(e,`path`);let n=0,r=-1,i=!0,a;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return``;let o=t.length-1,s=-1;for(a=e.length-1;a>=0;--a){let c=e.charCodeAt(a);if(c===47){if(!i){n=a+1;break}}else s===-1&&(i=!1,s=a+1),o>=0&&(c===t.charCodeAt(o)?--o===-1&&(r=a):(o=-1,r=s))}return n===r?r=s:r===-1&&(r=e.length),e.slice(n,r)}for(a=e.length-1;a>=0;--a)if(e.charCodeAt(a)===47){if(!i){n=a+1;break}}else r===-1&&(i=!1,r=a+1);return r===-1?``:e.slice(n,r)},extname(e){O(e,`path`);let t=-1,n=0,r=-1,i=!0,a=0;for(let o=e.length-1;o>=0;--o){let s=e.charCodeAt(o);if(s===47){if(!i){n=o+1;break}continue}r===-1&&(i=!1,r=o+1),s===46?t===-1?t=o:a!==1&&(a=1):t!==-1&&(a=-1)}return t===-1||r===-1||a===0||a===1&&t===r-1&&t===n+1?``:e.slice(t,r)},format:tt.bind(null,`/`),parse(e){O(e,`path`);let t={root:``,dir:``,base:``,ext:``,name:``};if(e.length===0)return t;let n=e.charCodeAt(0)===47,r;n?(t.root=`/`,r=1):r=0;let i=-1,a=0,o=-1,s=!0,c=e.length-1,l=0;for(;c>=r;--c){let t=e.charCodeAt(c);if(t===47){if(!s){a=c+1;break}continue}o===-1&&(s=!1,o=c+1),t===46?i===-1?i=c:l!==1&&(l=1):i!==-1&&(l=-1)}if(o!==-1){let r=a===0&&n?1:a;i===-1||l===0||l===1&&i===o-1&&i===a+1?t.base=t.name=e.slice(r,o):(t.name=e.slice(r,i),t.base=e.slice(r,o),t.ext=e.slice(i,o))}return a>0?t.dir=e.slice(0,a-1):n&&(t.dir=`/`),t},sep:`/`,delimiter:`:`,win32:null,posix:null};j.win32=A.win32=A,j.posix=A.posix=j,Xe?A.normalize:j.normalize;const rt=Xe?A.join:j.join;Xe?A.resolve:j.resolve,Xe?A.relative:j.relative,Xe?A.dirname:j.dirname,Xe?A.basename:j.basename,Xe?A.extname:j.extname,Xe?A.sep:j.sep;const it=/^\w[\w\d+.-]*$/,at=/^\//,ot=/^\/\//;function st(e,t){if(!e.scheme&&t)throw Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!it.test(e.scheme))throw Error(`[UriError]: Scheme contains illegal characters.`);if(e.path){if(e.authority){if(!at.test(e.path))throw Error(`[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character`)}else if(ot.test(e.path))throw Error(`[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")`)}}function ct(e,t){return!e&&!t?`file`:e}function lt(e,t){switch(e){case`https`:case`http`:case`file`:t?t[0]!==ut&&(t=ut+t):t=ut;break}return t}const ut=`/`,dt=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;var ft=class e{static isUri(t){return t instanceof e?!0:t?typeof t.authority==`string`&&typeof t.fragment==`string`&&typeof t.path==`string`&&typeof t.query==`string`&&typeof t.scheme==`string`&&typeof t.fsPath==`string`&&typeof t.with==`function`&&typeof t.toString==`function`:!1}constructor(e,t,n,r,i,a=!1){typeof e==`object`?(this.scheme=e.scheme||``,this.authority=e.authority||``,this.path=e.path||``,this.query=e.query||``,this.fragment=e.fragment||``):(this.scheme=ct(e,a),this.authority=t||``,this.path=lt(this.scheme,n||``),this.query=r||``,this.fragment=i||``,st(this,a))}get fsPath(){return vt(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:r,query:i,fragment:a}=e;return t===void 0?t=this.scheme:t===null&&(t=``),n===void 0?n=this.authority:n===null&&(n=``),r===void 0?r=this.path:r===null&&(r=``),i===void 0?i=this.query:i===null&&(i=``),a===void 0?a=this.fragment:a===null&&(a=``),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&a===this.fragment?this:new mt(t,n,r,i,a)}static parse(e,t=!1){let n=dt.exec(e);return n?new mt(n[2]||``,St(n[4]||``),St(n[5]||``),St(n[7]||``),St(n[9]||``),t):new mt(``,``,``,``,``)}static file(e){let t=``;if(ve&&(e=e.replace(/\\/g,`/`)),e[0]===`/`&&e[1]===`/`){let n=e.indexOf(`/`,2);n===-1?(t=e.substring(2),e=`/`):(t=e.substring(2,n),e=e.substring(n)||`/`)}return new mt(`file`,t,e,``,``)}static from(e,t){return new mt(e.scheme,e.authority,e.path,e.query,e.fragment,t)}static joinPath(t,...n){if(!t.path)throw Error(`[UriError]: cannot call joinPath on URI without path`);let r;return r=ve&&t.scheme===`file`?e.file(A.join(vt(t,!0),...n)).path:j.join(t.path,...n),t.with({path:r})}toString(e=!1){return yt(this,e)}toJSON(){return this}static revive(t){if(t){if(t instanceof e)return t;{let e=new mt(t);return e._formatted=t.external??null,e._fsPath=t._sep===pt?t.fsPath??null:null,e}}else return t}};const pt=ve?1:void 0;var mt=class extends ft{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||=vt(this,!1),this._fsPath}toString(e=!1){return e?yt(this,!0):(this._formatted||=yt(this,!1),this._formatted)}toJSON(){let e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=pt),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}};const ht={58:`%3A`,47:`%2F`,63:`%3F`,35:`%23`,91:`%5B`,93:`%5D`,64:`%40`,33:`%21`,36:`%24`,38:`%26`,39:`%27`,40:`%28`,41:`%29`,42:`%2A`,43:`%2B`,44:`%2C`,59:`%3B`,61:`%3D`,32:`%20`};function gt(e,t,n){let r,i=-1;for(let a=0;a<e.length;a++){let o=e.charCodeAt(a);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||o===45||o===46||o===95||o===126||t&&o===47||n&&o===91||n&&o===93||n&&o===58)i!==-1&&(r+=encodeURIComponent(e.substring(i,a)),i=-1),r!==void 0&&(r+=e.charAt(a));else{r===void 0&&(r=e.substr(0,a));let t=ht[o];t===void 0?i===-1&&(i=a):(i!==-1&&(r+=encodeURIComponent(e.substring(i,a)),i=-1),r+=t)}}return i!==-1&&(r+=encodeURIComponent(e.substring(i))),r===void 0?e:r}function _t(e){let t;for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);r===35||r===63?(t===void 0&&(t=e.substr(0,n)),t+=ht[r]):t!==void 0&&(t+=e[n])}return t===void 0?e:t}function vt(e,t){let n;return n=e.authority&&e.path.length>1&&e.scheme===`file`?`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,ve&&(n=n.replace(/\//g,`\\`)),n}function yt(e,t){let n=t?_t:gt,r=``,{scheme:i,authority:a,path:o,query:s,fragment:c}=e;if(i&&(r+=i,r+=`:`),(a||i===`file`)&&(r+=`/`,r+=`/`),a){let e=a.indexOf(`@`);if(e!==-1){let t=a.substr(0,e);a=a.substr(e+1),e=t.lastIndexOf(`:`),e===-1?r+=n(t,!1,!1):(r+=n(t.substr(0,e),!1,!1),r+=`:`,r+=n(t.substr(e+1),!1,!0)),r+=`@`}a=a.toLowerCase(),e=a.lastIndexOf(`:`),e===-1?r+=n(a,!1,!0):(r+=n(a.substr(0,e),!1,!0),r+=a.substr(e))}if(o){if(o.length>=3&&o.charCodeAt(0)===47&&o.charCodeAt(2)===58){let e=o.charCodeAt(1);e>=65&&e<=90&&(o=`/${String.fromCharCode(e+32)}:${o.substr(3)}`)}else if(o.length>=2&&o.charCodeAt(1)===58){let e=o.charCodeAt(0);e>=65&&e<=90&&(o=`${String.fromCharCode(e+32)}:${o.substr(2)}`)}r+=n(o,!0,!1)}return s&&(r+=`?`,r+=n(s,!1,!1)),c&&(r+=`#`,r+=t?c:gt(c,!1,!1)),r}function bt(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+bt(e.substr(3)):e}}const xt=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function St(e){return e.match(xt)?e.replace(xt,e=>bt(e)):e}var Ct;(function(e){e.inMemory=`inmemory`,e.vscode=`vscode`,e.internal=`private`,e.walkThrough=`walkThrough`,e.walkThroughSnippet=`walkThroughSnippet`,e.http=`http`,e.https=`https`,e.file=`file`,e.mailto=`mailto`,e.untitled=`untitled`,e.data=`data`,e.command=`command`,e.vscodeRemote=`vscode-remote`,e.vscodeRemoteResource=`vscode-remote-resource`,e.vscodeManagedRemoteResource=`vscode-managed-remote-resource`,e.vscodeUserData=`vscode-userdata`,e.vscodeCustomEditor=`vscode-custom-editor`,e.vscodeNotebookCell=`vscode-notebook-cell`,e.vscodeNotebookCellMetadata=`vscode-notebook-cell-metadata`,e.vscodeNotebookCellMetadataDiff=`vscode-notebook-cell-metadata-diff`,e.vscodeNotebookCellOutput=`vscode-notebook-cell-output`,e.vscodeNotebookCellOutputDiff=`vscode-notebook-cell-output-diff`,e.vscodeNotebookMetadata=`vscode-notebook-metadata`,e.vscodeInteractiveInput=`vscode-interactive-input`,e.vscodeSettings=`vscode-settings`,e.vscodeWorkspaceTrust=`vscode-workspace-trust`,e.vscodeTerminal=`vscode-terminal`,e.vscodeChatCodeBlock=`vscode-chat-code-block`,e.vscodeChatCodeCompareBlock=`vscode-chat-code-compare-block`,e.vscodeChatSesssion=`vscode-chat-editor`,e.webviewPanel=`webview-panel`,e.vscodeWebview=`vscode-webview`,e.extension=`extension`,e.vscodeFileResource=`vscode-file`,e.tmp=`tmp`,e.vsls=`vsls`,e.vscodeSourceControl=`vscode-scm`,e.commentsInput=`comment`,e.codeSetting=`code-setting`,e.outputChannel=`output`})(Ct||={});const wt=new class{constructor(){this._hosts=Object.create(null),this._ports=Object.create(null),this._connectionTokens=Object.create(null),this._preferredWebSchema=`http`,this._delegate=null,this._serverRootPath=`/`}setPreferredWebSchema(e){this._preferredWebSchema=e}get _remoteResourcesPath(){return j.join(this._serverRootPath,Ct.vscodeRemoteResource)}rewrite(e){if(this._delegate)try{return this._delegate(e)}catch(n){return t(n),e}let n=e.authority,r=this._hosts[n];r&&r.indexOf(`:`)!==-1&&r.indexOf(`[`)===-1&&(r=`[${r}]`);let i=this._ports[n],a=this._connectionTokens[n],o=`path=${encodeURIComponent(e.path)}`;return typeof a==`string`&&(o+=`&tkn=${encodeURIComponent(a)}`),ft.from({scheme:xe?this._preferredWebSchema:Ct.vscodeRemoteResource,authority:`${r}:${i}`,path:this._remoteResourcesPath,query:o})}},Tt=new class e{static{this.FALLBACK_AUTHORITY=`vscode-app`}asBrowserUri(e){let t=(this.toUri(e));return this.uriToBrowserUri(t)}uriToBrowserUri(t){return t.scheme===Ct.vscodeRemote?wt.rewrite(t):t.scheme===Ct.file&&(be||Se===`${Ct.vscodeFileResource}://${e.FALLBACK_AUTHORITY}`)?t.with({scheme:Ct.vscodeFileResource,authority:t.authority||e.FALLBACK_AUTHORITY,query:null,fragment:null}):t}toUri(e,t){if(ft.isUri(e))return e;if(globalThis._VSCODE_FILE_ROOT){let t=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(t))return ft.joinPath(ft.parse(t,!0),e);let n=(rt(t,e));return ft.file(n)}return ft.parse(t.toUrl(e))}};var Et;(function(e){let t=new Map([[`1`,{"Cross-Origin-Opener-Policy":`same-origin`}],[`2`,{"Cross-Origin-Embedder-Policy":`require-corp`}],[`3`,{"Cross-Origin-Opener-Policy":`same-origin`,"Cross-Origin-Embedder-Policy":`require-corp`}]]);e.CoopAndCoep=Object.freeze(t.get(`3`));let n=`vscode-coi`;function r(e){let r;typeof e==`string`?r=new URL(e).searchParams:e instanceof URL?r=e.searchParams:ft.isUri(e)&&(r=new URL(e.toString(!0)).searchParams);let i=r?.get(n);if(i)return t.get(i)}e.getHeadersFromQuery=r;function i(e,t,r){if(!globalThis.crossOriginIsolated)return;let i=t&&r?`3`:r?`2`:`1`;e instanceof URLSearchParams?e.set(n,i):e[n]=i}e.addSearchParam=i})(Et||={});const Dt=`default`;var Ot=class{constructor(e,t,n,r,i){this.vsWorker=e,this.req=t,this.channel=n,this.method=r,this.args=i,this.type=0}},kt=class{constructor(e,t,n,r){this.vsWorker=e,this.seq=t,this.res=n,this.err=r,this.type=1}},At=class{constructor(e,t,n,r,i){this.vsWorker=e,this.req=t,this.channel=n,this.eventName=r,this.arg=i,this.type=2}},jt=class{constructor(e,t,n){this.vsWorker=e,this.req=t,this.event=n,this.type=3}},Mt=class{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}},Nt=class{constructor(e){this._workerId=-1,this._handler=e,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(e){this._workerId=e}sendMessage(e,t,n){let r=String(++this._lastSentReq);return new Promise((i,a)=>{this._pendingReplies[r]={resolve:i,reject:a},this._send(new Ot(this._workerId,r,e,t,n))})}listen(e,t,n){let r=null,i=new T({onWillAddFirstListener:()=>{r=String(++this._lastSentReq),this._pendingEmitters.set(r,i),this._send(new At(this._workerId,r,e,t,n))},onDidRemoveLastListener:()=>{this._pendingEmitters.delete(r),this._send(new Mt(this._workerId,r)),r=null}});return i.event}handleMessage(e){!e||!e.vsWorker||this._workerId!==-1&&e.vsWorker!==this._workerId||this._handleMessage(e)}createProxyToRemoteChannel(e,t){return new Proxy(Object.create(null),{get:(n,r)=>(typeof r==`string`&&!n[r]&&(Ft(r)?n[r]=t=>this.listen(e,r,t):Pt(r)?n[r]=this.listen(e,r,void 0):r.charCodeAt(0)===36&&(n[r]=async(...n)=>(await t?.(),this.sendMessage(e,r,n)))),n[r])})}_handleMessage(e){switch(e.type){case 1:return this._handleReplyMessage(e);case 0:return this._handleRequestMessage(e);case 2:return this._handleSubscribeEventMessage(e);case 3:return this._handleEventMessage(e);case 4:return this._handleUnsubscribeEventMessage(e)}}_handleReplyMessage(e){if(!this._pendingReplies[e.seq]){console.warn(`Got reply to unknown seq`);return}let t=this._pendingReplies[e.seq];if(delete this._pendingReplies[e.seq],e.err){let n=e.err;e.err.$isError&&(n=Error(),n.name=e.err.name,n.message=e.err.message,n.stack=e.err.stack),t.reject(n);return}t.resolve(e.res)}_handleRequestMessage(e){let t=e.req;this._handler.handleMessage(e.channel,e.method,e.args).then(e=>{this._send(new kt(this._workerId,t,e,void 0))},e=>{e.detail instanceof Error&&(e.detail=n(e.detail)),this._send(new kt(this._workerId,t,void 0,n(e)))})}_handleSubscribeEventMessage(e){let t=e.req,n=this._handler.handleEvent(e.channel,e.eventName,e.arg)(e=>{this._send(new jt(this._workerId,t,e))});this._pendingEvents.set(t,n)}_handleEventMessage(e){if(!this._pendingEmitters.has(e.req)){console.warn(`Got event for unknown req`);return}this._pendingEmitters.get(e.req).fire(e.event)}_handleUnsubscribeEventMessage(e){if(!this._pendingEvents.has(e.req)){console.warn(`Got unsubscribe for unknown req`);return}this._pendingEvents.get(e.req).dispose(),this._pendingEvents.delete(e.req)}_send(e){let t=[];if(e.type===0)for(let n=0;n<e.args.length;n++)e.args[n]instanceof ArrayBuffer&&t.push(e.args[n]);else e.type===1&&e.res instanceof ArrayBuffer&&t.push(e.res);this._handler.sendMessage(e,t)}};function Pt(e){return e[0]===`o`&&e[1]===`n`&&Ne(e.charCodeAt(2))}function Ft(e){return/^onDynamic/.test(e)&&Ne(e.charCodeAt(9))}var It=class{constructor(e,t){this._localChannels=new Map,this._remoteChannels=new Map,this._requestHandlerFactory=t,this._requestHandler=null,this._protocol=new Nt({sendMessage:(t,n)=>{e(t,n)},handleMessage:(e,t,n)=>this._handleMessage(e,t,n),handleEvent:(e,t,n)=>this._handleEvent(e,t,n)})}onmessage(e){this._protocol.handleMessage(e)}_handleMessage(e,t,n){if(e===Dt&&t===`$initialize`)return this.initialize(n[0],n[1],n[2]);let r=e===Dt?this._requestHandler:this._localChannels.get(e);if(!r)return Promise.reject(Error(`Missing channel ${e} on worker thread`));if(typeof r[t]!=`function`)return Promise.reject(Error(`Missing method ${t} on worker thread channel ${e}`));try{return Promise.resolve(r[t].apply(r,n))}catch(e){return Promise.reject(e)}}_handleEvent(e,t,n){let r=e===Dt?this._requestHandler:this._localChannels.get(e);if(!r)throw Error(`Missing channel ${e} on worker thread`);if(Ft(t)){let e=r[t].call(r,n);if(typeof e!=`function`)throw Error(`Missing dynamic event ${t} on request handler.`);return e}if(Pt(t)){let e=r[t];if(typeof e!=`function`)throw Error(`Missing event ${t} on request handler.`);return e}throw Error(`Malformed event name ${t}`)}getChannel(e){if(!this._remoteChannels.has(e)){let t=this._protocol.createProxyToRemoteChannel(e);this._remoteChannels.set(e,t)}return this._remoteChannels.get(e)}async initialize(e,t,n){if(this._protocol.setWorkerId(e),this._requestHandlerFactory){this._requestHandler=this._requestHandlerFactory(this);return}return t&&(t.baseUrl!==void 0&&delete t.baseUrl,t.paths!==void 0&&t.paths.vs!==void 0&&delete t.paths.vs,t.trustedTypesPolicy!==void 0&&delete t.trustedTypesPolicy,t.catchError=!0,globalThis.require.config(t)),import(`${Tt.asBrowserUri(`${n}.js`).toString(!0)}`).then(e=>{if(this._requestHandler=e.create(this),!this._requestHandler)throw Error(`No RequestHandler!`)})}},Lt=class{constructor(e,t,n,r){this.originalStart=e,this.originalLength=t,this.modifiedStart=n,this.modifiedLength=r}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}};function Rt(e,t){return(t<<5)-t+e|0}function zt(e,t){t=Rt(149417,t);for(let n=0,r=e.length;n<r;n++)t=Rt(e.charCodeAt(n),t);return t}function Bt(e,t,n=32){let r=n-t,i=~((1<<r)-1);return(e<<t|(i&e)>>>r)>>>0}function Vt(e,t=0,n=e.byteLength,r=0){for(let i=0;i<n;i++)e[t+i]=r}function Ht(e,t,n=`0`){for(;e.length<t;)e=n+e;return e}function Ut(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map(e=>e.toString(16).padStart(2,`0`)).join(``):Ht((e>>>0).toString(16),t/4)}(class e{static{this._bigBlock32=new DataView(new ArrayBuffer(320))}constructor(){this._h0=1732584193,this._h1=4023233417,this._h2=2562383102,this._h3=271733878,this._h4=3285377520,this._buff=new Uint8Array(67),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}update(e){let t=e.length;if(t===0)return;let n=this._buff,r=this._buffLen,i=this._leftoverHighSurrogate,a,o;for(i===0?(a=e.charCodeAt(0),o=0):(a=i,o=-1,i=0);;){let s=a;if(Pe(a))if(o+1<t){let t=e.charCodeAt(o+1);Fe(t)?(o++,s=Ie(a,t)):s=65533}else{i=a;break}else Fe(a)&&(s=65533);if(r=this._push(n,r,s),o++,o<t)a=e.charCodeAt(o);else break}this._buffLen=r,this._leftoverHighSurrogate=i}_push(e,t,n){return n<128?e[t++]=n:n<2048?(e[t++]=192|(n&1984)>>>6,e[t++]=128|(n&63)>>>0):n<65536?(e[t++]=224|(n&61440)>>>12,e[t++]=128|(n&4032)>>>6,e[t++]=128|(n&63)>>>0):(e[t++]=240|(n&1835008)>>>18,e[t++]=128|(n&258048)>>>12,e[t++]=128|(n&4032)>>>6,e[t++]=128|(n&63)>>>0),t>=64&&(this._step(),t-=64,this._totalLen+=64,e[0]=e[64],e[1]=e[65],e[2]=e[66]),t}digest(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),Ut(this._h0)+Ut(this._h1)+Ut(this._h2)+Ut(this._h3)+Ut(this._h4)}_wrapUp(){this._buff[this._buffLen++]=128,Vt(this._buff,this._buffLen),this._buffLen>56&&(this._step(),Vt(this._buff));let e=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(e/4294967296),!1),this._buffDV.setUint32(60,e%4294967296,!1),this._step()}_step(){let t=e._bigBlock32,n=this._buffDV;for(let e=0;e<64;e+=4)t.setUint32(e,n.getUint32(e,!1),!1);for(let e=64;e<320;e+=4)t.setUint32(e,Bt(t.getUint32(e-12,!1)^t.getUint32(e-32,!1)^t.getUint32(e-56,!1)^t.getUint32(e-64,!1),1),!1);let r=this._h0,i=this._h1,a=this._h2,o=this._h3,s=this._h4,c,l,u;for(let e=0;e<80;e++)e<20?(c=i&a|~i&o,l=1518500249):e<40?(c=i^a^o,l=1859775393):e<60?(c=i&a|i&o|a&o,l=2400959708):(c=i^a^o,l=3395469782),u=Bt(r,5)+c+s+l+t.getUint32(e*4,!1)&4294967295,s=o,o=a,a=Bt(i,30),i=r,r=u;this._h0=this._h0+r&4294967295,this._h1=this._h1+i&4294967295,this._h2=this._h2+a&4294967295,this._h3=this._h3+o&4294967295,this._h4=this._h4+s&4294967295}});var Wt=class{constructor(e){this.source=e}getElements(){let e=this.source,t=new Int32Array(e.length);for(let n=0,r=e.length;n<r;n++)t[n]=e.charCodeAt(n);return t}};function Gt(e,t,n){return new Yt(new Wt(e),new Wt(t)).ComputeDiff(n).changes}var Kt=class{static Assert(e,t){if(!e)throw Error(t)}},qt=class{static Copy(e,t,n,r,i){for(let a=0;a<i;a++)n[r+a]=e[t+a]}static Copy2(e,t,n,r,i){for(let a=0;a<i;a++)n[r+a]=e[t+a]}},Jt=class{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new Lt(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_originalCount++}AddModifiedElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}},Yt=class e{constructor(t,n,r=null){this.ContinueProcessingPredicate=r,this._originalSequence=t,this._modifiedSequence=n;let[i,a,o]=e._getElements(t),[s,c,l]=e._getElements(n);this._hasStrings=o&&l,this._originalStringElements=i,this._originalElementsOrHash=a,this._modifiedStringElements=s,this._modifiedElementsOrHash=c,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(e){return e.length>0&&typeof e[0]==`string`}static _getElements(t){let n=t.getElements();if(e._isStringArray(n)){let e=new Int32Array(n.length);for(let t=0,r=n.length;t<r;t++)e[t]=zt(n[t],0);return[n,e,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}ElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._modifiedElementsOrHash[t]?this._hasStrings?this._originalStringElements[e]===this._modifiedStringElements[t]:!0:!1}ElementsAreStrictEqual(t,n){if(!this.ElementsAreEqual(t,n))return!1;let r=e._getStrictElement(this._originalSequence,t),i=e._getStrictElement(this._modifiedSequence,n);return r===i}static _getStrictElement(e,t){return typeof e.getStrictElement==`function`?e.getStrictElement(t):null}OriginalElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._originalElementsOrHash[t]?this._hasStrings?this._originalStringElements[e]===this._originalStringElements[t]:!0:!1}ModifiedElementsAreEqual(e,t){return this._modifiedElementsOrHash[e]===this._modifiedElementsOrHash[t]?this._hasStrings?this._modifiedStringElements[e]===this._modifiedStringElements[t]:!0:!1}ComputeDiff(e){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,e)}_ComputeDiff(e,t,n,r,i){let a=[!1],o=this.ComputeDiffRecursive(e,t,n,r,a);return i&&(o=this.PrettifyChanges(o)),{quitEarly:a[0],changes:o}}ComputeDiffRecursive(e,t,n,r,i){for(i[0]=!1;e<=t&&n<=r&&this.ElementsAreEqual(e,n);)e++,n++;for(;t>=e&&r>=n&&this.ElementsAreEqual(t,r);)t--,r--;if(e>t||n>r){let i;return n<=r?(Kt.Assert(e===t+1,`originalStart should only be one more than originalEnd`),i=[new Lt(e,0,n,r-n+1)]):e<=t?(Kt.Assert(n===r+1,`modifiedStart should only be one more than modifiedEnd`),i=[new Lt(e,t-e+1,n,0)]):(Kt.Assert(e===t+1,`originalStart should only be one more than originalEnd`),Kt.Assert(n===r+1,`modifiedStart should only be one more than modifiedEnd`),i=[]),i}let a=[0],o=[0],s=this.ComputeRecursionPoint(e,t,n,r,a,o,i),c=a[0],l=o[0];if(s!==null)return s;if(!i[0]){let a=this.ComputeDiffRecursive(e,c,n,l,i),o=[];return o=i[0]?[new Lt(c+1,t-(c+1)+1,l+1,r-(l+1)+1)]:this.ComputeDiffRecursive(c+1,t,l+1,r,i),this.ConcatenateChanges(a,o)}return[new Lt(e,t-e+1,n,r-n+1)]}WALKTRACE(e,t,n,r,i,a,o,s,c,l,u,d,f,p,m,h,g,_){let v=null,y=null,b=new Jt,x=t,S=n,C=f[0]-h[0]-r,w=-1073741824,ee=this.m_forwardHistory.length-1;do{let t=C+e;t===x||t<S&&c[t-1]<c[t+1]?(u=c[t+1],p=u-C-r,u<w&&b.MarkNextChange(),w=u,b.AddModifiedElement(u+1,p),C=t+1-e):(u=c[t-1]+1,p=u-C-r,u<w&&b.MarkNextChange(),w=u-1,b.AddOriginalElement(u,p+1),C=t-1-e),ee>=0&&(c=this.m_forwardHistory[ee],e=c[0],x=1,S=c.length-1)}while(--ee>=-1);if(v=b.getReverseChanges(),_[0]){let e=f[0]+1,t=h[0]+1;if(v!==null&&v.length>0){let n=v[v.length-1];e=Math.max(e,n.getOriginalEnd()),t=Math.max(t,n.getModifiedEnd())}y=[new Lt(e,d-e+1,t,m-t+1)]}else{b=new Jt,x=a,S=o,C=f[0]-h[0]-s,w=1073741824,ee=g?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{let e=C+i;e===x||e<S&&l[e-1]>=l[e+1]?(u=l[e+1]-1,p=u-C-s,u>w&&b.MarkNextChange(),w=u+1,b.AddOriginalElement(u+1,p+1),C=e+1-i):(u=l[e-1],p=u-C-s,u>w&&b.MarkNextChange(),w=u,b.AddModifiedElement(u+1,p+1),C=e-1-i),ee>=0&&(l=this.m_reverseHistory[ee],i=l[0],x=1,S=l.length-1)}while(--ee>=-1);y=b.getChanges()}return this.ConcatenateChanges(v,y)}ComputeRecursionPoint(e,t,n,r,i,a,o){let s=0,c=0,l=0,u=0,d=0,f=0;e--,n--,i[0]=0,a[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];let p=t-e+(r-n),m=p+1,h=new Int32Array(m),g=new Int32Array(m),_=r-n,v=t-e,y=e-n,b=t-r,x=(v-_)%2==0;h[_]=e,g[v]=t,o[0]=!1;for(let S=1;S<=p/2+1;S++){let p=0,C=0;l=this.ClipDiagonalBound(_-S,S,_,m),u=this.ClipDiagonalBound(_+S,S,_,m);for(let e=l;e<=u;e+=2){s=e===l||e<u&&h[e-1]<h[e+1]?h[e+1]:h[e-1]+1,c=s-(e-_)-y;let n=s;for(;s<t&&c<r&&this.ElementsAreEqual(s+1,c+1);)s++,c++;if(h[e]=s,s+c>p+C&&(p=s,C=c),!x&&Math.abs(e-v)<=S-1&&s>=g[e])return i[0]=s,a[0]=c,n<=g[e]&&S<=1448?this.WALKTRACE(_,l,u,y,v,d,f,b,h,g,s,t,i,c,r,a,x,o):null}let w=(p-e+(C-n)-S)/2;if(this.ContinueProcessingPredicate!==null&&!this.ContinueProcessingPredicate(p,w))return o[0]=!0,i[0]=p,a[0]=C,w>0&&S<=1448?this.WALKTRACE(_,l,u,y,v,d,f,b,h,g,s,t,i,c,r,a,x,o):(e++,n++,[new Lt(e,t-e+1,n,r-n+1)]);d=this.ClipDiagonalBound(v-S,S,v,m),f=this.ClipDiagonalBound(v+S,S,v,m);for(let p=d;p<=f;p+=2){s=p===d||p<f&&g[p-1]>=g[p+1]?g[p+1]-1:g[p-1],c=s-(p-v)-b;let m=s;for(;s>e&&c>n&&this.ElementsAreEqual(s,c);)s--,c--;if(g[p]=s,x&&Math.abs(p-_)<=S&&s<=h[p])return i[0]=s,a[0]=c,m>=h[p]&&S<=1448?this.WALKTRACE(_,l,u,y,v,d,f,b,h,g,s,t,i,c,r,a,x,o):null}if(S<=1447){let e=new Int32Array(u-l+2);e[0]=_-l+1,qt.Copy2(h,l,e,1,u-l+1),this.m_forwardHistory.push(e),e=new Int32Array(f-d+2),e[0]=v-d+1,qt.Copy2(g,d,e,1,f-d+1),this.m_reverseHistory.push(e)}}return this.WALKTRACE(_,l,u,y,v,d,f,b,h,g,s,t,i,c,r,a,x,o)}PrettifyChanges(e){for(let t=0;t<e.length;t++){let n=e[t],r=t<e.length-1?e[t+1].originalStart:this._originalElementsOrHash.length,i=t<e.length-1?e[t+1].modifiedStart:this._modifiedElementsOrHash.length,a=n.originalLength>0,o=n.modifiedLength>0;for(;n.originalStart+n.originalLength<r&&n.modifiedStart+n.modifiedLength<i&&(!a||this.OriginalElementsAreEqual(n.originalStart,n.originalStart+n.originalLength))&&(!o||this.ModifiedElementsAreEqual(n.modifiedStart,n.modifiedStart+n.modifiedLength));){let e=this.ElementsAreStrictEqual(n.originalStart,n.modifiedStart);if(this.ElementsAreStrictEqual(n.originalStart+n.originalLength,n.modifiedStart+n.modifiedLength)&&!e)break;n.originalStart++,n.modifiedStart++}let s=[null];if(t<e.length-1&&this.ChangesOverlap(e[t],e[t+1],s)){e[t]=s[0],e.splice(t+1,1),t--;continue}}for(let t=e.length-1;t>=0;t--){let n=e[t],r=0,i=0;if(t>0){let n=e[t-1];r=n.originalStart+n.originalLength,i=n.modifiedStart+n.modifiedLength}let a=n.originalLength>0,o=n.modifiedLength>0,s=0,c=this._boundaryScore(n.originalStart,n.originalLength,n.modifiedStart,n.modifiedLength);for(let e=1;;e++){let t=n.originalStart-e,l=n.modifiedStart-e;if(t<r||l<i||a&&!this.OriginalElementsAreEqual(t,t+n.originalLength)||o&&!this.ModifiedElementsAreEqual(l,l+n.modifiedLength))break;let u=(t===r&&l===i?5:0)+this._boundaryScore(t,n.originalLength,l,n.modifiedLength);u>c&&(c=u,s=e)}n.originalStart-=s,n.modifiedStart-=s;let l=[null];if(t>0&&this.ChangesOverlap(e[t-1],e[t],l)){e[t-1]=l[0],e.splice(t,1),t++;continue}}if(this._hasStrings)for(let t=1,n=e.length;t<n;t++){let n=e[t-1],r=e[t],i=r.originalStart-n.originalStart-n.originalLength,a=n.originalStart,o=r.originalStart+r.originalLength,s=o-a,c=n.modifiedStart,l=r.modifiedStart+r.modifiedLength,u=l-c;if(i<5&&s<20&&u<20){let e=this._findBetterContiguousSequence(a,s,c,u,i);if(e){let[t,a]=e;(t!==n.originalStart+n.originalLength||a!==n.modifiedStart+n.modifiedLength)&&(n.originalLength=t-n.originalStart,n.modifiedLength=a-n.modifiedStart,r.originalStart=t+i,r.modifiedStart=a+i,r.originalLength=o-r.originalStart,r.modifiedLength=l-r.modifiedStart)}}}return e}_findBetterContiguousSequence(e,t,n,r,i){if(t<i||r<i)return null;let a=e+t-i+1,o=n+r-i+1,s=0,c=0,l=0;for(let t=e;t<a;t++)for(let e=n;e<o;e++){let n=this._contiguousSequenceScore(t,e,i);n>0&&n>s&&(s=n,c=t,l=e)}return s>0?[c,l]:null}_contiguousSequenceScore(e,t,n){let r=0;for(let i=0;i<n;i++){if(!this.ElementsAreEqual(e+i,t+i))return 0;r+=this._originalStringElements[e+i].length}return r}_OriginalIsBoundary(e){return e<=0||e>=this._originalElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._originalStringElements[e])}_OriginalRegionIsBoundary(e,t){if(this._OriginalIsBoundary(e)||this._OriginalIsBoundary(e-1))return!0;if(t>0){let n=e+t;if(this._OriginalIsBoundary(n-1)||this._OriginalIsBoundary(n))return!0}return!1}_ModifiedIsBoundary(e){return e<=0||e>=this._modifiedElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[e])}_ModifiedRegionIsBoundary(e,t){if(this._ModifiedIsBoundary(e)||this._ModifiedIsBoundary(e-1))return!0;if(t>0){let n=e+t;if(this._ModifiedIsBoundary(n-1)||this._ModifiedIsBoundary(n))return!0}return!1}_boundaryScore(e,t,n,r){let i=this._OriginalRegionIsBoundary(e,t)?1:0,a=this._ModifiedRegionIsBoundary(n,r)?1:0;return i+a}ConcatenateChanges(e,t){let n=[];if(e.length===0||t.length===0)return t.length>0?t:e;if(this.ChangesOverlap(e[e.length-1],t[0],n)){let r=Array(e.length+t.length-1);return qt.Copy(e,0,r,0,e.length-1),r[e.length-1]=n[0],qt.Copy(t,1,r,e.length,t.length-1),r}else{let n=Array(e.length+t.length);return qt.Copy(e,0,n,0,e.length),qt.Copy(t,0,n,e.length,t.length),n}}ChangesOverlap(e,t,n){if(Kt.Assert(e.originalStart<=t.originalStart,`Left change is not less than or equal to right change`),Kt.Assert(e.modifiedStart<=t.modifiedStart,`Left change is not less than or equal to right change`),e.originalStart+e.originalLength>=t.originalStart||e.modifiedStart+e.modifiedLength>=t.modifiedStart){let r=e.originalStart,i=e.originalLength,a=e.modifiedStart,o=e.modifiedLength;return e.originalStart+e.originalLength>=t.originalStart&&(i=t.originalStart+t.originalLength-e.originalStart),e.modifiedStart+e.modifiedLength>=t.modifiedStart&&(o=t.modifiedStart+t.modifiedLength-e.modifiedStart),n[0]=new Lt(r,i,a,o),!0}else return n[0]=null,!1}ClipDiagonalBound(e,t,n,r){if(e>=0&&e<r)return e;let i=n,a=r-n-1,o=t%2==0;if(e<0){let e=i%2==0;return o===e?0:1}else{let e=a%2==0;return o===e?r-1:r-2}}},M=class e{constructor(e,t){this.lineNumber=e,this.column=t}with(t=this.lineNumber,n=this.column){return t===this.lineNumber&&n===this.column?this:new e(t,n)}delta(e=0,t=0){return this.with(this.lineNumber+e,this.column+t)}equals(t){return e.equals(this,t)}static equals(e,t){return!e&&!t?!0:!!e&&!!t&&e.lineNumber===t.lineNumber&&e.column===t.column}isBefore(t){return e.isBefore(this,t)}static isBefore(e,t){return e.lineNumber<t.lineNumber?!0:t.lineNumber<e.lineNumber?!1:e.column<t.column}isBeforeOrEqual(t){return e.isBeforeOrEqual(this,t)}static isBeforeOrEqual(e,t){return e.lineNumber<t.lineNumber?!0:t.lineNumber<e.lineNumber?!1:e.column<=t.column}static compare(e,t){let n=e.lineNumber|0,r=t.lineNumber|0;if(n===r){let n=e.column|0,r=t.column|0;return n-r}return n-r}clone(){return new e(this.lineNumber,this.column)}toString(){return`(`+this.lineNumber+`,`+this.column+`)`}static lift(t){return new e(t.lineNumber,t.column)}static isIPosition(e){return e&&typeof e.lineNumber==`number`&&typeof e.column==`number`}toJSON(){return{lineNumber:this.lineNumber,column:this.column}}},N=class e{constructor(e,t,n,r){e>n||e===n&&t>r?(this.startLineNumber=n,this.startColumn=r,this.endLineNumber=e,this.endColumn=t):(this.startLineNumber=e,this.startColumn=t,this.endLineNumber=n,this.endColumn=r)}isEmpty(){return e.isEmpty(this)}static isEmpty(e){return e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn}containsPosition(t){return e.containsPosition(this,t)}static containsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber||t.lineNumber===e.startLineNumber&&t.column<e.startColumn||t.lineNumber===e.endLineNumber&&t.column>e.endColumn)}static strictContainsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber||t.lineNumber===e.startLineNumber&&t.column<=e.startColumn||t.lineNumber===e.endLineNumber&&t.column>=e.endColumn)}containsRange(t){return e.containsRange(this,t)}static containsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber||t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber||t.startLineNumber===e.startLineNumber&&t.startColumn<e.startColumn||t.endLineNumber===e.endLineNumber&&t.endColumn>e.endColumn)}strictContainsRange(t){return e.strictContainsRange(this,t)}static strictContainsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber||t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber||t.startLineNumber===e.startLineNumber&&t.startColumn<=e.startColumn||t.endLineNumber===e.endLineNumber&&t.endColumn>=e.endColumn)}plusRange(t){return e.plusRange(this,t)}static plusRange(t,n){let r,i,a,o;return n.startLineNumber<t.startLineNumber?(r=n.startLineNumber,i=n.startColumn):n.startLineNumber===t.startLineNumber?(r=n.startLineNumber,i=Math.min(n.startColumn,t.startColumn)):(r=t.startLineNumber,i=t.startColumn),n.endLineNumber>t.endLineNumber?(a=n.endLineNumber,o=n.endColumn):n.endLineNumber===t.endLineNumber?(a=n.endLineNumber,o=Math.max(n.endColumn,t.endColumn)):(a=t.endLineNumber,o=t.endColumn),new e(r,i,a,o)}intersectRanges(t){return e.intersectRanges(this,t)}static intersectRanges(t,n){let r=t.startLineNumber,i=t.startColumn,a=t.endLineNumber,o=t.endColumn,s=n.startLineNumber,c=n.startColumn,l=n.endLineNumber,u=n.endColumn;return r<s?(r=s,i=c):r===s&&(i=Math.max(i,c)),a>l?(a=l,o=u):a===l&&(o=Math.min(o,u)),r>a||r===a&&i>o?null:new e(r,i,a,o)}equalsRange(t){return e.equalsRange(this,t)}static equalsRange(e,t){return!e&&!t?!0:!!e&&!!t&&e.startLineNumber===t.startLineNumber&&e.startColumn===t.startColumn&&e.endLineNumber===t.endLineNumber&&e.endColumn===t.endColumn}getEndPosition(){return e.getEndPosition(this)}static getEndPosition(e){return new M(e.endLineNumber,e.endColumn)}getStartPosition(){return e.getStartPosition(this)}static getStartPosition(e){return new M(e.startLineNumber,e.startColumn)}toString(){return`[`+this.startLineNumber+`,`+this.startColumn+` -> `+this.endLineNumber+`,`+this.endColumn+`]`}setEndPosition(t,n){return new e(this.startLineNumber,this.startColumn,t,n)}setStartPosition(t,n){return new e(t,n,this.endLineNumber,this.endColumn)}collapseToStart(){return e.collapseToStart(this)}static collapseToStart(t){return new e(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}collapseToEnd(){return e.collapseToEnd(this)}static collapseToEnd(t){return new e(t.endLineNumber,t.endColumn,t.endLineNumber,t.endColumn)}delta(t){return new e(this.startLineNumber+t,this.startColumn,this.endLineNumber+t,this.endColumn)}static fromPositions(t,n=t){return new e(t.lineNumber,t.column,n.lineNumber,n.column)}static lift(t){return t?new e(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(e){return e&&typeof e.startLineNumber==`number`&&typeof e.startColumn==`number`&&typeof e.endLineNumber==`number`&&typeof e.endColumn==`number`}static areIntersectingOrTouching(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<t.startColumn||t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<e.startColumn)}static areIntersecting(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<=t.startColumn||t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<=e.startColumn)}static compareRangesUsingStarts(e,t){if(e&&t){let n=e.startLineNumber|0,r=t.startLineNumber|0;if(n===r){let n=e.startColumn|0,r=t.startColumn|0;if(n===r){let n=e.endLineNumber|0,r=t.endLineNumber|0;if(n===r){let n=e.endColumn|0,r=t.endColumn|0;return n-r}return n-r}return n-r}return n-r}return(e?1:0)-(t?1:0)}static compareRangesUsingEnds(e,t){return e.endLineNumber===t.endLineNumber?e.endColumn===t.endColumn?e.startLineNumber===t.startLineNumber?e.startColumn-t.startColumn:e.startLineNumber-t.startLineNumber:e.endColumn-t.endColumn:e.endLineNumber-t.endLineNumber}static spansMultipleLines(e){return e.endLineNumber>e.startLineNumber}toJSON(){return this}};function Xt(e){return e<0?0:e>255?255:e|0}function Zt(e){return e<0?0:e>4294967295?4294967295:e|0}var Qt=class e{constructor(t){let n=Xt(t);this._defaultValue=n,this._asciiMap=e._createAsciiMap(n),this._map=new Map}static _createAsciiMap(e){let t=new Uint8Array(256);return t.fill(e),t}set(e,t){let n=Xt(t);e>=0&&e<256?this._asciiMap[e]=n:this._map.set(e,n)}get(e){return e>=0&&e<256?this._asciiMap[e]:this._map.get(e)||this._defaultValue}clear(){this._asciiMap.fill(this._defaultValue),this._map.clear()}},$t=class{constructor(e,t,n){let r=new Uint8Array(e*t);for(let i=0,a=e*t;i<a;i++)r[i]=n;this._data=r,this.rows=e,this.cols=t}get(e,t){return this._data[e*this.cols+t]}set(e,t,n){this._data[e*this.cols+t]=n}},en=class{constructor(e){let t=0,n=0;for(let r=0,i=e.length;r<i;r++){let[i,a,o]=e[r];a>t&&(t=a),i>n&&(n=i),o>n&&(n=o)}t++,n++;let r=new $t(n,t,0);for(let t=0,n=e.length;t<n;t++){let[n,i,a]=e[t];r.set(n,i,a)}this._states=r,this._maxCharCode=t}nextState(e,t){return t<0||t>=this._maxCharCode?0:this._states.get(e,t)}};let tn=null;function nn(){return tn===null&&(tn=new en([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),tn}let rn=null;function an(){if(rn===null){rn=new Qt(0);for(let e=0;e<35;e++)rn.set(` 	<>'"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…`.charCodeAt(e),1);for(let e=0;e<4;e++)rn.set(`.,;:`.charCodeAt(e),2)}return rn}var on=class e{static _createLink(e,t,n,r,i){let a=i-1;do{let n=t.charCodeAt(a);if(e.get(n)!==2)break;a--}while(a>r);if(r>0){let e=t.charCodeAt(r-1),n=t.charCodeAt(a);(e===40&&n===41||e===91&&n===93||e===123&&n===125)&&a--}return{range:{startLineNumber:n,startColumn:r+1,endLineNumber:n,endColumn:a+2},url:t.substring(r,a+1)}}static computeLinks(t,n=nn()){let r=an(),i=[];for(let a=1,o=t.getLineCount();a<=o;a++){let o=t.getLineContent(a),s=o.length,c=0,l=0,u=0,d=1,f=!1,p=!1,m=!1,h=!1;for(;c<s;){let t=!1,s=o.charCodeAt(c);if(d===13){let n;switch(s){case 40:f=!0,n=0;break;case 41:n=f?0:1;break;case 91:m=!0,p=!0,n=0;break;case 93:m=!1,n=p?0:1;break;case 123:h=!0,n=0;break;case 125:n=h?0:1;break;case 39:case 34:case 96:n=u===s?1:u===39||u===34||u===96?0:1;break;case 42:n=u===42?1:0;break;case 124:n=u===124?1:0;break;case 32:n=m?0:1;break;default:n=r.get(s)}n===1&&(i.push(e._createLink(r,o,a,l,c)),t=!0)}else if(d===12){let e;s===91?(p=!0,e=0):e=r.get(s),e===1?t=!0:d=13}else d=n.nextState(d,s),d===0&&(t=!0);t&&(d=1,f=!1,p=!1,h=!1,l=c+1,u=s),c++}d===13&&i.push(e._createLink(r,o,a,l,s))}return i}};function sn(e){return!e||typeof e.getLineCount!=`function`||typeof e.getLineContent!=`function`?[]:on.computeLinks(e)}var cn=class e{constructor(){this._defaultValueSet=[[`true`,`false`],[`True`,`False`],[`Private`,`Public`,`Friend`,`ReadOnly`,`Partial`,`Protected`,`WriteOnly`],[`public`,`protected`,`private`]]}static{this.INSTANCE=new e}navigateValueSet(e,t,n,r,i){if(e&&t){let n=this.doNavigateValueSet(t,i);if(n)return{range:e,value:n}}if(n&&r){let e=this.doNavigateValueSet(r,i);if(e)return{range:n,value:e}}return null}doNavigateValueSet(e,t){let n=this.numberReplace(e,t);return n===null?this.textReplace(e,t):n}numberReplace(e,t){let n=10**(e.length-(e.lastIndexOf(`.`)+1)),r=Number(e),i=parseFloat(e);return!isNaN(r)&&!isNaN(i)&&r===i?r===0&&!t?null:(r=Math.floor(r*n),r+=t?n:-n,String(r/n)):null}textReplace(e,t){return this.valueSetsReplace(this._defaultValueSet,e,t)}valueSetsReplace(e,t,n){let r=null;for(let i=0,a=e.length;r===null&&i<a;i++)r=this.valueSetReplace(e[i],t,n);return r}valueSetReplace(e,t,n){let r=e.indexOf(t);return r>=0?(r+=n?1:-1,r<0?r=e.length-1:r%=e.length,e[r]):null}};const ln=Object.freeze(function(e,t){let n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}});var un;(function(e){function t(t){return t===e.None||t===e.Cancelled||t instanceof dn?!0:!t||typeof t!=`object`?!1:typeof t.isCancellationRequested==`boolean`&&typeof t.onCancellationRequested==`function`}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:S.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:ln})})(un||={});var dn=class{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?ln:(this._emitter||=new T,this._emitter.event)}dispose(){this._emitter&&=(this._emitter.dispose(),null)}},fn=class{constructor(e){this._token=void 0,this._parentListener=void 0,this._parentListener=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this._token||=new dn,this._token}cancel(){this._token?this._token instanceof dn&&this._token.cancel():this._token=un.Cancelled}dispose(e=!1){e&&this.cancel(),this._parentListener?.dispose(),this._token?this._token instanceof dn&&this._token.dispose():this._token=un.None}},pn=class{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(e,t){this._keyCodeToStr[e]=t,this._strToKeyCode[t.toLowerCase()]=e}keyCodeToStr(e){return this._keyCodeToStr[e]}strToKeyCode(e){return this._strToKeyCode[e.toLowerCase()]||0}};const mn=new pn,hn=new pn,gn=new pn,_n=Array(230),vn={},yn=[],bn=Object.create(null),xn=Object.create(null),Sn=[],Cn=[];for(let e=0;e<=193;e++)Sn[e]=-1;for(let e=0;e<=132;e++)Cn[e]=-1;(function(){let e=[[1,0,`None`,0,`unknown`,0,`VK_UNKNOWN`,``,``],[1,1,`Hyper`,0,``,0,``,``,``],[1,2,`Super`,0,``,0,``,``,``],[1,3,`Fn`,0,``,0,``,``,``],[1,4,`FnLock`,0,``,0,``,``,``],[1,5,`Suspend`,0,``,0,``,``,``],[1,6,`Resume`,0,``,0,``,``,``],[1,7,`Turbo`,0,``,0,``,``,``],[1,8,`Sleep`,0,``,0,`VK_SLEEP`,``,``],[1,9,`WakeUp`,0,``,0,``,``,``],[0,10,`KeyA`,31,`A`,65,`VK_A`,``,``],[0,11,`KeyB`,32,`B`,66,`VK_B`,``,``],[0,12,`KeyC`,33,`C`,67,`VK_C`,``,``],[0,13,`KeyD`,34,`D`,68,`VK_D`,``,``],[0,14,`KeyE`,35,`E`,69,`VK_E`,``,``],[0,15,`KeyF`,36,`F`,70,`VK_F`,``,``],[0,16,`KeyG`,37,`G`,71,`VK_G`,``,``],[0,17,`KeyH`,38,`H`,72,`VK_H`,``,``],[0,18,`KeyI`,39,`I`,73,`VK_I`,``,``],[0,19,`KeyJ`,40,`J`,74,`VK_J`,``,``],[0,20,`KeyK`,41,`K`,75,`VK_K`,``,``],[0,21,`KeyL`,42,`L`,76,`VK_L`,``,``],[0,22,`KeyM`,43,`M`,77,`VK_M`,``,``],[0,23,`KeyN`,44,`N`,78,`VK_N`,``,``],[0,24,`KeyO`,45,`O`,79,`VK_O`,``,``],[0,25,`KeyP`,46,`P`,80,`VK_P`,``,``],[0,26,`KeyQ`,47,`Q`,81,`VK_Q`,``,``],[0,27,`KeyR`,48,`R`,82,`VK_R`,``,``],[0,28,`KeyS`,49,`S`,83,`VK_S`,``,``],[0,29,`KeyT`,50,`T`,84,`VK_T`,``,``],[0,30,`KeyU`,51,`U`,85,`VK_U`,``,``],[0,31,`KeyV`,52,`V`,86,`VK_V`,``,``],[0,32,`KeyW`,53,`W`,87,`VK_W`,``,``],[0,33,`KeyX`,54,`X`,88,`VK_X`,``,``],[0,34,`KeyY`,55,`Y`,89,`VK_Y`,``,``],[0,35,`KeyZ`,56,`Z`,90,`VK_Z`,``,``],[0,36,`Digit1`,22,`1`,49,`VK_1`,``,``],[0,37,`Digit2`,23,`2`,50,`VK_2`,``,``],[0,38,`Digit3`,24,`3`,51,`VK_3`,``,``],[0,39,`Digit4`,25,`4`,52,`VK_4`,``,``],[0,40,`Digit5`,26,`5`,53,`VK_5`,``,``],[0,41,`Digit6`,27,`6`,54,`VK_6`,``,``],[0,42,`Digit7`,28,`7`,55,`VK_7`,``,``],[0,43,`Digit8`,29,`8`,56,`VK_8`,``,``],[0,44,`Digit9`,30,`9`,57,`VK_9`,``,``],[0,45,`Digit0`,21,`0`,48,`VK_0`,``,``],[1,46,`Enter`,3,`Enter`,13,`VK_RETURN`,``,``],[1,47,`Escape`,9,`Escape`,27,`VK_ESCAPE`,``,``],[1,48,`Backspace`,1,`Backspace`,8,`VK_BACK`,``,``],[1,49,`Tab`,2,`Tab`,9,`VK_TAB`,``,``],[1,50,`Space`,10,`Space`,32,`VK_SPACE`,``,``],[0,51,`Minus`,88,`-`,189,`VK_OEM_MINUS`,`-`,`OEM_MINUS`],[0,52,`Equal`,86,`=`,187,`VK_OEM_PLUS`,`=`,`OEM_PLUS`],[0,53,`BracketLeft`,92,`[`,219,`VK_OEM_4`,`[`,`OEM_4`],[0,54,`BracketRight`,94,`]`,221,`VK_OEM_6`,`]`,`OEM_6`],[0,55,`Backslash`,93,`\\`,220,`VK_OEM_5`,`\\`,`OEM_5`],[0,56,`IntlHash`,0,``,0,``,``,``],[0,57,`Semicolon`,85,`;`,186,`VK_OEM_1`,`;`,`OEM_1`],[0,58,`Quote`,95,`'`,222,`VK_OEM_7`,`'`,`OEM_7`],[0,59,`Backquote`,91,"`",192,`VK_OEM_3`,"`",`OEM_3`],[0,60,`Comma`,87,`,`,188,`VK_OEM_COMMA`,`,`,`OEM_COMMA`],[0,61,`Period`,89,`.`,190,`VK_OEM_PERIOD`,`.`,`OEM_PERIOD`],[0,62,`Slash`,90,`/`,191,`VK_OEM_2`,`/`,`OEM_2`],[1,63,`CapsLock`,8,`CapsLock`,20,`VK_CAPITAL`,``,``],[1,64,`F1`,59,`F1`,112,`VK_F1`,``,``],[1,65,`F2`,60,`F2`,113,`VK_F2`,``,``],[1,66,`F3`,61,`F3`,114,`VK_F3`,``,``],[1,67,`F4`,62,`F4`,115,`VK_F4`,``,``],[1,68,`F5`,63,`F5`,116,`VK_F5`,``,``],[1,69,`F6`,64,`F6`,117,`VK_F6`,``,``],[1,70,`F7`,65,`F7`,118,`VK_F7`,``,``],[1,71,`F8`,66,`F8`,119,`VK_F8`,``,``],[1,72,`F9`,67,`F9`,120,`VK_F9`,``,``],[1,73,`F10`,68,`F10`,121,`VK_F10`,``,``],[1,74,`F11`,69,`F11`,122,`VK_F11`,``,``],[1,75,`F12`,70,`F12`,123,`VK_F12`,``,``],[1,76,`PrintScreen`,0,``,0,``,``,``],[1,77,`ScrollLock`,84,`ScrollLock`,145,`VK_SCROLL`,``,``],[1,78,`Pause`,7,`PauseBreak`,19,`VK_PAUSE`,``,``],[1,79,`Insert`,19,`Insert`,45,`VK_INSERT`,``,``],[1,80,`Home`,14,`Home`,36,`VK_HOME`,``,``],[1,81,`PageUp`,11,`PageUp`,33,`VK_PRIOR`,``,``],[1,82,`Delete`,20,`Delete`,46,`VK_DELETE`,``,``],[1,83,`End`,13,`End`,35,`VK_END`,``,``],[1,84,`PageDown`,12,`PageDown`,34,`VK_NEXT`,``,``],[1,85,`ArrowRight`,17,`RightArrow`,39,`VK_RIGHT`,`Right`,``],[1,86,`ArrowLeft`,15,`LeftArrow`,37,`VK_LEFT`,`Left`,``],[1,87,`ArrowDown`,18,`DownArrow`,40,`VK_DOWN`,`Down`,``],[1,88,`ArrowUp`,16,`UpArrow`,38,`VK_UP`,`Up`,``],[1,89,`NumLock`,83,`NumLock`,144,`VK_NUMLOCK`,``,``],[1,90,`NumpadDivide`,113,`NumPad_Divide`,111,`VK_DIVIDE`,``,``],[1,91,`NumpadMultiply`,108,`NumPad_Multiply`,106,`VK_MULTIPLY`,``,``],[1,92,`NumpadSubtract`,111,`NumPad_Subtract`,109,`VK_SUBTRACT`,``,``],[1,93,`NumpadAdd`,109,`NumPad_Add`,107,`VK_ADD`,``,``],[1,94,`NumpadEnter`,3,``,0,``,``,``],[1,95,`Numpad1`,99,`NumPad1`,97,`VK_NUMPAD1`,``,``],[1,96,`Numpad2`,100,`NumPad2`,98,`VK_NUMPAD2`,``,``],[1,97,`Numpad3`,101,`NumPad3`,99,`VK_NUMPAD3`,``,``],[1,98,`Numpad4`,102,`NumPad4`,100,`VK_NUMPAD4`,``,``],[1,99,`Numpad5`,103,`NumPad5`,101,`VK_NUMPAD5`,``,``],[1,100,`Numpad6`,104,`NumPad6`,102,`VK_NUMPAD6`,``,``],[1,101,`Numpad7`,105,`NumPad7`,103,`VK_NUMPAD7`,``,``],[1,102,`Numpad8`,106,`NumPad8`,104,`VK_NUMPAD8`,``,``],[1,103,`Numpad9`,107,`NumPad9`,105,`VK_NUMPAD9`,``,``],[1,104,`Numpad0`,98,`NumPad0`,96,`VK_NUMPAD0`,``,``],[1,105,`NumpadDecimal`,112,`NumPad_Decimal`,110,`VK_DECIMAL`,``,``],[0,106,`IntlBackslash`,97,`OEM_102`,226,`VK_OEM_102`,``,``],[1,107,`ContextMenu`,58,`ContextMenu`,93,``,``,``],[1,108,`Power`,0,``,0,``,``,``],[1,109,`NumpadEqual`,0,``,0,``,``,``],[1,110,`F13`,71,`F13`,124,`VK_F13`,``,``],[1,111,`F14`,72,`F14`,125,`VK_F14`,``,``],[1,112,`F15`,73,`F15`,126,`VK_F15`,``,``],[1,113,`F16`,74,`F16`,127,`VK_F16`,``,``],[1,114,`F17`,75,`F17`,128,`VK_F17`,``,``],[1,115,`F18`,76,`F18`,129,`VK_F18`,``,``],[1,116,`F19`,77,`F19`,130,`VK_F19`,``,``],[1,117,`F20`,78,`F20`,131,`VK_F20`,``,``],[1,118,`F21`,79,`F21`,132,`VK_F21`,``,``],[1,119,`F22`,80,`F22`,133,`VK_F22`,``,``],[1,120,`F23`,81,`F23`,134,`VK_F23`,``,``],[1,121,`F24`,82,`F24`,135,`VK_F24`,``,``],[1,122,`Open`,0,``,0,``,``,``],[1,123,`Help`,0,``,0,``,``,``],[1,124,`Select`,0,``,0,``,``,``],[1,125,`Again`,0,``,0,``,``,``],[1,126,`Undo`,0,``,0,``,``,``],[1,127,`Cut`,0,``,0,``,``,``],[1,128,`Copy`,0,``,0,``,``,``],[1,129,`Paste`,0,``,0,``,``,``],[1,130,`Find`,0,``,0,``,``,``],[1,131,`AudioVolumeMute`,117,`AudioVolumeMute`,173,`VK_VOLUME_MUTE`,``,``],[1,132,`AudioVolumeUp`,118,`AudioVolumeUp`,175,`VK_VOLUME_UP`,``,``],[1,133,`AudioVolumeDown`,119,`AudioVolumeDown`,174,`VK_VOLUME_DOWN`,``,``],[1,134,`NumpadComma`,110,`NumPad_Separator`,108,`VK_SEPARATOR`,``,``],[0,135,`IntlRo`,115,`ABNT_C1`,193,`VK_ABNT_C1`,``,``],[1,136,`KanaMode`,0,``,0,``,``,``],[0,137,`IntlYen`,0,``,0,``,``,``],[1,138,`Convert`,0,``,0,``,``,``],[1,139,`NonConvert`,0,``,0,``,``,``],[1,140,`Lang1`,0,``,0,``,``,``],[1,141,`Lang2`,0,``,0,``,``,``],[1,142,`Lang3`,0,``,0,``,``,``],[1,143,`Lang4`,0,``,0,``,``,``],[1,144,`Lang5`,0,``,0,``,``,``],[1,145,`Abort`,0,``,0,``,``,``],[1,146,`Props`,0,``,0,``,``,``],[1,147,`NumpadParenLeft`,0,``,0,``,``,``],[1,148,`NumpadParenRight`,0,``,0,``,``,``],[1,149,`NumpadBackspace`,0,``,0,``,``,``],[1,150,`NumpadMemoryStore`,0,``,0,``,``,``],[1,151,`NumpadMemoryRecall`,0,``,0,``,``,``],[1,152,`NumpadMemoryClear`,0,``,0,``,``,``],[1,153,`NumpadMemoryAdd`,0,``,0,``,``,``],[1,154,`NumpadMemorySubtract`,0,``,0,``,``,``],[1,155,`NumpadClear`,131,`Clear`,12,`VK_CLEAR`,``,``],[1,156,`NumpadClearEntry`,0,``,0,``,``,``],[1,0,``,5,`Ctrl`,17,`VK_CONTROL`,``,``],[1,0,``,4,`Shift`,16,`VK_SHIFT`,``,``],[1,0,``,6,`Alt`,18,`VK_MENU`,``,``],[1,0,``,57,`Meta`,91,`VK_COMMAND`,``,``],[1,157,`ControlLeft`,5,``,0,`VK_LCONTROL`,``,``],[1,158,`ShiftLeft`,4,``,0,`VK_LSHIFT`,``,``],[1,159,`AltLeft`,6,``,0,`VK_LMENU`,``,``],[1,160,`MetaLeft`,57,``,0,`VK_LWIN`,``,``],[1,161,`ControlRight`,5,``,0,`VK_RCONTROL`,``,``],[1,162,`ShiftRight`,4,``,0,`VK_RSHIFT`,``,``],[1,163,`AltRight`,6,``,0,`VK_RMENU`,``,``],[1,164,`MetaRight`,57,``,0,`VK_RWIN`,``,``],[1,165,`BrightnessUp`,0,``,0,``,``,``],[1,166,`BrightnessDown`,0,``,0,``,``,``],[1,167,`MediaPlay`,0,``,0,``,``,``],[1,168,`MediaRecord`,0,``,0,``,``,``],[1,169,`MediaFastForward`,0,``,0,``,``,``],[1,170,`MediaRewind`,0,``,0,``,``,``],[1,171,`MediaTrackNext`,124,`MediaTrackNext`,176,`VK_MEDIA_NEXT_TRACK`,``,``],[1,172,`MediaTrackPrevious`,125,`MediaTrackPrevious`,177,`VK_MEDIA_PREV_TRACK`,``,``],[1,173,`MediaStop`,126,`MediaStop`,178,`VK_MEDIA_STOP`,``,``],[1,174,`Eject`,0,``,0,``,``,``],[1,175,`MediaPlayPause`,127,`MediaPlayPause`,179,`VK_MEDIA_PLAY_PAUSE`,``,``],[1,176,`MediaSelect`,128,`LaunchMediaPlayer`,181,`VK_MEDIA_LAUNCH_MEDIA_SELECT`,``,``],[1,177,`LaunchMail`,129,`LaunchMail`,180,`VK_MEDIA_LAUNCH_MAIL`,``,``],[1,178,`LaunchApp2`,130,`LaunchApp2`,183,`VK_MEDIA_LAUNCH_APP2`,``,``],[1,179,`LaunchApp1`,0,``,0,`VK_MEDIA_LAUNCH_APP1`,``,``],[1,180,`SelectTask`,0,``,0,``,``,``],[1,181,`LaunchScreenSaver`,0,``,0,``,``,``],[1,182,`BrowserSearch`,120,`BrowserSearch`,170,`VK_BROWSER_SEARCH`,``,``],[1,183,`BrowserHome`,121,`BrowserHome`,172,`VK_BROWSER_HOME`,``,``],[1,184,`BrowserBack`,122,`BrowserBack`,166,`VK_BROWSER_BACK`,``,``],[1,185,`BrowserForward`,123,`BrowserForward`,167,`VK_BROWSER_FORWARD`,``,``],[1,186,`BrowserStop`,0,``,0,`VK_BROWSER_STOP`,``,``],[1,187,`BrowserRefresh`,0,``,0,`VK_BROWSER_REFRESH`,``,``],[1,188,`BrowserFavorites`,0,``,0,`VK_BROWSER_FAVORITES`,``,``],[1,189,`ZoomToggle`,0,``,0,``,``,``],[1,190,`MailReply`,0,``,0,``,``,``],[1,191,`MailForward`,0,``,0,``,``,``],[1,192,`MailSend`,0,``,0,``,``,``],[1,0,``,114,`KeyInComposition`,229,``,``,``],[1,0,``,116,`ABNT_C2`,194,`VK_ABNT_C2`,``,``],[1,0,``,96,`OEM_8`,223,`VK_OEM_8`,``,``],[1,0,``,0,``,0,`VK_KANA`,``,``],[1,0,``,0,``,0,`VK_HANGUL`,``,``],[1,0,``,0,``,0,`VK_JUNJA`,``,``],[1,0,``,0,``,0,`VK_FINAL`,``,``],[1,0,``,0,``,0,`VK_HANJA`,``,``],[1,0,``,0,``,0,`VK_KANJI`,``,``],[1,0,``,0,``,0,`VK_CONVERT`,``,``],[1,0,``,0,``,0,`VK_NONCONVERT`,``,``],[1,0,``,0,``,0,`VK_ACCEPT`,``,``],[1,0,``,0,``,0,`VK_MODECHANGE`,``,``],[1,0,``,0,``,0,`VK_SELECT`,``,``],[1,0,``,0,``,0,`VK_PRINT`,``,``],[1,0,``,0,``,0,`VK_EXECUTE`,``,``],[1,0,``,0,``,0,`VK_SNAPSHOT`,``,``],[1,0,``,0,``,0,`VK_HELP`,``,``],[1,0,``,0,``,0,`VK_APPS`,``,``],[1,0,``,0,``,0,`VK_PROCESSKEY`,``,``],[1,0,``,0,``,0,`VK_PACKET`,``,``],[1,0,``,0,``,0,`VK_DBE_SBCSCHAR`,``,``],[1,0,``,0,``,0,`VK_DBE_DBCSCHAR`,``,``],[1,0,``,0,``,0,`VK_ATTN`,``,``],[1,0,``,0,``,0,`VK_CRSEL`,``,``],[1,0,``,0,``,0,`VK_EXSEL`,``,``],[1,0,``,0,``,0,`VK_EREOF`,``,``],[1,0,``,0,``,0,`VK_PLAY`,``,``],[1,0,``,0,``,0,`VK_ZOOM`,``,``],[1,0,``,0,``,0,`VK_NONAME`,``,``],[1,0,``,0,``,0,`VK_PA1`,``,``],[1,0,``,0,``,0,`VK_OEM_CLEAR`,``,``]],t=[],n=[];for(let r of e){let[e,i,a,o,s,c,l,u,d]=r;if(n[i]||(n[i]=!0,yn[i]=a,bn[a]=i,xn[a.toLowerCase()]=i,e&&(Sn[i]=o,o!==0&&o!==3&&o!==5&&o!==4&&o!==6&&o!==57&&(Cn[o]=i))),!t[o]){if(t[o]=!0,!s)throw Error(`String representation missing for key code ${o} around scan code ${a}`);mn.define(o,s),hn.define(o,u||s),gn.define(o,d||u||s)}c&&(_n[c]=o),l&&(vn[l]=o)}Cn[3]=46})();var wn;(function(e){function t(e){return mn.keyCodeToStr(e)}e.toString=t;function n(e){return mn.strToKeyCode(e)}e.fromString=n;function r(e){return hn.keyCodeToStr(e)}e.toUserSettingsUS=r;function i(e){return gn.keyCodeToStr(e)}e.toUserSettingsGeneral=i;function a(e){return hn.strToKeyCode(e)||gn.strToKeyCode(e)}e.fromUserSettings=a;function o(e){if(e>=98&&e<=113)return null;switch(e){case 16:return`Up`;case 18:return`Down`;case 15:return`Left`;case 17:return`Right`}return mn.keyCodeToStr(e)}e.toElectronAccelerator=o})(wn||={});function Tn(e,t){let n=(t&65535)<<16>>>0;return(e|n)>>>0}var En=class e extends N{constructor(e,t,n,r){super(e,t,n,r),this.selectionStartLineNumber=e,this.selectionStartColumn=t,this.positionLineNumber=n,this.positionColumn=r}toString(){return`[`+this.selectionStartLineNumber+`,`+this.selectionStartColumn+` -> `+this.positionLineNumber+`,`+this.positionColumn+`]`}equalsSelection(t){return e.selectionsEqual(this,t)}static selectionsEqual(e,t){return e.selectionStartLineNumber===t.selectionStartLineNumber&&e.selectionStartColumn===t.selectionStartColumn&&e.positionLineNumber===t.positionLineNumber&&e.positionColumn===t.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(t,n){return this.getDirection()===0?new e(this.startLineNumber,this.startColumn,t,n):new e(t,n,this.startLineNumber,this.startColumn)}getPosition(){return new M(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new M(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(t,n){return this.getDirection()===0?new e(t,n,this.endLineNumber,this.endColumn):new e(this.endLineNumber,this.endColumn,t,n)}static fromPositions(t,n=t){return new e(t.lineNumber,t.column,n.lineNumber,n.column)}static fromRange(t,n){return n===0?new e(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):new e(t.endLineNumber,t.endColumn,t.startLineNumber,t.startColumn)}static liftSelection(t){return new e(t.selectionStartLineNumber,t.selectionStartColumn,t.positionLineNumber,t.positionColumn)}static selectionsArrEqual(e,t){if(e&&!t||!e&&t)return!1;if(!e&&!t)return!0;if(e.length!==t.length)return!1;for(let n=0,r=e.length;n<r;n++)if(!this.selectionsEqual(e[n],t[n]))return!1;return!0}static isISelection(e){return e&&typeof e.selectionStartLineNumber==`number`&&typeof e.selectionStartColumn==`number`&&typeof e.positionLineNumber==`number`&&typeof e.positionColumn==`number`}static createWithDirection(t,n,r,i,a){return a===0?new e(t,n,r,i):new e(r,i,t,n)}};function Dn(e){return typeof e==`string`}const On=Object.create(null);function P(e,t){if(Dn(t)){let n=On[t];if(n===void 0)throw Error(`${e} references an unknown codicon: ${t}`);t=n}return On[e]=t,{id:e}}const kn={add:P(`add`,6e4),plus:P(`plus`,6e4),gistNew:P(`gist-new`,6e4),repoCreate:P(`repo-create`,6e4),lightbulb:P(`lightbulb`,60001),lightBulb:P(`light-bulb`,60001),repo:P(`repo`,60002),repoDelete:P(`repo-delete`,60002),gistFork:P(`gist-fork`,60003),repoForked:P(`repo-forked`,60003),gitPullRequest:P(`git-pull-request`,60004),gitPullRequestAbandoned:P(`git-pull-request-abandoned`,60004),recordKeys:P(`record-keys`,60005),keyboard:P(`keyboard`,60005),tag:P(`tag`,60006),gitPullRequestLabel:P(`git-pull-request-label`,60006),tagAdd:P(`tag-add`,60006),tagRemove:P(`tag-remove`,60006),person:P(`person`,60007),personFollow:P(`person-follow`,60007),personOutline:P(`person-outline`,60007),personFilled:P(`person-filled`,60007),gitBranch:P(`git-branch`,60008),gitBranchCreate:P(`git-branch-create`,60008),gitBranchDelete:P(`git-branch-delete`,60008),sourceControl:P(`source-control`,60008),mirror:P(`mirror`,60009),mirrorPublic:P(`mirror-public`,60009),star:P(`star`,60010),starAdd:P(`star-add`,60010),starDelete:P(`star-delete`,60010),starEmpty:P(`star-empty`,60010),comment:P(`comment`,60011),commentAdd:P(`comment-add`,60011),alert:P(`alert`,60012),warning:P(`warning`,60012),search:P(`search`,60013),searchSave:P(`search-save`,60013),logOut:P(`log-out`,60014),signOut:P(`sign-out`,60014),logIn:P(`log-in`,60015),signIn:P(`sign-in`,60015),eye:P(`eye`,60016),eyeUnwatch:P(`eye-unwatch`,60016),eyeWatch:P(`eye-watch`,60016),circleFilled:P(`circle-filled`,60017),primitiveDot:P(`primitive-dot`,60017),closeDirty:P(`close-dirty`,60017),debugBreakpoint:P(`debug-breakpoint`,60017),debugBreakpointDisabled:P(`debug-breakpoint-disabled`,60017),debugHint:P(`debug-hint`,60017),terminalDecorationSuccess:P(`terminal-decoration-success`,60017),primitiveSquare:P(`primitive-square`,60018),edit:P(`edit`,60019),pencil:P(`pencil`,60019),info:P(`info`,60020),issueOpened:P(`issue-opened`,60020),gistPrivate:P(`gist-private`,60021),gitForkPrivate:P(`git-fork-private`,60021),lock:P(`lock`,60021),mirrorPrivate:P(`mirror-private`,60021),close:P(`close`,60022),removeClose:P(`remove-close`,60022),x:P(`x`,60022),repoSync:P(`repo-sync`,60023),sync:P(`sync`,60023),clone:P(`clone`,60024),desktopDownload:P(`desktop-download`,60024),beaker:P(`beaker`,60025),microscope:P(`microscope`,60025),vm:P(`vm`,60026),deviceDesktop:P(`device-desktop`,60026),file:P(`file`,60027),fileText:P(`file-text`,60027),more:P(`more`,60028),ellipsis:P(`ellipsis`,60028),kebabHorizontal:P(`kebab-horizontal`,60028),mailReply:P(`mail-reply`,60029),reply:P(`reply`,60029),organization:P(`organization`,60030),organizationFilled:P(`organization-filled`,60030),organizationOutline:P(`organization-outline`,60030),newFile:P(`new-file`,60031),fileAdd:P(`file-add`,60031),newFolder:P(`new-folder`,60032),fileDirectoryCreate:P(`file-directory-create`,60032),trash:P(`trash`,60033),trashcan:P(`trashcan`,60033),history:P(`history`,60034),clock:P(`clock`,60034),folder:P(`folder`,60035),fileDirectory:P(`file-directory`,60035),symbolFolder:P(`symbol-folder`,60035),logoGithub:P(`logo-github`,60036),markGithub:P(`mark-github`,60036),github:P(`github`,60036),terminal:P(`terminal`,60037),console:P(`console`,60037),repl:P(`repl`,60037),zap:P(`zap`,60038),symbolEvent:P(`symbol-event`,60038),error:P(`error`,60039),stop:P(`stop`,60039),variable:P(`variable`,60040),symbolVariable:P(`symbol-variable`,60040),array:P(`array`,60042),symbolArray:P(`symbol-array`,60042),symbolModule:P(`symbol-module`,60043),symbolPackage:P(`symbol-package`,60043),symbolNamespace:P(`symbol-namespace`,60043),symbolObject:P(`symbol-object`,60043),symbolMethod:P(`symbol-method`,60044),symbolFunction:P(`symbol-function`,60044),symbolConstructor:P(`symbol-constructor`,60044),symbolBoolean:P(`symbol-boolean`,60047),symbolNull:P(`symbol-null`,60047),symbolNumeric:P(`symbol-numeric`,60048),symbolNumber:P(`symbol-number`,60048),symbolStructure:P(`symbol-structure`,60049),symbolStruct:P(`symbol-struct`,60049),symbolParameter:P(`symbol-parameter`,60050),symbolTypeParameter:P(`symbol-type-parameter`,60050),symbolKey:P(`symbol-key`,60051),symbolText:P(`symbol-text`,60051),symbolReference:P(`symbol-reference`,60052),goToFile:P(`go-to-file`,60052),symbolEnum:P(`symbol-enum`,60053),symbolValue:P(`symbol-value`,60053),symbolRuler:P(`symbol-ruler`,60054),symbolUnit:P(`symbol-unit`,60054),activateBreakpoints:P(`activate-breakpoints`,60055),archive:P(`archive`,60056),arrowBoth:P(`arrow-both`,60057),arrowDown:P(`arrow-down`,60058),arrowLeft:P(`arrow-left`,60059),arrowRight:P(`arrow-right`,60060),arrowSmallDown:P(`arrow-small-down`,60061),arrowSmallLeft:P(`arrow-small-left`,60062),arrowSmallRight:P(`arrow-small-right`,60063),arrowSmallUp:P(`arrow-small-up`,60064),arrowUp:P(`arrow-up`,60065),bell:P(`bell`,60066),bold:P(`bold`,60067),book:P(`book`,60068),bookmark:P(`bookmark`,60069),debugBreakpointConditionalUnverified:P(`debug-breakpoint-conditional-unverified`,60070),debugBreakpointConditional:P(`debug-breakpoint-conditional`,60071),debugBreakpointConditionalDisabled:P(`debug-breakpoint-conditional-disabled`,60071),debugBreakpointDataUnverified:P(`debug-breakpoint-data-unverified`,60072),debugBreakpointData:P(`debug-breakpoint-data`,60073),debugBreakpointDataDisabled:P(`debug-breakpoint-data-disabled`,60073),debugBreakpointLogUnverified:P(`debug-breakpoint-log-unverified`,60074),debugBreakpointLog:P(`debug-breakpoint-log`,60075),debugBreakpointLogDisabled:P(`debug-breakpoint-log-disabled`,60075),briefcase:P(`briefcase`,60076),broadcast:P(`broadcast`,60077),browser:P(`browser`,60078),bug:P(`bug`,60079),calendar:P(`calendar`,60080),caseSensitive:P(`case-sensitive`,60081),check:P(`check`,60082),checklist:P(`checklist`,60083),chevronDown:P(`chevron-down`,60084),chevronLeft:P(`chevron-left`,60085),chevronRight:P(`chevron-right`,60086),chevronUp:P(`chevron-up`,60087),chromeClose:P(`chrome-close`,60088),chromeMaximize:P(`chrome-maximize`,60089),chromeMinimize:P(`chrome-minimize`,60090),chromeRestore:P(`chrome-restore`,60091),circleOutline:P(`circle-outline`,60092),circle:P(`circle`,60092),debugBreakpointUnverified:P(`debug-breakpoint-unverified`,60092),terminalDecorationIncomplete:P(`terminal-decoration-incomplete`,60092),circleSlash:P(`circle-slash`,60093),circuitBoard:P(`circuit-board`,60094),clearAll:P(`clear-all`,60095),clippy:P(`clippy`,60096),closeAll:P(`close-all`,60097),cloudDownload:P(`cloud-download`,60098),cloudUpload:P(`cloud-upload`,60099),code:P(`code`,60100),collapseAll:P(`collapse-all`,60101),colorMode:P(`color-mode`,60102),commentDiscussion:P(`comment-discussion`,60103),creditCard:P(`credit-card`,60105),dash:P(`dash`,60108),dashboard:P(`dashboard`,60109),database:P(`database`,60110),debugContinue:P(`debug-continue`,60111),debugDisconnect:P(`debug-disconnect`,60112),debugPause:P(`debug-pause`,60113),debugRestart:P(`debug-restart`,60114),debugStart:P(`debug-start`,60115),debugStepInto:P(`debug-step-into`,60116),debugStepOut:P(`debug-step-out`,60117),debugStepOver:P(`debug-step-over`,60118),debugStop:P(`debug-stop`,60119),debug:P(`debug`,60120),deviceCameraVideo:P(`device-camera-video`,60121),deviceCamera:P(`device-camera`,60122),deviceMobile:P(`device-mobile`,60123),diffAdded:P(`diff-added`,60124),diffIgnored:P(`diff-ignored`,60125),diffModified:P(`diff-modified`,60126),diffRemoved:P(`diff-removed`,60127),diffRenamed:P(`diff-renamed`,60128),diff:P(`diff`,60129),diffSidebyside:P(`diff-sidebyside`,60129),discard:P(`discard`,60130),editorLayout:P(`editor-layout`,60131),emptyWindow:P(`empty-window`,60132),exclude:P(`exclude`,60133),extensions:P(`extensions`,60134),eyeClosed:P(`eye-closed`,60135),fileBinary:P(`file-binary`,60136),fileCode:P(`file-code`,60137),fileMedia:P(`file-media`,60138),filePdf:P(`file-pdf`,60139),fileSubmodule:P(`file-submodule`,60140),fileSymlinkDirectory:P(`file-symlink-directory`,60141),fileSymlinkFile:P(`file-symlink-file`,60142),fileZip:P(`file-zip`,60143),files:P(`files`,60144),filter:P(`filter`,60145),flame:P(`flame`,60146),foldDown:P(`fold-down`,60147),foldUp:P(`fold-up`,60148),fold:P(`fold`,60149),folderActive:P(`folder-active`,60150),folderOpened:P(`folder-opened`,60151),gear:P(`gear`,60152),gift:P(`gift`,60153),gistSecret:P(`gist-secret`,60154),gist:P(`gist`,60155),gitCommit:P(`git-commit`,60156),gitCompare:P(`git-compare`,60157),compareChanges:P(`compare-changes`,60157),gitMerge:P(`git-merge`,60158),githubAction:P(`github-action`,60159),githubAlt:P(`github-alt`,60160),globe:P(`globe`,60161),grabber:P(`grabber`,60162),graph:P(`graph`,60163),gripper:P(`gripper`,60164),heart:P(`heart`,60165),home:P(`home`,60166),horizontalRule:P(`horizontal-rule`,60167),hubot:P(`hubot`,60168),inbox:P(`inbox`,60169),issueReopened:P(`issue-reopened`,60171),issues:P(`issues`,60172),italic:P(`italic`,60173),jersey:P(`jersey`,60174),json:P(`json`,60175),kebabVertical:P(`kebab-vertical`,60176),key:P(`key`,60177),law:P(`law`,60178),lightbulbAutofix:P(`lightbulb-autofix`,60179),linkExternal:P(`link-external`,60180),link:P(`link`,60181),listOrdered:P(`list-ordered`,60182),listUnordered:P(`list-unordered`,60183),liveShare:P(`live-share`,60184),loading:P(`loading`,60185),location:P(`location`,60186),mailRead:P(`mail-read`,60187),mail:P(`mail`,60188),markdown:P(`markdown`,60189),megaphone:P(`megaphone`,60190),mention:P(`mention`,60191),milestone:P(`milestone`,60192),gitPullRequestMilestone:P(`git-pull-request-milestone`,60192),mortarBoard:P(`mortar-board`,60193),move:P(`move`,60194),multipleWindows:P(`multiple-windows`,60195),mute:P(`mute`,60196),noNewline:P(`no-newline`,60197),note:P(`note`,60198),octoface:P(`octoface`,60199),openPreview:P(`open-preview`,60200),package:P(`package`,60201),paintcan:P(`paintcan`,60202),pin:P(`pin`,60203),play:P(`play`,60204),run:P(`run`,60204),plug:P(`plug`,60205),preserveCase:P(`preserve-case`,60206),preview:P(`preview`,60207),project:P(`project`,60208),pulse:P(`pulse`,60209),question:P(`question`,60210),quote:P(`quote`,60211),radioTower:P(`radio-tower`,60212),reactions:P(`reactions`,60213),references:P(`references`,60214),refresh:P(`refresh`,60215),regex:P(`regex`,60216),remoteExplorer:P(`remote-explorer`,60217),remote:P(`remote`,60218),remove:P(`remove`,60219),replaceAll:P(`replace-all`,60220),replace:P(`replace`,60221),repoClone:P(`repo-clone`,60222),repoForcePush:P(`repo-force-push`,60223),repoPull:P(`repo-pull`,60224),repoPush:P(`repo-push`,60225),report:P(`report`,60226),requestChanges:P(`request-changes`,60227),rocket:P(`rocket`,60228),rootFolderOpened:P(`root-folder-opened`,60229),rootFolder:P(`root-folder`,60230),rss:P(`rss`,60231),ruby:P(`ruby`,60232),saveAll:P(`save-all`,60233),saveAs:P(`save-as`,60234),save:P(`save`,60235),screenFull:P(`screen-full`,60236),screenNormal:P(`screen-normal`,60237),searchStop:P(`search-stop`,60238),server:P(`server`,60240),settingsGear:P(`settings-gear`,60241),settings:P(`settings`,60242),shield:P(`shield`,60243),smiley:P(`smiley`,60244),sortPrecedence:P(`sort-precedence`,60245),splitHorizontal:P(`split-horizontal`,60246),splitVertical:P(`split-vertical`,60247),squirrel:P(`squirrel`,60248),starFull:P(`star-full`,60249),starHalf:P(`star-half`,60250),symbolClass:P(`symbol-class`,60251),symbolColor:P(`symbol-color`,60252),symbolConstant:P(`symbol-constant`,60253),symbolEnumMember:P(`symbol-enum-member`,60254),symbolField:P(`symbol-field`,60255),symbolFile:P(`symbol-file`,60256),symbolInterface:P(`symbol-interface`,60257),symbolKeyword:P(`symbol-keyword`,60258),symbolMisc:P(`symbol-misc`,60259),symbolOperator:P(`symbol-operator`,60260),symbolProperty:P(`symbol-property`,60261),wrench:P(`wrench`,60261),wrenchSubaction:P(`wrench-subaction`,60261),symbolSnippet:P(`symbol-snippet`,60262),tasklist:P(`tasklist`,60263),telescope:P(`telescope`,60264),textSize:P(`text-size`,60265),threeBars:P(`three-bars`,60266),thumbsdown:P(`thumbsdown`,60267),thumbsup:P(`thumbsup`,60268),tools:P(`tools`,60269),triangleDown:P(`triangle-down`,60270),triangleLeft:P(`triangle-left`,60271),triangleRight:P(`triangle-right`,60272),triangleUp:P(`triangle-up`,60273),twitter:P(`twitter`,60274),unfold:P(`unfold`,60275),unlock:P(`unlock`,60276),unmute:P(`unmute`,60277),unverified:P(`unverified`,60278),verified:P(`verified`,60279),versions:P(`versions`,60280),vmActive:P(`vm-active`,60281),vmOutline:P(`vm-outline`,60282),vmRunning:P(`vm-running`,60283),watch:P(`watch`,60284),whitespace:P(`whitespace`,60285),wholeWord:P(`whole-word`,60286),window:P(`window`,60287),wordWrap:P(`word-wrap`,60288),zoomIn:P(`zoom-in`,60289),zoomOut:P(`zoom-out`,60290),listFilter:P(`list-filter`,60291),listFlat:P(`list-flat`,60292),listSelection:P(`list-selection`,60293),selection:P(`selection`,60293),listTree:P(`list-tree`,60294),debugBreakpointFunctionUnverified:P(`debug-breakpoint-function-unverified`,60295),debugBreakpointFunction:P(`debug-breakpoint-function`,60296),debugBreakpointFunctionDisabled:P(`debug-breakpoint-function-disabled`,60296),debugStackframeActive:P(`debug-stackframe-active`,60297),circleSmallFilled:P(`circle-small-filled`,60298),debugStackframeDot:P(`debug-stackframe-dot`,60298),terminalDecorationMark:P(`terminal-decoration-mark`,60298),debugStackframe:P(`debug-stackframe`,60299),debugStackframeFocused:P(`debug-stackframe-focused`,60299),debugBreakpointUnsupported:P(`debug-breakpoint-unsupported`,60300),symbolString:P(`symbol-string`,60301),debugReverseContinue:P(`debug-reverse-continue`,60302),debugStepBack:P(`debug-step-back`,60303),debugRestartFrame:P(`debug-restart-frame`,60304),debugAlt:P(`debug-alt`,60305),callIncoming:P(`call-incoming`,60306),callOutgoing:P(`call-outgoing`,60307),menu:P(`menu`,60308),expandAll:P(`expand-all`,60309),feedback:P(`feedback`,60310),gitPullRequestReviewer:P(`git-pull-request-reviewer`,60310),groupByRefType:P(`group-by-ref-type`,60311),ungroupByRefType:P(`ungroup-by-ref-type`,60312),account:P(`account`,60313),gitPullRequestAssignee:P(`git-pull-request-assignee`,60313),bellDot:P(`bell-dot`,60314),debugConsole:P(`debug-console`,60315),library:P(`library`,60316),output:P(`output`,60317),runAll:P(`run-all`,60318),syncIgnored:P(`sync-ignored`,60319),pinned:P(`pinned`,60320),githubInverted:P(`github-inverted`,60321),serverProcess:P(`server-process`,60322),serverEnvironment:P(`server-environment`,60323),pass:P(`pass`,60324),issueClosed:P(`issue-closed`,60324),stopCircle:P(`stop-circle`,60325),playCircle:P(`play-circle`,60326),record:P(`record`,60327),debugAltSmall:P(`debug-alt-small`,60328),vmConnect:P(`vm-connect`,60329),cloud:P(`cloud`,60330),merge:P(`merge`,60331),export:P(`export`,60332),graphLeft:P(`graph-left`,60333),magnet:P(`magnet`,60334),notebook:P(`notebook`,60335),redo:P(`redo`,60336),checkAll:P(`check-all`,60337),pinnedDirty:P(`pinned-dirty`,60338),passFilled:P(`pass-filled`,60339),circleLargeFilled:P(`circle-large-filled`,60340),circleLarge:P(`circle-large`,60341),circleLargeOutline:P(`circle-large-outline`,60341),combine:P(`combine`,60342),gather:P(`gather`,60342),table:P(`table`,60343),variableGroup:P(`variable-group`,60344),typeHierarchy:P(`type-hierarchy`,60345),typeHierarchySub:P(`type-hierarchy-sub`,60346),typeHierarchySuper:P(`type-hierarchy-super`,60347),gitPullRequestCreate:P(`git-pull-request-create`,60348),runAbove:P(`run-above`,60349),runBelow:P(`run-below`,60350),notebookTemplate:P(`notebook-template`,60351),debugRerun:P(`debug-rerun`,60352),workspaceTrusted:P(`workspace-trusted`,60353),workspaceUntrusted:P(`workspace-untrusted`,60354),workspaceUnknown:P(`workspace-unknown`,60355),terminalCmd:P(`terminal-cmd`,60356),terminalDebian:P(`terminal-debian`,60357),terminalLinux:P(`terminal-linux`,60358),terminalPowershell:P(`terminal-powershell`,60359),terminalTmux:P(`terminal-tmux`,60360),terminalUbuntu:P(`terminal-ubuntu`,60361),terminalBash:P(`terminal-bash`,60362),arrowSwap:P(`arrow-swap`,60363),copy:P(`copy`,60364),personAdd:P(`person-add`,60365),filterFilled:P(`filter-filled`,60366),wand:P(`wand`,60367),debugLineByLine:P(`debug-line-by-line`,60368),inspect:P(`inspect`,60369),layers:P(`layers`,60370),layersDot:P(`layers-dot`,60371),layersActive:P(`layers-active`,60372),compass:P(`compass`,60373),compassDot:P(`compass-dot`,60374),compassActive:P(`compass-active`,60375),azure:P(`azure`,60376),issueDraft:P(`issue-draft`,60377),gitPullRequestClosed:P(`git-pull-request-closed`,60378),gitPullRequestDraft:P(`git-pull-request-draft`,60379),debugAll:P(`debug-all`,60380),debugCoverage:P(`debug-coverage`,60381),runErrors:P(`run-errors`,60382),folderLibrary:P(`folder-library`,60383),debugContinueSmall:P(`debug-continue-small`,60384),beakerStop:P(`beaker-stop`,60385),graphLine:P(`graph-line`,60386),graphScatter:P(`graph-scatter`,60387),pieChart:P(`pie-chart`,60388),bracket:P(`bracket`,60175),bracketDot:P(`bracket-dot`,60389),bracketError:P(`bracket-error`,60390),lockSmall:P(`lock-small`,60391),azureDevops:P(`azure-devops`,60392),verifiedFilled:P(`verified-filled`,60393),newline:P(`newline`,60394),layout:P(`layout`,60395),layoutActivitybarLeft:P(`layout-activitybar-left`,60396),layoutActivitybarRight:P(`layout-activitybar-right`,60397),layoutPanelLeft:P(`layout-panel-left`,60398),layoutPanelCenter:P(`layout-panel-center`,60399),layoutPanelJustify:P(`layout-panel-justify`,60400),layoutPanelRight:P(`layout-panel-right`,60401),layoutPanel:P(`layout-panel`,60402),layoutSidebarLeft:P(`layout-sidebar-left`,60403),layoutSidebarRight:P(`layout-sidebar-right`,60404),layoutStatusbar:P(`layout-statusbar`,60405),layoutMenubar:P(`layout-menubar`,60406),layoutCentered:P(`layout-centered`,60407),target:P(`target`,60408),indent:P(`indent`,60409),recordSmall:P(`record-small`,60410),errorSmall:P(`error-small`,60411),terminalDecorationError:P(`terminal-decoration-error`,60411),arrowCircleDown:P(`arrow-circle-down`,60412),arrowCircleLeft:P(`arrow-circle-left`,60413),arrowCircleRight:P(`arrow-circle-right`,60414),arrowCircleUp:P(`arrow-circle-up`,60415),layoutSidebarRightOff:P(`layout-sidebar-right-off`,60416),layoutPanelOff:P(`layout-panel-off`,60417),layoutSidebarLeftOff:P(`layout-sidebar-left-off`,60418),blank:P(`blank`,60419),heartFilled:P(`heart-filled`,60420),map:P(`map`,60421),mapHorizontal:P(`map-horizontal`,60421),foldHorizontal:P(`fold-horizontal`,60421),mapFilled:P(`map-filled`,60422),mapHorizontalFilled:P(`map-horizontal-filled`,60422),foldHorizontalFilled:P(`fold-horizontal-filled`,60422),circleSmall:P(`circle-small`,60423),bellSlash:P(`bell-slash`,60424),bellSlashDot:P(`bell-slash-dot`,60425),commentUnresolved:P(`comment-unresolved`,60426),gitPullRequestGoToChanges:P(`git-pull-request-go-to-changes`,60427),gitPullRequestNewChanges:P(`git-pull-request-new-changes`,60428),searchFuzzy:P(`search-fuzzy`,60429),commentDraft:P(`comment-draft`,60430),send:P(`send`,60431),sparkle:P(`sparkle`,60432),insert:P(`insert`,60433),mic:P(`mic`,60434),thumbsdownFilled:P(`thumbsdown-filled`,60435),thumbsupFilled:P(`thumbsup-filled`,60436),coffee:P(`coffee`,60437),snake:P(`snake`,60438),game:P(`game`,60439),vr:P(`vr`,60440),chip:P(`chip`,60441),piano:P(`piano`,60442),music:P(`music`,60443),micFilled:P(`mic-filled`,60444),repoFetch:P(`repo-fetch`,60445),copilot:P(`copilot`,60446),lightbulbSparkle:P(`lightbulb-sparkle`,60447),robot:P(`robot`,60448),sparkleFilled:P(`sparkle-filled`,60449),diffSingle:P(`diff-single`,60450),diffMultiple:P(`diff-multiple`,60451),surroundWith:P(`surround-with`,60452),share:P(`share`,60453),gitStash:P(`git-stash`,60454),gitStashApply:P(`git-stash-apply`,60455),gitStashPop:P(`git-stash-pop`,60456),vscode:P(`vscode`,60457),vscodeInsiders:P(`vscode-insiders`,60458),codeOss:P(`code-oss`,60459),runCoverage:P(`run-coverage`,60460),runAllCoverage:P(`run-all-coverage`,60461),coverage:P(`coverage`,60462),githubProject:P(`github-project`,60463),mapVertical:P(`map-vertical`,60464),foldVertical:P(`fold-vertical`,60464),mapVerticalFilled:P(`map-vertical-filled`,60465),foldVerticalFilled:P(`fold-vertical-filled`,60465),goToSearch:P(`go-to-search`,60466),percentage:P(`percentage`,60467),sortPercentage:P(`sort-percentage`,60467),attach:P(`attach`,60468)},An={dialogError:P(`dialog-error`,`error`),dialogWarning:P(`dialog-warning`,`warning`),dialogInfo:P(`dialog-info`,`info`),dialogClose:P(`dialog-close`,`close`),treeItemExpanded:P(`tree-item-expanded`,`chevron-down`),treeFilterOnTypeOn:P(`tree-filter-on-type-on`,`list-filter`),treeFilterOnTypeOff:P(`tree-filter-on-type-off`,`list-selection`),treeFilterClear:P(`tree-filter-clear`,`close`),treeItemLoading:P(`tree-item-loading`,`loading`),menuSelection:P(`menu-selection`,`check`),menuSubmenu:P(`menu-submenu`,`chevron-right`),menuBarMore:P(`menubar-more`,`more`),scrollbarButtonLeft:P(`scrollbar-button-left`,`triangle-left`),scrollbarButtonRight:P(`scrollbar-button-right`,`triangle-right`),scrollbarButtonUp:P(`scrollbar-button-up`,`triangle-up`),scrollbarButtonDown:P(`scrollbar-button-down`,`triangle-down`),toolBarMore:P(`toolbar-more`,`more`),quickInputBack:P(`quick-input-back`,`arrow-left`),dropDownButton:P(`drop-down-button`,60084),symbolCustomColor:P(`symbol-customcolor`,60252),exportIcon:P(`export`,60332),workspaceUnspecified:P(`workspace-unspecified`,60355),newLine:P(`newline`,60394),thumbsDownFilled:P(`thumbsdown-filled`,60435),thumbsUpFilled:P(`thumbsup-filled`,60436),gitFetch:P(`git-fetch`,60445),lightbulbSparkleAutofix:P(`lightbulb-sparkle-autofix`,60447),debugBreakpointPending:P(`debug-breakpoint-pending`,60377)},F={...kn,...An};var jn=class{constructor(){this._tokenizationSupports=new Map,this._factories=new Map,this._onDidChange=new T,this.onDidChange=this._onDidChange.event,this._colorMap=null}handleChange(e){this._onDidChange.fire({changedLanguages:e,changedColorMap:!1})}register(e,t){return this._tokenizationSupports.set(e,t),this.handleChange([e]),h(()=>{this._tokenizationSupports.get(e)===t&&(this._tokenizationSupports.delete(e),this.handleChange([e]))})}get(e){return this._tokenizationSupports.get(e)||null}registerFactory(e,t){this._factories.get(e)?.dispose();let n=new Mn(this,e,t);return this._factories.set(e,n),h(()=>{let t=this._factories.get(e);!t||t!==n||(this._factories.delete(e),t.dispose())})}async getOrCreate(e){let t=this.get(e);if(t)return t;let n=this._factories.get(e);return!n||n.isResolved?null:(await n.resolve(),this.get(e))}isResolved(e){if(this.get(e))return!0;let t=this._factories.get(e);return!!(!t||t.isResolved)}setColorMap(e){this._colorMap=e,this._onDidChange.fire({changedLanguages:Array.from(this._tokenizationSupports.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}},Mn=class extends _{get isResolved(){return this._isResolved}constructor(e,t,n){super(),this._registry=e,this._languageId=t,this._factory=n,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}dispose(){this._isDisposed=!0,super.dispose()}async resolve(){return this._resolvePromise||=this._create(),this._resolvePromise}async _create(){let e=await this._factory.tokenizationSupport;this._isResolved=!0,e&&!this._isDisposed&&this._register(this._registry.register(this._languageId,e))}},Nn=class{constructor(e,t,n){this.offset=e,this.type=t,this.language=n,this._tokenBrand=void 0}toString(){return`(`+this.offset+`, `+this.type+`)`}},Pn;(function(e){e[e.Increase=0]=`Increase`,e[e.Decrease=1]=`Decrease`})(Pn||={});var Fn;(function(e){let t=new Map;t.set(0,F.symbolMethod),t.set(1,F.symbolFunction),t.set(2,F.symbolConstructor),t.set(3,F.symbolField),t.set(4,F.symbolVariable),t.set(5,F.symbolClass),t.set(6,F.symbolStruct),t.set(7,F.symbolInterface),t.set(8,F.symbolModule),t.set(9,F.symbolProperty),t.set(10,F.symbolEvent),t.set(11,F.symbolOperator),t.set(12,F.symbolUnit),t.set(13,F.symbolValue),t.set(15,F.symbolEnum),t.set(14,F.symbolConstant),t.set(15,F.symbolEnum),t.set(16,F.symbolEnumMember),t.set(17,F.symbolKeyword),t.set(27,F.symbolSnippet),t.set(18,F.symbolText),t.set(19,F.symbolColor),t.set(20,F.symbolFile),t.set(21,F.symbolReference),t.set(22,F.symbolCustomColor),t.set(23,F.symbolFolder),t.set(24,F.symbolTypeParameter),t.set(25,F.account),t.set(26,F.issues);function n(e){let n=t.get(e);return n||=(console.info(`No codicon found for CompletionItemKind `+e),F.symbolProperty),n}e.toIcon=n;let r=new Map;r.set(`method`,0),r.set(`function`,1),r.set(`constructor`,2),r.set(`field`,3),r.set(`variable`,4),r.set(`class`,5),r.set(`struct`,6),r.set(`interface`,7),r.set(`module`,8),r.set(`property`,9),r.set(`event`,10),r.set(`operator`,11),r.set(`unit`,12),r.set(`value`,13),r.set(`constant`,14),r.set(`enum`,15),r.set(`enum-member`,16),r.set(`enumMember`,16),r.set(`keyword`,17),r.set(`snippet`,27),r.set(`text`,18),r.set(`color`,19),r.set(`file`,20),r.set(`reference`,21),r.set(`customcolor`,22),r.set(`folder`,23),r.set(`type-parameter`,24),r.set(`typeParameter`,24),r.set(`account`,25),r.set(`issue`,26);function i(e,t){let n=r.get(e);return n===void 0&&!t&&(n=9),n}e.fromString=i})(Fn||={});var In;(function(e){e[e.Automatic=0]=`Automatic`,e[e.Explicit=1]=`Explicit`})(In||={});var Ln;(function(e){e[e.Automatic=0]=`Automatic`,e[e.PasteAs=1]=`PasteAs`})(Ln||={});var Rn;(function(e){e[e.Invoke=1]=`Invoke`,e[e.TriggerCharacter=2]=`TriggerCharacter`,e[e.ContentChange=3]=`ContentChange`})(Rn||={});var zn;(function(e){e[e.Text=0]=`Text`,e[e.Read=1]=`Read`,e[e.Write=2]=`Write`})(zn||={}),E(`Array`,`array`),E(`Boolean`,`boolean`),E(`Class`,`class`),E(`Constant`,`constant`),E(`Constructor`,`constructor`),E(`Enum`,`enumeration`),E(`EnumMember`,`enumeration member`),E(`Event`,`event`),E(`Field`,`field`),E(`File`,`file`),E(`Function`,`function`),E(`Interface`,`interface`),E(`Key`,`key`),E(`Method`,`method`),E(`Module`,`module`),E(`Namespace`,`namespace`),E(`Null`,`null`),E(`Number`,`number`),E(`Object`,`object`),E(`Operator`,`operator`),E(`Package`,`package`),E(`Property`,`property`),E(`String`,`string`),E(`Struct`,`struct`),E(`TypeParameter`,`type parameter`),E(`Variable`,`variable`);var Bn;(function(e){let t=new Map;t.set(0,F.symbolFile),t.set(1,F.symbolModule),t.set(2,F.symbolNamespace),t.set(3,F.symbolPackage),t.set(4,F.symbolClass),t.set(5,F.symbolMethod),t.set(6,F.symbolProperty),t.set(7,F.symbolField),t.set(8,F.symbolConstructor),t.set(9,F.symbolEnum),t.set(10,F.symbolInterface),t.set(11,F.symbolFunction),t.set(12,F.symbolVariable),t.set(13,F.symbolConstant),t.set(14,F.symbolString),t.set(15,F.symbolNumber),t.set(16,F.symbolBoolean),t.set(17,F.symbolArray),t.set(18,F.symbolObject),t.set(19,F.symbolKey),t.set(20,F.symbolNull),t.set(21,F.symbolEnumMember),t.set(22,F.symbolStruct),t.set(23,F.symbolEvent),t.set(24,F.symbolOperator),t.set(25,F.symbolTypeParameter);function n(e){let n=t.get(e);return n||=(console.info(`No codicon found for SymbolKind `+e),F.symbolProperty),n}e.toIcon=n})(Bn||={}),class e{static{this.Comment=new e(`comment`)}static{this.Imports=new e(`imports`)}static{this.Region=new e(`region`)}static fromValue(t){switch(t){case`comment`:return e.Comment;case`imports`:return e.Imports;case`region`:return e.Region}return new e(t)}constructor(e){this.value=e}};var Vn;(function(e){e[e.AIGenerated=1]=`AIGenerated`})(Vn||={});var Hn;(function(e){e[e.Invoke=0]=`Invoke`,e[e.Automatic=1]=`Automatic`})(Hn||={});var Un;(function(e){function t(e){return!e||typeof e!=`object`?!1:typeof e.id==`string`&&typeof e.title==`string`}e.is=t})(Un||={});var Wn;(function(e){e[e.Type=1]=`Type`,e[e.Parameter=2]=`Parameter`})(Wn||={}),new jn,new jn;var Gn;(function(e){e[e.Invoke=0]=`Invoke`,e[e.Automatic=1]=`Automatic`})(Gn||={});var Kn;(function(e){e[e.Unknown=0]=`Unknown`,e[e.Disabled=1]=`Disabled`,e[e.Enabled=2]=`Enabled`})(Kn||={});var qn;(function(e){e[e.Invoke=1]=`Invoke`,e[e.Auto=2]=`Auto`})(qn||={});var Jn;(function(e){e[e.None=0]=`None`,e[e.KeepWhitespace=1]=`KeepWhitespace`,e[e.InsertAsSnippet=4]=`InsertAsSnippet`})(Jn||={});var Yn;(function(e){e[e.Method=0]=`Method`,e[e.Function=1]=`Function`,e[e.Constructor=2]=`Constructor`,e[e.Field=3]=`Field`,e[e.Variable=4]=`Variable`,e[e.Class=5]=`Class`,e[e.Struct=6]=`Struct`,e[e.Interface=7]=`Interface`,e[e.Module=8]=`Module`,e[e.Property=9]=`Property`,e[e.Event=10]=`Event`,e[e.Operator=11]=`Operator`,e[e.Unit=12]=`Unit`,e[e.Value=13]=`Value`,e[e.Constant=14]=`Constant`,e[e.Enum=15]=`Enum`,e[e.EnumMember=16]=`EnumMember`,e[e.Keyword=17]=`Keyword`,e[e.Text=18]=`Text`,e[e.Color=19]=`Color`,e[e.File=20]=`File`,e[e.Reference=21]=`Reference`,e[e.Customcolor=22]=`Customcolor`,e[e.Folder=23]=`Folder`,e[e.TypeParameter=24]=`TypeParameter`,e[e.User=25]=`User`,e[e.Issue=26]=`Issue`,e[e.Snippet=27]=`Snippet`})(Yn||={});var Xn;(function(e){e[e.Deprecated=1]=`Deprecated`})(Xn||={});var Zn;(function(e){e[e.Invoke=0]=`Invoke`,e[e.TriggerCharacter=1]=`TriggerCharacter`,e[e.TriggerForIncompleteCompletions=2]=`TriggerForIncompleteCompletions`})(Zn||={});var Qn;(function(e){e[e.EXACT=0]=`EXACT`,e[e.ABOVE=1]=`ABOVE`,e[e.BELOW=2]=`BELOW`})(Qn||={});var $n;(function(e){e[e.NotSet=0]=`NotSet`,e[e.ContentFlush=1]=`ContentFlush`,e[e.RecoverFromMarkers=2]=`RecoverFromMarkers`,e[e.Explicit=3]=`Explicit`,e[e.Paste=4]=`Paste`,e[e.Undo=5]=`Undo`,e[e.Redo=6]=`Redo`})($n||={});var er;(function(e){e[e.LF=1]=`LF`,e[e.CRLF=2]=`CRLF`})(er||={});var tr;(function(e){e[e.Text=0]=`Text`,e[e.Read=1]=`Read`,e[e.Write=2]=`Write`})(tr||={});var nr;(function(e){e[e.None=0]=`None`,e[e.Keep=1]=`Keep`,e[e.Brackets=2]=`Brackets`,e[e.Advanced=3]=`Advanced`,e[e.Full=4]=`Full`})(nr||={});var rr;(function(e){e[e.acceptSuggestionOnCommitCharacter=0]=`acceptSuggestionOnCommitCharacter`,e[e.acceptSuggestionOnEnter=1]=`acceptSuggestionOnEnter`,e[e.accessibilitySupport=2]=`accessibilitySupport`,e[e.accessibilityPageSize=3]=`accessibilityPageSize`,e[e.ariaLabel=4]=`ariaLabel`,e[e.ariaRequired=5]=`ariaRequired`,e[e.autoClosingBrackets=6]=`autoClosingBrackets`,e[e.autoClosingComments=7]=`autoClosingComments`,e[e.screenReaderAnnounceInlineSuggestion=8]=`screenReaderAnnounceInlineSuggestion`,e[e.autoClosingDelete=9]=`autoClosingDelete`,e[e.autoClosingOvertype=10]=`autoClosingOvertype`,e[e.autoClosingQuotes=11]=`autoClosingQuotes`,e[e.autoIndent=12]=`autoIndent`,e[e.automaticLayout=13]=`automaticLayout`,e[e.autoSurround=14]=`autoSurround`,e[e.bracketPairColorization=15]=`bracketPairColorization`,e[e.guides=16]=`guides`,e[e.codeLens=17]=`codeLens`,e[e.codeLensFontFamily=18]=`codeLensFontFamily`,e[e.codeLensFontSize=19]=`codeLensFontSize`,e[e.colorDecorators=20]=`colorDecorators`,e[e.colorDecoratorsLimit=21]=`colorDecoratorsLimit`,e[e.columnSelection=22]=`columnSelection`,e[e.comments=23]=`comments`,e[e.contextmenu=24]=`contextmenu`,e[e.copyWithSyntaxHighlighting=25]=`copyWithSyntaxHighlighting`,e[e.cursorBlinking=26]=`cursorBlinking`,e[e.cursorSmoothCaretAnimation=27]=`cursorSmoothCaretAnimation`,e[e.cursorStyle=28]=`cursorStyle`,e[e.cursorSurroundingLines=29]=`cursorSurroundingLines`,e[e.cursorSurroundingLinesStyle=30]=`cursorSurroundingLinesStyle`,e[e.cursorWidth=31]=`cursorWidth`,e[e.disableLayerHinting=32]=`disableLayerHinting`,e[e.disableMonospaceOptimizations=33]=`disableMonospaceOptimizations`,e[e.domReadOnly=34]=`domReadOnly`,e[e.dragAndDrop=35]=`dragAndDrop`,e[e.dropIntoEditor=36]=`dropIntoEditor`,e[e.emptySelectionClipboard=37]=`emptySelectionClipboard`,e[e.experimentalWhitespaceRendering=38]=`experimentalWhitespaceRendering`,e[e.extraEditorClassName=39]=`extraEditorClassName`,e[e.fastScrollSensitivity=40]=`fastScrollSensitivity`,e[e.find=41]=`find`,e[e.fixedOverflowWidgets=42]=`fixedOverflowWidgets`,e[e.folding=43]=`folding`,e[e.foldingStrategy=44]=`foldingStrategy`,e[e.foldingHighlight=45]=`foldingHighlight`,e[e.foldingImportsByDefault=46]=`foldingImportsByDefault`,e[e.foldingMaximumRegions=47]=`foldingMaximumRegions`,e[e.unfoldOnClickAfterEndOfLine=48]=`unfoldOnClickAfterEndOfLine`,e[e.fontFamily=49]=`fontFamily`,e[e.fontInfo=50]=`fontInfo`,e[e.fontLigatures=51]=`fontLigatures`,e[e.fontSize=52]=`fontSize`,e[e.fontWeight=53]=`fontWeight`,e[e.fontVariations=54]=`fontVariations`,e[e.formatOnPaste=55]=`formatOnPaste`,e[e.formatOnType=56]=`formatOnType`,e[e.glyphMargin=57]=`glyphMargin`,e[e.gotoLocation=58]=`gotoLocation`,e[e.hideCursorInOverviewRuler=59]=`hideCursorInOverviewRuler`,e[e.hover=60]=`hover`,e[e.inDiffEditor=61]=`inDiffEditor`,e[e.inlineSuggest=62]=`inlineSuggest`,e[e.inlineEdit=63]=`inlineEdit`,e[e.letterSpacing=64]=`letterSpacing`,e[e.lightbulb=65]=`lightbulb`,e[e.lineDecorationsWidth=66]=`lineDecorationsWidth`,e[e.lineHeight=67]=`lineHeight`,e[e.lineNumbers=68]=`lineNumbers`,e[e.lineNumbersMinChars=69]=`lineNumbersMinChars`,e[e.linkedEditing=70]=`linkedEditing`,e[e.links=71]=`links`,e[e.matchBrackets=72]=`matchBrackets`,e[e.minimap=73]=`minimap`,e[e.mouseStyle=74]=`mouseStyle`,e[e.mouseWheelScrollSensitivity=75]=`mouseWheelScrollSensitivity`,e[e.mouseWheelZoom=76]=`mouseWheelZoom`,e[e.multiCursorMergeOverlapping=77]=`multiCursorMergeOverlapping`,e[e.multiCursorModifier=78]=`multiCursorModifier`,e[e.multiCursorPaste=79]=`multiCursorPaste`,e[e.multiCursorLimit=80]=`multiCursorLimit`,e[e.occurrencesHighlight=81]=`occurrencesHighlight`,e[e.overviewRulerBorder=82]=`overviewRulerBorder`,e[e.overviewRulerLanes=83]=`overviewRulerLanes`,e[e.padding=84]=`padding`,e[e.pasteAs=85]=`pasteAs`,e[e.parameterHints=86]=`parameterHints`,e[e.peekWidgetDefaultFocus=87]=`peekWidgetDefaultFocus`,e[e.placeholder=88]=`placeholder`,e[e.definitionLinkOpensInPeek=89]=`definitionLinkOpensInPeek`,e[e.quickSuggestions=90]=`quickSuggestions`,e[e.quickSuggestionsDelay=91]=`quickSuggestionsDelay`,e[e.readOnly=92]=`readOnly`,e[e.readOnlyMessage=93]=`readOnlyMessage`,e[e.renameOnType=94]=`renameOnType`,e[e.renderControlCharacters=95]=`renderControlCharacters`,e[e.renderFinalNewline=96]=`renderFinalNewline`,e[e.renderLineHighlight=97]=`renderLineHighlight`,e[e.renderLineHighlightOnlyWhenFocus=98]=`renderLineHighlightOnlyWhenFocus`,e[e.renderValidationDecorations=99]=`renderValidationDecorations`,e[e.renderWhitespace=100]=`renderWhitespace`,e[e.revealHorizontalRightPadding=101]=`revealHorizontalRightPadding`,e[e.roundedSelection=102]=`roundedSelection`,e[e.rulers=103]=`rulers`,e[e.scrollbar=104]=`scrollbar`,e[e.scrollBeyondLastColumn=105]=`scrollBeyondLastColumn`,e[e.scrollBeyondLastLine=106]=`scrollBeyondLastLine`,e[e.scrollPredominantAxis=107]=`scrollPredominantAxis`,e[e.selectionClipboard=108]=`selectionClipboard`,e[e.selectionHighlight=109]=`selectionHighlight`,e[e.selectOnLineNumbers=110]=`selectOnLineNumbers`,e[e.showFoldingControls=111]=`showFoldingControls`,e[e.showUnused=112]=`showUnused`,e[e.snippetSuggestions=113]=`snippetSuggestions`,e[e.smartSelect=114]=`smartSelect`,e[e.smoothScrolling=115]=`smoothScrolling`,e[e.stickyScroll=116]=`stickyScroll`,e[e.stickyTabStops=117]=`stickyTabStops`,e[e.stopRenderingLineAfter=118]=`stopRenderingLineAfter`,e[e.suggest=119]=`suggest`,e[e.suggestFontSize=120]=`suggestFontSize`,e[e.suggestLineHeight=121]=`suggestLineHeight`,e[e.suggestOnTriggerCharacters=122]=`suggestOnTriggerCharacters`,e[e.suggestSelection=123]=`suggestSelection`,e[e.tabCompletion=124]=`tabCompletion`,e[e.tabIndex=125]=`tabIndex`,e[e.unicodeHighlighting=126]=`unicodeHighlighting`,e[e.unusualLineTerminators=127]=`unusualLineTerminators`,e[e.useShadowDOM=128]=`useShadowDOM`,e[e.useTabStops=129]=`useTabStops`,e[e.wordBreak=130]=`wordBreak`,e[e.wordSegmenterLocales=131]=`wordSegmenterLocales`,e[e.wordSeparators=132]=`wordSeparators`,e[e.wordWrap=133]=`wordWrap`,e[e.wordWrapBreakAfterCharacters=134]=`wordWrapBreakAfterCharacters`,e[e.wordWrapBreakBeforeCharacters=135]=`wordWrapBreakBeforeCharacters`,e[e.wordWrapColumn=136]=`wordWrapColumn`,e[e.wordWrapOverride1=137]=`wordWrapOverride1`,e[e.wordWrapOverride2=138]=`wordWrapOverride2`,e[e.wrappingIndent=139]=`wrappingIndent`,e[e.wrappingStrategy=140]=`wrappingStrategy`,e[e.showDeprecated=141]=`showDeprecated`,e[e.inlayHints=142]=`inlayHints`,e[e.editorClassName=143]=`editorClassName`,e[e.pixelRatio=144]=`pixelRatio`,e[e.tabFocusMode=145]=`tabFocusMode`,e[e.layoutInfo=146]=`layoutInfo`,e[e.wrappingInfo=147]=`wrappingInfo`,e[e.defaultColorDecorators=148]=`defaultColorDecorators`,e[e.colorDecoratorsActivatedOn=149]=`colorDecoratorsActivatedOn`,e[e.inlineCompletionsAccessibilityVerbose=150]=`inlineCompletionsAccessibilityVerbose`})(rr||={});var ir;(function(e){e[e.TextDefined=0]=`TextDefined`,e[e.LF=1]=`LF`,e[e.CRLF=2]=`CRLF`})(ir||={});var ar;(function(e){e[e.LF=0]=`LF`,e[e.CRLF=1]=`CRLF`})(ar||={});var or;(function(e){e[e.Left=1]=`Left`,e[e.Center=2]=`Center`,e[e.Right=3]=`Right`})(or||={});var sr;(function(e){e[e.Increase=0]=`Increase`,e[e.Decrease=1]=`Decrease`})(sr||={});var cr;(function(e){e[e.None=0]=`None`,e[e.Indent=1]=`Indent`,e[e.IndentOutdent=2]=`IndentOutdent`,e[e.Outdent=3]=`Outdent`})(cr||={});var lr;(function(e){e[e.Both=0]=`Both`,e[e.Right=1]=`Right`,e[e.Left=2]=`Left`,e[e.None=3]=`None`})(lr||={});var ur;(function(e){e[e.Type=1]=`Type`,e[e.Parameter=2]=`Parameter`})(ur||={});var dr;(function(e){e[e.Automatic=0]=`Automatic`,e[e.Explicit=1]=`Explicit`})(dr||={});var fr;(function(e){e[e.Invoke=0]=`Invoke`,e[e.Automatic=1]=`Automatic`})(fr||={});var pr;(function(e){e[e.DependsOnKbLayout=-1]=`DependsOnKbLayout`,e[e.Unknown=0]=`Unknown`,e[e.Backspace=1]=`Backspace`,e[e.Tab=2]=`Tab`,e[e.Enter=3]=`Enter`,e[e.Shift=4]=`Shift`,e[e.Ctrl=5]=`Ctrl`,e[e.Alt=6]=`Alt`,e[e.PauseBreak=7]=`PauseBreak`,e[e.CapsLock=8]=`CapsLock`,e[e.Escape=9]=`Escape`,e[e.Space=10]=`Space`,e[e.PageUp=11]=`PageUp`,e[e.PageDown=12]=`PageDown`,e[e.End=13]=`End`,e[e.Home=14]=`Home`,e[e.LeftArrow=15]=`LeftArrow`,e[e.UpArrow=16]=`UpArrow`,e[e.RightArrow=17]=`RightArrow`,e[e.DownArrow=18]=`DownArrow`,e[e.Insert=19]=`Insert`,e[e.Delete=20]=`Delete`,e[e.Digit0=21]=`Digit0`,e[e.Digit1=22]=`Digit1`,e[e.Digit2=23]=`Digit2`,e[e.Digit3=24]=`Digit3`,e[e.Digit4=25]=`Digit4`,e[e.Digit5=26]=`Digit5`,e[e.Digit6=27]=`Digit6`,e[e.Digit7=28]=`Digit7`,e[e.Digit8=29]=`Digit8`,e[e.Digit9=30]=`Digit9`,e[e.KeyA=31]=`KeyA`,e[e.KeyB=32]=`KeyB`,e[e.KeyC=33]=`KeyC`,e[e.KeyD=34]=`KeyD`,e[e.KeyE=35]=`KeyE`,e[e.KeyF=36]=`KeyF`,e[e.KeyG=37]=`KeyG`,e[e.KeyH=38]=`KeyH`,e[e.KeyI=39]=`KeyI`,e[e.KeyJ=40]=`KeyJ`,e[e.KeyK=41]=`KeyK`,e[e.KeyL=42]=`KeyL`,e[e.KeyM=43]=`KeyM`,e[e.KeyN=44]=`KeyN`,e[e.KeyO=45]=`KeyO`,e[e.KeyP=46]=`KeyP`,e[e.KeyQ=47]=`KeyQ`,e[e.KeyR=48]=`KeyR`,e[e.KeyS=49]=`KeyS`,e[e.KeyT=50]=`KeyT`,e[e.KeyU=51]=`KeyU`,e[e.KeyV=52]=`KeyV`,e[e.KeyW=53]=`KeyW`,e[e.KeyX=54]=`KeyX`,e[e.KeyY=55]=`KeyY`,e[e.KeyZ=56]=`KeyZ`,e[e.Meta=57]=`Meta`,e[e.ContextMenu=58]=`ContextMenu`,e[e.F1=59]=`F1`,e[e.F2=60]=`F2`,e[e.F3=61]=`F3`,e[e.F4=62]=`F4`,e[e.F5=63]=`F5`,e[e.F6=64]=`F6`,e[e.F7=65]=`F7`,e[e.F8=66]=`F8`,e[e.F9=67]=`F9`,e[e.F10=68]=`F10`,e[e.F11=69]=`F11`,e[e.F12=70]=`F12`,e[e.F13=71]=`F13`,e[e.F14=72]=`F14`,e[e.F15=73]=`F15`,e[e.F16=74]=`F16`,e[e.F17=75]=`F17`,e[e.F18=76]=`F18`,e[e.F19=77]=`F19`,e[e.F20=78]=`F20`,e[e.F21=79]=`F21`,e[e.F22=80]=`F22`,e[e.F23=81]=`F23`,e[e.F24=82]=`F24`,e[e.NumLock=83]=`NumLock`,e[e.ScrollLock=84]=`ScrollLock`,e[e.Semicolon=85]=`Semicolon`,e[e.Equal=86]=`Equal`,e[e.Comma=87]=`Comma`,e[e.Minus=88]=`Minus`,e[e.Period=89]=`Period`,e[e.Slash=90]=`Slash`,e[e.Backquote=91]=`Backquote`,e[e.BracketLeft=92]=`BracketLeft`,e[e.Backslash=93]=`Backslash`,e[e.BracketRight=94]=`BracketRight`,e[e.Quote=95]=`Quote`,e[e.OEM_8=96]=`OEM_8`,e[e.IntlBackslash=97]=`IntlBackslash`,e[e.Numpad0=98]=`Numpad0`,e[e.Numpad1=99]=`Numpad1`,e[e.Numpad2=100]=`Numpad2`,e[e.Numpad3=101]=`Numpad3`,e[e.Numpad4=102]=`Numpad4`,e[e.Numpad5=103]=`Numpad5`,e[e.Numpad6=104]=`Numpad6`,e[e.Numpad7=105]=`Numpad7`,e[e.Numpad8=106]=`Numpad8`,e[e.Numpad9=107]=`Numpad9`,e[e.NumpadMultiply=108]=`NumpadMultiply`,e[e.NumpadAdd=109]=`NumpadAdd`,e[e.NUMPAD_SEPARATOR=110]=`NUMPAD_SEPARATOR`,e[e.NumpadSubtract=111]=`NumpadSubtract`,e[e.NumpadDecimal=112]=`NumpadDecimal`,e[e.NumpadDivide=113]=`NumpadDivide`,e[e.KEY_IN_COMPOSITION=114]=`KEY_IN_COMPOSITION`,e[e.ABNT_C1=115]=`ABNT_C1`,e[e.ABNT_C2=116]=`ABNT_C2`,e[e.AudioVolumeMute=117]=`AudioVolumeMute`,e[e.AudioVolumeUp=118]=`AudioVolumeUp`,e[e.AudioVolumeDown=119]=`AudioVolumeDown`,e[e.BrowserSearch=120]=`BrowserSearch`,e[e.BrowserHome=121]=`BrowserHome`,e[e.BrowserBack=122]=`BrowserBack`,e[e.BrowserForward=123]=`BrowserForward`,e[e.MediaTrackNext=124]=`MediaTrackNext`,e[e.MediaTrackPrevious=125]=`MediaTrackPrevious`,e[e.MediaStop=126]=`MediaStop`,e[e.MediaPlayPause=127]=`MediaPlayPause`,e[e.LaunchMediaPlayer=128]=`LaunchMediaPlayer`,e[e.LaunchMail=129]=`LaunchMail`,e[e.LaunchApp2=130]=`LaunchApp2`,e[e.Clear=131]=`Clear`,e[e.MAX_VALUE=132]=`MAX_VALUE`})(pr||={});var mr;(function(e){e[e.Hint=1]=`Hint`,e[e.Info=2]=`Info`,e[e.Warning=4]=`Warning`,e[e.Error=8]=`Error`})(mr||={});var hr;(function(e){e[e.Unnecessary=1]=`Unnecessary`,e[e.Deprecated=2]=`Deprecated`})(hr||={});var gr;(function(e){e[e.Inline=1]=`Inline`,e[e.Gutter=2]=`Gutter`})(gr||={});var _r;(function(e){e[e.Normal=1]=`Normal`,e[e.Underlined=2]=`Underlined`})(_r||={});var vr;(function(e){e[e.UNKNOWN=0]=`UNKNOWN`,e[e.TEXTAREA=1]=`TEXTAREA`,e[e.GUTTER_GLYPH_MARGIN=2]=`GUTTER_GLYPH_MARGIN`,e[e.GUTTER_LINE_NUMBERS=3]=`GUTTER_LINE_NUMBERS`,e[e.GUTTER_LINE_DECORATIONS=4]=`GUTTER_LINE_DECORATIONS`,e[e.GUTTER_VIEW_ZONE=5]=`GUTTER_VIEW_ZONE`,e[e.CONTENT_TEXT=6]=`CONTENT_TEXT`,e[e.CONTENT_EMPTY=7]=`CONTENT_EMPTY`,e[e.CONTENT_VIEW_ZONE=8]=`CONTENT_VIEW_ZONE`,e[e.CONTENT_WIDGET=9]=`CONTENT_WIDGET`,e[e.OVERVIEW_RULER=10]=`OVERVIEW_RULER`,e[e.SCROLLBAR=11]=`SCROLLBAR`,e[e.OVERLAY_WIDGET=12]=`OVERLAY_WIDGET`,e[e.OUTSIDE_EDITOR=13]=`OUTSIDE_EDITOR`})(vr||={});var yr;(function(e){e[e.AIGenerated=1]=`AIGenerated`})(yr||={});var br;(function(e){e[e.Invoke=0]=`Invoke`,e[e.Automatic=1]=`Automatic`})(br||={});var xr;(function(e){e[e.TOP_RIGHT_CORNER=0]=`TOP_RIGHT_CORNER`,e[e.BOTTOM_RIGHT_CORNER=1]=`BOTTOM_RIGHT_CORNER`,e[e.TOP_CENTER=2]=`TOP_CENTER`})(xr||={});var Sr;(function(e){e[e.Left=1]=`Left`,e[e.Center=2]=`Center`,e[e.Right=4]=`Right`,e[e.Full=7]=`Full`})(Sr||={});var Cr;(function(e){e[e.Word=0]=`Word`,e[e.Line=1]=`Line`,e[e.Suggest=2]=`Suggest`})(Cr||={});var wr;(function(e){e[e.Left=0]=`Left`,e[e.Right=1]=`Right`,e[e.None=2]=`None`,e[e.LeftOfInjectedText=3]=`LeftOfInjectedText`,e[e.RightOfInjectedText=4]=`RightOfInjectedText`})(wr||={});var Tr;(function(e){e[e.Off=0]=`Off`,e[e.On=1]=`On`,e[e.Relative=2]=`Relative`,e[e.Interval=3]=`Interval`,e[e.Custom=4]=`Custom`})(Tr||={});var Er;(function(e){e[e.None=0]=`None`,e[e.Text=1]=`Text`,e[e.Blocks=2]=`Blocks`})(Er||={});var Dr;(function(e){e[e.Smooth=0]=`Smooth`,e[e.Immediate=1]=`Immediate`})(Dr||={});var Or;(function(e){e[e.Auto=1]=`Auto`,e[e.Hidden=2]=`Hidden`,e[e.Visible=3]=`Visible`})(Or||={});var kr;(function(e){e[e.LTR=0]=`LTR`,e[e.RTL=1]=`RTL`})(kr||={});var Ar;(function(e){e.Off=`off`,e.OnCode=`onCode`,e.On=`on`})(Ar||={});var jr;(function(e){e[e.Invoke=1]=`Invoke`,e[e.TriggerCharacter=2]=`TriggerCharacter`,e[e.ContentChange=3]=`ContentChange`})(jr||={});var Mr;(function(e){e[e.File=0]=`File`,e[e.Module=1]=`Module`,e[e.Namespace=2]=`Namespace`,e[e.Package=3]=`Package`,e[e.Class=4]=`Class`,e[e.Method=5]=`Method`,e[e.Property=6]=`Property`,e[e.Field=7]=`Field`,e[e.Constructor=8]=`Constructor`,e[e.Enum=9]=`Enum`,e[e.Interface=10]=`Interface`,e[e.Function=11]=`Function`,e[e.Variable=12]=`Variable`,e[e.Constant=13]=`Constant`,e[e.String=14]=`String`,e[e.Number=15]=`Number`,e[e.Boolean=16]=`Boolean`,e[e.Array=17]=`Array`,e[e.Object=18]=`Object`,e[e.Key=19]=`Key`,e[e.Null=20]=`Null`,e[e.EnumMember=21]=`EnumMember`,e[e.Struct=22]=`Struct`,e[e.Event=23]=`Event`,e[e.Operator=24]=`Operator`,e[e.TypeParameter=25]=`TypeParameter`})(Mr||={});var Nr;(function(e){e[e.Deprecated=1]=`Deprecated`})(Nr||={});var Pr;(function(e){e[e.Hidden=0]=`Hidden`,e[e.Blink=1]=`Blink`,e[e.Smooth=2]=`Smooth`,e[e.Phase=3]=`Phase`,e[e.Expand=4]=`Expand`,e[e.Solid=5]=`Solid`})(Pr||={});var Fr;(function(e){e[e.Line=1]=`Line`,e[e.Block=2]=`Block`,e[e.Underline=3]=`Underline`,e[e.LineThin=4]=`LineThin`,e[e.BlockOutline=5]=`BlockOutline`,e[e.UnderlineThin=6]=`UnderlineThin`})(Fr||={});var Ir;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]=`AlwaysGrowsWhenTypingAtEdges`,e[e.NeverGrowsWhenTypingAtEdges=1]=`NeverGrowsWhenTypingAtEdges`,e[e.GrowsOnlyWhenTypingBefore=2]=`GrowsOnlyWhenTypingBefore`,e[e.GrowsOnlyWhenTypingAfter=3]=`GrowsOnlyWhenTypingAfter`})(Ir||={});var Lr;(function(e){e[e.None=0]=`None`,e[e.Same=1]=`Same`,e[e.Indent=2]=`Indent`,e[e.DeepIndent=3]=`DeepIndent`})(Lr||={});var Rr=class{static{this.CtrlCmd=2048}static{this.Shift=1024}static{this.Alt=512}static{this.WinCtrl=256}static chord(e,t){return Tn(e,t)}};function zr(){return{editor:void 0,languages:void 0,CancellationTokenSource:fn,Emitter:T,KeyCode:pr,KeyMod:Rr,Position:M,Range:N,Selection:En,SelectionDirection:kr,MarkerSeverity:mr,MarkerTag:hr,Uri:ft,Token:Nn}}var Br=class e{static{this.CHANNEL_NAME=`editorWorkerHost`}static getChannel(t){return t.getChannel(e.CHANNEL_NAME)}static setChannel(t,n){t.setChannel(e.CHANNEL_NAME,n)}},Vr,Hr,Ur=class{constructor(e,t){this.uri=e,this.value=t}};function Wr(e){return Array.isArray(e)}(class e{static{this.defaultToKey=e=>e.toString()}constructor(t,n){if(this[Vr]=`ResourceMap`,t instanceof e)this.map=new Map(t.map),this.toKey=n??e.defaultToKey;else if(Wr(t)){this.map=new Map,this.toKey=n??e.defaultToKey;for(let[e,n]of t)this.set(e,n)}else this.map=new Map,this.toKey=t??e.defaultToKey}set(e,t){return this.map.set(this.toKey(e),new Ur(e,t)),this}get(e){return this.map.get(this.toKey(e))?.value}has(e){return this.map.has(this.toKey(e))}get size(){return this.map.size}clear(){this.map.clear()}delete(e){return this.map.delete(this.toKey(e))}forEach(e,t){t!==void 0&&(e=e.bind(t));for(let[t,n]of this.map)e(n.value,n.uri,this)}*values(){for(let e of this.map.values())yield e.value}*keys(){for(let e of this.map.values())yield e.uri}*entries(){for(let e of this.map.values())yield[e.uri,e.value]}*[(Vr=Symbol.toStringTag,Symbol.iterator)](){for(let[,e]of this.map)yield[e.uri,e.value]}});var Gr=class{constructor(){this[Hr]=`LinkedMap`,this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0,this._state=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0,this._state++}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}get first(){return this._head?.value}get last(){return this._tail?.value}has(e){return this._map.has(e)}get(e,t=0){let n=this._map.get(e);if(n)return t!==0&&this.touch(n,t),n.value}set(e,t,n=0){let r=this._map.get(e);if(r)r.value=t,n!==0&&this.touch(r,n);else{switch(r={key:e,value:t,next:void 0,previous:void 0},n){case 0:this.addItemLast(r);break;case 1:this.addItemFirst(r);break;case 2:this.addItemLast(r);break;default:this.addItemLast(r);break}this._map.set(e,r),this._size++}return this}delete(e){return!!this.remove(e)}remove(e){let t=this._map.get(e);if(t)return this._map.delete(e),this.removeItem(t),this._size--,t.value}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw Error(`Invalid list`);let e=this._head;return this._map.delete(e.key),this.removeItem(e),this._size--,e.value}forEach(e,t){let n=this._state,r=this._head;for(;r;){if(t?e.bind(t)(r.value,r.key,this):e(r.value,r.key,this),this._state!==n)throw Error(`LinkedMap got modified during iteration.`);r=r.next}}keys(){let e=this,t=this._state,n=this._head,r={[Symbol.iterator](){return r},next(){if(e._state!==t)throw Error(`LinkedMap got modified during iteration.`);if(n){let e={value:n.key,done:!1};return n=n.next,e}else return{value:void 0,done:!0}}};return r}values(){let e=this,t=this._state,n=this._head,r={[Symbol.iterator](){return r},next(){if(e._state!==t)throw Error(`LinkedMap got modified during iteration.`);if(n){let e={value:n.value,done:!1};return n=n.next,e}else return{value:void 0,done:!0}}};return r}entries(){let e=this,t=this._state,n=this._head,r={[Symbol.iterator](){return r},next(){if(e._state!==t)throw Error(`LinkedMap got modified during iteration.`);if(n){let e={value:[n.key,n.value],done:!1};return n=n.next,e}else return{value:void 0,done:!0}}};return r}[(Hr=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}trimOld(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this._head,n=this.size;for(;t&&n>e;)this._map.delete(t.key),t=t.next,n--;this._head=t,this._size=n,t&&(t.previous=void 0),this._state++}trimNew(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this._tail,n=this.size;for(;t&&n>e;)this._map.delete(t.key),t=t.previous,n--;this._tail=t,this._size=n,t&&(t.next=void 0),this._state++}addItemFirst(e){if(!this._head&&!this._tail)this._tail=e;else if(this._head)e.next=this._head,this._head.previous=e;else throw Error(`Invalid list`);this._head=e,this._state++}addItemLast(e){if(!this._head&&!this._tail)this._head=e;else if(this._tail)e.previous=this._tail,this._tail.next=e;else throw Error(`Invalid list`);this._tail=e,this._state++}removeItem(e){if(e===this._head&&e===this._tail)this._head=void 0,this._tail=void 0;else if(e===this._head){if(!e.next)throw Error(`Invalid list`);e.next.previous=void 0,this._head=e.next}else if(e===this._tail){if(!e.previous)throw Error(`Invalid list`);e.previous.next=void 0,this._tail=e.previous}else{let t=e.next,n=e.previous;if(!t||!n)throw Error(`Invalid list`);t.previous=n,n.next=t}e.next=void 0,e.previous=void 0,this._state++}touch(e,t){if(!this._head||!this._tail)throw Error(`Invalid list`);if(!(t!==1&&t!==2)){if(t===1){if(e===this._head)return;let t=e.next,n=e.previous;e===this._tail?(n.next=void 0,this._tail=n):(t.previous=n,n.next=t),e.previous=void 0,e.next=this._head,this._head.previous=e,this._head=e,this._state++}else if(t===2){if(e===this._tail)return;let t=e.next,n=e.previous;e===this._head?(t.previous=void 0,this._head=t):(t.previous=n,n.next=t),e.next=void 0,e.previous=this._tail,this._tail.next=e,this._tail=e,this._state++}}}toJSON(){let e=[];return this.forEach((t,n)=>{e.push([n,t])}),e}fromJSON(e){this.clear();for(let[t,n]of e)this.set(t,n)}},Kr=class extends Gr{constructor(e,t=1){super(),this._limit=e,this._ratio=Math.min(Math.max(0,t),1)}get limit(){return this._limit}set limit(e){this._limit=e,this.checkTrim()}get(e,t=2){return super.get(e,t)}peek(e){return super.get(e,0)}set(e,t){return super.set(e,t,2),this}checkTrim(){this.size>this._limit&&this.trim(Math.round(this._limit*this._ratio))}},qr=class extends Kr{constructor(e,t=1){super(e,t)}trim(e){this.trimOld(e)}set(e,t){return super.set(e,t),this.checkTrim(),this}},Jr=class{constructor(){this.map=new Map}add(e,t){let n=this.map.get(e);n||(n=new Set,this.map.set(e,n)),n.add(t)}delete(e,t){let n=this.map.get(e);n&&(n.delete(t),n.size===0&&this.map.delete(e))}forEach(e,t){let n=this.map.get(e);n&&n.forEach(t)}get(e){return this.map.get(e)||new Set}};new qr(10);function Yr(e){let t=[];for(;Object.prototype!==e;)t=t.concat(Object.getOwnPropertyNames(e)),e=Object.getPrototypeOf(e);return t}function Xr(e){let t=[];for(let n of Yr(e))typeof e[n]==`function`&&t.push(n);return t}function Zr(e,t){let n=e=>function(){let n=Array.prototype.slice.call(arguments,0);return t(e,n)},r={};for(let t of e)r[t]=n(t);return r}var Qr;(function(e){e[e.Left=1]=`Left`,e[e.Center=2]=`Center`,e[e.Right=4]=`Right`,e[e.Full=7]=`Full`})(Qr||={});var $r;(function(e){e[e.Left=1]=`Left`,e[e.Center=2]=`Center`,e[e.Right=3]=`Right`})($r||={});var ei;(function(e){e[e.Both=0]=`Both`,e[e.Right=1]=`Right`,e[e.Left=2]=`Left`,e[e.None=3]=`None`})(ei||={});function ti(e,t,n,r,i){if(r===0)return!0;let a=t.charCodeAt(r-1);if(e.get(a)!==0||a===13||a===10)return!0;if(i>0){let n=t.charCodeAt(r);if(e.get(n)!==0)return!0}return!1}function ni(e,t,n,r,i){if(r+i===n)return!0;let a=t.charCodeAt(r+i);if(e.get(a)!==0||a===13||a===10)return!0;if(i>0){let n=t.charCodeAt(r+i-1);if(e.get(n)!==0)return!0}return!1}function ri(e,t,n,r,i){return ti(e,t,n,r,i)&&ni(e,t,n,r,i)}var ii=class{constructor(e,t){this._wordSeparators=e,this._searchRegex=t,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(e){this._searchRegex.lastIndex=e,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(e){let t=e.length,n;do{if(this._prevMatchStartIndex+this._prevMatchLength===t||(n=this._searchRegex.exec(e),!n))return null;let r=n.index,i=n[0].length;if(r===this._prevMatchStartIndex&&i===this._prevMatchLength){if(i===0){Le(e,t,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=r,this._prevMatchLength=i,!this._wordSeparators||ri(this._wordSeparators,e,t,r,i))return n}while(n);return null}};function ai(e,t=`Unreachable`){throw Error(t)}function oi(e){e()||(e(),t(new s(`Assertion Failed`)))}function si(e,t){let n=0;for(;n<e.length-1;){let r=e[n],i=e[n+1];if(!t(r,i))return!1;n++}return!0}function ci(e=``){let t=`(-?\\d*\\.\\d\\w*)|([^`;for(let n of`\`~!@#$%^&*()-=+[{]}\\|;:'",.<>/?`){if(e.indexOf(n)>=0)continue;t+=`\\`+n}return t+=`\\s]+)`,new RegExp(t,`g`)}const li=ci();function ui(e){let t=li;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n=`g`;e.ignoreCase&&(n+=`i`),e.multiline&&(n+=`m`),e.unicode&&(n+=`u`),t=new RegExp(e.source,n)}return t.lastIndex=0,t}const di=new y;di.unshift({maxLen:1e3,windowSize:15,timeBudget:150});function fi(e,t,n,r,i){if(t=ui(t),i||=l.first(di),n.length>i.maxLen){let a=e-i.maxLen/2;return a<0?a=0:r+=a,n=n.substring(a,e+i.maxLen/2),fi(e,t,n,r,i)}let a=Date.now(),o=e-1-r,s=-1,c=null;for(let e=1;!(Date.now()-a>=i.timeBudget);e++){let r=o-i.windowSize*e;t.lastIndex=Math.max(0,r);let a=pi(t,n,o,s);if(!a&&c||(c=a,r<=0))break;s=r}if(c){let e={word:c[0],startColumn:r+1+c.index,endColumn:r+1+c.index+c[0].length};return t.lastIndex=0,e}return null}function pi(e,t,n,r){let i;for(;i=e.exec(t);){let t=i.index||0;if(t<=n&&e.lastIndex>=n)return i;if(r>0&&t>r)return null}return null}var mi=class{static computeUnicodeHighlights(e,t,n){let r=n?n.startLineNumber:1,i=n?n.endLineNumber:e.getLineCount(),a=new gi(t),o=a.getCandidateCodePoints(),s;s=o===`allNonBasicAscii`?RegExp(`[^\\t\\n\\r\\x20-\\x7E]`,`g`):RegExp(`${hi(Array.from(o))}`,`g`);let c=new ii(null,s),l=[],u=!1,d,f=0,p=0,m=0;forLoop:for(let t=r,n=i;t<=n;t++){let n=e.getLineContent(t),r=n.length;c.reset(0);do if(d=c.next(n),d){let e=d.index,i=d.index+d[0].length;if(e>0){let t=n.charCodeAt(e-1);Pe(t)&&e--}if(i+1<r){let e=n.charCodeAt(i-1);Pe(e)&&i++}let o=n.substring(e,i),s=fi(e+1,li,n,0);s&&s.endColumn<=e+1&&(s=null);let c=a.shouldHighlightNonBasicASCII(o,s?s.word:null);if(c!==0){if(c===3?f++:c===2?p++:c===1?m++:ai(c),l.length>=1e3){u=!0;break forLoop}l.push(new N(t,e+1,t,i+1))}}while(d)}return{ranges:l,hasMore:u,ambiguousCharacterCount:f,invisibleCharacterCount:p,nonBasicAsciiCharacterCount:m}}static computeUnicodeHighlightReason(e,t){let n=new gi(t);switch(n.shouldHighlightNonBasicASCII(e,null)){case 0:return null;case 2:return{kind:1};case 3:{let r=e.codePointAt(0),i=n.ambiguousCharacters.getPrimaryConfusable(r),a=Ve.getLocales().filter(e=>!Ve.getInstance(new Set([...t.allowedLocales,e])).isAmbiguous(r));return{kind:0,confusableWith:String.fromCodePoint(i),notAmbiguousInLocales:a}}case 1:return{kind:2}}}};function hi(e,t){return`[${ke(e.map(e=>String.fromCodePoint(e)).join(``))}]`}var gi=class{constructor(e){this.options=e,this.allowedCodePoints=new Set(e.allowedCodePoints),this.ambiguousCharacters=Ve.getInstance(new Set(e.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return`allNonBasicAscii`;let e=new Set;if(this.options.invisibleCharacters)for(let t of He.codePoints)_i(String.fromCodePoint(t))||e.add(t);if(this.options.ambiguousCharacters)for(let t of this.ambiguousCharacters.getConfusableCodePoints())e.add(t);for(let t of this.allowedCodePoints)e.delete(t);return e}shouldHighlightNonBasicASCII(e,t){let n=e.codePointAt(0);if(this.allowedCodePoints.has(n))return 0;if(this.options.nonBasicASCII)return 1;let r=!1,i=!1;if(t)for(let e of t){let t=e.codePointAt(0),n=ze(e);r||=n,!n&&!this.ambiguousCharacters.isAmbiguous(t)&&!He.isInvisibleCharacter(t)&&(i=!0)}return!r&&i?0:this.options.invisibleCharacters&&!_i(e)&&He.isInvisibleCharacter(n)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(n)?3:0}};function _i(e){return e===` `||e===`
`||e===`	`}var vi=class{constructor(e,t,n){this.changes=e,this.moves=t,this.hitTimeout=n}},yi=class{constructor(e,t){this.lineRangeMapping=e,this.changes=t}},I=class e{static addRange(t,n){let r=0;for(;r<n.length&&n[r].endExclusive<t.start;)r++;let i=r;for(;i<n.length&&n[i].start<=t.endExclusive;)i++;if(r===i)n.splice(r,0,t);else{let a=Math.min(t.start,n[r].start),o=Math.max(t.endExclusive,n[i-1].endExclusive);n.splice(r,i-r,new e(a,o))}}static tryCreate(t,n){if(!(t>n))return new e(t,n)}static ofLength(t){return new e(0,t)}static ofStartAndLength(t,n){return new e(t,t+n)}constructor(e,t){if(this.start=e,this.endExclusive=t,e>t)throw new s(`Invalid range: ${this.toString()}`)}get isEmpty(){return this.start===this.endExclusive}delta(t){return new e(this.start+t,this.endExclusive+t)}deltaStart(t){return new e(this.start+t,this.endExclusive)}deltaEnd(t){return new e(this.start,this.endExclusive+t)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}contains(e){return this.start<=e&&e<this.endExclusive}join(t){return new e(Math.min(this.start,t.start),Math.max(this.endExclusive,t.endExclusive))}intersect(t){let n=Math.max(this.start,t.start),r=Math.min(this.endExclusive,t.endExclusive);if(n<=r)return new e(n,r)}intersects(e){let t=Math.max(this.start,e.start),n=Math.min(this.endExclusive,e.endExclusive);return t<n}isBefore(e){return this.endExclusive<=e.start}isAfter(e){return this.start>=e.endExclusive}slice(e){return e.slice(this.start,this.endExclusive)}substring(e){return e.substring(this.start,this.endExclusive)}clip(e){if(this.isEmpty)throw new s(`Invalid clipping range: ${this.toString()}`);return Math.max(this.start,Math.min(this.endExclusive-1,e))}clipCyclic(e){if(this.isEmpty)throw new s(`Invalid clipping range: ${this.toString()}`);return e<this.start?this.endExclusive-(this.start-e)%this.length:e>=this.endExclusive?this.start+(e-this.start)%this.length:e}forEach(e){for(let t=this.start;t<this.endExclusive;t++)e(t)}};function bi(e,t){let n=xi(e,t);return n===-1?void 0:e[n]}function xi(e,t,n=0,r=e.length){let i=n,a=r;for(;i<a;){let n=Math.floor((i+a)/2);t(e[n])?i=n+1:a=n}return i-1}function Si(e,t){let n=Ci(e,t);return n===e.length?void 0:e[n]}function Ci(e,t,n=0,r=e.length){let i=n,a=r;for(;i<a;){let n=Math.floor((i+a)/2);t(e[n])?a=n:i=n+1}return i}var wi=class e{static{this.assertInvariants=!1}constructor(e){this._array=e,this._findLastMonotonousLastIdx=0}findLastMonotonous(t){if(e.assertInvariants){if(this._prevFindLastPredicate){for(let e of this._array)if(this._prevFindLastPredicate(e)&&!t(e))throw Error(`MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.`)}this._prevFindLastPredicate=t}let n=xi(this._array,t,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=n+1,n===-1?void 0:this._array[n]}},L=class e{static fromRangeInclusive(t){return new e(t.startLineNumber,t.endLineNumber+1)}static joinMany(e){if(e.length===0)return[];let t=new Ti(e[0].slice());for(let n=1;n<e.length;n++)t=t.getUnion(new Ti(e[n].slice()));return t.ranges}static join(t){if(t.length===0)throw new s(`lineRanges cannot be empty`);let n=t[0].startLineNumber,r=t[0].endLineNumberExclusive;for(let e=1;e<t.length;e++)n=Math.min(n,t[e].startLineNumber),r=Math.max(r,t[e].endLineNumberExclusive);return new e(n,r)}static ofLength(t,n){return new e(t,t+n)}static deserialize(t){return new e(t[0],t[1])}constructor(e,t){if(e>t)throw new s(`startLineNumber ${e} cannot be after endLineNumberExclusive ${t}`);this.startLineNumber=e,this.endLineNumberExclusive=t}contains(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(t){return new e(this.startLineNumber+t,this.endLineNumberExclusive+t)}deltaLength(t){return new e(this.startLineNumber,this.endLineNumberExclusive+t)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(t){return new e(Math.min(this.startLineNumber,t.startLineNumber),Math.max(this.endLineNumberExclusive,t.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}intersect(t){let n=Math.max(this.startLineNumber,t.startLineNumber),r=Math.min(this.endLineNumberExclusive,t.endLineNumberExclusive);if(n<=r)return new e(n,r)}intersectsStrict(e){return this.startLineNumber<e.endLineNumberExclusive&&e.startLineNumber<this.endLineNumberExclusive}overlapOrTouch(e){return this.startLineNumber<=e.endLineNumberExclusive&&e.startLineNumber<=this.endLineNumberExclusive}equals(e){return this.startLineNumber===e.startLineNumber&&this.endLineNumberExclusive===e.endLineNumberExclusive}toInclusiveRange(){return this.isEmpty?null:new N(this.startLineNumber,1,this.endLineNumberExclusive-1,2**53-1)}toExclusiveRange(){return new N(this.startLineNumber,1,this.endLineNumberExclusive,1)}mapToLineArray(e){let t=[];for(let n=this.startLineNumber;n<this.endLineNumberExclusive;n++)t.push(e(n));return t}forEach(e){for(let t=this.startLineNumber;t<this.endLineNumberExclusive;t++)e(t)}serialize(){return[this.startLineNumber,this.endLineNumberExclusive]}includes(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}toOffsetRange(){return new I(this.startLineNumber-1,this.endLineNumberExclusive-1)}},Ti=class e{constructor(e=[]){this._normalizedRanges=e}get ranges(){return this._normalizedRanges}addRange(e){if(e.length===0)return;let t=Ci(this._normalizedRanges,t=>t.endLineNumberExclusive>=e.startLineNumber),n=xi(this._normalizedRanges,t=>t.startLineNumber<=e.endLineNumberExclusive)+1;if(t===n)this._normalizedRanges.splice(t,0,e);else if(t===n-1){let n=this._normalizedRanges[t];this._normalizedRanges[t]=n.join(e)}else{let r=this._normalizedRanges[t].join(this._normalizedRanges[n-1]).join(e);this._normalizedRanges.splice(t,n-t,r)}}contains(e){let t=bi(this._normalizedRanges,t=>t.startLineNumber<=e);return!!t&&t.endLineNumberExclusive>e}intersects(e){let t=bi(this._normalizedRanges,t=>t.startLineNumber<e.endLineNumberExclusive);return!!t&&t.endLineNumberExclusive>e.startLineNumber}getUnion(t){if(this._normalizedRanges.length===0)return t;if(t._normalizedRanges.length===0)return this;let n=[],r=0,i=0,a=null;for(;r<this._normalizedRanges.length||i<t._normalizedRanges.length;){let e=null;if(r<this._normalizedRanges.length&&i<t._normalizedRanges.length){let n=this._normalizedRanges[r],a=t._normalizedRanges[i];n.startLineNumber<a.startLineNumber?(e=n,r++):(e=a,i++)}else r<this._normalizedRanges.length?(e=this._normalizedRanges[r],r++):(e=t._normalizedRanges[i],i++);a===null?a=e:a.endLineNumberExclusive>=e.startLineNumber?a=new L(a.startLineNumber,Math.max(a.endLineNumberExclusive,e.endLineNumberExclusive)):(n.push(a),a=e)}return a!==null&&n.push(a),new e(n)}subtractFrom(t){let n=Ci(this._normalizedRanges,e=>e.endLineNumberExclusive>=t.startLineNumber),r=xi(this._normalizedRanges,e=>e.startLineNumber<=t.endLineNumberExclusive)+1;if(n===r)return new e([t]);let i=[],a=t.startLineNumber;for(let e=n;e<r;e++){let t=this._normalizedRanges[e];t.startLineNumber>a&&i.push(new L(a,t.startLineNumber)),a=t.endLineNumberExclusive}return a<t.endLineNumberExclusive&&i.push(new L(a,t.endLineNumberExclusive)),new e(i)}toString(){return this._normalizedRanges.map(e=>e.toString()).join(`, `)}getIntersection(t){let n=[],r=0,i=0;for(;r<this._normalizedRanges.length&&i<t._normalizedRanges.length;){let e=this._normalizedRanges[r],a=t._normalizedRanges[i],o=e.intersect(a);o&&!o.isEmpty&&n.push(o),e.endLineNumberExclusive<a.endLineNumberExclusive?r++:i++}return new e(n)}getWithDelta(t){return new e(this._normalizedRanges.map(e=>e.delta(t)))}};(class e{static{this.zero=new e(0,0)}static betweenPositions(t,n){return t.lineNumber===n.lineNumber?new e(0,n.column-t.column):new e(n.lineNumber-t.lineNumber,n.column-1)}static ofRange(t){return e.betweenPositions(t.getStartPosition(),t.getEndPosition())}static ofText(t){let n=0,r=0;for(let e of t)e===`
`?(n++,r=0):r++;return new e(n,r)}constructor(e,t){this.lineCount=e,this.columnCount=t}isGreaterThanOrEqualTo(e){return this.lineCount===e.lineCount?this.columnCount>=e.columnCount:this.lineCount>e.lineCount}createRange(e){return this.lineCount===0?new N(e.lineNumber,e.column,e.lineNumber,e.column+this.columnCount):new N(e.lineNumber,e.column,e.lineNumber+this.lineCount,this.columnCount+1)}addToPosition(e){return this.lineCount===0?new M(e.lineNumber,e.column+this.columnCount):new M(e.lineNumber+this.lineCount,this.columnCount+1)}toString(){return`${this.lineCount},${this.columnCount}`}});var Ei=class{constructor(e,t){this.range=e,this.text=t}toSingleEditOperation(){return{range:this.range,text:this.text}}},Di=class e{static inverse(t,n,r){let i=[],a=1,o=1;for(let n of t){let t=new e(new L(a,n.original.startLineNumber),new L(o,n.modified.startLineNumber));t.modified.isEmpty||i.push(t),a=n.original.endLineNumberExclusive,o=n.modified.endLineNumberExclusive}let s=new e(new L(a,n+1),new L(o,r+1));return s.modified.isEmpty||i.push(s),i}static clip(t,n,r){let i=[];for(let a of t){let t=a.original.intersect(n),o=a.modified.intersect(r);t&&!t.isEmpty&&o&&!o.isEmpty&&i.push(new e(t,o))}return i}constructor(e,t){this.original=e,this.modified=t}toString(){return`{${this.original.toString()}->${this.modified.toString()}}`}flip(){return new e(this.modified,this.original)}join(t){return new e(this.original.join(t.original),this.modified.join(t.modified))}toRangeMapping(){let e=this.original.toInclusiveRange(),t=this.modified.toInclusiveRange();if(e&&t)return new ji(e,t);if(this.original.startLineNumber===1||this.modified.startLineNumber===1){if(!(this.modified.startLineNumber===1&&this.original.startLineNumber===1))throw new s(`not a valid diff`);return new ji(new N(this.original.startLineNumber,1,this.original.endLineNumberExclusive,1),new N(this.modified.startLineNumber,1,this.modified.endLineNumberExclusive,1))}else return new ji(new N(this.original.startLineNumber-1,2**53-1,this.original.endLineNumberExclusive-1,2**53-1),new N(this.modified.startLineNumber-1,2**53-1,this.modified.endLineNumberExclusive-1,2**53-1))}toRangeMapping2(e,t){if(ki(this.original.endLineNumberExclusive,e)&&ki(this.modified.endLineNumberExclusive,t))return new ji(new N(this.original.startLineNumber,1,this.original.endLineNumberExclusive,1),new N(this.modified.startLineNumber,1,this.modified.endLineNumberExclusive,1));if(!this.original.isEmpty&&!this.modified.isEmpty)return new ji(N.fromPositions(new M(this.original.startLineNumber,1),Oi(new M(this.original.endLineNumberExclusive-1,2**53-1),e)),N.fromPositions(new M(this.modified.startLineNumber,1),Oi(new M(this.modified.endLineNumberExclusive-1,2**53-1),t)));if(this.original.startLineNumber>1&&this.modified.startLineNumber>1)return new ji(N.fromPositions(Oi(new M(this.original.startLineNumber-1,2**53-1),e),Oi(new M(this.original.endLineNumberExclusive-1,2**53-1),e)),N.fromPositions(Oi(new M(this.modified.startLineNumber-1,2**53-1),t),Oi(new M(this.modified.endLineNumberExclusive-1,2**53-1),t)));throw new s}};function Oi(e,t){if(e.lineNumber<1)return new M(1,1);if(e.lineNumber>t.length)return new M(t.length,t[t.length-1].length+1);let n=t[e.lineNumber-1];return e.column>n.length+1?new M(e.lineNumber,n.length+1):e}function ki(e,t){return e>=1&&e<=t.length}var Ai=class e extends Di{static fromRangeMappings(t){let n=L.join(t.map(e=>L.fromRangeInclusive(e.originalRange))),r=L.join(t.map(e=>L.fromRangeInclusive(e.modifiedRange)));return new e(n,r,t)}constructor(e,t,n){super(e,t),this.innerChanges=n}flip(){return new e(this.modified,this.original,this.innerChanges?.map(e=>e.flip()))}withInnerChangesFromLineRanges(){return new e(this.original,this.modified,[this.toRangeMapping()])}},ji=class e{static assertSorted(e){for(let t=1;t<e.length;t++){let n=e[t-1],r=e[t];if(!(n.originalRange.getEndPosition().isBeforeOrEqual(r.originalRange.getStartPosition())&&n.modifiedRange.getEndPosition().isBeforeOrEqual(r.modifiedRange.getStartPosition())))throw new s(`Range mappings must be sorted`)}}constructor(e,t){this.originalRange=e,this.modifiedRange=t}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}flip(){return new e(this.modifiedRange,this.originalRange)}toTextEdit(e){let t=e.getValueOfRange(this.modifiedRange);return new Ei(this.originalRange,t)}},Mi=class{computeDiff(e,t,n){let r=new zi(e,t,{maxComputationTime:n.maxComputationTimeMs,shouldIgnoreTrimWhitespace:n.ignoreTrimWhitespace,shouldComputeCharChanges:!0,shouldMakePrettyDiff:!0,shouldPostProcessCharChanges:!0}).computeDiff(),i=[],a=null;for(let e of r.changes){let t;t=e.originalEndLineNumber===0?new L(e.originalStartLineNumber+1,e.originalStartLineNumber+1):new L(e.originalStartLineNumber,e.originalEndLineNumber+1);let n;n=e.modifiedEndLineNumber===0?new L(e.modifiedStartLineNumber+1,e.modifiedStartLineNumber+1):new L(e.modifiedStartLineNumber,e.modifiedEndLineNumber+1);let r=new Ai(t,n,e.charChanges?.map(e=>new ji(new N(e.originalStartLineNumber,e.originalStartColumn,e.originalEndLineNumber,e.originalEndColumn),new N(e.modifiedStartLineNumber,e.modifiedStartColumn,e.modifiedEndLineNumber,e.modifiedEndColumn))));a&&(a.modified.endLineNumberExclusive===r.modified.startLineNumber||a.original.endLineNumberExclusive===r.original.startLineNumber)&&(r=new Ai(a.original.join(r.original),a.modified.join(r.modified),a.innerChanges&&r.innerChanges?a.innerChanges.concat(r.innerChanges):void 0),i.pop()),i.push(r),a=r}return oi(()=>si(i,(e,t)=>t.original.startLineNumber-e.original.endLineNumberExclusive===t.modified.startLineNumber-e.modified.endLineNumberExclusive&&e.original.endLineNumberExclusive<t.original.startLineNumber&&e.modified.endLineNumberExclusive<t.modified.startLineNumber)),new vi(i,[],r.quitEarly)}};function Ni(e,t,n,r){return new Yt(e,t,n).ComputeDiff(r)}var Pi=class{constructor(e){let t=[],n=[];for(let r=0,i=e.length;r<i;r++)t[r]=Bi(e[r],1),n[r]=Vi(e[r],1);this.lines=e,this._startColumns=t,this._endColumns=n}getElements(){let e=[];for(let t=0,n=this.lines.length;t<n;t++)e[t]=this.lines[t].substring(this._startColumns[t]-1,this._endColumns[t]-1);return e}getStrictElement(e){return this.lines[e]}getStartLineNumber(e){return e+1}getEndLineNumber(e){return e+1}createCharSequence(e,t,n){let r=[],i=[],a=[],o=0;for(let s=t;s<=n;s++){let t=this.lines[s],c=e?this._startColumns[s]:1,l=e?this._endColumns[s]:t.length+1;for(let e=c;e<l;e++)r[o]=t.charCodeAt(e-1),i[o]=s+1,a[o]=e,o++;!e&&s<n&&(r[o]=10,i[o]=s+1,a[o]=t.length+1,o++)}return new Fi(r,i,a)}},Fi=class{constructor(e,t,n){this._charCodes=e,this._lineNumbers=t,this._columns=n}toString(){return`[`+this._charCodes.map((e,t)=>(e===10?`\\n`:String.fromCharCode(e))+`-(${this._lineNumbers[t]},${this._columns[t]})`).join(`, `)+`]`}_assertIndex(e,t){if(e<0||e>=t.length)throw Error(`Illegal index`)}getElements(){return this._charCodes}getStartLineNumber(e){return e>0&&e===this._lineNumbers.length?this.getEndLineNumber(e-1):(this._assertIndex(e,this._lineNumbers),this._lineNumbers[e])}getEndLineNumber(e){return e===-1?this.getStartLineNumber(e+1):(this._assertIndex(e,this._lineNumbers),this._charCodes[e]===10?this._lineNumbers[e]+1:this._lineNumbers[e])}getStartColumn(e){return e>0&&e===this._columns.length?this.getEndColumn(e-1):(this._assertIndex(e,this._columns),this._columns[e])}getEndColumn(e){return e===-1?this.getStartColumn(e+1):(this._assertIndex(e,this._columns),this._charCodes[e]===10?1:this._columns[e]+1)}},Ii=class e{constructor(e,t,n,r,i,a,o,s){this.originalStartLineNumber=e,this.originalStartColumn=t,this.originalEndLineNumber=n,this.originalEndColumn=r,this.modifiedStartLineNumber=i,this.modifiedStartColumn=a,this.modifiedEndLineNumber=o,this.modifiedEndColumn=s}static createFromDiffChange(t,n,r){let i=n.getStartLineNumber(t.originalStart),a=n.getStartColumn(t.originalStart),o=n.getEndLineNumber(t.originalStart+t.originalLength-1),s=n.getEndColumn(t.originalStart+t.originalLength-1),c=r.getStartLineNumber(t.modifiedStart),l=r.getStartColumn(t.modifiedStart),u=r.getEndLineNumber(t.modifiedStart+t.modifiedLength-1),d=r.getEndColumn(t.modifiedStart+t.modifiedLength-1);return new e(i,a,o,s,c,l,u,d)}};function Li(e){if(e.length<=1)return e;let t=[e[0]],n=t[0];for(let r=1,i=e.length;r<i;r++){let i=e[r],a=i.originalStart-(n.originalStart+n.originalLength),o=i.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(a,o)<3?(n.originalLength=i.originalStart+i.originalLength-n.originalStart,n.modifiedLength=i.modifiedStart+i.modifiedLength-n.modifiedStart):(t.push(i),n=i)}return t}var Ri=class e{constructor(e,t,n,r,i){this.originalStartLineNumber=e,this.originalEndLineNumber=t,this.modifiedStartLineNumber=n,this.modifiedEndLineNumber=r,this.charChanges=i}static createFromDiffResult(t,n,r,i,a,o,s){let c,l,u,d,f;if(n.originalLength===0?(c=r.getStartLineNumber(n.originalStart)-1,l=0):(c=r.getStartLineNumber(n.originalStart),l=r.getEndLineNumber(n.originalStart+n.originalLength-1)),n.modifiedLength===0?(u=i.getStartLineNumber(n.modifiedStart)-1,d=0):(u=i.getStartLineNumber(n.modifiedStart),d=i.getEndLineNumber(n.modifiedStart+n.modifiedLength-1)),o&&n.originalLength>0&&n.originalLength<20&&n.modifiedLength>0&&n.modifiedLength<20&&a()){let e=r.createCharSequence(t,n.originalStart,n.originalStart+n.originalLength-1),o=i.createCharSequence(t,n.modifiedStart,n.modifiedStart+n.modifiedLength-1);if(e.getElements().length>0&&o.getElements().length>0){let t=Ni(e,o,a,!0).changes;s&&(t=Li(t)),f=[];for(let n=0,r=t.length;n<r;n++)f.push(Ii.createFromDiffChange(t[n],e,o))}}return new e(c,l,u,d,f)}},zi=class{constructor(e,t,n){this.shouldComputeCharChanges=n.shouldComputeCharChanges,this.shouldPostProcessCharChanges=n.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=n.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=n.shouldMakePrettyDiff,this.originalLines=e,this.modifiedLines=t,this.original=new Pi(e),this.modified=new Pi(t),this.continueLineDiff=Hi(n.maxComputationTime),this.continueCharDiff=Hi(n.maxComputationTime===0?0:Math.min(n.maxComputationTime,5e3))}computeDiff(){if(this.original.lines.length===1&&this.original.lines[0].length===0)return this.modified.lines.length===1&&this.modified.lines[0].length===0?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:void 0}]};if(this.modified.lines.length===1&&this.modified.lines[0].length===0)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:void 0}]};let e=Ni(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),t=e.changes,n=e.quitEarly;if(this.shouldIgnoreTrimWhitespace){let e=[];for(let n=0,r=t.length;n<r;n++)e.push(Ri.createFromDiffResult(this.shouldIgnoreTrimWhitespace,t[n],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:n,changes:e}}let r=[],i=0,a=0;for(let e=-1,n=t.length;e<n;e++){let o=e+1<n?t[e+1]:null,s=o?o.originalStart:this.originalLines.length,c=o?o.modifiedStart:this.modifiedLines.length;for(;i<s&&a<c;){let e=this.originalLines[i],t=this.modifiedLines[a];if(e!==t){{let n=Bi(e,1),o=Bi(t,1);for(;n>1&&o>1;){let r=e.charCodeAt(n-2),i=t.charCodeAt(o-2);if(r!==i)break;n--,o--}(n>1||o>1)&&this._pushTrimWhitespaceCharChange(r,i+1,1,n,a+1,1,o)}{let n=Vi(e,1),o=Vi(t,1),s=e.length+1,c=t.length+1;for(;n<s&&o<c;){let t=e.charCodeAt(n-1),r=e.charCodeAt(o-1);if(t!==r)break;n++,o++}(n<s||o<c)&&this._pushTrimWhitespaceCharChange(r,i+1,n,s,a+1,o,c)}}i++,a++}o&&(r.push(Ri.createFromDiffResult(this.shouldIgnoreTrimWhitespace,o,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),i+=o.originalLength,a+=o.modifiedLength)}return{quitEarly:n,changes:r}}_pushTrimWhitespaceCharChange(e,t,n,r,i,a,o){if(this._mergeTrimWhitespaceCharChange(e,t,n,r,i,a,o))return;let s;this.shouldComputeCharChanges&&(s=[new Ii(t,n,t,r,i,a,i,o)]),e.push(new Ri(t,t,i,i,s))}_mergeTrimWhitespaceCharChange(e,t,n,r,i,a,o){let s=e.length;if(s===0)return!1;let c=e[s-1];return c.originalEndLineNumber===0||c.modifiedEndLineNumber===0?!1:c.originalEndLineNumber===t&&c.modifiedEndLineNumber===i?(this.shouldComputeCharChanges&&c.charChanges&&c.charChanges.push(new Ii(t,n,t,r,i,a,i,o)),!0):c.originalEndLineNumber+1===t&&c.modifiedEndLineNumber+1===i?(c.originalEndLineNumber=t,c.modifiedEndLineNumber=i,this.shouldComputeCharChanges&&c.charChanges&&c.charChanges.push(new Ii(t,n,t,r,i,a,i,o)),!0):!1}};function Bi(e,t){let n=je(e);return n===-1?t:n+1}function Vi(e,t){let n=Me(e);return n===-1?t:n+2}function Hi(e){if(e===0)return()=>!0;let t=Date.now();return()=>Date.now()-t<e}function Ui(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t||e.length!==t.length)return!1;for(let r=0,i=e.length;r<i;r++)if(!n(e[r],t[r]))return!1;return!0}function*Wi(e,t){let n,r;for(let i of e)r!==void 0&&t(r,i)?n.push(i):(n&&(yield n),n=[i]),r=i;n&&(yield n)}function Gi(e,t){for(let n=0;n<=e.length;n++)t(n===0?void 0:e[n-1],n===e.length?void 0:e[n])}function Ki(e,t){for(let n=0;n<e.length;n++)t(n===0?void 0:e[n-1],e[n],n+1===e.length?void 0:e[n+1])}function qi(e,t){for(let n of t)e.push(n)}var Ji;(function(e){function t(e){return e<0}e.isLessThan=t;function n(e){return e<=0}e.isLessThanOrEqual=n;function r(e){return e>0}e.isGreaterThan=r;function i(e){return e===0}e.isNeitherLessOrGreaterThan=i,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0})(Ji||={});function Yi(e,t){return(n,r)=>t(e(n),e(r))}const Xi=(e,t)=>e-t;function Zi(e){return(t,n)=>-e(t,n)}(class e{static{this.empty=new e(e=>{})}constructor(e){this.iterate=e}toArray(){let e=[];return this.iterate(t=>(e.push(t),!0)),e}filter(t){return new e(e=>this.iterate(n=>t(n)?e(n):!0))}map(t){return new e(e=>this.iterate(n=>e(t(n))))}findLast(e){let t;return this.iterate(n=>(e(n)&&(t=n),!0)),t}findLastMaxBy(e){let t,n=!0;return this.iterate(r=>((n||Ji.isGreaterThan(e(r,t)))&&(n=!1,t=r),!0)),t}});var Qi=class e{static trivial(t,n){return new e([new R(I.ofLength(t.length),I.ofLength(n.length))],!1)}static trivialTimedOut(t,n){return new e([new R(I.ofLength(t.length),I.ofLength(n.length))],!0)}constructor(e,t){this.diffs=e,this.hitTimeout=t}},R=class e{static invert(t,n){let r=[];return Gi(t,(t,i)=>{r.push(e.fromOffsetPairs(t?t.getEndExclusives():$i.zero,i?i.getStarts():new $i(n,(t?t.seq2Range.endExclusive-t.seq1Range.endExclusive:0)+n)))}),r}static fromOffsetPairs(t,n){return new e(new I(t.offset1,n.offset1),new I(t.offset2,n.offset2))}static assertSorted(e){let t;for(let n of e){if(t&&!(t.seq1Range.endExclusive<=n.seq1Range.start&&t.seq2Range.endExclusive<=n.seq2Range.start))throw new s(`Sequence diffs must be sorted`);t=n}}constructor(e,t){this.seq1Range=e,this.seq2Range=t}swap(){return new e(this.seq2Range,this.seq1Range)}toString(){return`${this.seq1Range} <-> ${this.seq2Range}`}join(t){return new e(this.seq1Range.join(t.seq1Range),this.seq2Range.join(t.seq2Range))}delta(t){return t===0?this:new e(this.seq1Range.delta(t),this.seq2Range.delta(t))}deltaStart(t){return t===0?this:new e(this.seq1Range.deltaStart(t),this.seq2Range.deltaStart(t))}deltaEnd(t){return t===0?this:new e(this.seq1Range.deltaEnd(t),this.seq2Range.deltaEnd(t))}intersect(t){let n=this.seq1Range.intersect(t.seq1Range),r=this.seq2Range.intersect(t.seq2Range);if(!(!n||!r))return new e(n,r)}getStarts(){return new $i(this.seq1Range.start,this.seq2Range.start)}getEndExclusives(){return new $i(this.seq1Range.endExclusive,this.seq2Range.endExclusive)}},$i=class e{static{this.zero=new e(0,0)}static{this.max=new e(2**53-1,2**53-1)}constructor(e,t){this.offset1=e,this.offset2=t}toString(){return`${this.offset1} <-> ${this.offset2}`}delta(t){return t===0?this:new e(this.offset1+t,this.offset2+t)}equals(e){return this.offset1===e.offset1&&this.offset2===e.offset2}},ea=class e{static{this.instance=new e}isValid(){return!0}},ta=class{constructor(e){if(this.timeout=e,this.startTime=Date.now(),this.valid=!0,e<=0)throw new s(`timeout must be positive`)}isValid(){return!(Date.now()-this.startTime<this.timeout)&&this.valid&&(this.valid=!1),this.valid}},na=class{constructor(e,t){this.width=e,this.height=t,this.array=[],this.array=Array(e*t)}get(e,t){return this.array[e+t*this.width]}set(e,t,n){this.array[e+t*this.width]=n}};function ra(e){return e===32||e===9}var ia=class e{static{this.chrKeys=new Map}static getKey(e){let t=this.chrKeys.get(e);return t===void 0&&(t=this.chrKeys.size,this.chrKeys.set(e,t)),t}constructor(t,n,r){this.range=t,this.lines=n,this.source=r,this.histogram=[];let i=0;for(let r=t.startLineNumber-1;r<t.endLineNumberExclusive-1;r++){let t=n[r];for(let n=0;n<t.length;n++){i++;let r=t[n],a=e.getKey(r);this.histogram[a]=(this.histogram[a]||0)+1}i++;let a=e.getKey(`
`);this.histogram[a]=(this.histogram[a]||0)+1}this.totalCount=i}computeSimilarity(e){let t=0,n=Math.max(this.histogram.length,e.histogram.length);for(let r=0;r<n;r++)t+=Math.abs((this.histogram[r]??0)-(e.histogram[r]??0));return 1-t/(this.totalCount+e.totalCount)}},aa=class{compute(e,t,n=ea.instance,r){if(e.length===0||t.length===0)return Qi.trivial(e,t);let i=new na(e.length,t.length),a=new na(e.length,t.length),o=new na(e.length,t.length);for(let s=0;s<e.length;s++)for(let c=0;c<t.length;c++){if(!n.isValid())return Qi.trivialTimedOut(e,t);let l=s===0?0:i.get(s-1,c),u=c===0?0:i.get(s,c-1),d;e.getElement(s)===t.getElement(c)?(d=s===0||c===0?0:i.get(s-1,c-1),s>0&&c>0&&a.get(s-1,c-1)===3&&(d+=o.get(s-1,c-1)),d+=r?r(s,c):1):d=-1;let f=Math.max(l,u,d);if(f===d){let e=s>0&&c>0?o.get(s-1,c-1):0;o.set(s,c,e+1),a.set(s,c,3)}else f===l?(o.set(s,c,0),a.set(s,c,1)):f===u&&(o.set(s,c,0),a.set(s,c,2));i.set(s,c,f)}let s=[],c=e.length,l=t.length;function u(e,t){(e+1!==c||t+1!==l)&&s.push(new R(new I(e+1,c),new I(t+1,l))),c=e,l=t}let d=e.length-1,f=t.length-1;for(;d>=0&&f>=0;)a.get(d,f)===3?(u(d,f),d--,f--):a.get(d,f)===1?d--:f--;return u(-1,-1),s.reverse(),new Qi(s,!1)}},oa=class{compute(e,t,n=ea.instance){if(e.length===0||t.length===0)return Qi.trivial(e,t);let r=e,i=t;function a(e,t){for(;e<r.length&&t<i.length&&r.getElement(e)===i.getElement(t);)e++,t++;return e}let o=0,s=new ca;s.set(0,a(0,0));let c=new la;c.set(0,s.get(0)===0?null:new sa(null,0,0,s.get(0)));let l=0;loop:for(;;){if(o++,!n.isValid())return Qi.trivialTimedOut(r,i);let e=-Math.min(o,i.length+o%2),t=Math.min(o,r.length+o%2);for(l=e;l<=t;l+=2){let n=0,o=l===t?-1:s.get(l+1),u=l===e?-1:s.get(l-1)+1;n++;let d=Math.min(Math.max(o,u),r.length),f=d-l;if(n++,d>r.length||f>i.length)continue;let p=a(d,f);s.set(l,p);let m=d===o?c.get(l+1):c.get(l-1);if(c.set(l,p===d?m:new sa(m,d,f,p-d)),s.get(l)===r.length&&s.get(l)-l===i.length)break loop}}let u=c.get(l),d=[],f=r.length,p=i.length;for(;;){let e=u?u.x+u.length:0,t=u?u.y+u.length:0;if((e!==f||t!==p)&&d.push(new R(new I(e,f),new I(t,p))),!u)break;f=u.x,p=u.y,u=u.prev}return d.reverse(),new Qi(d,!1)}},sa=class{constructor(e,t,n,r){this.prev=e,this.x=t,this.y=n,this.length=r}},ca=class{constructor(){this.positiveArr=new Int32Array(10),this.negativeArr=new Int32Array(10)}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,t){if(e<0){if(e=-e-1,e>=this.negativeArr.length){let e=this.negativeArr;this.negativeArr=new Int32Array(e.length*2),this.negativeArr.set(e)}this.negativeArr[e]=t}else{if(e>=this.positiveArr.length){let e=this.positiveArr;this.positiveArr=new Int32Array(e.length*2),this.positiveArr.set(e)}this.positiveArr[e]=t}}},la=class{constructor(){this.positiveArr=[],this.negativeArr=[]}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,t){e<0?(e=-e-1,this.negativeArr[e]=t):this.positiveArr[e]=t}},ua=class{constructor(e,t,n){this.lines=e,this.range=t,this.considerWhitespaceChanges=n,this.elements=[],this.firstElementOffsetByLineIdx=[],this.lineStartOffsets=[],this.trimmedWsLengthsByLineIdx=[],this.firstElementOffsetByLineIdx.push(0);for(let t=this.range.startLineNumber;t<=this.range.endLineNumber;t++){let r=e[t-1],i=0;t===this.range.startLineNumber&&this.range.startColumn>1&&(i=this.range.startColumn-1,r=r.substring(i)),this.lineStartOffsets.push(i);let a=0;if(!n){let e=r.trimStart();a=r.length-e.length,r=e.trimEnd()}this.trimmedWsLengthsByLineIdx.push(a);let o=t===this.range.endLineNumber?Math.min(this.range.endColumn-1-i-a,r.length):r.length;for(let e=0;e<o;e++)this.elements.push(r.charCodeAt(e));t<this.range.endLineNumber&&(this.elements.push(10),this.firstElementOffsetByLineIdx.push(this.elements.length))}}toString(){return`Slice: "${this.text}"`}get text(){return this.getText(new I(0,this.length))}getText(e){return this.elements.slice(e.start,e.endExclusive).map(e=>String.fromCharCode(e)).join(``)}getElement(e){return this.elements[e]}get length(){return this.elements.length}getBoundaryScore(e){let t=ma(e>0?this.elements[e-1]:-1),n=ma(e<this.elements.length?this.elements[e]:-1);if(t===7&&n===8)return 0;if(t===8)return 150;let r=0;return t!==n&&(r+=10,t===0&&n===1&&(r+=1)),r+=pa(t),r+=pa(n),r}translateOffset(e,t=`right`){let n=xi(this.firstElementOffsetByLineIdx,t=>t<=e),r=e-this.firstElementOffsetByLineIdx[n];return new M(this.range.startLineNumber+n,1+this.lineStartOffsets[n]+r+(r===0&&t===`left`?0:this.trimmedWsLengthsByLineIdx[n]))}translateRange(e){let t=this.translateOffset(e.start,`right`),n=this.translateOffset(e.endExclusive,`left`);return n.isBefore(t)?N.fromPositions(n,n):N.fromPositions(t,n)}findWordContaining(e){if(e<0||e>=this.elements.length||!da(this.elements[e]))return;let t=e;for(;t>0&&da(this.elements[t-1]);)t--;let n=e;for(;n<this.elements.length&&da(this.elements[n]);)n++;return new I(t,n)}countLinesIn(e){return this.translateOffset(e.endExclusive).lineNumber-this.translateOffset(e.start).lineNumber}isStronglyEqual(e,t){return this.elements[e]===this.elements[t]}extendToFullLines(e){let t=bi(this.firstElementOffsetByLineIdx,t=>t<=e.start)??0,n=Si(this.firstElementOffsetByLineIdx,t=>e.endExclusive<=t)??this.elements.length;return new I(t,n)}};function da(e){return e>=97&&e<=122||e>=65&&e<=90||e>=48&&e<=57}const fa={0:0,1:0,2:0,3:10,4:2,5:30,6:3,7:10,8:10};function pa(e){return fa[e]}function ma(e){return e===10?8:e===13?7:ra(e)?6:e>=97&&e<=122?0:e>=65&&e<=90?1:e>=48&&e<=57?2:e===-1?3:e===44||e===59?5:4}function ha(e,t,n,r,i,a){let{moves:o,excludedChanges:s}=_a(e,t,n,a);if(!a.isValid())return[];let c=e.filter(e=>!s.has(e)),l=va(c,r,i,t,n,a);return qi(o,l),o=ba(o),o=o.filter(e=>{let n=e.original.toOffsetRange().slice(t).map(e=>e.trim());return n.join(`
`).length>=15&&ga(n,e=>e.length>=2)>=2}),o=xa(e,o),o}function ga(e,t){let n=0;for(let r of e)t(r)&&n++;return n}function _a(e,t,n,r){let i=[],a=e.filter(e=>e.modified.isEmpty&&e.original.length>=3).map(e=>new ia(e.original,t,e)),o=new Set(e.filter(e=>e.original.isEmpty&&e.modified.length>=3).map(e=>new ia(e.modified,n,e))),s=new Set;for(let e of a){let t=-1,n;for(let r of o){let i=e.computeSimilarity(r);i>t&&(t=i,n=r)}if(t>.9&&n&&(o.delete(n),i.push(new Di(e.range,n.range)),s.add(e.source),s.add(n.source)),!r.isValid())return{moves:i,excludedChanges:s}}return{moves:i,excludedChanges:s}}function va(e,t,n,r,i,a){let o=[],s=new Jr;for(let n of e)for(let e=n.original.startLineNumber;e<n.original.endLineNumberExclusive-2;e++){let n=`${t[e-1]}:${t[e+1-1]}:${t[e+2-1]}`;s.add(n,{range:new L(e,e+3)})}let c=[];e.sort(Yi(e=>e.modified.startLineNumber,Xi));for(let t of e){let e=[];for(let r=t.modified.startLineNumber;r<t.modified.endLineNumberExclusive-2;r++){let t=`${n[r-1]}:${n[r+1-1]}:${n[r+2-1]}`,i=new L(r,r+3),a=[];s.forEach(t,({range:t})=>{for(let n of e)if(n.originalLineRange.endLineNumberExclusive+1===t.endLineNumberExclusive&&n.modifiedLineRange.endLineNumberExclusive+1===i.endLineNumberExclusive){n.originalLineRange=new L(n.originalLineRange.startLineNumber,t.endLineNumberExclusive),n.modifiedLineRange=new L(n.modifiedLineRange.startLineNumber,i.endLineNumberExclusive),a.push(n);return}let n={modifiedLineRange:i,originalLineRange:t};c.push(n),a.push(n)}),e=a}if(!a.isValid())return[]}c.sort(Zi(Yi(e=>e.modifiedLineRange.length,Xi)));let l=new Ti,u=new Ti;for(let e of c){let t=e.modifiedLineRange.startLineNumber-e.originalLineRange.startLineNumber,n=l.subtractFrom(e.modifiedLineRange),r=u.subtractFrom(e.originalLineRange).getWithDelta(t),i=n.getIntersection(r);for(let e of i.ranges){if(e.length<3)continue;let n=e,r=e.delta(-t);o.push(new Di(r,n)),l.addRange(n),u.addRange(r)}}o.sort(Yi(e=>e.original.startLineNumber,Xi));let d=new wi(e);for(let t=0;t<o.length;t++){let n=o[t],s=d.findLastMonotonous(e=>e.original.startLineNumber<=n.original.startLineNumber),c=bi(e,e=>e.modified.startLineNumber<=n.modified.startLineNumber),f=Math.max(n.original.startLineNumber-s.original.startLineNumber,n.modified.startLineNumber-c.modified.startLineNumber),p=d.findLastMonotonous(e=>e.original.startLineNumber<n.original.endLineNumberExclusive),m=bi(e,e=>e.modified.startLineNumber<n.modified.endLineNumberExclusive),h=Math.max(p.original.endLineNumberExclusive-n.original.endLineNumberExclusive,m.modified.endLineNumberExclusive-n.modified.endLineNumberExclusive),g;for(g=0;g<f;g++){let e=n.original.startLineNumber-g-1,t=n.modified.startLineNumber-g-1;if(e>r.length||t>i.length||l.contains(t)||u.contains(e)||!ya(r[e-1],i[t-1],a))break}g>0&&(u.addRange(new L(n.original.startLineNumber-g,n.original.startLineNumber)),l.addRange(new L(n.modified.startLineNumber-g,n.modified.startLineNumber)));let _;for(_=0;_<h;_++){let e=n.original.endLineNumberExclusive+_,t=n.modified.endLineNumberExclusive+_;if(e>r.length||t>i.length||l.contains(t)||u.contains(e)||!ya(r[e-1],i[t-1],a))break}_>0&&(u.addRange(new L(n.original.endLineNumberExclusive,n.original.endLineNumberExclusive+_)),l.addRange(new L(n.modified.endLineNumberExclusive,n.modified.endLineNumberExclusive+_))),(g>0||_>0)&&(o[t]=new Di(new L(n.original.startLineNumber-g,n.original.endLineNumberExclusive+_),new L(n.modified.startLineNumber-g,n.modified.endLineNumberExclusive+_)))}return o}function ya(e,t,n){if(e.trim()===t.trim())return!0;if(e.length>300&&t.length>300)return!1;let r=new oa().compute(new ua([e],new N(1,1,1,e.length),!1),new ua([t],new N(1,1,1,t.length),!1),n),i=0,a=R.invert(r.diffs,e.length);for(let t of a)t.seq1Range.forEach(t=>{ra(e.charCodeAt(t))||i++});function o(t){let n=0;for(let r=0;r<e.length;r++)ra(t.charCodeAt(r))||n++;return n}let s=o(e.length>t.length?e:t);return i/s>.6&&s>10}function ba(e){if(e.length===0)return e;e.sort(Yi(e=>e.original.startLineNumber,Xi));let t=[e[0]];for(let n=1;n<e.length;n++){let r=t[t.length-1],i=e[n],a=i.original.startLineNumber-r.original.endLineNumberExclusive,o=i.modified.startLineNumber-r.modified.endLineNumberExclusive;if(a>=0&&o>=0&&a+o<=2){t[t.length-1]=r.join(i);continue}t.push(i)}return t}function xa(e,t){let n=new wi(e);return t=t.filter(t=>{let r=n.findLastMonotonous(e=>e.original.startLineNumber<t.original.endLineNumberExclusive)||new Di(new L(1,1),new L(1,1)),i=bi(e,e=>e.modified.startLineNumber<t.modified.endLineNumberExclusive);return r!==i}),t}function Sa(e,t,n){let r=n;return r=Ca(e,t,r),r=Ca(e,t,r),r=wa(e,t,r),r}function Ca(e,t,n){if(n.length===0)return n;let r=[];r.push(n[0]);for(let i=1;i<n.length;i++){let a=r[r.length-1],o=n[i];if(o.seq1Range.isEmpty||o.seq2Range.isEmpty){let n=o.seq1Range.start-a.seq1Range.endExclusive,i;for(i=1;i<=n&&!(e.getElement(o.seq1Range.start-i)!==e.getElement(o.seq1Range.endExclusive-i)||t.getElement(o.seq2Range.start-i)!==t.getElement(o.seq2Range.endExclusive-i));i++);if(i--,i===n){r[r.length-1]=new R(new I(a.seq1Range.start,o.seq1Range.endExclusive-n),new I(a.seq2Range.start,o.seq2Range.endExclusive-n));continue}o=o.delta(-i)}r.push(o)}let i=[];for(let n=0;n<r.length-1;n++){let a=r[n+1],o=r[n];if(o.seq1Range.isEmpty||o.seq2Range.isEmpty){let i=a.seq1Range.start-o.seq1Range.endExclusive,s;for(s=0;s<i&&!(!e.isStronglyEqual(o.seq1Range.start+s,o.seq1Range.endExclusive+s)||!t.isStronglyEqual(o.seq2Range.start+s,o.seq2Range.endExclusive+s));s++);if(s===i){r[n+1]=new R(new I(o.seq1Range.start+i,a.seq1Range.endExclusive),new I(o.seq2Range.start+i,a.seq2Range.endExclusive));continue}s>0&&(o=o.delta(s))}i.push(o)}return r.length>0&&i.push(r[r.length-1]),i}function wa(e,t,n){if(!e.getBoundaryScore||!t.getBoundaryScore)return n;for(let r=0;r<n.length;r++){let i=r>0?n[r-1]:void 0,a=n[r],o=r+1<n.length?n[r+1]:void 0,s=new I(i?i.seq1Range.endExclusive+1:0,o?o.seq1Range.start-1:e.length),c=new I(i?i.seq2Range.endExclusive+1:0,o?o.seq2Range.start-1:t.length);a.seq1Range.isEmpty?n[r]=Ta(a,e,t,s,c):a.seq2Range.isEmpty&&(n[r]=Ta(a.swap(),t,e,c,s).swap())}return n}function Ta(e,t,n,r,i){let a=1;for(;e.seq1Range.start-a>=r.start&&e.seq2Range.start-a>=i.start&&n.isStronglyEqual(e.seq2Range.start-a,e.seq2Range.endExclusive-a)&&a<100;)a++;a--;let o=0;for(;e.seq1Range.start+o<r.endExclusive&&e.seq2Range.endExclusive+o<i.endExclusive&&n.isStronglyEqual(e.seq2Range.start+o,e.seq2Range.endExclusive+o)&&o<100;)o++;if(a===0&&o===0)return e;let s=0,c=-1;for(let r=-a;r<=o;r++){let i=e.seq2Range.start+r,a=e.seq2Range.endExclusive+r,o=e.seq1Range.start+r,l=t.getBoundaryScore(o)+n.getBoundaryScore(i)+n.getBoundaryScore(a);l>c&&(c=l,s=r)}return e.delta(s)}function Ea(e,t,n){let r=[];for(let e of n){let t=r[r.length-1];if(!t){r.push(e);continue}e.seq1Range.start-t.seq1Range.endExclusive<=2||e.seq2Range.start-t.seq2Range.endExclusive<=2?r[r.length-1]=new R(t.seq1Range.join(e.seq1Range),t.seq2Range.join(e.seq2Range)):r.push(e)}return r}function Da(e,t,n){let r=R.invert(n,e.length),i=[],a=new $i(0,0);function o(n,o){if(n.offset1<a.offset1||n.offset2<a.offset2)return;let s=e.findWordContaining(n.offset1),c=t.findWordContaining(n.offset2);if(!s||!c)return;let l=new R(s,c),u=l.intersect(o),d=u.seq1Range.length,f=u.seq2Range.length;for(;r.length>0;){let n=r[0];if(!(n.seq1Range.intersects(l.seq1Range)||n.seq2Range.intersects(l.seq2Range)))break;let i=e.findWordContaining(n.seq1Range.start),a=t.findWordContaining(n.seq2Range.start),o=new R(i,a),s=o.intersect(n);if(d+=s.seq1Range.length,f+=s.seq2Range.length,l=l.join(o),l.seq1Range.endExclusive>=n.seq1Range.endExclusive)r.shift();else break}d+f<(l.seq1Range.length+l.seq2Range.length)*2/3&&i.push(l),a=l.getEndExclusives()}for(;r.length>0;){let e=r.shift();e.seq1Range.isEmpty||(o(e.getStarts(),e),o(e.getEndExclusives().delta(-1),e))}return Oa(n,i)}function Oa(e,t){let n=[];for(;e.length>0||t.length>0;){let r=e[0],i=t[0],a;a=r&&(!i||r.seq1Range.start<i.seq1Range.start)?e.shift():t.shift(),n.length>0&&n[n.length-1].seq1Range.endExclusive>=a.seq1Range.start?n[n.length-1]=n[n.length-1].join(a):n.push(a)}return n}function ka(e,t,n){let r=n;if(r.length===0)return r;let i=0,a;do{a=!1;let t=[r[0]];for(let n=1;n<r.length;n++){let i=r[n],o=t[t.length-1];function s(t,n){let r=new I(o.seq1Range.endExclusive,i.seq1Range.start);return e.getText(r).replace(/\s/g,``).length<=4&&(t.seq1Range.length+t.seq2Range.length>5||n.seq1Range.length+n.seq2Range.length>5)}s(o,i)?(a=!0,t[t.length-1]=t[t.length-1].join(i)):t.push(i)}r=t}while(i++<10&&a);return r}function Aa(e,t,n){let r=n;if(r.length===0)return r;let i=0,a;do{a=!1;let n=[r[0]];for(let i=1;i<r.length;i++){let o=r[i],s=n[n.length-1];function c(n,r){let i=new I(s.seq1Range.endExclusive,o.seq1Range.start);if(e.countLinesIn(i)>5||i.length>500)return!1;let a=e.getText(i).trim();if(a.length>20||a.split(/\r\n|\r|\n/).length>1)return!1;let c=e.countLinesIn(n.seq1Range),l=n.seq1Range.length,u=t.countLinesIn(n.seq2Range),d=n.seq2Range.length,f=e.countLinesIn(r.seq1Range),p=r.seq1Range.length,m=t.countLinesIn(r.seq2Range),h=r.seq2Range.length;function g(e){return Math.min(e,130)}return(g(c*40+l)**1.5+g(u*40+d)**1.5)**1.5+(g(f*40+p)**1.5+g(m*40+h)**1.5)**1.5>(130**1.5)**1.5*1.3}c(s,o)?(a=!0,n[n.length-1]=n[n.length-1].join(o)):n.push(o)}r=n}while(i++<10&&a);let o=[];return Ki(r,(t,n,r)=>{let i=n;function a(e){return e.length>0&&e.trim().length<=3&&n.seq1Range.length+n.seq2Range.length>100}let s=e.extendToFullLines(n.seq1Range),c=e.getText(new I(s.start,n.seq1Range.start));a(c)&&(i=i.deltaStart(-c.length));let l=e.getText(new I(n.seq1Range.endExclusive,s.endExclusive));a(l)&&(i=i.deltaEnd(l.length));let u=R.fromOffsetPairs(t?t.getEndExclusives():$i.zero,r?r.getStarts():$i.max),d=i.intersect(u);o.length>0&&d.getStarts().equals(o[o.length-1].getEndExclusives())?o[o.length-1]=o[o.length-1].join(d):o.push(d)}),o}var ja=class{constructor(e,t){this.trimmedHash=e,this.lines=t}getElement(e){return this.trimmedHash[e]}get length(){return this.trimmedHash.length}getBoundaryScore(e){let t=e===0?0:Ma(this.lines[e-1]),n=e===this.lines.length?0:Ma(this.lines[e]);return 1e3-(t+n)}getText(e){return this.lines.slice(e.start,e.endExclusive).join(`
`)}isStronglyEqual(e,t){return this.lines[e]===this.lines[t]}};function Ma(e){let t=0;for(;t<e.length&&(e.charCodeAt(t)===32||e.charCodeAt(t)===9);)t++;return t}var Na=class{constructor(){this.dynamicProgrammingDiffing=new aa,this.myersDiffingAlgorithm=new oa}computeDiff(e,t,n){if(e.length<=1&&Ui(e,t,(e,t)=>e===t))return new vi([],[],!1);if(e.length===1&&e[0].length===0||t.length===1&&t[0].length===0)return new vi([new Ai(new L(1,e.length+1),new L(1,t.length+1),[new ji(new N(1,1,e.length,e[e.length-1].length+1),new N(1,1,t.length,t[t.length-1].length+1))])],[],!1);let r=n.maxComputationTimeMs===0?ea.instance:new ta(n.maxComputationTimeMs),i=!n.ignoreTrimWhitespace,a=new Map;function o(e){let t=a.get(e);return t===void 0&&(t=a.size,a.set(e,t)),t}let s=e.map(e=>o(e.trim())),c=t.map(e=>o(e.trim())),l=new ja(s,e),u=new ja(c,t),d=(()=>l.length+u.length<1700?this.dynamicProgrammingDiffing.compute(l,u,r,(n,r)=>e[n]===t[r]?t[r].length===0?.1:1+Math.log(1+t[r].length):.99):this.myersDiffingAlgorithm.compute(l,u,r))(),f=d.diffs,p=d.hitTimeout;f=Sa(l,u,f),f=ka(l,u,f);let m=[],h=n=>{if(i)for(let a=0;a<n;a++){let n=g+a,o=_+a;if(e[n]!==t[o]){let a=this.refineDiff(e,t,new R(new I(n,n+1),new I(o,o+1)),r,i);for(let e of a.mappings)m.push(e);a.hitTimeout&&(p=!0)}}},g=0,_=0;for(let n of f){oi(()=>n.seq1Range.start-g===n.seq2Range.start-_);let a=n.seq1Range.start-g;h(a),g=n.seq1Range.endExclusive,_=n.seq2Range.endExclusive;let o=this.refineDiff(e,t,n,r,i);o.hitTimeout&&(p=!0);for(let e of o.mappings)m.push(e)}h(e.length-g);let v=Pa(m,e,t),y=[];return n.computeMoves&&(y=this.computeMoves(v,e,t,s,c,r,i)),oi(()=>{function n(e,t){if(e.lineNumber<1||e.lineNumber>t.length)return!1;let n=t[e.lineNumber-1];return!(e.column<1||e.column>n.length+1)}function r(e,t){return!(e.startLineNumber<1||e.startLineNumber>t.length+1||e.endLineNumberExclusive<1||e.endLineNumberExclusive>t.length+1)}for(let i of v){if(!i.innerChanges)return!1;for(let r of i.innerChanges)if(!(n(r.modifiedRange.getStartPosition(),t)&&n(r.modifiedRange.getEndPosition(),t)&&n(r.originalRange.getStartPosition(),e)&&n(r.originalRange.getEndPosition(),e)))return!1;if(!r(i.modified,t)||!r(i.original,e))return!1}return!0}),new vi(v,y,p)}computeMoves(e,t,n,r,i,a,o){return ha(e,t,n,r,i,a).map(e=>{let r=this.refineDiff(t,n,new R(e.original.toOffsetRange(),e.modified.toOffsetRange()),a,o),i=Pa(r.mappings,t,n,!0);return new yi(e,i)})}refineDiff(e,t,n,r,i){let a=Ia(n).toRangeMapping2(e,t),o=new ua(e,a.originalRange,i),s=new ua(t,a.modifiedRange,i),c=o.length+s.length<500?this.dynamicProgrammingDiffing.compute(o,s,r):this.myersDiffingAlgorithm.compute(o,s,r),l=c.diffs;return l=Sa(o,s,l),l=Da(o,s,l),l=Ea(o,s,l),l=Aa(o,s,l),{mappings:l.map(e=>new ji(o.translateRange(e.seq1Range),s.translateRange(e.seq2Range))),hitTimeout:c.hitTimeout}}};function Pa(e,t,n,r=!1){let i=[];for(let r of Wi(e.map(e=>Fa(e,t,n)),(e,t)=>e.original.overlapOrTouch(t.original)||e.modified.overlapOrTouch(t.modified))){let e=r[0],t=r[r.length-1];i.push(new Ai(e.original.join(t.original),e.modified.join(t.modified),r.map(e=>e.innerChanges[0])))}return oi(()=>!r&&i.length>0&&(i[0].modified.startLineNumber!==i[0].original.startLineNumber||n.length-i[i.length-1].modified.endLineNumberExclusive!==t.length-i[i.length-1].original.endLineNumberExclusive)?!1:si(i,(e,t)=>t.original.startLineNumber-e.original.endLineNumberExclusive===t.modified.startLineNumber-e.modified.endLineNumberExclusive&&e.original.endLineNumberExclusive<t.original.startLineNumber&&e.modified.endLineNumberExclusive<t.modified.startLineNumber)),i}function Fa(e,t,n){let r=0,i=0;e.modifiedRange.endColumn===1&&e.originalRange.endColumn===1&&e.originalRange.startLineNumber+r<=e.originalRange.endLineNumber&&e.modifiedRange.startLineNumber+r<=e.modifiedRange.endLineNumber&&(i=-1),e.modifiedRange.startColumn-1>=n[e.modifiedRange.startLineNumber-1].length&&e.originalRange.startColumn-1>=t[e.originalRange.startLineNumber-1].length&&e.originalRange.startLineNumber<=e.originalRange.endLineNumber+i&&e.modifiedRange.startLineNumber<=e.modifiedRange.endLineNumber+i&&(r=1);let a=new L(e.originalRange.startLineNumber+r,e.originalRange.endLineNumber+1+i),o=new L(e.modifiedRange.startLineNumber+r,e.modifiedRange.endLineNumber+1+i);return new Ai(a,o,[e])}function Ia(e){return new Di(new L(e.seq1Range.start+1,e.seq1Range.endExclusive+1),new L(e.seq2Range.start+1,e.seq2Range.endExclusive+1))}const La={getLegacy:()=>new Mi,getDefault:()=>new Na};function Ra(e,t){let n=10**t;return Math.round(e*n)/n}var z=class{constructor(e,t,n,r=1){this._rgbaBrand=void 0,this.r=Math.min(255,Math.max(0,e))|0,this.g=Math.min(255,Math.max(0,t))|0,this.b=Math.min(255,Math.max(0,n))|0,this.a=Ra(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.r===t.r&&e.g===t.g&&e.b===t.b&&e.a===t.a}},za=class e{constructor(e,t,n,r){this._hslaBrand=void 0,this.h=Math.max(Math.min(360,e),0)|0,this.s=Ra(Math.max(Math.min(1,t),0),3),this.l=Ra(Math.max(Math.min(1,n),0),3),this.a=Ra(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.h===t.h&&e.s===t.s&&e.l===t.l&&e.a===t.a}static fromRGBA(t){let n=t.r/255,r=t.g/255,i=t.b/255,a=t.a,o=Math.max(n,r,i),s=Math.min(n,r,i),c=0,l=0,u=(s+o)/2,d=o-s;if(d>0){switch(l=Math.min(u<=.5?d/(2*u):d/(2-2*u),1),o){case n:c=(r-i)/d+(r<i?6:0);break;case r:c=(i-n)/d+2;break;case i:c=(n-r)/d+4;break}c*=60,c=Math.round(c)}return new e(c,l,u,a)}static _hue2rgb(e,t,n){return n<0&&(n+=1),n>1&&--n,n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}static toRGBA(t){let n=t.h/360,{s:r,l:i,a}=t,o,s,c;if(r===0)o=s=c=i;else{let t=i<.5?i*(1+r):i+r-i*r,a=2*i-t;o=e._hue2rgb(a,t,n+1/3),s=e._hue2rgb(a,t,n),c=e._hue2rgb(a,t,n-1/3)}return new z(Math.round(o*255),Math.round(s*255),Math.round(c*255),a)}},Ba=class e{constructor(e,t,n,r){this._hsvaBrand=void 0,this.h=Math.max(Math.min(360,e),0)|0,this.s=Ra(Math.max(Math.min(1,t),0),3),this.v=Ra(Math.max(Math.min(1,n),0),3),this.a=Ra(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.h===t.h&&e.s===t.s&&e.v===t.v&&e.a===t.a}static fromRGBA(t){let n=t.r/255,r=t.g/255,i=t.b/255,a=Math.max(n,r,i),o=a-Math.min(n,r,i),s=a===0?0:o/a,c;return c=o===0?0:a===n?((r-i)/o%6+6)%6:a===r?(i-n)/o+2:(n-r)/o+4,new e(Math.round(c*60),s,a,t.a)}static toRGBA(e){let{h:t,s:n,v:r,a:i}=e,a=r*n,o=a*(1-Math.abs(t/60%2-1)),s=r-a,[c,l,u]=[0,0,0];return t<60?(c=a,l=o):t<120?(c=o,l=a):t<180?(l=a,u=o):t<240?(l=o,u=a):t<300?(c=o,u=a):t<=360&&(c=a,u=o),c=Math.round((c+s)*255),l=Math.round((l+s)*255),u=Math.round((u+s)*255),new z(c,l,u,i)}},Va=class e{static fromHex(t){return e.Format.CSS.parseHex(t)||e.red}static equals(e,t){return!e&&!t?!0:!e||!t?!1:e.equals(t)}get hsla(){return this._hsla?this._hsla:za.fromRGBA(this.rgba)}get hsva(){return this._hsva?this._hsva:Ba.fromRGBA(this.rgba)}constructor(e){if(e)if(e instanceof z)this.rgba=e;else if(e instanceof za)this._hsla=e,this.rgba=za.toRGBA(e);else if(e instanceof Ba)this._hsva=e,this.rgba=Ba.toRGBA(e);else throw Error(`Invalid color ctor argument`);else throw Error(`Color needs a value`)}equals(e){return!!e&&z.equals(this.rgba,e.rgba)&&za.equals(this.hsla,e.hsla)&&Ba.equals(this.hsva,e.hsva)}getRelativeLuminance(){let t=e._relativeLuminanceForComponent(this.rgba.r),n=e._relativeLuminanceForComponent(this.rgba.g),r=e._relativeLuminanceForComponent(this.rgba.b),i=.2126*t+.7152*n+.0722*r;return Ra(i,4)}static _relativeLuminanceForComponent(e){let t=e/255;return t<=.03928?t/12.92:((t+.055)/1.055)**2.4}isLighter(){return(this.rgba.r*299+this.rgba.g*587+this.rgba.b*114)/1e3>=128}isLighterThan(e){let t=this.getRelativeLuminance(),n=e.getRelativeLuminance();return t>n}isDarkerThan(e){let t=this.getRelativeLuminance(),n=e.getRelativeLuminance();return t<n}lighten(t){return new e(new za(this.hsla.h,this.hsla.s,this.hsla.l+this.hsla.l*t,this.hsla.a))}darken(t){return new e(new za(this.hsla.h,this.hsla.s,this.hsla.l-this.hsla.l*t,this.hsla.a))}transparent(t){let{r:n,g:r,b:i,a}=this.rgba;return new e(new z(n,r,i,a*t))}isTransparent(){return this.rgba.a===0}isOpaque(){return this.rgba.a===1}opposite(){return new e(new z(255-this.rgba.r,255-this.rgba.g,255-this.rgba.b,this.rgba.a))}makeOpaque(t){if(this.isOpaque()||t.rgba.a!==1)return this;let{r:n,g:r,b:i,a}=this.rgba;return new e(new z(t.rgba.r-a*(t.rgba.r-n),t.rgba.g-a*(t.rgba.g-r),t.rgba.b-a*(t.rgba.b-i),1))}toString(){return this._toString||=e.Format.CSS.format(this),this._toString}static getLighterColor(e,t,n){if(e.isLighterThan(t))return e;n||=.5;let r=e.getRelativeLuminance(),i=t.getRelativeLuminance();return n=n*(i-r)/i,e.lighten(n)}static getDarkerColor(e,t,n){if(e.isDarkerThan(t))return e;n||=.5;let r=e.getRelativeLuminance(),i=t.getRelativeLuminance();return n=n*(r-i)/r,e.darken(n)}static{this.white=new e(new z(255,255,255,1))}static{this.black=new e(new z(0,0,0,1))}static{this.red=new e(new z(255,0,0,1))}static{this.blue=new e(new z(0,0,255,1))}static{this.green=new e(new z(0,255,0,1))}static{this.cyan=new e(new z(0,255,255,1))}static{this.lightgrey=new e(new z(211,211,211,1))}static{this.transparent=new e(new z(0,0,0,0))}};(function(e){(function(t){(function(t){function n(t){return t.rgba.a===1?`rgb(${t.rgba.r}, ${t.rgba.g}, ${t.rgba.b})`:e.Format.CSS.formatRGBA(t)}t.formatRGB=n;function r(e){return`rgba(${e.rgba.r}, ${e.rgba.g}, ${e.rgba.b}, ${+e.rgba.a.toFixed(2)})`}t.formatRGBA=r;function i(t){return t.hsla.a===1?`hsl(${t.hsla.h}, ${(t.hsla.s*100).toFixed(2)}%, ${(t.hsla.l*100).toFixed(2)}%)`:e.Format.CSS.formatHSLA(t)}t.formatHSL=i;function a(e){return`hsla(${e.hsla.h}, ${(e.hsla.s*100).toFixed(2)}%, ${(e.hsla.l*100).toFixed(2)}%, ${e.hsla.a.toFixed(2)})`}t.formatHSLA=a;function o(e){let t=e.toString(16);return t.length===2?t:`0`+t}function s(e){return`#${o(e.rgba.r)}${o(e.rgba.g)}${o(e.rgba.b)}`}t.formatHex=s;function c(t,n=!1){return n&&t.rgba.a===1?e.Format.CSS.formatHex(t):`#${o(t.rgba.r)}${o(t.rgba.g)}${o(t.rgba.b)}${o(Math.round(t.rgba.a*255))}`}t.formatHexA=c;function l(t){return t.isOpaque()?e.Format.CSS.formatHex(t):e.Format.CSS.formatRGBA(t)}t.format=l;function u(t){let n=t.length;if(n===0||t.charCodeAt(0)!==35)return null;if(n===7){let n=16*d(t.charCodeAt(1))+d(t.charCodeAt(2)),r=16*d(t.charCodeAt(3))+d(t.charCodeAt(4)),i=16*d(t.charCodeAt(5))+d(t.charCodeAt(6));return new e(new z(n,r,i,1))}if(n===9){let n=16*d(t.charCodeAt(1))+d(t.charCodeAt(2)),r=16*d(t.charCodeAt(3))+d(t.charCodeAt(4)),i=16*d(t.charCodeAt(5))+d(t.charCodeAt(6)),a=16*d(t.charCodeAt(7))+d(t.charCodeAt(8));return new e(new z(n,r,i,a/255))}if(n===4){let n=d(t.charCodeAt(1)),r=d(t.charCodeAt(2)),i=d(t.charCodeAt(3));return new e(new z(16*n+n,16*r+r,16*i+i))}if(n===5){let n=d(t.charCodeAt(1)),r=d(t.charCodeAt(2)),i=d(t.charCodeAt(3)),a=d(t.charCodeAt(4));return new e(new z(16*n+n,16*r+r,16*i+i,(16*a+a)/255))}return null}t.parseHex=u;function d(e){switch(e){case 48:return 0;case 49:return 1;case 50:return 2;case 51:return 3;case 52:return 4;case 53:return 5;case 54:return 6;case 55:return 7;case 56:return 8;case 57:return 9;case 97:return 10;case 65:return 10;case 98:return 11;case 66:return 11;case 99:return 12;case 67:return 12;case 100:return 13;case 68:return 13;case 101:return 14;case 69:return 14;case 102:return 15;case 70:return 15}return 0}})(t.CSS||={})})(e.Format||={})})(Va||={});function Ha(e){let t=[];for(let n of e){let e=Number(n);(e||e===0&&n.replace(/\s/g,``)!==``)&&t.push(e)}return t}function Ua(e,t,n,r){return{red:e/255,blue:n/255,green:t/255,alpha:r}}function Wa(e,t){let n=t.index,r=t[0].length;if(!n)return;let i=e.positionAt(n);return{startLineNumber:i.lineNumber,startColumn:i.column,endLineNumber:i.lineNumber,endColumn:i.column+r}}function Ga(e,t){if(!e)return;let n=Va.Format.CSS.parseHex(t);if(n)return{range:e,color:Ua(n.rgba.r,n.rgba.g,n.rgba.b,n.rgba.a)}}function Ka(e,t,n){if(!e||t.length!==1)return;let r=t[0].values(),i=Ha(r);return{range:e,color:Ua(i[0],i[1],i[2],n?i[3]:1)}}function qa(e,t,n){if(!e||t.length!==1)return;let r=t[0].values(),i=Ha(r),a=new Va(new za(i[0],i[1]/100,i[2]/100,n?i[3]:1));return{range:e,color:Ua(a.rgba.r,a.rgba.g,a.rgba.b,a.rgba.a)}}function Ja(e,t){return typeof e==`string`?[...e.matchAll(t)]:e.findMatches(t)}function Ya(e){let t=[],n=Ja(e,/\b(rgb|rgba|hsl|hsla)(\([0-9\s,.\%]*\))|(#)([A-Fa-f0-9]{3})\b|(#)([A-Fa-f0-9]{4})\b|(#)([A-Fa-f0-9]{6})\b|(#)([A-Fa-f0-9]{8})\b/gm);if(n.length>0)for(let r of n){let n=r.filter(e=>e!==void 0),i=n[1],a=n[2];if(!a)continue;let o;i===`rgb`?o=Ka(Wa(e,r),Ja(a,/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*\)$/gm),!1):i===`rgba`?o=Ka(Wa(e,r),Ja(a,/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm),!0):i===`hsl`?o=qa(Wa(e,r),Ja(a,/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*\)$/gm),!1):i===`hsla`?o=qa(Wa(e,r),Ja(a,/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm),!0):i===`#`&&(o=Ga(Wa(e,r),i+a)),o&&t.push(o)}return t}function Xa(e){return!e||typeof e.getValue!=`function`||typeof e.positionAt!=`function`?[]:Ya(e)}const Za=RegExp(`\\bMARK:\\s*(.*)$`,`d`),Qa=/^-+|-+$/g;function $a(e,t){let n=[];if(t.findRegionSectionHeaders&&t.foldingRules?.markers){let r=eo(e,t);n=n.concat(r)}if(t.findMarkSectionHeaders){let t=to(e);n=n.concat(t)}return n}function eo(e,t){let n=[],r=e.getLineCount();for(let i=1;i<=r;i++){let r=e.getLineContent(i),a=r.match(t.foldingRules.markers.start);if(a){let e={startLineNumber:i,startColumn:a[0].length+1,endLineNumber:i,endColumn:r.length+1};if(e.endColumn>e.startColumn){let t={range:e,...ro(r.substring(a[0].length)),shouldBeInComments:!1};(t.text||t.hasSeparatorLine)&&n.push(t)}}}return n}function to(e){let t=[],n=e.getLineCount();for(let r=1;r<=n;r++){let n=e.getLineContent(r);no(n,r,t)}return t}function no(e,t,n){Za.lastIndex=0;let r=Za.exec(e);if(r){let e=r.indices[1][0]+1,i=r.indices[1][1]+1,a={startLineNumber:t,startColumn:e,endLineNumber:t,endColumn:i};if(a.endColumn>a.startColumn){let e={range:a,...ro(r[1]),shouldBeInComments:!0};(e.text||e.hasSeparatorLine)&&n.push(e)}}}function ro(e){e=e.trim();let t=e.startsWith(`-`);return e=e.replace(Qa,``),{text:e,hasSeparatorLine:t}}(function(){typeof globalThis.requestIdleCallback!=`function`||globalThis.cancelIdleCallback})();var io;(function(e){async function t(e){let t,n=await Promise.all(e.map(e=>e.then(e=>e,e=>{t||=e})));if(t!==void 0)throw t;return n}e.settled=t;function n(e){return new Promise(async(t,n)=>{try{await e(t,n)}catch(e){n(e)}})}e.withAsyncBody=n})(io||={}),class e{static fromArray(t){return new e(e=>{e.emitMany(t)})}static fromPromise(t){return new e(async e=>{e.emitMany(await t)})}static fromPromises(t){return new e(async e=>{await Promise.all(t.map(async t=>e.emitOne(await t)))})}static merge(t){return new e(async e=>{await Promise.all(t.map(async t=>{for await(let n of t)e.emitOne(n)}))})}static{this.EMPTY=e.fromArray([])}constructor(e,t){this._state=0,this._results=[],this._error=null,this._onReturn=t,this._onStateChanged=new T,queueMicrotask(async()=>{let t={emitOne:e=>this.emitOne(e),emitMany:e=>this.emitMany(e),reject:e=>this.reject(e)};try{await Promise.resolve(e(t)),this.resolve()}catch(e){this.reject(e)}finally{t.emitOne=void 0,t.emitMany=void 0,t.reject=void 0}})}[Symbol.asyncIterator](){let e=0;return{next:async()=>{do{if(this._state===2)throw this._error;if(e<this._results.length)return{done:!1,value:this._results[e++]};if(this._state===1)return{done:!0,value:void 0};await S.toPromise(this._onStateChanged.event)}while(!0)},return:async()=>(this._onReturn?.(),{done:!0,value:void 0})}}static map(t,n){return new e(async e=>{for await(let r of t)e.emitOne(n(r))})}map(t){return e.map(this,t)}static filter(t,n){return new e(async e=>{for await(let r of t)n(r)&&e.emitOne(r)})}filter(t){return e.filter(this,t)}static coalesce(t){return e.filter(t,e=>!!e)}coalesce(){return e.coalesce(this)}static async toPromise(e){let t=[];for await(let n of e)t.push(n);return t}toPromise(){return e.toPromise(this)}emitOne(e){this._state===0&&(this._results.push(e),this._onStateChanged.fire())}emitMany(e){this._state===0&&(this._results=this._results.concat(e),this._onStateChanged.fire())}resolve(){this._state===0&&(this._state=1,this._onStateChanged.fire())}reject(e){this._state===0&&(this._state=2,this._error=e,this._onStateChanged.fire())}};var ao=class{constructor(e){this.values=e,this.prefixSum=new Uint32Array(e.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(e,t){e=Zt(e);let n=this.values,r=this.prefixSum,i=t.length;return i===0?!1:(this.values=new Uint32Array(n.length+i),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e),e+i),this.values.set(t,e),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(e,t){return e=Zt(e),t=Zt(t),this.values[e]===t?!1:(this.values[e]=t,e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),!0)}removeValues(e,t){e=Zt(e),t=Zt(t);let n=this.values,r=this.prefixSum;if(e>=n.length)return!1;let i=n.length-e;return t>=i&&(t=i),t===0?!1:(this.values=new Uint32Array(n.length-t),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e+t),e),this.prefixSum=new Uint32Array(this.values.length),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return this.values.length===0?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(e){return e<0?0:(e=Zt(e),this._getPrefixSum(e))}_getPrefixSum(e){if(e<=this.prefixSumValidIndex[0])return this.prefixSum[e];let t=this.prefixSumValidIndex[0]+1;t===0&&(this.prefixSum[0]=this.values[0],t++),e>=this.values.length&&(e=this.values.length-1);for(let n=t;n<=e;n++)this.prefixSum[n]=this.prefixSum[n-1]+this.values[n];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],e),this.prefixSum[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,n=this.values.length-1,r=0,i=0,a=0;for(;t<=n;)if(r=t+(n-t)/2|0,i=this.prefixSum[r],a=i-this.values[r],e<a)n=r-1;else if(e>=i)t=r+1;else break;return new oo(r,e-a)}},oo=class{constructor(e,t){this.index=e,this.remainder=t,this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}},so=class{constructor(e,t,n,r){this._uri=e,this._lines=t,this._eol=n,this._versionId=r,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return this._cachedTextValue===null&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(e){e.eol&&e.eol!==this._eol&&(this._eol=e.eol,this._lineStarts=null);let t=e.changes;for(let e of t)this._acceptDeleteRange(e.range),this._acceptInsertText(new M(e.range.startLineNumber,e.range.startColumn),e.text);this._versionId=e.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){let e=this._eol.length,t=this._lines.length,n=new Uint32Array(t);for(let r=0;r<t;r++)n[r]=this._lines[r].length+e;this._lineStarts=new ao(n)}}_setLineText(e,t){this._lines[e]=t,this._lineStarts&&this._lineStarts.setValue(e,this._lines[e].length+this._eol.length)}_acceptDeleteRange(e){if(e.startLineNumber===e.endLineNumber){if(e.startColumn===e.endColumn)return;this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.startLineNumber-1].substring(e.endColumn-1));return}this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.endLineNumber-1].substring(e.endColumn-1)),this._lines.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber)}_acceptInsertText(e,t){if(t.length===0)return;let n=Ae(t);if(n.length===1){this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]+this._lines[e.lineNumber-1].substring(e.column-1));return}n[n.length-1]+=this._lines[e.lineNumber-1].substring(e.column-1),this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]);let r=new Uint32Array(n.length-1);for(let t=1;t<n.length;t++)this._lines.splice(e.lineNumber+t-1,0,n[t]),r[t-1]=n[t].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(e.lineNumber,r)}},co=class{constructor(){this._models=Object.create(null)}getModel(e){return this._models[e]}getModels(){let e=[];return Object.keys(this._models).forEach(t=>e.push(this._models[t])),e}$acceptNewModel(e){this._models[e.url]=new lo(ft.parse(e.url),e.lines,e.EOL,e.versionId)}$acceptModelChanged(e,t){this._models[e]&&this._models[e].onEvents(t)}$acceptRemovedModel(e){this._models[e]&&delete this._models[e]}},lo=class extends so{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}findMatches(e){let t=[];for(let n=0;n<this._lines.length;n++){let r=this._lines[n],i=this.offsetAt(new M(n+1,1)),a=r.matchAll(e);for(let e of a)(e.index||e.index===0)&&(e.index+=i),t.push(e)}return t}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(e){return this._lines[e-1]}getWordAtPosition(e,t){let n=fi(e.column,ui(t),this._lines[e.lineNumber-1],0);return n?new N(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}words(e){let t=this._lines,n=this._wordenize.bind(this),r=0,i=``,a=0,o=[];return{*[Symbol.iterator](){for(;;)if(a<o.length){let e=i.substring(o[a].start,o[a].end);a+=1,yield e}else if(r<t.length)i=t[r],o=n(i,e),a=0,r+=1;else break}}}getLineWords(e,t){let n=this._lines[e-1],r=this._wordenize(n,t),i=[];for(let e of r)i.push({word:n.substring(e.start,e.end),startColumn:e.start+1,endColumn:e.end+1});return i}_wordenize(e,t){let n=[],r;for(t.lastIndex=0;(r=t.exec(e))&&r[0].length!==0;)n.push({start:r.index,end:r.index+r[0].length});return n}getValueInRange(e){if(e=this._validateRange(e),e.startLineNumber===e.endLineNumber)return this._lines[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);let t=this._eol,n=e.startLineNumber-1,r=e.endLineNumber-1,i=[];i.push(this._lines[n].substring(e.startColumn-1));for(let e=n+1;e<r;e++)i.push(this._lines[e]);return i.push(this._lines[r].substring(0,e.endColumn-1)),i.join(t)}offsetAt(e){return e=this._validatePosition(e),this._ensureLineStarts(),this._lineStarts.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this._ensureLineStarts();let t=this._lineStarts.getIndexOf(e),n=this._lines[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}_validateRange(e){let t=this._validatePosition({lineNumber:e.startLineNumber,column:e.startColumn}),n=this._validatePosition({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}_validatePosition(e){if(!M.isIPosition(e))throw Error(`bad position`);let{lineNumber:t,column:n}=e,r=!1;if(t<1)t=1,n=1,r=!0;else if(t>this._lines.length)t=this._lines.length,n=this._lines[t-1].length+1,r=!0;else{let e=this._lines[t-1].length+1;n<1?(n=1,r=!0):n>e&&(n=e,r=!0)}return r?{lineNumber:t,column:n}:e}},uo=class{constructor(){this._workerTextModelSyncServer=new co}dispose(){}_getModel(e){return this._workerTextModelSyncServer.getModel(e)}_getModels(){return this._workerTextModelSyncServer.getModels()}$acceptNewModel(e){this._workerTextModelSyncServer.$acceptNewModel(e)}$acceptModelChanged(e,t){this._workerTextModelSyncServer.$acceptModelChanged(e,t)}$acceptRemovedModel(e){this._workerTextModelSyncServer.$acceptRemovedModel(e)}async $computeUnicodeHighlights(e,t,n){let r=this._getModel(e);return r?mi.computeUnicodeHighlights(r,t,n):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}}async $findSectionHeaders(e,t){let n=this._getModel(e);return n?$a(n,t):[]}async $computeDiff(e,t,n,r){let i=this._getModel(e),a=this._getModel(t);return!i||!a?null:fo.computeDiff(i,a,n,r)}static computeDiff(e,t,n,r){let i=r===`advanced`?La.getDefault():La.getLegacy(),a=e.getLinesContent(),o=t.getLinesContent(),s=i.computeDiff(a,o,n),c=s.changes.length>0?!1:this._modelsAreIdentical(e,t);function l(e){return e.map(e=>[e.original.startLineNumber,e.original.endLineNumberExclusive,e.modified.startLineNumber,e.modified.endLineNumberExclusive,e.innerChanges?.map(e=>[e.originalRange.startLineNumber,e.originalRange.startColumn,e.originalRange.endLineNumber,e.originalRange.endColumn,e.modifiedRange.startLineNumber,e.modifiedRange.startColumn,e.modifiedRange.endLineNumber,e.modifiedRange.endColumn])])}return{identical:c,quitEarly:s.hitTimeout,changes:l(s.changes),moves:s.moves.map(e=>[e.lineRangeMapping.original.startLineNumber,e.lineRangeMapping.original.endLineNumberExclusive,e.lineRangeMapping.modified.startLineNumber,e.lineRangeMapping.modified.endLineNumberExclusive,l(e.changes)])}}static _modelsAreIdentical(e,t){let n=e.getLineCount(),r=t.getLineCount();if(n!==r)return!1;for(let r=1;r<=n;r++){let n=e.getLineContent(r),i=t.getLineContent(r);if(n!==i)return!1}return!0}static{this._diffLimit=1e5}async $computeMoreMinimalEdits(e,t,n){let r=this._getModel(e);if(!r)return t;let i=[],a;t=t.slice(0).sort((e,t)=>{if(e.range&&t.range)return N.compareRangesUsingStarts(e.range,t.range);let n=e.range?0:1,r=t.range?0:1;return n-r});let o=0;for(let e=1;e<t.length;e++)N.getEndPosition(t[o].range).equals(N.getStartPosition(t[e].range))?(t[o].range=N.fromPositions(N.getStartPosition(t[o].range),N.getEndPosition(t[e].range)),t[o].text+=t[e].text):(o++,t[o]=t[e]);t.length=o+1;for(let{range:e,text:o,eol:s}of t){if(typeof s==`number`&&(a=s),N.isEmpty(e)&&!o)continue;let t=r.getValueInRange(e);if(o=o.replace(/\r\n|\n|\r/g,r.eol),t===o)continue;if(Math.max(o.length,t.length)>fo._diffLimit){i.push({range:e,text:o});continue}let c=Gt(t,o,n),l=r.offsetAt(N.lift(e).getStartPosition());for(let e of c){let t=r.positionAt(l+e.originalStart),n=r.positionAt(l+e.originalStart+e.originalLength),a={text:o.substr(e.modifiedStart,e.modifiedLength),range:{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}};r.getValueInRange(a.range)!==a.text&&i.push(a)}}return typeof a==`number`&&i.push({eol:a,text:``,range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),i}async $computeLinks(e){let t=this._getModel(e);return t?sn(t):null}async $computeDefaultDocumentColors(e){let t=this._getModel(e);return t?Xa(t):null}static{this._suggestionsLimit=1e4}async $textualSuggest(e,t,n,r){let i=new x,a=new RegExp(n,r),o=new Set;outer:for(let n of e){let e=this._getModel(n);if(!e)continue;for(let n of e.words(a)){if(n===t||!isNaN(Number(n)))continue;if(o.add(n),o.size>fo._suggestionsLimit)break outer}}return{words:Array.from(o),duration:i.elapsed()}}async $computeWordRanges(e,t,n,r){let i=this._getModel(e);if(!i)return Object.create(null);let a=new RegExp(n,r),o=Object.create(null);for(let e=t.startLineNumber;e<t.endLineNumber;e++){let t=i.getLineWords(e,a);for(let n of t){if(!isNaN(Number(n.word)))continue;let t=o[n.word];t||(t=[],o[n.word]=t),t.push({startLineNumber:e,startColumn:n.startColumn,endLineNumber:e,endColumn:n.endColumn})}}return o}async $navigateValueSet(e,t,n,r,i){let a=this._getModel(e);if(!a)return null;let o=new RegExp(r,i);t.startColumn===t.endColumn&&(t={startLineNumber:t.startLineNumber,startColumn:t.startColumn,endLineNumber:t.endLineNumber,endColumn:t.endColumn+1});let s=a.getValueInRange(t),c=a.getWordAtPosition({lineNumber:t.startLineNumber,column:t.startColumn},o);if(!c)return null;let l=a.getValueInRange(c);return cn.INSTANCE.navigateValueSet(t,s,c,l,n)}},fo=class extends uo{constructor(e,t){super(),this._host=e,this._foreignModuleFactory=t,this._foreignModule=null}async $ping(){return`pong`}$loadForeignModule(e,t,n){let r={host:Zr(n,(e,t)=>this._host.$fhr(e,t)),getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(r,t),Promise.resolve(Xr(this._foreignModule))):new Promise((n,i)=>{import(`${Tt.asBrowserUri(`${e}.js`).toString(!0)}`).then(e=>{this._foreignModule=e.create(r,t),n(Xr(this._foreignModule))}).catch(i)})}$fmr(e,t){if(!this._foreignModule||typeof this._foreignModule[e]!=`function`)return Promise.reject(Error(`Missing requestHandler or method: `+e));try{return Promise.resolve(this._foreignModule[e].apply(this._foreignModule,t))}catch(e){return Promise.reject(e)}}};typeof importScripts==`function`&&(globalThis.monaco=zr());let po=!1;function mo(e){if(po)return;po=!0;let t=new It(e=>{globalThis.postMessage(e)},t=>new fo(Br.getChannel(t),e));globalThis.onmessage=e=>{t.onmessage(e.data)}}globalThis.onmessage=e=>{po||mo(null)};function ho(e,t=!1){let n=e.length,r=0,i=``,a=0,o=16,s=0,c=0,l=0,u=0,d=0;function f(t,n){let i=0,a=0;for(;i<t||!n;){let t=e.charCodeAt(r);if(t>=48&&t<=57)a=a*16+t-48;else if(t>=65&&t<=70)a=a*16+t-65+10;else if(t>=97&&t<=102)a=a*16+t-97+10;else break;r++,i++}return i<t&&(a=-1),a}function p(e){r=e,i=``,a=0,o=16,d=0}function m(){let t=r;if(e.charCodeAt(r)===48)r++;else for(r++;r<e.length&&vo(e.charCodeAt(r));)r++;if(r<e.length&&e.charCodeAt(r)===46)if(r++,r<e.length&&vo(e.charCodeAt(r)))for(r++;r<e.length&&vo(e.charCodeAt(r));)r++;else return d=3,e.substring(t,r);let n=r;if(r<e.length&&(e.charCodeAt(r)===69||e.charCodeAt(r)===101))if(r++,(r<e.length&&e.charCodeAt(r)===43||e.charCodeAt(r)===45)&&r++,r<e.length&&vo(e.charCodeAt(r))){for(r++;r<e.length&&vo(e.charCodeAt(r));)r++;n=r}else d=3;return e.substring(t,n)}function h(){let t=``,i=r;for(;;){if(r>=n){t+=e.substring(i,r),d=2;break}let a=e.charCodeAt(r);if(a===34){t+=e.substring(i,r),r++;break}if(a===92){if(t+=e.substring(i,r),r++,r>=n){d=2;break}switch(e.charCodeAt(r++)){case 34:t+=`"`;break;case 92:t+=`\\`;break;case 47:t+=`/`;break;case 98:t+=`\b`;break;case 102:t+=`\f`;break;case 110:t+=`
`;break;case 114:t+=`\r`;break;case 116:t+=`	`;break;case 117:let e=f(4,!0);e>=0?t+=String.fromCharCode(e):d=4;break;default:d=5}i=r;continue}if(a>=0&&a<=31)if(_o(a)){t+=e.substring(i,r),d=2;break}else d=6;r++}return t}function g(){if(i=``,d=0,a=r,c=s,u=l,r>=n)return a=n,o=17;let t=e.charCodeAt(r);if(go(t)){do r++,i+=String.fromCharCode(t),t=e.charCodeAt(r);while(go(t));return o=15}if(_o(t))return r++,i+=String.fromCharCode(t),t===13&&e.charCodeAt(r)===10&&(r++,i+=`
`),s++,l=r,o=14;switch(t){case 123:return r++,o=1;case 125:return r++,o=2;case 91:return r++,o=3;case 93:return r++,o=4;case 58:return r++,o=6;case 44:return r++,o=5;case 34:return r++,i=h(),o=10;case 47:let c=r-1;if(e.charCodeAt(r+1)===47){for(r+=2;r<n&&!_o(e.charCodeAt(r));)r++;return i=e.substring(c,r),o=12}if(e.charCodeAt(r+1)===42){r+=2;let t=n-1,a=!1;for(;r<t;){let t=e.charCodeAt(r);if(t===42&&e.charCodeAt(r+1)===47){r+=2,a=!0;break}r++,_o(t)&&(t===13&&e.charCodeAt(r)===10&&r++,s++,l=r)}return a||(r++,d=1),i=e.substring(c,r),o=13}return i+=String.fromCharCode(t),r++,o=16;case 45:if(i+=String.fromCharCode(t),r++,r===n||!vo(e.charCodeAt(r)))return o=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return i+=m(),o=11;default:for(;r<n&&_(t);)r++,t=e.charCodeAt(r);if(a!==r){switch(i=e.substring(a,r),i){case`true`:return o=8;case`false`:return o=9;case`null`:return o=7}return o=16}return i+=String.fromCharCode(t),r++,o=16}}function _(e){if(go(e)||_o(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}function v(){let e;do e=g();while(e>=12&&e<=15);return e}return{setPosition:p,getPosition:()=>r,scan:t?v:g,getToken:()=>o,getTokenValue:()=>i,getTokenOffset:()=>a,getTokenLength:()=>r-a,getTokenStartLine:()=>c,getTokenStartCharacter:()=>a-u,getTokenError:()=>d}}function go(e){return e===32||e===9}function _o(e){return e===10||e===13}function vo(e){return e>=48&&e<=57}var yo;(function(e){e[e.lineFeed=10]=`lineFeed`,e[e.carriageReturn=13]=`carriageReturn`,e[e.space=32]=`space`,e[e._0=48]=`_0`,e[e._1=49]=`_1`,e[e._2=50]=`_2`,e[e._3=51]=`_3`,e[e._4=52]=`_4`,e[e._5=53]=`_5`,e[e._6=54]=`_6`,e[e._7=55]=`_7`,e[e._8=56]=`_8`,e[e._9=57]=`_9`,e[e.a=97]=`a`,e[e.b=98]=`b`,e[e.c=99]=`c`,e[e.d=100]=`d`,e[e.e=101]=`e`,e[e.f=102]=`f`,e[e.g=103]=`g`,e[e.h=104]=`h`,e[e.i=105]=`i`,e[e.j=106]=`j`,e[e.k=107]=`k`,e[e.l=108]=`l`,e[e.m=109]=`m`,e[e.n=110]=`n`,e[e.o=111]=`o`,e[e.p=112]=`p`,e[e.q=113]=`q`,e[e.r=114]=`r`,e[e.s=115]=`s`,e[e.t=116]=`t`,e[e.u=117]=`u`,e[e.v=118]=`v`,e[e.w=119]=`w`,e[e.x=120]=`x`,e[e.y=121]=`y`,e[e.z=122]=`z`,e[e.A=65]=`A`,e[e.B=66]=`B`,e[e.C=67]=`C`,e[e.D=68]=`D`,e[e.E=69]=`E`,e[e.F=70]=`F`,e[e.G=71]=`G`,e[e.H=72]=`H`,e[e.I=73]=`I`,e[e.J=74]=`J`,e[e.K=75]=`K`,e[e.L=76]=`L`,e[e.M=77]=`M`,e[e.N=78]=`N`,e[e.O=79]=`O`,e[e.P=80]=`P`,e[e.Q=81]=`Q`,e[e.R=82]=`R`,e[e.S=83]=`S`,e[e.T=84]=`T`,e[e.U=85]=`U`,e[e.V=86]=`V`,e[e.W=87]=`W`,e[e.X=88]=`X`,e[e.Y=89]=`Y`,e[e.Z=90]=`Z`,e[e.asterisk=42]=`asterisk`,e[e.backslash=92]=`backslash`,e[e.closeBrace=125]=`closeBrace`,e[e.closeBracket=93]=`closeBracket`,e[e.colon=58]=`colon`,e[e.comma=44]=`comma`,e[e.dot=46]=`dot`,e[e.doubleQuote=34]=`doubleQuote`,e[e.minus=45]=`minus`,e[e.openBrace=123]=`openBrace`,e[e.openBracket=91]=`openBracket`,e[e.plus=43]=`plus`,e[e.slash=47]=`slash`,e[e.formFeed=12]=`formFeed`,e[e.tab=9]=`tab`})(yo||={});var B=Array(20).fill(0).map((e,t)=>` `.repeat(t)),bo=200,xo={" ":{"\n":Array(bo).fill(0).map((e,t)=>`
`+` `.repeat(t)),"\r":Array(bo).fill(0).map((e,t)=>`\r`+` `.repeat(t)),"\r\n":Array(bo).fill(0).map((e,t)=>`\r
`+` `.repeat(t))},"	":{"\n":Array(bo).fill(0).map((e,t)=>`
`+`	`.repeat(t)),"\r":Array(bo).fill(0).map((e,t)=>`\r`+`	`.repeat(t)),"\r\n":Array(bo).fill(0).map((e,t)=>`\r
`+`	`.repeat(t))}},So=[`
`,`\r`,`\r
`];function Co(e,t,n){let r,i,a,o,s;if(t){for(o=t.offset,s=o+t.length,a=o;a>0&&!Do(e,a-1);)a--;let c=s;for(;c<e.length&&!Do(e,c);)c++;i=e.substring(a,c),r=To(i,n)}else i=e,r=0,a=0,o=0,s=e.length;let c=Eo(n,e),l=So.includes(c),u=0,d=0,f;f=n.insertSpaces?B[n.tabSize||4]??wo(B[1],n.tabSize||4):`	`;let p=f===`	`?`	`:` `,m=ho(i,!1),h=!1;function g(){if(u>1)return wo(c,u)+wo(f,r+d);let e=f.length*(r+d);return!l||e>xo[p][c].length?c+wo(f,r+d):e<=0?c:xo[p][c][e]}function _(){let e=m.scan();for(u=0;e===15||e===14;)e===14&&n.keepLines?u+=1:e===14&&(u=1),e=m.scan();return h=e===16||m.getTokenError()!==0,e}let v=[];function y(n,r,i){!h&&(!t||r<s&&i>o)&&e.substring(r,i)!==n&&v.push({offset:r,length:i-r,content:n})}let b=_();if(n.keepLines&&u>0&&y(wo(c,u),0,0),b!==17){let e=m.getTokenOffset()+a,t=f.length*r<20&&n.insertSpaces?B[f.length*r]:wo(f,r);y(t,a,e)}for(;b!==17;){let e=m.getTokenOffset()+m.getTokenLength()+a,t=_(),r=``,i=!1;for(;u===0&&(t===12||t===13);){let n=m.getTokenOffset()+a;y(B[1],e,n),e=m.getTokenOffset()+m.getTokenLength()+a,i=t===12,r=i?g():``,t=_()}if(t===2)b!==1&&d--,n.keepLines&&u>0||!n.keepLines&&b!==1?r=g():n.keepLines&&(r=B[1]);else if(t===4)b!==3&&d--,n.keepLines&&u>0||!n.keepLines&&b!==3?r=g():n.keepLines&&(r=B[1]);else{switch(b){case 3:case 1:d++,r=n.keepLines&&u>0||!n.keepLines?g():B[1];break;case 5:r=n.keepLines&&u>0||!n.keepLines?g():B[1];break;case 12:r=g();break;case 13:u>0?r=g():i||(r=B[1]);break;case 6:n.keepLines&&u>0?r=g():i||(r=B[1]);break;case 10:n.keepLines&&u>0?r=g():t===6&&!i&&(r=``);break;case 7:case 8:case 9:case 11:case 2:case 4:n.keepLines&&u>0?r=g():(t===12||t===13)&&!i?r=B[1]:t!==5&&t!==17&&(h=!0);break;case 16:h=!0;break}u>0&&(t===12||t===13)&&(r=g())}t===17&&(r=n.keepLines&&u>0?g():n.insertFinalNewline?c:``);let o=m.getTokenOffset()+a;y(r,e,o),b=t}return v}function wo(e,t){let n=``;for(let r=0;r<t;r++)n+=e;return n}function To(e,t){let n=0,r=0,i=t.tabSize||4;for(;n<e.length;){let t=e.charAt(n);if(t===B[1])r++;else if(t===`	`)r+=i;else break;n++}return Math.floor(r/i)}function Eo(e,t){for(let e=0;e<t.length;e++){let n=t.charAt(e);if(n===`\r`)return e+1<t.length&&t.charAt(e+1)===`
`?`\r
`:`\r`;if(n===`
`)return`
`}return e&&e.eol||`
`}function Do(e,t){return`\r
`.indexOf(e.charAt(t))!==-1}var Oo;(function(e){e.DEFAULT={allowTrailingComma:!1}})(Oo||={});function ko(e,t=[],n=Oo.DEFAULT){let r=null,i=[],a=[];function o(e){Array.isArray(i)?i.push(e):r!==null&&(i[r]=e)}return Po(e,{onObjectBegin:()=>{let e={};o(e),a.push(i),i=e,r=null},onObjectProperty:e=>{r=e},onObjectEnd:()=>{i=a.pop()},onArrayBegin:()=>{let e=[];o(e),a.push(i),i=e,r=null},onArrayEnd:()=>{i=a.pop()},onLiteralValue:o,onError:(e,n,r)=>{t.push({error:e,offset:n,length:r})}},n),i[0]}function Ao(e){if(!e.parent||!e.parent.children)return[];let t=Ao(e.parent);if(e.parent.type===`property`){let n=e.parent.children[0].value;t.push(n)}else if(e.parent.type===`array`){let n=e.parent.children.indexOf(e);n!==-1&&t.push(n)}return t}function jo(e){switch(e.type){case`array`:return e.children.map(jo);case`object`:let t=Object.create(null);for(let n of e.children){let e=n.children[1];e&&(t[n.children[0].value]=jo(e))}return t;case`null`:case`string`:case`number`:case`boolean`:return e.value;default:return}}function Mo(e,t,n=!1){return t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}function No(e,t,n=!1){if(Mo(e,t,n)){let r=e.children;if(Array.isArray(r))for(let e=0;e<r.length&&r[e].offset<=t;e++){let i=No(r[e],t,n);if(i)return i}return e}}function Po(e,t,n=Oo.DEFAULT){let r=ho(e,!1),i=[];function a(e){return e?()=>e(r.getTokenOffset(),r.getTokenLength(),r.getTokenStartLine(),r.getTokenStartCharacter()):()=>!0}function o(e){return e?()=>e(r.getTokenOffset(),r.getTokenLength(),r.getTokenStartLine(),r.getTokenStartCharacter(),()=>i.slice()):()=>!0}function s(e){return e?t=>e(t,r.getTokenOffset(),r.getTokenLength(),r.getTokenStartLine(),r.getTokenStartCharacter()):()=>!0}function c(e){return e?t=>e(t,r.getTokenOffset(),r.getTokenLength(),r.getTokenStartLine(),r.getTokenStartCharacter(),()=>i.slice()):()=>!0}let l=o(t.onObjectBegin),u=c(t.onObjectProperty),d=a(t.onObjectEnd),f=o(t.onArrayBegin),p=a(t.onArrayEnd),m=c(t.onLiteralValue),h=s(t.onSeparator),g=a(t.onComment),_=s(t.onError),v=n&&n.disallowComments,y=n&&n.allowTrailingComma;function b(){for(;;){let e=r.scan();switch(r.getTokenError()){case 4:x(14);break;case 5:x(15);break;case 3:x(13);break;case 1:v||x(11);break;case 2:x(12);break;case 6:x(16);break}switch(e){case 12:case 13:v?x(10):g();break;case 16:x(1);break;case 15:case 14:break;default:return e}}}function x(e,t=[],n=[]){if(_(e),t.length+n.length>0){let e=r.getToken();for(;e!==17;){if(t.indexOf(e)!==-1){b();break}else if(n.indexOf(e)!==-1)break;e=b()}}}function S(e){let t=r.getTokenValue();return e?m(t):(u(t),i.push(t)),b(),!0}function C(){switch(r.getToken()){case 11:let e=r.getTokenValue(),t=Number(e);isNaN(t)&&(x(2),t=0),m(t);break;case 7:m(null);break;case 8:m(!0);break;case 9:m(!1);break;default:return!1}return b(),!0}function w(){return r.getToken()===10?(S(!1),r.getToken()===6?(h(`:`),b(),ne()||x(4,[],[2,5])):x(5,[],[2,5]),i.pop(),!0):(x(3,[],[2,5]),!1)}function ee(){l(),b();let e=!1;for(;r.getToken()!==2&&r.getToken()!==17;){if(r.getToken()===5){if(e||x(4,[],[]),h(`,`),b(),r.getToken()===2&&y)break}else e&&x(6,[],[]);w()||x(4,[],[2,5]),e=!0}return d(),r.getToken()===2?b():x(7,[2],[]),!0}function te(){f(),b();let e=!0,t=!1;for(;r.getToken()!==4&&r.getToken()!==17;){if(r.getToken()===5){if(t||x(4,[],[]),h(`,`),b(),r.getToken()===4&&y)break}else t&&x(6,[],[]);e?(i.push(0),e=!1):i[i.length-1]++,ne()||x(4,[],[4,5]),t=!0}return p(),e||i.pop(),r.getToken()===4?b():x(8,[4],[]),!0}function ne(){switch(r.getToken()){case 3:return te();case 1:return ee();case 10:return S(!0);default:return C()}}return b(),r.getToken()===17?n.allowEmptyContent?!0:(x(4,[],[]),!1):ne()?(r.getToken()!==17&&x(9,[],[]),!0):(x(4,[],[]),!1)}var Fo=ho,Io;(function(e){e[e.None=0]=`None`,e[e.UnexpectedEndOfComment=1]=`UnexpectedEndOfComment`,e[e.UnexpectedEndOfString=2]=`UnexpectedEndOfString`,e[e.UnexpectedEndOfNumber=3]=`UnexpectedEndOfNumber`,e[e.InvalidUnicode=4]=`InvalidUnicode`,e[e.InvalidEscapeCharacter=5]=`InvalidEscapeCharacter`,e[e.InvalidCharacter=6]=`InvalidCharacter`})(Io||={});var Lo;(function(e){e[e.OpenBraceToken=1]=`OpenBraceToken`,e[e.CloseBraceToken=2]=`CloseBraceToken`,e[e.OpenBracketToken=3]=`OpenBracketToken`,e[e.CloseBracketToken=4]=`CloseBracketToken`,e[e.CommaToken=5]=`CommaToken`,e[e.ColonToken=6]=`ColonToken`,e[e.NullKeyword=7]=`NullKeyword`,e[e.TrueKeyword=8]=`TrueKeyword`,e[e.FalseKeyword=9]=`FalseKeyword`,e[e.StringLiteral=10]=`StringLiteral`,e[e.NumericLiteral=11]=`NumericLiteral`,e[e.LineCommentTrivia=12]=`LineCommentTrivia`,e[e.BlockCommentTrivia=13]=`BlockCommentTrivia`,e[e.LineBreakTrivia=14]=`LineBreakTrivia`,e[e.Trivia=15]=`Trivia`,e[e.Unknown=16]=`Unknown`,e[e.EOF=17]=`EOF`})(Lo||={});var Ro=ko,zo=No,Bo=Ao,Vo=jo,Ho;(function(e){e[e.InvalidSymbol=1]=`InvalidSymbol`,e[e.InvalidNumberFormat=2]=`InvalidNumberFormat`,e[e.PropertyNameExpected=3]=`PropertyNameExpected`,e[e.ValueExpected=4]=`ValueExpected`,e[e.ColonExpected=5]=`ColonExpected`,e[e.CommaExpected=6]=`CommaExpected`,e[e.CloseBraceExpected=7]=`CloseBraceExpected`,e[e.CloseBracketExpected=8]=`CloseBracketExpected`,e[e.EndOfFileExpected=9]=`EndOfFileExpected`,e[e.InvalidCommentToken=10]=`InvalidCommentToken`,e[e.UnexpectedEndOfComment=11]=`UnexpectedEndOfComment`,e[e.UnexpectedEndOfString=12]=`UnexpectedEndOfString`,e[e.UnexpectedEndOfNumber=13]=`UnexpectedEndOfNumber`,e[e.InvalidUnicode=14]=`InvalidUnicode`,e[e.InvalidEscapeCharacter=15]=`InvalidEscapeCharacter`,e[e.InvalidCharacter=16]=`InvalidCharacter`})(Ho||={});function Uo(e,t,n){return Co(e,t,n)}function Wo(e,t){if(e===t)return!0;if(e==null||t==null||typeof e!=typeof t||typeof e!=`object`||Array.isArray(e)!==Array.isArray(t))return!1;let n,r;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(n=0;n<e.length;n++)if(!Wo(e[n],t[n]))return!1}else{let i=[];for(r in e)i.push(r);i.sort();let a=[];for(r in t)a.push(r);if(a.sort(),!Wo(i,a))return!1;for(n=0;n<i.length;n++)if(!Wo(e[i[n]],t[i[n]]))return!1}return!0}function V(e){return typeof e==`number`}function Go(e){return e!==void 0}function Ko(e){return typeof e==`boolean`}function qo(e){return typeof e==`string`}function Jo(e){return typeof e==`object`&&!!e&&!Array.isArray(e)}function Yo(e,t){if(e.length<t.length)return!1;for(let n=0;n<t.length;n++)if(e[n]!==t[n])return!1;return!0}function Xo(e,t){let n=e.length-t.length;return n>0?e.lastIndexOf(t)===n:n===0?e===t:!1}function Zo(e){let t=``;Yo(e,`(?i)`)&&(e=e.substring(4),t=`i`);try{return new RegExp(e,t+`u`)}catch{try{return new RegExp(e,t)}catch{return}}}function Qo(e){let t=0;for(let n=0;n<e.length;n++){t++;let r=e.charCodeAt(n);55296<=r&&r<=56319&&n++}return t}var $o;(function(e){function t(e){return typeof e==`string`}e.is=t})($o||={});var es;(function(e){function t(e){return typeof e==`string`}e.is=t})(es||={});var ts;(function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647;function t(t){return typeof t==`number`&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}e.is=t})(ts||={});var ns;(function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647;function t(t){return typeof t==`number`&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}e.is=t})(ns||={});var H;(function(e){function t(e,t){return e===Number.MAX_VALUE&&(e=ns.MAX_VALUE),t===Number.MAX_VALUE&&(t=ns.MAX_VALUE),{line:e,character:t}}e.create=t;function n(e){let t=e;return K.objectLiteral(t)&&K.uinteger(t.line)&&K.uinteger(t.character)}e.is=n})(H||={});var U;(function(e){function t(e,t,n,r){if(K.uinteger(e)&&K.uinteger(t)&&K.uinteger(n)&&K.uinteger(r))return{start:H.create(e,t),end:H.create(n,r)};if(H.is(e)&&H.is(t))return{start:e,end:t};throw Error(`Range#create called with invalid arguments[${e}, ${t}, ${n}, ${r}]`)}e.create=t;function n(e){let t=e;return K.objectLiteral(t)&&H.is(t.start)&&H.is(t.end)}e.is=n})(U||={});var rs;(function(e){function t(e,t){return{uri:e,range:t}}e.create=t;function n(e){let t=e;return K.objectLiteral(t)&&U.is(t.range)&&(K.string(t.uri)||K.undefined(t.uri))}e.is=n})(rs||={});var os;(function(e){function t(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}}e.create=t;function n(e){let t=e;return K.objectLiteral(t)&&U.is(t.targetRange)&&K.string(t.targetUri)&&U.is(t.targetSelectionRange)&&(U.is(t.originSelectionRange)||K.undefined(t.originSelectionRange))}e.is=n})(os||={});var ss;(function(e){function t(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}}e.create=t;function n(e){let t=e;return K.objectLiteral(t)&&K.numberRange(t.red,0,1)&&K.numberRange(t.green,0,1)&&K.numberRange(t.blue,0,1)&&K.numberRange(t.alpha,0,1)}e.is=n})(ss||={});var cs;(function(e){function t(e,t){return{range:e,color:t}}e.create=t;function n(e){let t=e;return K.objectLiteral(t)&&U.is(t.range)&&ss.is(t.color)}e.is=n})(cs||={});var ls;(function(e){function t(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}}e.create=t;function n(e){let t=e;return K.objectLiteral(t)&&K.string(t.label)&&(K.undefined(t.textEdit)||_s.is(t))&&(K.undefined(t.additionalTextEdits)||K.typedArray(t.additionalTextEdits,_s.is))}e.is=n})(ls||={});var us;(function(e){e.Comment=`comment`,e.Imports=`imports`,e.Region=`region`})(us||={});var ds;(function(e){function t(e,t,n,r,i,a){let o={startLine:e,endLine:t};return K.defined(n)&&(o.startCharacter=n),K.defined(r)&&(o.endCharacter=r),K.defined(i)&&(o.kind=i),K.defined(a)&&(o.collapsedText=a),o}e.create=t;function n(e){let t=e;return K.objectLiteral(t)&&K.uinteger(t.startLine)&&K.uinteger(t.startLine)&&(K.undefined(t.startCharacter)||K.uinteger(t.startCharacter))&&(K.undefined(t.endCharacter)||K.uinteger(t.endCharacter))&&(K.undefined(t.kind)||K.string(t.kind))}e.is=n})(ds||={});var fs;(function(e){function t(e,t){return{location:e,message:t}}e.create=t;function n(e){let t=e;return K.defined(t)&&rs.is(t.location)&&K.string(t.message)}e.is=n})(fs||={});var W;(function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4})(W||={});var ps;(function(e){e.Unnecessary=1,e.Deprecated=2})(ps||={});var ms;(function(e){function t(e){let t=e;return K.objectLiteral(t)&&K.string(t.href)}e.is=t})(ms||={});var hs;(function(e){function t(e,t,n,r,i,a){let o={range:e,message:t};return K.defined(n)&&(o.severity=n),K.defined(r)&&(o.code=r),K.defined(i)&&(o.source=i),K.defined(a)&&(o.relatedInformation=a),o}e.create=t;function n(e){let t=e;return K.defined(t)&&U.is(t.range)&&K.string(t.message)&&(K.number(t.severity)||K.undefined(t.severity))&&(K.integer(t.code)||K.string(t.code)||K.undefined(t.code))&&(K.undefined(t.codeDescription)||K.string(t.codeDescription?.href))&&(K.string(t.source)||K.undefined(t.source))&&(K.undefined(t.relatedInformation)||K.typedArray(t.relatedInformation,fs.is))}e.is=n})(hs||={});var gs;(function(e){function t(e,t,...n){let r={title:e,command:t};return K.defined(n)&&n.length>0&&(r.arguments=n),r}e.create=t;function n(e){let t=e;return K.defined(t)&&K.string(t.title)&&K.string(t.command)}e.is=n})(gs||={});var _s;(function(e){function t(e,t){return{range:e,newText:t}}e.replace=t;function n(e,t){return{range:{start:e,end:e},newText:t}}e.insert=n;function r(e){return{range:e,newText:``}}e.del=r;function i(e){let t=e;return K.objectLiteral(t)&&K.string(t.newText)&&U.is(t.range)}e.is=i})(_s||={});var vs;(function(e){function t(e,t,n){let r={label:e};return t!==void 0&&(r.needsConfirmation=t),n!==void 0&&(r.description=n),r}e.create=t;function n(e){let t=e;return K.objectLiteral(t)&&K.string(t.label)&&(K.boolean(t.needsConfirmation)||t.needsConfirmation===void 0)&&(K.string(t.description)||t.description===void 0)}e.is=n})(vs||={});var ys;(function(e){function t(e){let t=e;return K.string(t)}e.is=t})(ys||={});var bs;(function(e){function t(e,t,n){return{range:e,newText:t,annotationId:n}}e.replace=t;function n(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}}e.insert=n;function r(e,t){return{range:e,newText:``,annotationId:t}}e.del=r;function i(e){let t=e;return _s.is(t)&&(vs.is(t.annotationId)||ys.is(t.annotationId))}e.is=i})(bs||={});var xs;(function(e){function t(e,t){return{textDocument:e,edits:t}}e.create=t;function n(e){let t=e;return K.defined(t)&&Os.is(t.textDocument)&&Array.isArray(t.edits)}e.is=n})(xs||={});var Ss;(function(e){function t(e,t,n){let r={kind:`create`,uri:e};return t!==void 0&&(t.overwrite!==void 0||t.ignoreIfExists!==void 0)&&(r.options=t),n!==void 0&&(r.annotationId=n),r}e.create=t;function n(e){let t=e;return t&&t.kind===`create`&&K.string(t.uri)&&(t.options===void 0||(t.options.overwrite===void 0||K.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===void 0||K.boolean(t.options.ignoreIfExists)))&&(t.annotationId===void 0||ys.is(t.annotationId))}e.is=n})(Ss||={});var Cs;(function(e){function t(e,t,n,r){let i={kind:`rename`,oldUri:e,newUri:t};return n!==void 0&&(n.overwrite!==void 0||n.ignoreIfExists!==void 0)&&(i.options=n),r!==void 0&&(i.annotationId=r),i}e.create=t;function n(e){let t=e;return t&&t.kind===`rename`&&K.string(t.oldUri)&&K.string(t.newUri)&&(t.options===void 0||(t.options.overwrite===void 0||K.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===void 0||K.boolean(t.options.ignoreIfExists)))&&(t.annotationId===void 0||ys.is(t.annotationId))}e.is=n})(Cs||={});var ws;(function(e){function t(e,t,n){let r={kind:`delete`,uri:e};return t!==void 0&&(t.recursive!==void 0||t.ignoreIfNotExists!==void 0)&&(r.options=t),n!==void 0&&(r.annotationId=n),r}e.create=t;function n(e){let t=e;return t&&t.kind===`delete`&&K.string(t.uri)&&(t.options===void 0||(t.options.recursive===void 0||K.boolean(t.options.recursive))&&(t.options.ignoreIfNotExists===void 0||K.boolean(t.options.ignoreIfNotExists)))&&(t.annotationId===void 0||ys.is(t.annotationId))}e.is=n})(ws||={});var Ts;(function(e){function t(e){let t=e;return t&&(t.changes!==void 0||t.documentChanges!==void 0)&&(t.documentChanges===void 0||t.documentChanges.every(e=>K.string(e.kind)?Ss.is(e)||Cs.is(e)||ws.is(e):xs.is(e)))}e.is=t})(Ts||={});var Es;(function(e){function t(e){return{uri:e}}e.create=t;function n(e){let t=e;return K.defined(t)&&K.string(t.uri)}e.is=n})(Es||={});var Ds;(function(e){function t(e,t){return{uri:e,version:t}}e.create=t;function n(e){let t=e;return K.defined(t)&&K.string(t.uri)&&K.integer(t.version)}e.is=n})(Ds||={});var Os;(function(e){function t(e,t){return{uri:e,version:t}}e.create=t;function n(e){let t=e;return K.defined(t)&&K.string(t.uri)&&(t.version===null||K.integer(t.version))}e.is=n})(Os||={});var ks;(function(e){function t(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}}e.create=t;function n(e){let t=e;return K.defined(t)&&K.string(t.uri)&&K.string(t.languageId)&&K.integer(t.version)&&K.string(t.text)}e.is=n})(ks||={});var As;(function(e){e.PlainText=`plaintext`,e.Markdown=`markdown`;function t(t){let n=t;return n===e.PlainText||n===e.Markdown}e.is=t})(As||={});var js;(function(e){function t(e){let t=e;return K.objectLiteral(e)&&As.is(t.kind)&&K.string(t.value)}e.is=t})(js||={});var Ms;(function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25})(Ms||={});var G;(function(e){e.PlainText=1,e.Snippet=2})(G||={});var Ns;(function(e){e.Deprecated=1})(Ns||={});var Ps;(function(e){function t(e,t,n){return{newText:e,insert:t,replace:n}}e.create=t;function n(e){let t=e;return t&&K.string(t.newText)&&U.is(t.insert)&&U.is(t.replace)}e.is=n})(Ps||={});var Fs;(function(e){e.asIs=1,e.adjustIndentation=2})(Fs||={});var Is;(function(e){function t(e){let t=e;return t&&(K.string(t.detail)||t.detail===void 0)&&(K.string(t.description)||t.description===void 0)}e.is=t})(Is||={});var Ls;(function(e){function t(e){return{label:e}}e.create=t})(Ls||={});var Rs;(function(e){function t(e,t){return{items:e||[],isIncomplete:!!t}}e.create=t})(Rs||={});var zs;(function(e){function t(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,`\\$&`)}e.fromPlainText=t;function n(e){let t=e;return K.string(t)||K.objectLiteral(t)&&K.string(t.language)&&K.string(t.value)}e.is=n})(zs||={});var Bs;(function(e){function t(e){let t=e;return!!t&&K.objectLiteral(t)&&(js.is(t.contents)||zs.is(t.contents)||K.typedArray(t.contents,zs.is))&&(e.range===void 0||U.is(e.range))}e.is=t})(Bs||={});var Vs;(function(e){function t(e,t){return t?{label:e,documentation:t}:{label:e}}e.create=t})(Vs||={});var Hs;(function(e){function t(e,t,...n){let r={label:e};return K.defined(t)&&(r.documentation=t),K.defined(n)?r.parameters=n:r.parameters=[],r}e.create=t})(Hs||={});var Us;(function(e){e.Text=1,e.Read=2,e.Write=3})(Us||={});var Ws;(function(e){function t(e,t){let n={range:e};return K.number(t)&&(n.kind=t),n}e.create=t})(Ws||={});var Gs;(function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26})(Gs||={});var Ks;(function(e){e.Deprecated=1})(Ks||={});var qs;(function(e){function t(e,t,n,r,i){let a={name:e,kind:t,location:{uri:r,range:n}};return i&&(a.containerName=i),a}e.create=t})(qs||={});var Js;(function(e){function t(e,t,n,r){return r===void 0?{name:e,kind:t,location:{uri:n}}:{name:e,kind:t,location:{uri:n,range:r}}}e.create=t})(Js||={});var Ys;(function(e){function t(e,t,n,r,i,a){let o={name:e,detail:t,kind:n,range:r,selectionRange:i};return a!==void 0&&(o.children=a),o}e.create=t;function n(e){let t=e;return t&&K.string(t.name)&&K.number(t.kind)&&U.is(t.range)&&U.is(t.selectionRange)&&(t.detail===void 0||K.string(t.detail))&&(t.deprecated===void 0||K.boolean(t.deprecated))&&(t.children===void 0||Array.isArray(t.children))&&(t.tags===void 0||Array.isArray(t.tags))}e.is=n})(Ys||={});var Xs;(function(e){e.Empty=``,e.QuickFix=`quickfix`,e.Refactor=`refactor`,e.RefactorExtract=`refactor.extract`,e.RefactorInline=`refactor.inline`,e.RefactorRewrite=`refactor.rewrite`,e.Source=`source`,e.SourceOrganizeImports=`source.organizeImports`,e.SourceFixAll=`source.fixAll`})(Xs||={});var Zs;(function(e){e.Invoked=1,e.Automatic=2})(Zs||={});var Qs;(function(e){function t(e,t,n){let r={diagnostics:e};return t!=null&&(r.only=t),n!=null&&(r.triggerKind=n),r}e.create=t;function n(e){let t=e;return K.defined(t)&&K.typedArray(t.diagnostics,hs.is)&&(t.only===void 0||K.typedArray(t.only,K.string))&&(t.triggerKind===void 0||t.triggerKind===Zs.Invoked||t.triggerKind===Zs.Automatic)}e.is=n})(Qs||={});var $s;(function(e){function t(e,t,n){let r={title:e},i=!0;return typeof t==`string`?(i=!1,r.kind=t):gs.is(t)?r.command=t:r.edit=t,i&&n!==void 0&&(r.kind=n),r}e.create=t;function n(e){let t=e;return t&&K.string(t.title)&&(t.diagnostics===void 0||K.typedArray(t.diagnostics,hs.is))&&(t.kind===void 0||K.string(t.kind))&&(t.edit!==void 0||t.command!==void 0)&&(t.command===void 0||gs.is(t.command))&&(t.isPreferred===void 0||K.boolean(t.isPreferred))&&(t.edit===void 0||Ts.is(t.edit))}e.is=n})($s||={});var ec;(function(e){function t(e,t){let n={range:e};return K.defined(t)&&(n.data=t),n}e.create=t;function n(e){let t=e;return K.defined(t)&&U.is(t.range)&&(K.undefined(t.command)||gs.is(t.command))}e.is=n})(ec||={});var tc;(function(e){function t(e,t){return{tabSize:e,insertSpaces:t}}e.create=t;function n(e){let t=e;return K.defined(t)&&K.uinteger(t.tabSize)&&K.boolean(t.insertSpaces)}e.is=n})(tc||={});var nc;(function(e){function t(e,t,n){return{range:e,target:t,data:n}}e.create=t;function n(e){let t=e;return K.defined(t)&&U.is(t.range)&&(K.undefined(t.target)||K.string(t.target))}e.is=n})(nc||={});var rc;(function(e){function t(e,t){return{range:e,parent:t}}e.create=t;function n(t){let n=t;return K.objectLiteral(n)&&U.is(n.range)&&(n.parent===void 0||e.is(n.parent))}e.is=n})(rc||={});var ic;(function(e){e.namespace=`namespace`,e.type=`type`,e.class=`class`,e.enum=`enum`,e.interface=`interface`,e.struct=`struct`,e.typeParameter=`typeParameter`,e.parameter=`parameter`,e.variable=`variable`,e.property=`property`,e.enumMember=`enumMember`,e.event=`event`,e.function=`function`,e.method=`method`,e.macro=`macro`,e.keyword=`keyword`,e.modifier=`modifier`,e.comment=`comment`,e.string=`string`,e.number=`number`,e.regexp=`regexp`,e.operator=`operator`,e.decorator=`decorator`})(ic||={});var ac;(function(e){e.declaration=`declaration`,e.definition=`definition`,e.readonly=`readonly`,e.static=`static`,e.deprecated=`deprecated`,e.abstract=`abstract`,e.async=`async`,e.modification=`modification`,e.documentation=`documentation`,e.defaultLibrary=`defaultLibrary`})(ac||={});var oc;(function(e){function t(e){let t=e;return K.objectLiteral(t)&&(t.resultId===void 0||typeof t.resultId==`string`)&&Array.isArray(t.data)&&(t.data.length===0||typeof t.data[0]==`number`)}e.is=t})(oc||={});var sc;(function(e){function t(e,t){return{range:e,text:t}}e.create=t;function n(e){let t=e;return t!=null&&U.is(t.range)&&K.string(t.text)}e.is=n})(sc||={});var cc;(function(e){function t(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}}e.create=t;function n(e){let t=e;return t!=null&&U.is(t.range)&&K.boolean(t.caseSensitiveLookup)&&(K.string(t.variableName)||t.variableName===void 0)}e.is=n})(cc||={});var lc;(function(e){function t(e,t){return{range:e,expression:t}}e.create=t;function n(e){let t=e;return t!=null&&U.is(t.range)&&(K.string(t.expression)||t.expression===void 0)}e.is=n})(lc||={});var uc;(function(e){function t(e,t){return{frameId:e,stoppedLocation:t}}e.create=t;function n(e){let t=e;return K.defined(t)&&U.is(e.stoppedLocation)}e.is=n})(uc||={});var dc;(function(e){e.Type=1,e.Parameter=2;function t(e){return e===1||e===2}e.is=t})(dc||={});var fc;(function(e){function t(e){return{value:e}}e.create=t;function n(e){let t=e;return K.objectLiteral(t)&&(t.tooltip===void 0||K.string(t.tooltip)||js.is(t.tooltip))&&(t.location===void 0||rs.is(t.location))&&(t.command===void 0||gs.is(t.command))}e.is=n})(fc||={});var pc;(function(e){function t(e,t,n){let r={position:e,label:t};return n!==void 0&&(r.kind=n),r}e.create=t;function n(e){let t=e;return K.objectLiteral(t)&&H.is(t.position)&&(K.string(t.label)||K.typedArray(t.label,fc.is))&&(t.kind===void 0||dc.is(t.kind))&&t.textEdits===void 0||K.typedArray(t.textEdits,_s.is)&&(t.tooltip===void 0||K.string(t.tooltip)||js.is(t.tooltip))&&(t.paddingLeft===void 0||K.boolean(t.paddingLeft))&&(t.paddingRight===void 0||K.boolean(t.paddingRight))}e.is=n})(pc||={});var mc;(function(e){function t(e){return{kind:`snippet`,value:e}}e.createSnippet=t})(mc||={});var hc;(function(e){function t(e,t,n,r){return{insertText:e,filterText:t,range:n,command:r}}e.create=t})(hc||={});var gc;(function(e){function t(e){return{items:e}}e.create=t})(gc||={});var _c;(function(e){e.Invoked=0,e.Automatic=1})(_c||={});var vc;(function(e){function t(e,t){return{range:e,text:t}}e.create=t})(vc||={});var yc;(function(e){function t(e,t){return{triggerKind:e,selectedCompletionInfo:t}}e.create=t})(yc||={});var bc;(function(e){function t(e){let t=e;return K.objectLiteral(t)&&es.is(t.uri)&&K.string(t.name)}e.is=t})(bc||={});var xc;(function(e){function t(e,t,n,r){return new Sc(e,t,n,r)}e.create=t;function n(e){let t=e;return!!(K.defined(t)&&K.string(t.uri)&&(K.undefined(t.languageId)||K.string(t.languageId))&&K.uinteger(t.lineCount)&&K.func(t.getText)&&K.func(t.positionAt)&&K.func(t.offsetAt))}e.is=n;function r(e,t){let n=e.getText(),r=i(t,(e,t)=>{let n=e.range.start.line-t.range.start.line;return n===0?e.range.start.character-t.range.start.character:n}),a=n.length;for(let t=r.length-1;t>=0;t--){let i=r[t],o=e.offsetAt(i.range.start),s=e.offsetAt(i.range.end);if(s<=a)n=n.substring(0,o)+i.newText+n.substring(s,n.length);else throw Error(`Overlapping edit`);a=o}return n}e.applyEdits=r;function i(e,t){if(e.length<=1)return e;let n=e.length/2|0,r=e.slice(0,n),a=e.slice(n);i(r,t),i(a,t);let o=0,s=0,c=0;for(;o<r.length&&s<a.length;)t(r[o],a[s])<=0?e[c++]=r[o++]:e[c++]=a[s++];for(;o<r.length;)e[c++]=r[o++];for(;s<a.length;)e[c++]=a[s++];return e}})(xc||={});var Sc=class{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],t=this._content,n=!0;for(let r=0;r<t.length;r++){n&&=(e.push(r),!1);let i=t.charAt(r);n=i===`\r`||i===`
`,i===`\r`&&r+1<t.length&&t.charAt(r+1)===`
`&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(r===0)return H.create(0,e);for(;n<r;){let i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}let i=n-1;return H.create(i,e-t[i])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}},K;(function(e){let t=Object.prototype.toString;function n(e){return e!==void 0}e.defined=n;function r(e){return e===void 0}e.undefined=r;function i(e){return e===!0||e===!1}e.boolean=i;function a(e){return t.call(e)===`[object String]`}e.string=a;function o(e){return t.call(e)===`[object Number]`}e.number=o;function s(e,n,r){return t.call(e)===`[object Number]`&&n<=e&&e<=r}e.numberRange=s;function c(e){return t.call(e)===`[object Number]`&&-2147483648<=e&&e<=2147483647}e.integer=c;function l(e){return t.call(e)===`[object Number]`&&0<=e&&e<=2147483647}e.uinteger=l;function u(e){return t.call(e)===`[object Function]`}e.func=u;function d(e){return typeof e==`object`&&!!e}e.objectLiteral=d;function f(e,t){return Array.isArray(e)&&e.every(t)}e.typedArray=f})(K||={});var Cc=class e{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(t,n){for(let n of t)if(e.isIncremental(n)){let e=Dc(n.range),t=this.offsetAt(e.start),r=this.offsetAt(e.end);this._content=this._content.substring(0,t)+n.text+this._content.substring(r,this._content.length);let i=Math.max(e.start.line,0),a=Math.max(e.end.line,0),o=this._lineOffsets,s=Ec(n.text,!1,t);if(a-i===s.length)for(let e=0,t=s.length;e<t;e++)o[e+i+1]=s[e];else s.length<1e4?o.splice(i+1,a-i,...s):this._lineOffsets=o=o.slice(0,i+1).concat(s,o.slice(a+1));let c=n.text.length-(r-t);if(c!==0)for(let e=i+1+s.length,t=o.length;e<t;e++)o[e]=o[e]+c}else if(e.isFull(n))this._content=n.text,this._lineOffsets=void 0;else throw Error(`Unknown change event received`);this._version=n}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=Ec(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(r===0)return{line:0,character:e};for(;n<r;){let i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}let i=n-1;return{line:i,character:e-t[i]}}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){let t=e;return t!=null&&typeof t.text==`string`&&t.range!==void 0&&(t.rangeLength===void 0||typeof t.rangeLength==`number`)}static isFull(e){let t=e;return t!=null&&typeof t.text==`string`&&t.range===void 0&&t.rangeLength===void 0}},wc;(function(e){function t(e,t,n,r){return new Cc(e,t,n,r)}e.create=t;function n(e,t,n){if(e instanceof Cc)return e.update(t,n),e;throw Error(`TextDocument.update: document must be created by TextDocument.create`)}e.update=n;function r(e,t){let n=e.getText(),r=Tc(t.map(Oc),(e,t)=>{let n=e.range.start.line-t.range.start.line;return n===0?e.range.start.character-t.range.start.character:n}),i=0,a=[];for(let t of r){let r=e.offsetAt(t.range.start);if(r<i)throw Error(`Overlapping edit`);r>i&&a.push(n.substring(i,r)),t.newText.length&&a.push(t.newText),i=e.offsetAt(t.range.end)}return a.push(n.substr(i)),a.join(``)}e.applyEdits=r})(wc||={});function Tc(e,t){if(e.length<=1)return e;let n=e.length/2|0,r=e.slice(0,n),i=e.slice(n);Tc(r,t),Tc(i,t);let a=0,o=0,s=0;for(;a<r.length&&o<i.length;)t(r[a],i[o])<=0?e[s++]=r[a++]:e[s++]=i[o++];for(;a<r.length;)e[s++]=r[a++];for(;o<i.length;)e[s++]=i[o++];return e}function Ec(e,t,n=0){let r=t?[n]:[];for(let t=0;t<e.length;t++){let i=e.charCodeAt(t);(i===13||i===10)&&(i===13&&t+1<e.length&&e.charCodeAt(t+1)===10&&t++,r.push(n+t+1))}return r}function Dc(e){let t=e.start,n=e.end;return t.line>n.line||t.line===n.line&&t.character>n.character?{start:n,end:t}:e}function Oc(e){let t=Dc(e.range);return t===e.range?e:{newText:e.newText,range:t}}var q;(function(e){e[e.Undefined=0]=`Undefined`,e[e.EnumValueMismatch=1]=`EnumValueMismatch`,e[e.Deprecated=2]=`Deprecated`,e[e.UnexpectedEndOfComment=257]=`UnexpectedEndOfComment`,e[e.UnexpectedEndOfString=258]=`UnexpectedEndOfString`,e[e.UnexpectedEndOfNumber=259]=`UnexpectedEndOfNumber`,e[e.InvalidUnicode=260]=`InvalidUnicode`,e[e.InvalidEscapeCharacter=261]=`InvalidEscapeCharacter`,e[e.InvalidCharacter=262]=`InvalidCharacter`,e[e.PropertyExpected=513]=`PropertyExpected`,e[e.CommaExpected=514]=`CommaExpected`,e[e.ColonExpected=515]=`ColonExpected`,e[e.ValueExpected=516]=`ValueExpected`,e[e.CommaOrCloseBacketExpected=517]=`CommaOrCloseBacketExpected`,e[e.CommaOrCloseBraceExpected=518]=`CommaOrCloseBraceExpected`,e[e.TrailingComma=519]=`TrailingComma`,e[e.DuplicateKey=520]=`DuplicateKey`,e[e.CommentNotPermitted=521]=`CommentNotPermitted`,e[e.PropertyKeysMustBeDoublequoted=528]=`PropertyKeysMustBeDoublequoted`,e[e.SchemaResolveError=768]=`SchemaResolveError`,e[e.SchemaUnsupportedFeature=769]=`SchemaUnsupportedFeature`})(q||={});var kc;(function(e){e[e.v3=3]=`v3`,e[e.v4=4]=`v4`,e[e.v6=6]=`v6`,e[e.v7=7]=`v7`,e[e.v2019_09=19]=`v2019_09`,e[e.v2020_12=20]=`v2020_12`})(kc||={});var Ac;(function(e){e.LATEST={textDocument:{completion:{completionItem:{documentationFormat:[As.Markdown,As.PlainText],commitCharactersSupport:!0,labelDetailsSupport:!0}}}}})(Ac||={});var jc;function J(...e){let t=e[0],n,r,i;if(typeof t==`string`)n=t,r=t,e.splice(0,1),i=!e||typeof e[0]!=`object`?e:e[0];else if(t instanceof Array){let n=e.slice(1);if(t.length!==n.length+1)throw Error(`expected a string as the first argument to l10n.t`);let r=t[0];for(let e=1;e<t.length;e++)r+=`{${e-1}}`+t[e];return J(r,...n)}else r=t.message,n=r,t.comment&&t.comment.length>0&&(n+=`/${Array.isArray(t.comment)?t.comment.join(``):t.comment}`),i=t.args??{};let a=jc?.[n];return a?typeof a==`string`?Nc(a,i):a.comment?Nc(a.message,i):Nc(r,i):Nc(r,i)}var Mc=/{([^}]+)}/g;function Nc(e,t){return Object.keys(t).length===0?e:e.replace(Mc,(e,n)=>t[n]??e)}var Pc={"color-hex":{errorMessage:J(`Invalid color format. Use #RGB, #RGBA, #RRGGBB or #RRGGBBAA.`),pattern:/^#([0-9A-Fa-f]{3,4}|([0-9A-Fa-f]{2}){3,4})$/},"date-time":{errorMessage:J(`String is not a RFC3339 date-time.`),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},date:{errorMessage:J(`String is not a RFC3339 date.`),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/i},time:{errorMessage:J(`String is not a RFC3339 time.`),pattern:/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},email:{errorMessage:J(`String is not an e-mail address.`),pattern:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}))$/},hostname:{errorMessage:J(`String is not a hostname.`),pattern:/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i},ipv4:{errorMessage:J(`String is not an IPv4 address.`),pattern:/^(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)$/},ipv6:{errorMessage:J(`String is not an IPv6 address.`),pattern:/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i}},Fc=class{constructor(e,t,n=0){this.offset=t,this.length=n,this.parent=e}get children(){return[]}toString(){return`type: `+this.type+` (`+this.offset+`/`+this.length+`)`+(this.parent?` parent: {`+this.parent.toString()+`}`:``)}},Ic=class extends Fc{constructor(e,t){super(e,t),this.type=`null`,this.value=null}},Lc=class extends Fc{constructor(e,t,n){super(e,n),this.type=`boolean`,this.value=t}},Rc=class extends Fc{constructor(e,t){super(e,t),this.type=`array`,this.items=[]}get children(){return this.items}},zc=class extends Fc{constructor(e,t){super(e,t),this.type=`number`,this.isInteger=!0,this.value=NaN}},Bc=class extends Fc{constructor(e,t,n){super(e,t,n),this.type=`string`,this.value=``}},Vc=class extends Fc{constructor(e,t,n){super(e,t),this.type=`property`,this.colonOffset=-1,this.keyNode=n}get children(){return this.valueNode?[this.keyNode,this.valueNode]:[this.keyNode]}},Hc=class extends Fc{constructor(e,t){super(e,t),this.type=`object`,this.properties=[]}get children(){return this.properties}};function Y(e){return Ko(e)?e?{}:{not:{}}:e}var Uc;(function(e){e[e.Key=0]=`Key`,e[e.Enum=1]=`Enum`})(Uc||={});var Wc={"http://json-schema.org/draft-03/schema#":kc.v3,"http://json-schema.org/draft-04/schema#":kc.v4,"http://json-schema.org/draft-06/schema#":kc.v6,"http://json-schema.org/draft-07/schema#":kc.v7,"https://json-schema.org/draft/2019-09/schema":kc.v2019_09,"https://json-schema.org/draft/2020-12/schema":kc.v2020_12},Gc=class{constructor(e){this.schemaDraft=e}},Kc=class e{constructor(e=-1,t){this.focusOffset=e,this.exclude=t,this.schemas=[]}add(e){this.schemas.push(e)}merge(e){Array.prototype.push.apply(this.schemas,e.schemas)}include(e){return(this.focusOffset===-1||Zc(e,this.focusOffset))&&e!==this.exclude}newSub(){return new e(-1,this.exclude)}},qc=class{constructor(){}get schemas(){return[]}add(e){}merge(e){}include(e){return!0}newSub(){return this}};qc.instance=new qc;var X=class{constructor(){this.problems=[],this.propertiesMatches=0,this.processedProperties=new Set,this.propertiesValueMatches=0,this.primaryValueMatches=0,this.enumValueMatch=!1,this.enumValues=void 0}hasProblems(){return!!this.problems.length}merge(e){this.problems=this.problems.concat(e.problems),this.propertiesMatches+=e.propertiesMatches,this.propertiesValueMatches+=e.propertiesValueMatches,this.mergeProcessedProperties(e)}mergeEnumValues(e){if(!this.enumValueMatch&&!e.enumValueMatch&&this.enumValues&&e.enumValues){this.enumValues=this.enumValues.concat(e.enumValues);for(let e of this.problems)e.code===q.EnumValueMismatch&&(e.message=J(`Value is not accepted. Valid values: {0}.`,this.enumValues.map(e=>JSON.stringify(e)).join(`, `)))}}mergePropertyMatch(e){this.problems=this.problems.concat(e.problems),this.propertiesMatches++,(e.enumValueMatch||!e.hasProblems()&&e.propertiesMatches)&&this.propertiesValueMatches++,e.enumValueMatch&&e.enumValues&&e.enumValues.length===1&&this.primaryValueMatches++}mergeProcessedProperties(e){e.processedProperties.forEach(e=>this.processedProperties.add(e))}compare(e){let t=this.hasProblems();return t===e.hasProblems()?this.enumValueMatch===e.enumValueMatch?this.primaryValueMatches===e.primaryValueMatches?this.propertiesValueMatches===e.propertiesValueMatches?this.propertiesMatches-e.propertiesMatches:this.propertiesValueMatches-e.propertiesValueMatches:this.primaryValueMatches-e.primaryValueMatches:e.enumValueMatch?-1:1:t?-1:1}};function Jc(e,t=[]){return new Qc(e,t,[])}function Yc(e){return Vo(e)}function Xc(e){return Bo(e)}function Zc(e,t,n=!1){return t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}var Qc=class{constructor(e,t=[],n=[]){this.root=e,this.syntaxErrors=t,this.comments=n}getNodeFromOffset(e,t=!1){if(this.root)return zo(this.root,e,t)}visit(e){if(this.root){let t=n=>{let r=e(n),i=n.children;if(Array.isArray(i))for(let e=0;e<i.length&&r;e++)r=t(i[e]);return r};t(this.root)}}validate(e,t,n=W.Warning,r){if(this.root&&t){let i=new X;return Z(this.root,t,i,qc.instance,new Gc(r??$c(t))),i.problems.map(t=>{let r=U.create(e.positionAt(t.location.offset),e.positionAt(t.location.offset+t.location.length));return hs.create(r,t.message,t.severity??n,t.code)})}}getMatchingSchemas(e,t=-1,n){if(this.root&&e){let r=new Kc(t,n),i=$c(e),a=new Gc(i);return Z(this.root,e,new X,r,a),r.schemas}return[]}};function $c(e,t=kc.v2020_12){let n=e.$schema;return n?Wc[n]??t:t}function Z(e,t,n,r,i){if(!e||!r.include(e))return;if(e.type===`property`)return Z(e.valueNode,t,n,r,i);let a=e;switch(o(),a.type){case`object`:u(a);break;case`array`:l(a);break;case`string`:c(a);break;case`number`:s(a);break}r.add({node:a,schema:t});function o(){function e(e){return a.type===e||e===`integer`&&a.type===`number`&&a.isInteger}if(Array.isArray(t.type)?t.type.some(e)||n.problems.push({location:{offset:a.offset,length:a.length},message:t.errorMessage||J(`Incorrect type. Expected one of {0}.`,t.type.join(`, `))}):t.type&&(e(t.type)||n.problems.push({location:{offset:a.offset,length:a.length},message:t.errorMessage||J(`Incorrect type. Expected "{0}".`,t.type)})),Array.isArray(t.allOf))for(let e of t.allOf){let t=new X,o=r.newSub();Z(a,Y(e),t,o,i),n.merge(t),r.merge(o)}let o=Y(t.not);if(o){let e=new X,s=r.newSub();Z(a,o,e,s,i),e.hasProblems()||n.problems.push({location:{offset:a.offset,length:a.length},message:t.errorMessage||J(`Matches a schema that is not allowed.`)});for(let e of s.schemas)e.inverted=!e.inverted,r.add(e)}let s=(e,t)=>{let o=[],s;for(let n of e){let e=Y(n),c=new X,l=r.newSub();if(Z(a,e,c,l,i),c.hasProblems()||o.push(e),!s)s={schema:e,validationResult:c,matchingSchemas:l};else if(!t&&!c.hasProblems()&&!s.validationResult.hasProblems())s.matchingSchemas.merge(l),s.validationResult.propertiesMatches+=c.propertiesMatches,s.validationResult.propertiesValueMatches+=c.propertiesValueMatches,s.validationResult.mergeProcessedProperties(c);else{let t=c.compare(s.validationResult);t>0?s={schema:e,validationResult:c,matchingSchemas:l}:t===0&&(s.matchingSchemas.merge(l),s.validationResult.mergeEnumValues(c))}}return o.length>1&&t&&n.problems.push({location:{offset:a.offset,length:1},message:J(`Matches multiple schemas when only one must validate.`)}),s&&(n.merge(s.validationResult),r.merge(s.matchingSchemas)),o.length};Array.isArray(t.anyOf)&&s(t.anyOf,!1),Array.isArray(t.oneOf)&&s(t.oneOf,!0);let c=e=>{let t=new X,o=r.newSub();Z(a,Y(e),t,o,i),n.merge(t),r.merge(o)},l=(e,t,o)=>{let s=Y(e),l=new X,u=r.newSub();Z(a,s,l,u,i),r.merge(u),n.mergeProcessedProperties(l),l.hasProblems()?o&&c(o):t&&c(t)},u=Y(t.if);if(u&&l(u,Y(t.then),Y(t.else)),Array.isArray(t.enum)){let e=Yc(a),r=!1;for(let n of t.enum)if(Wo(e,n)){r=!0;break}n.enumValues=t.enum,n.enumValueMatch=r,r||n.problems.push({location:{offset:a.offset,length:a.length},code:q.EnumValueMismatch,message:t.errorMessage||J(`Value is not accepted. Valid values: {0}.`,t.enum.map(e=>JSON.stringify(e)).join(`, `))})}if(Go(t.const)){let e=Yc(a);Wo(e,t.const)?n.enumValueMatch=!0:(n.problems.push({location:{offset:a.offset,length:a.length},code:q.EnumValueMismatch,message:t.errorMessage||J(`Value must be {0}.`,JSON.stringify(t.const))}),n.enumValueMatch=!1),n.enumValues=[t.const]}let d=t.deprecationMessage;if(d||t.deprecated){d||=J(`Value is deprecated`);let e=a.parent?.type===`property`?a.parent:a;n.problems.push({location:{offset:e.offset,length:e.length},severity:W.Warning,message:d,code:q.Deprecated})}}function s(e){let r=e.value;function i(e){let t=/^(-?\d+)(?:\.(\d+))?(?:e([-+]\d+))?$/.exec(e.toString());return t&&{value:Number(t[1]+(t[2]||``)),multiplier:(t[2]?.length||0)-(parseInt(t[3])||0)}}if(V(t.multipleOf)){let a=-1;if(Number.isInteger(t.multipleOf))a=r%t.multipleOf;else{let e=i(t.multipleOf),n=i(r);if(e&&n){let t=10**Math.abs(n.multiplier-e.multiplier);n.multiplier<e.multiplier?n.value*=t:e.value*=t,a=n.value%e.value}}a!==0&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Value is not divisible by {0}.`,t.multipleOf)})}function a(e,t){if(V(t))return t;if(Ko(t)&&t)return e}function o(e,t){if(!Ko(t)||!t)return e}let s=a(t.minimum,t.exclusiveMinimum);V(s)&&r<=s&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Value is below the exclusive minimum of {0}.`,s)});let c=a(t.maximum,t.exclusiveMaximum);V(c)&&r>=c&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Value is above the exclusive maximum of {0}.`,c)});let l=o(t.minimum,t.exclusiveMinimum);V(l)&&r<l&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Value is below the minimum of {0}.`,l)});let u=o(t.maximum,t.exclusiveMaximum);V(u)&&r>u&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Value is above the maximum of {0}.`,u)})}function c(e){if(V(t.minLength)&&Qo(e.value)<t.minLength&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`String is shorter than the minimum length of {0}.`,t.minLength)}),V(t.maxLength)&&Qo(e.value)>t.maxLength&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`String is longer than the maximum length of {0}.`,t.maxLength)}),qo(t.pattern)&&(Zo(t.pattern)?.test(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||J(`String does not match the pattern of "{0}".`,t.pattern)})),t.format)switch(t.format){case`uri`:case`uri-reference`:{let r;if(!e.value)r=J(`URI expected.`);else{let n=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/.exec(e.value);n?!n[2]&&t.format===`uri`&&(r=J(`URI with a scheme is expected.`)):r=J(`URI is expected.`)}r&&n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||J(`String is not a URI: {0}`,r)})}break;case`color-hex`:case`date-time`:case`date`:case`time`:case`email`:case`hostname`:case`ipv4`:case`ipv6`:let r=Pc[t.format];(!e.value||!r.pattern.exec(e.value))&&n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||r.errorMessage});default:}}function l(e){let a,o;i.schemaDraft>=kc.v2020_12?(a=t.prefixItems,o=Array.isArray(t.items)?void 0:t.items):(a=Array.isArray(t.items)?t.items:void 0,o=Array.isArray(t.items)?t.additionalItems:t.items);let s=0;if(a!==void 0){let t=Math.min(a.length,e.items.length);for(;s<t;s++){let t=a[s],o=Y(t),c=new X,l=e.items[s];l&&(Z(l,o,c,r,i),n.mergePropertyMatch(c)),n.processedProperties.add(String(s))}}if(o!==void 0&&s<e.items.length)if(typeof o==`boolean`)for(o===!1&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Array has too many items according to schema. Expected {0} or fewer.`,s)});s<e.items.length;s++)n.processedProperties.add(String(s)),n.propertiesValueMatches++;else for(;s<e.items.length;s++){let t=new X;Z(e.items[s],o,t,r,i),n.mergePropertyMatch(t),n.processedProperties.add(String(s))}let c=Y(t.contains);if(c){let r=0;for(let t=0;t<e.items.length;t++){let a=e.items[t],o=new X;Z(a,c,o,qc.instance,i),o.hasProblems()||(r++,i.schemaDraft>=kc.v2020_12&&n.processedProperties.add(String(t)))}r===0&&!V(t.minContains)&&n.problems.push({location:{offset:e.offset,length:e.length},message:t.errorMessage||J(`Array does not contain required item.`)}),V(t.minContains)&&r<t.minContains&&n.problems.push({location:{offset:e.offset,length:e.length},message:t.errorMessage||J(`Array has too few items that match the contains contraint. Expected {0} or more.`,t.minContains)}),V(t.maxContains)&&r>t.maxContains&&n.problems.push({location:{offset:e.offset,length:e.length},message:t.errorMessage||J(`Array has too many items that match the contains contraint. Expected {0} or less.`,t.maxContains)})}let l=t.unevaluatedItems;if(l!==void 0)for(let a=0;a<e.items.length;a++){if(!n.processedProperties.has(String(a)))if(l===!1)n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Item does not match any validation rule from the array.`)});else{let o=new X;Z(e.items[a],t.unevaluatedItems,o,r,i),n.mergePropertyMatch(o)}n.processedProperties.add(String(a)),n.propertiesValueMatches++}if(V(t.minItems)&&e.items.length<t.minItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Array has too few items. Expected {0} or more.`,t.minItems)}),V(t.maxItems)&&e.items.length>t.maxItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Array has too many items. Expected {0} or fewer.`,t.maxItems)}),t.uniqueItems===!0){let t=function(){for(let e=0;e<r.length-1;e++){let t=r[e];for(let n=e+1;n<r.length;n++)if(Wo(t,r[n]))return!0}return!1},r=Yc(e);t()&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Array has duplicate items.`)})}}function u(e){let a=Object.create(null),o=new Set;for(let t of e.properties){let e=t.keyNode.value;a[e]=t.valueNode,o.add(e)}if(Array.isArray(t.required)){for(let r of t.required)if(!a[r]){let t=e.parent&&e.parent.type===`property`&&e.parent.keyNode,i=t?{offset:t.offset,length:t.length}:{offset:e.offset,length:1};n.problems.push({location:i,message:J(`Missing property "{0}".`,r)})}}let s=e=>{o.delete(e),n.processedProperties.add(e)};if(t.properties)for(let e of Object.keys(t.properties)){s(e);let o=t.properties[e],c=a[e];if(c)if(Ko(o))if(o)n.propertiesMatches++,n.propertiesValueMatches++;else{let r=c.parent;n.problems.push({location:{offset:r.keyNode.offset,length:r.keyNode.length},message:t.errorMessage||J(`Property {0} is not allowed.`,e)})}else{let e=new X;Z(c,o,e,r,i),n.mergePropertyMatch(e)}}if(t.patternProperties)for(let e of Object.keys(t.patternProperties)){let c=Zo(e);if(c){let l=[];for(let s of o)if(c.test(s)){l.push(s);let o=a[s];if(o){let a=t.patternProperties[e];if(Ko(a))if(a)n.propertiesMatches++,n.propertiesValueMatches++;else{let e=o.parent;n.problems.push({location:{offset:e.keyNode.offset,length:e.keyNode.length},message:t.errorMessage||J(`Property {0} is not allowed.`,s)})}else{let e=new X;Z(o,a,e,r,i),n.mergePropertyMatch(e)}}}l.forEach(s)}}let c=t.additionalProperties;if(c!==void 0)for(let e of o){s(e);let o=a[e];if(o){if(c===!1){let r=o.parent;n.problems.push({location:{offset:r.keyNode.offset,length:r.keyNode.length},message:t.errorMessage||J(`Property {0} is not allowed.`,e)})}else if(c!==!0){let e=new X;Z(o,c,e,r,i),n.mergePropertyMatch(e)}}}let l=t.unevaluatedProperties;if(l!==void 0){let e=[];for(let s of o)if(!n.processedProperties.has(s)){e.push(s);let o=a[s];if(o){if(l===!1){let e=o.parent;n.problems.push({location:{offset:e.keyNode.offset,length:e.keyNode.length},message:t.errorMessage||J(`Property {0} is not allowed.`,s)})}else if(l!==!0){let e=new X;Z(o,l,e,r,i),n.mergePropertyMatch(e)}}}e.forEach(s)}if(V(t.maxProperties)&&e.properties.length>t.maxProperties&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Object has more properties than limit of {0}.`,t.maxProperties)}),V(t.minProperties)&&e.properties.length<t.minProperties&&n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Object has fewer properties than the required number of {0}`,t.minProperties)}),t.dependentRequired)for(let e in t.dependentRequired){let n=a[e],r=t.dependentRequired[e];n&&Array.isArray(r)&&d(e,r)}if(t.dependentSchemas)for(let e in t.dependentSchemas){let n=a[e],r=t.dependentSchemas[e];n&&Jo(r)&&d(e,r)}if(t.dependencies)for(let e in t.dependencies)a[e]&&d(e,t.dependencies[e]);let u=Y(t.propertyNames);if(u)for(let t of e.properties){let e=t.keyNode;e&&Z(e,u,n,qc.instance,i)}function d(t,o){if(Array.isArray(o))for(let r of o)a[r]?n.propertiesValueMatches++:n.problems.push({location:{offset:e.offset,length:e.length},message:J(`Object is missing property {0} required by property {1}.`,r,t)});else{let t=Y(o);if(t){let a=new X;Z(e,t,a,r,i),n.mergePropertyMatch(a)}}}}}function el(e,t){let n=[],r=-1,i=e.getText(),a=Fo(i,!1),o=t&&t.collectComments?[]:void 0;function s(){for(;;){let t=a.scan();switch(u(),t){case 12:case 13:Array.isArray(o)&&o.push(U.create(e.positionAt(a.getTokenOffset()),e.positionAt(a.getTokenOffset()+a.getTokenLength())));break;case 15:case 14:break;default:return t}}}function c(t,i,a,o,s=W.Error){if(n.length===0||a!==r){let c=U.create(e.positionAt(a),e.positionAt(o));n.push(hs.create(c,t,s,i,e.languageId)),r=a}}function l(e,t,n=void 0,r=[],o=[]){let l=a.getTokenOffset(),u=a.getTokenOffset()+a.getTokenLength();if(l===u&&l>0){for(l--;l>0&&/\s/.test(i.charAt(l));)l--;u=l+1}if(c(e,t,l,u),n&&d(n,!1),r.length+o.length>0){let e=a.getToken();for(;e!==17;){if(r.indexOf(e)!==-1){s();break}else if(o.indexOf(e)!==-1)break;e=s()}}return n}function u(){switch(a.getTokenError()){case 4:return l(J(`Invalid unicode sequence in string.`),q.InvalidUnicode),!0;case 5:return l(J(`Invalid escape character in string.`),q.InvalidEscapeCharacter),!0;case 3:return l(J(`Unexpected end of number.`),q.UnexpectedEndOfNumber),!0;case 1:return l(J(`Unexpected end of comment.`),q.UnexpectedEndOfComment),!0;case 2:return l(J(`Unexpected end of string.`),q.UnexpectedEndOfString),!0;case 6:return l(J(`Invalid characters in string. Control characters must be escaped.`),q.InvalidCharacter),!0}return!1}function d(e,t){return e.length=a.getTokenOffset()+a.getTokenLength()-e.offset,t&&s(),e}function f(e){if(a.getToken()!==3)return;let t=new Rc(e,a.getTokenOffset());s();let n=!1;for(;a.getToken()!==4&&a.getToken()!==17;){if(a.getToken()===5){n||l(J(`Value expected`),q.ValueExpected);let e=a.getTokenOffset();if(s(),a.getToken()===4){n&&c(J(`Trailing comma`),q.TrailingComma,e,e+1);continue}}else n&&l(J(`Expected comma`),q.CommaExpected);let e=y(t);e?t.items.push(e):l(J(`Value expected`),q.ValueExpected,void 0,[],[4,5]),n=!0}return a.getToken()===4?d(t,!0):l(J(`Expected comma or closing bracket`),q.CommaOrCloseBacketExpected,t)}let p=new Bc(void 0,0,0);function m(t,n){let r=new Vc(t,a.getTokenOffset(),p),i=g(r);if(!i)if(a.getToken()===16){l(J(`Property keys must be doublequoted`),q.PropertyKeysMustBeDoublequoted);let e=new Bc(r,a.getTokenOffset(),a.getTokenLength());e.value=a.getTokenValue(),i=e,s()}else return;if(r.keyNode=i,i.value!==`//`){let e=n[i.value];e?(c(J(`Duplicate object key`),q.DuplicateKey,r.keyNode.offset,r.keyNode.offset+r.keyNode.length,W.Warning),Jo(e)&&c(J(`Duplicate object key`),q.DuplicateKey,e.keyNode.offset,e.keyNode.offset+e.keyNode.length,W.Warning),n[i.value]=!0):n[i.value]=r}if(a.getToken()===6)r.colonOffset=a.getTokenOffset(),s();else if(l(J(`Colon expected`),q.ColonExpected),a.getToken()===10&&e.positionAt(i.offset+i.length).line<e.positionAt(a.getTokenOffset()).line)return r.length=i.length,r;let o=y(r);return o?(r.valueNode=o,r.length=o.offset+o.length-r.offset,r):l(J(`Value expected`),q.ValueExpected,r,[],[2,5])}function h(e){if(a.getToken()!==1)return;let t=new Hc(e,a.getTokenOffset()),n=Object.create(null);s();let r=!1;for(;a.getToken()!==2&&a.getToken()!==17;){if(a.getToken()===5){r||l(J(`Property expected`),q.PropertyExpected);let e=a.getTokenOffset();if(s(),a.getToken()===2){r&&c(J(`Trailing comma`),q.TrailingComma,e,e+1);continue}}else r&&l(J(`Expected comma`),q.CommaExpected);let e=m(t,n);e?t.properties.push(e):l(J(`Property expected`),q.PropertyExpected,void 0,[],[2,5]),r=!0}return a.getToken()===2?d(t,!0):l(J(`Expected comma or closing brace`),q.CommaOrCloseBraceExpected,t)}function g(e){if(a.getToken()!==10)return;let t=new Bc(e,a.getTokenOffset());return t.value=a.getTokenValue(),d(t,!0)}function _(e){if(a.getToken()!==11)return;let t=new zc(e,a.getTokenOffset());if(a.getTokenError()===0){let e=a.getTokenValue();try{let n=JSON.parse(e);if(!V(n))return l(J(`Invalid number format.`),q.Undefined,t);t.value=n}catch{return l(J(`Invalid number format.`),q.Undefined,t)}t.isInteger=e.indexOf(`.`)===-1}return d(t,!0)}function v(e){switch(a.getToken()){case 7:return d(new Ic(e,a.getTokenOffset()),!0);case 8:return d(new Lc(e,!0,a.getTokenOffset()),!0);case 9:return d(new Lc(e,!1,a.getTokenOffset()),!0);default:return}}function y(e){return f(e)||h(e)||g(e)||_(e)||v(e)}let b;return s()!==17&&(b=y(b),b?a.getToken()!==17&&l(J(`End of file expected.`),q.Undefined):l(J(`Expected a JSON object, array or literal.`),q.Undefined)),new Qc(b,n,o)}function tl(e,t,n){if(typeof e==`object`&&e){let r=t+`	`;if(Array.isArray(e)){if(e.length===0)return`[]`;let i=`[
`;for(let t=0;t<e.length;t++)i+=r+tl(e[t],r,n),t<e.length-1&&(i+=`,`),i+=`
`;return i+=t+`]`,i}else{let i=Object.keys(e);if(i.length===0)return`{}`;let a=`{
`;for(let t=0;t<i.length;t++){let o=i[t];a+=r+JSON.stringify(o)+`: `+tl(e[o],r,n),t<i.length-1&&(a+=`,`),a+=`
`}return a+=t+`}`,a}}return n(e)}var nl=class{constructor(e,t=[],n=Promise,r={}){this.schemaService=e,this.contributions=t,this.promiseConstructor=n,this.clientCapabilities=r}doResolve(e){for(let t=this.contributions.length-1;t>=0;t--){let n=this.contributions[t].resolveCompletion;if(n){let t=n(e);if(t)return t}}return this.promiseConstructor.resolve(e)}doComplete(e,t,n){let r={items:[],isIncomplete:!1},i=e.getText(),a=e.offsetAt(t),o=n.getNodeFromOffset(a,!0);if(this.isInComment(e,o?o.offset:0,a))return Promise.resolve(r);if(o&&a===o.offset+o.length&&a>0){let e=i[a-1];(o.type===`object`&&e===`}`||o.type===`array`&&e===`]`)&&(o=o.parent)}let s=this.getCurrentWord(e,a),c;if(o&&(o.type===`string`||o.type===`number`||o.type===`boolean`||o.type===`null`))c=U.create(e.positionAt(o.offset),e.positionAt(o.offset+o.length));else{let n=a-s.length;n>0&&i[n-1]===`"`&&n--,c=U.create(e.positionAt(n),t)}let l=new Map,u={add:e=>{let t=e.label,n=l.get(t);if(n)n.documentation||=e.documentation,n.detail||=e.detail,n.labelDetails||=e.labelDetails;else{if(t=t.replace(/[\n]/g,`↵`),t.length>60){let e=t.substr(0,57).trim()+`...`;l.has(e)||(t=e)}e.textEdit=_s.replace(c,e.insertText),e.label=t,l.set(t,e),r.items.push(e)}},setAsIncomplete:()=>{r.isIncomplete=!0},error:e=>{console.error(e)},getNumberOfProposals:()=>r.items.length};return this.schemaService.getSchemaForResource(e.uri,n).then(t=>{let d=[],f=!0,p=``,m;if(o&&o.type===`string`){let e=o.parent;e&&e.type===`property`&&e.keyNode===o&&(f=!e.valueNode,m=e,p=i.substr(o.offset+1,o.length-2),e&&(o=e.parent))}if(o&&o.type===`object`){if(o.offset===a)return r;o.properties.forEach(e=>{(!m||m!==e)&&l.set(e.keyNode.value,Ls.create(`__`))});let h=``;f&&(h=this.evaluateSeparatorAfter(e,e.offsetAt(c.end))),t?this.getPropertyCompletions(t,n,o,f,h,u):this.getSchemaLessPropertyCompletions(n,o,p,u);let g=Xc(o);this.contributions.forEach(t=>{let n=t.collectPropertyCompletions(e.uri,g,s,f,h===``,u);n&&d.push(n)}),!t&&s.length>0&&i.charAt(a-s.length-1)!==`"`&&(u.add({kind:Ms.Property,label:this.getLabelForValue(s),insertText:this.getInsertTextForProperty(s,void 0,!1,h),insertTextFormat:G.Snippet,documentation:``}),u.setAsIncomplete())}let h={};return t?this.getValueCompletions(t,n,o,a,e,u,h):this.getSchemaLessValueCompletions(n,o,a,e,u),this.contributions.length>0&&this.getContributedValueCompletions(n,o,a,e,u,d),this.promiseConstructor.all(d).then(()=>{if(u.getNumberOfProposals()===0){let t=a;o&&(o.type===`string`||o.type===`number`||o.type===`boolean`||o.type===`null`)&&(t=o.offset+o.length);let n=this.evaluateSeparatorAfter(e,t);this.addFillerValueCompletions(h,n,u)}return r})})}getPropertyCompletions(e,t,n,r,i,a){t.getMatchingSchemas(e.schema,n.offset).forEach(e=>{if(e.node===n&&!e.inverted){let t=e.schema.properties;t&&Object.keys(t).forEach(e=>{let n=t[e];if(typeof n==`object`&&!n.deprecationMessage&&!n.doNotSuggest){let t={kind:Ms.Property,label:e,insertText:this.getInsertTextForProperty(e,n,r,i),insertTextFormat:G.Snippet,filterText:this.getFilterTextForValue(e),documentation:this.fromMarkup(n.markdownDescription)||n.description||``};n.suggestSortText!==void 0&&(t.sortText=n.suggestSortText),t.insertText&&Xo(t.insertText,`$1${i}`)&&(t.command={title:`Suggest`,command:`editor.action.triggerSuggest`}),a.add(t)}});let n=e.schema.propertyNames;if(typeof n==`object`&&!n.deprecationMessage&&!n.doNotSuggest){let e=(e,t=void 0)=>{let o={kind:Ms.Property,label:e,insertText:this.getInsertTextForProperty(e,void 0,r,i),insertTextFormat:G.Snippet,filterText:this.getFilterTextForValue(e),documentation:t||this.fromMarkup(n.markdownDescription)||n.description||``};n.suggestSortText!==void 0&&(o.sortText=n.suggestSortText),o.insertText&&Xo(o.insertText,`$1${i}`)&&(o.command={title:`Suggest`,command:`editor.action.triggerSuggest`}),a.add(o)};if(n.enum)for(let t=0;t<n.enum.length;t++){let r;n.markdownEnumDescriptions&&t<n.markdownEnumDescriptions.length?r=this.fromMarkup(n.markdownEnumDescriptions[t]):n.enumDescriptions&&t<n.enumDescriptions.length&&(r=n.enumDescriptions[t]),e(n.enum[t],r)}n.const&&e(n.const)}}})}getSchemaLessPropertyCompletions(e,t,n,r){let i=e=>{e.properties.forEach(e=>{let t=e.keyNode.value;r.add({kind:Ms.Property,label:t,insertText:this.getInsertTextForValue(t,``),insertTextFormat:G.Snippet,filterText:this.getFilterTextForValue(t),documentation:``})})};if(t.parent)if(t.parent.type===`property`){let n=t.parent.keyNode.value;e.visit(e=>(e.type===`property`&&e!==t.parent&&e.keyNode.value===n&&e.valueNode&&e.valueNode.type===`object`&&i(e.valueNode),!0))}else t.parent.type===`array`&&t.parent.items.forEach(e=>{e.type===`object`&&e!==t&&i(e)});else t.type===`object`&&r.add({kind:Ms.Property,label:`$schema`,insertText:this.getInsertTextForProperty(`$schema`,void 0,!0,``),insertTextFormat:G.Snippet,documentation:``,filterText:this.getFilterTextForValue(`$schema`)})}getSchemaLessValueCompletions(e,t,n,r,i){let a=n;if(t&&(t.type===`string`||t.type===`number`||t.type===`boolean`||t.type===`null`)&&(a=t.offset+t.length,t=t.parent),!t){i.add({kind:this.getSuggestionKind(`object`),label:`Empty object`,insertText:this.getInsertTextForValue({},``),insertTextFormat:G.Snippet,documentation:``}),i.add({kind:this.getSuggestionKind(`array`),label:`Empty array`,insertText:this.getInsertTextForValue([],``),insertTextFormat:G.Snippet,documentation:``});return}let o=this.evaluateSeparatorAfter(r,a),s=e=>{e.parent&&!Zc(e.parent,n,!0)&&i.add({kind:this.getSuggestionKind(e.type),label:this.getLabelTextForMatchingNode(e,r),insertText:this.getInsertTextForMatchingNode(e,r,o),insertTextFormat:G.Snippet,documentation:``}),e.type===`boolean`&&this.addBooleanValueCompletion(!e.value,o,i)};if(t.type===`property`&&n>(t.colonOffset||0)){let r=t.valueNode;if(r&&(n>r.offset+r.length||r.type===`object`||r.type===`array`))return;let a=t.keyNode.value;e.visit(e=>(e.type===`property`&&e.keyNode.value===a&&e.valueNode&&s(e.valueNode),!0)),a===`$schema`&&t.parent&&!t.parent.parent&&this.addDollarSchemaCompletions(o,i)}if(t.type===`array`)if(t.parent&&t.parent.type===`property`){let n=t.parent.keyNode.value;e.visit(e=>(e.type===`property`&&e.keyNode.value===n&&e.valueNode&&e.valueNode.type===`array`&&e.valueNode.items.forEach(s),!0))}else t.items.forEach(s)}getValueCompletions(e,t,n,r,i,a,o){let s=r,c,l;if(n&&(n.type===`string`||n.type===`number`||n.type===`boolean`||n.type===`null`)&&(s=n.offset+n.length,l=n,n=n.parent),!n){this.addSchemaValueCompletions(e.schema,``,a,o);return}if(n.type===`property`&&r>(n.colonOffset||0)){let e=n.valueNode;if(e&&r>e.offset+e.length)return;c=n.keyNode.value,n=n.parent}if(n&&(c!==void 0||n.type===`array`)){let u=this.evaluateSeparatorAfter(i,s),d=t.getMatchingSchemas(e.schema,n.offset,l);for(let e of d)if(e.node===n&&!e.inverted&&e.schema){if(n.type===`array`&&e.schema.items){let t=a;if(e.schema.uniqueItems){let e=new Set;n.children.forEach(t=>{t.type!==`array`&&t.type!==`object`&&e.add(this.getLabelForValue(Yc(t)))}),t={...a,add(t){e.has(t.label)||a.add(t)}}}if(Array.isArray(e.schema.items)){let a=this.findItemAtOffset(n,i,r);a<e.schema.items.length&&this.addSchemaValueCompletions(e.schema.items[a],u,t,o)}else this.addSchemaValueCompletions(e.schema.items,u,t,o)}if(c!==void 0){let t=!1;if(e.schema.properties){let n=e.schema.properties[c];n&&(t=!0,this.addSchemaValueCompletions(n,u,a,o))}if(e.schema.patternProperties&&!t){for(let n of Object.keys(e.schema.patternProperties))if(Zo(n)?.test(c)){t=!0;let r=e.schema.patternProperties[n];this.addSchemaValueCompletions(r,u,a,o)}}if(e.schema.additionalProperties&&!t){let t=e.schema.additionalProperties;this.addSchemaValueCompletions(t,u,a,o)}}}c===`$schema`&&!n.parent&&this.addDollarSchemaCompletions(u,a),o.boolean&&(this.addBooleanValueCompletion(!0,u,a),this.addBooleanValueCompletion(!1,u,a)),o.null&&this.addNullValueCompletion(u,a)}}getContributedValueCompletions(e,t,n,r,i,a){if(!t)this.contributions.forEach(e=>{let t=e.collectDefaultCompletions(r.uri,i);t&&a.push(t)});else if((t.type===`string`||t.type===`number`||t.type===`boolean`||t.type===`null`)&&(t=t.parent),t&&t.type===`property`&&n>(t.colonOffset||0)){let e=t.keyNode.value,o=t.valueNode;if((!o||n<=o.offset+o.length)&&t.parent){let n=Xc(t.parent);this.contributions.forEach(t=>{let o=t.collectValueCompletions(r.uri,n,e,i);o&&a.push(o)})}}}addSchemaValueCompletions(e,t,n,r){typeof e==`object`&&(this.addEnumValueCompletions(e,t,n),this.addDefaultValueCompletions(e,t,n),this.collectTypes(e,r),Array.isArray(e.allOf)&&e.allOf.forEach(e=>this.addSchemaValueCompletions(e,t,n,r)),Array.isArray(e.anyOf)&&e.anyOf.forEach(e=>this.addSchemaValueCompletions(e,t,n,r)),Array.isArray(e.oneOf)&&e.oneOf.forEach(e=>this.addSchemaValueCompletions(e,t,n,r)))}addDefaultValueCompletions(e,t,n,r=0){let i=!1;if(Go(e.default)){let a=e.type,o=e.default;for(let e=r;e>0;e--)o=[o],a=`array`;let s={kind:this.getSuggestionKind(a),label:this.getLabelForValue(o),insertText:this.getInsertTextForValue(o,t),insertTextFormat:G.Snippet};this.doesSupportsLabelDetails()?s.labelDetails={description:J(`Default value`)}:s.detail=J(`Default value`),n.add(s),i=!0}Array.isArray(e.examples)&&e.examples.forEach(a=>{let o=e.type,s=a;for(let e=r;e>0;e--)s=[s],o=`array`;n.add({kind:this.getSuggestionKind(o),label:this.getLabelForValue(s),insertText:this.getInsertTextForValue(s,t),insertTextFormat:G.Snippet}),i=!0}),Array.isArray(e.defaultSnippets)&&e.defaultSnippets.forEach(a=>{let o=e.type,s=a.body,c=a.label,l,u;if(Go(s)){e.type;for(let e=r;e>0;e--)s=[s];l=this.getInsertTextForSnippetValue(s,t),u=this.getFilterTextForSnippetValue(s),c||=this.getLabelForSnippetValue(s)}else if(typeof a.bodyText==`string`){let e=``,n=``,i=``;for(let t=r;t>0;t--)e=e+i+`[
`,n=n+`
`+i+`]`,i+=`	`,o=`array`;l=e+i+a.bodyText.split(`
`).join(`
`+i)+n+t,c||=l,u=l.replace(/[\n]/g,``)}else return;n.add({kind:this.getSuggestionKind(o),label:c,documentation:this.fromMarkup(a.markdownDescription)||a.description,insertText:l,insertTextFormat:G.Snippet,filterText:u}),i=!0}),!i&&typeof e.items==`object`&&!Array.isArray(e.items)&&r<5&&this.addDefaultValueCompletions(e.items,t,n,r+1)}addEnumValueCompletions(e,t,n){if(Go(e.const)&&n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(e.const),insertText:this.getInsertTextForValue(e.const,t),insertTextFormat:G.Snippet,documentation:this.fromMarkup(e.markdownDescription)||e.description}),Array.isArray(e.enum))for(let r=0,i=e.enum.length;r<i;r++){let i=e.enum[r],a=this.fromMarkup(e.markdownDescription)||e.description;e.markdownEnumDescriptions&&r<e.markdownEnumDescriptions.length&&this.doesSupportMarkdown()?a=this.fromMarkup(e.markdownEnumDescriptions[r]):e.enumDescriptions&&r<e.enumDescriptions.length&&(a=e.enumDescriptions[r]),n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(i),insertText:this.getInsertTextForValue(i,t),insertTextFormat:G.Snippet,documentation:a})}}collectTypes(e,t){if(Array.isArray(e.enum)||Go(e.const))return;let n=e.type;Array.isArray(n)?n.forEach(e=>t[e]=!0):n&&(t[n]=!0)}addFillerValueCompletions(e,t,n){e.object&&n.add({kind:this.getSuggestionKind(`object`),label:`{}`,insertText:this.getInsertTextForGuessedValue({},t),insertTextFormat:G.Snippet,detail:J(`New object`),documentation:``}),e.array&&n.add({kind:this.getSuggestionKind(`array`),label:`[]`,insertText:this.getInsertTextForGuessedValue([],t),insertTextFormat:G.Snippet,detail:J(`New array`),documentation:``})}addBooleanValueCompletion(e,t,n){n.add({kind:this.getSuggestionKind(`boolean`),label:e?`true`:`false`,insertText:this.getInsertTextForValue(e,t),insertTextFormat:G.Snippet,documentation:``})}addNullValueCompletion(e,t){t.add({kind:this.getSuggestionKind(`null`),label:`null`,insertText:`null`+e,insertTextFormat:G.Snippet,documentation:``})}addDollarSchemaCompletions(e,t){this.schemaService.getRegisteredSchemaIds(e=>e===`http`||e===`https`).forEach(n=>{n.startsWith(`http://json-schema.org/draft-`)&&(n+=`#`),t.add({kind:Ms.Module,label:this.getLabelForValue(n),filterText:this.getFilterTextForValue(n),insertText:this.getInsertTextForValue(n,e),insertTextFormat:G.Snippet,documentation:``})})}getLabelForValue(e){return JSON.stringify(e)}getValueFromLabel(e){return JSON.parse(e)}getFilterTextForValue(e){return JSON.stringify(e)}getFilterTextForSnippetValue(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,`$1`)}getLabelForSnippetValue(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,`$1`)}getInsertTextForPlainText(e){return e.replace(/[\\\$\}]/g,`\\$&`)}getInsertTextForValue(e,t){let n=JSON.stringify(e,null,`	`);return n===`{}`?`{$1}`+t:n===`[]`?`[$1]`+t:this.getInsertTextForPlainText(n+t)}getInsertTextForSnippetValue(e,t){return tl(e,``,e=>typeof e==`string`&&e[0]===`^`?e.substr(1):JSON.stringify(e))+t}getInsertTextForGuessedValue(e,t){switch(typeof e){case`object`:return e===null?"${1:null}"+t:this.getInsertTextForValue(e,t);case`string`:let n=JSON.stringify(e);return n=n.substr(1,n.length-2),n=this.getInsertTextForPlainText(n),'"${1:'+n+`}"`+t;case`number`:case`boolean`:return"${1:"+JSON.stringify(e)+`}`+t}return this.getInsertTextForValue(e,t)}getSuggestionKind(e){if(Array.isArray(e)){let t=e;e=t.length>0?t[0]:void 0}if(!e)return Ms.Value;switch(e){case`string`:return Ms.Value;case`object`:return Ms.Module;case`property`:return Ms.Property;default:return Ms.Value}}getLabelTextForMatchingNode(e,t){switch(e.type){case`array`:return`[]`;case`object`:return`{}`;default:return t.getText().substr(e.offset,e.length)}}getInsertTextForMatchingNode(e,t,n){switch(e.type){case`array`:return this.getInsertTextForValue([],n);case`object`:return this.getInsertTextForValue({},n);default:let r=t.getText().substr(e.offset,e.length)+n;return this.getInsertTextForPlainText(r)}}getInsertTextForProperty(e,t,n,r){let i=this.getInsertTextForValue(e,``);if(!n)return i;let a=i+`: `,o,s=0;if(t){if(Array.isArray(t.defaultSnippets)){if(t.defaultSnippets.length===1){let e=t.defaultSnippets[0].body;Go(e)&&(o=this.getInsertTextForSnippetValue(e,``))}s+=t.defaultSnippets.length}if(t.enum&&(!o&&t.enum.length===1&&(o=this.getInsertTextForGuessedValue(t.enum[0],``)),s+=t.enum.length),Go(t.const)&&(o||=this.getInsertTextForGuessedValue(t.const,``),s++),Go(t.default)&&(o||=this.getInsertTextForGuessedValue(t.default,``),s++),Array.isArray(t.examples)&&t.examples.length&&(o||=this.getInsertTextForGuessedValue(t.examples[0],``),s+=t.examples.length),s===0){let e=Array.isArray(t.type)?t.type[0]:t.type;switch(e||(t.properties?e=`object`:t.items&&(e=`array`)),e){case`boolean`:o=`$1`;break;case`string`:o=`"$1"`;break;case`object`:o=`{$1}`;break;case`array`:o=`[$1]`;break;case`number`:case`integer`:o="${1:0}";break;case`null`:o="${1:null}";break;default:return i}}}return(!o||s>1)&&(o=`$1`),a+o+r}getCurrentWord(e,t){let n=t-1,r=e.getText();for(;n>=0&&` 	
\r\v":{[,]}`.indexOf(r.charAt(n))===-1;)n--;return r.substring(n+1,t)}evaluateSeparatorAfter(e,t){let n=Fo(e.getText(),!0);switch(n.setPosition(t),n.scan()){case 5:case 2:case 4:case 17:return``;default:return`,`}}findItemAtOffset(e,t,n){let r=Fo(t.getText(),!0),i=e.items;for(let e=i.length-1;e>=0;e--){let t=i[e];if(n>t.offset+t.length)return r.setPosition(t.offset+t.length),r.scan()===5&&n>=r.getTokenOffset()+r.getTokenLength()?e+1:e;if(n>=t.offset)return e}return 0}isInComment(e,t,n){let r=Fo(e.getText(),!1);r.setPosition(t);let i=r.scan();for(;i!==17&&r.getTokenOffset()+r.getTokenLength()<n;)i=r.scan();return(i===12||i===13)&&r.getTokenOffset()<=n}fromMarkup(e){if(e&&this.doesSupportMarkdown())return{kind:As.Markdown,value:e}}doesSupportMarkdown(){if(!Go(this.supportsMarkdown)){let e=this.clientCapabilities.textDocument?.completion?.completionItem?.documentationFormat;this.supportsMarkdown=Array.isArray(e)&&e.indexOf(As.Markdown)!==-1}return this.supportsMarkdown}doesSupportsCommitCharacters(){return Go(this.supportsCommitCharacters)||(this.labelDetailsSupport=this.clientCapabilities.textDocument?.completion?.completionItem?.commitCharactersSupport),this.supportsCommitCharacters}doesSupportsLabelDetails(){return Go(this.labelDetailsSupport)||(this.labelDetailsSupport=this.clientCapabilities.textDocument?.completion?.completionItem?.labelDetailsSupport),this.labelDetailsSupport}},rl=class{constructor(e,t=[],n){this.schemaService=e,this.contributions=t,this.promise=n||Promise}doHover(e,t,n){let r=e.offsetAt(t),i=n.getNodeFromOffset(r);if(!i||(i.type===`object`||i.type===`array`)&&r>i.offset+1&&r<i.offset+i.length-1)return this.promise.resolve(null);let a=i;if(i.type===`string`){let e=i.parent;if(e&&e.type===`property`&&e.keyNode===i&&(i=e.valueNode,!i))return this.promise.resolve(null)}let o=U.create(e.positionAt(a.offset),e.positionAt(a.offset+a.length)),s=e=>({contents:e,range:o}),c=Xc(i);for(let t=this.contributions.length-1;t>=0;t--){let n=this.contributions[t].getInfoContribution(e.uri,c);if(n)return n.then(e=>s(e))}return this.schemaService.getSchemaForResource(e.uri,n).then(e=>{if(e&&i){let t=n.getMatchingSchemas(e.schema,i.offset),r,a,o,c;t.every(e=>{if(e.node===i&&!e.inverted&&e.schema&&(r||=e.schema.title,a=a||e.schema.markdownDescription||il(e.schema.description),e.schema.enum)){let t=e.schema.enum.indexOf(Yc(i));e.schema.markdownEnumDescriptions?o=e.schema.markdownEnumDescriptions[t]:e.schema.enumDescriptions&&(o=il(e.schema.enumDescriptions[t])),o&&(c=e.schema.enum[t],typeof c!=`string`&&(c=JSON.stringify(c)))}return!0});let l=``;return r&&(l=il(r)),a&&(l.length>0&&(l+=`

`),l+=a),o&&(l.length>0&&(l+=`

`),l+=`\`${al(c)}\`: ${o}`),s([l])}return null})}};function il(e){if(e)return e.replace(/([^\n\r])(\r?\n)([^\n\r])/gm,`$1

$3`).replace(/[\\`*_{}[\]()#+\-.!]/g,`\\$&`)}function al(e){return e.indexOf("`")===-1?e:"`` "+e+" ``"}var ol=class{constructor(e,t){this.jsonSchemaService=e,this.promise=t,this.validationEnabled=!0}configure(e){e&&(this.validationEnabled=e.validate!==!1,this.commentSeverity=e.allowComments?void 0:W.Error)}doValidation(e,t,n,r){if(!this.validationEnabled)return this.promise.resolve([]);let i=[],a={},o=e=>{let t=e.range.start.line+` `+e.range.start.character+` `+e.message;a[t]||(a[t]=!0,i.push(e))},s=r=>{let a=n?.trailingCommas?ul(n.trailingCommas):W.Error,s=n?.comments?ul(n.comments):this.commentSeverity,c=n?.schemaValidation?ul(n.schemaValidation):W.Warning,l=n?.schemaRequest?ul(n.schemaRequest):W.Warning;if(r){let i=(n,r)=>{if(t.root&&l){let i=t.root,a=i.type===`object`?i.properties[0]:void 0;if(a&&a.keyNode.value===`$schema`){let t=a.valueNode||a,i=U.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length));o(hs.create(i,n,l,r))}else{let t=U.create(e.positionAt(i.offset),e.positionAt(i.offset+1));o(hs.create(t,n,l,r))}}};if(r.errors.length)i(r.errors[0],q.SchemaResolveError);else if(c){for(let e of r.warnings)i(e,q.SchemaUnsupportedFeature);let a=t.validate(e,r.schema,c,n?.schemaDraft);a&&a.forEach(o)}cl(r.schema)&&(s=void 0),ll(r.schema)&&(a=void 0)}for(let e of t.syntaxErrors){if(e.code===q.TrailingComma){if(typeof a!=`number`)continue;e.severity=a}o(e)}if(typeof s==`number`){let e=J(`Comments are not permitted in JSON.`);t.comments.forEach(t=>{o(hs.create(t,e,s,q.CommentNotPermitted))})}return i};if(r){let e=r.id||`schemaservice://untitled/`+ sl++;return this.jsonSchemaService.registerExternalSchema({uri:e,schema:r}).getResolvedSchema().then(e=>s(e))}return this.jsonSchemaService.getSchemaForResource(e.uri,t).then(e=>s(e))}getLanguageStatus(e,t){return{schemas:this.jsonSchemaService.getSchemaURIsForResource(e.uri,t)}}},sl=0;function cl(e){if(e&&typeof e==`object`){if(Ko(e.allowComments))return e.allowComments;if(e.allOf)for(let t of e.allOf){let e=cl(t);if(Ko(e))return e}}}function ll(e){if(e&&typeof e==`object`){if(Ko(e.allowTrailingCommas))return e.allowTrailingCommas;let t=e;if(Ko(t.allowsTrailingCommas))return t.allowsTrailingCommas;if(e.allOf)for(let t of e.allOf){let e=ll(t);if(Ko(e))return e}}}function ul(e){switch(e){case`error`:return W.Error;case`warning`:return W.Warning;case`ignore`:return}}var dl=48,fl=57,pl=65,ml=97,hl=102;function Q(e){return e<dl?0:e<=fl?e-dl:(e<ml&&(e+=ml-pl),e>=ml&&e<=hl?e-ml+10:0)}function gl(e){if(e[0]===`#`)switch(e.length){case 4:return{red:Q(e.charCodeAt(1))*17/255,green:Q(e.charCodeAt(2))*17/255,blue:Q(e.charCodeAt(3))*17/255,alpha:1};case 5:return{red:Q(e.charCodeAt(1))*17/255,green:Q(e.charCodeAt(2))*17/255,blue:Q(e.charCodeAt(3))*17/255,alpha:Q(e.charCodeAt(4))*17/255};case 7:return{red:(Q(e.charCodeAt(1))*16+Q(e.charCodeAt(2)))/255,green:(Q(e.charCodeAt(3))*16+Q(e.charCodeAt(4)))/255,blue:(Q(e.charCodeAt(5))*16+Q(e.charCodeAt(6)))/255,alpha:1};case 9:return{red:(Q(e.charCodeAt(1))*16+Q(e.charCodeAt(2)))/255,green:(Q(e.charCodeAt(3))*16+Q(e.charCodeAt(4)))/255,blue:(Q(e.charCodeAt(5))*16+Q(e.charCodeAt(6)))/255,alpha:(Q(e.charCodeAt(7))*16+Q(e.charCodeAt(8)))/255}}}var _l=class{constructor(e){this.schemaService=e}findDocumentSymbols(e,t,n={resultLimit:Number.MAX_VALUE}){let r=t.root;if(!r)return[];let i=n.resultLimit||Number.MAX_VALUE,a=e.uri;if((a===`vscode://defaultsettings/keybindings.json`||Xo(a.toLowerCase(),`/user/keybindings.json`))&&r.type===`array`){let t=[];for(let o of r.items)if(o.type===`object`){for(let r of o.properties)if(r.keyNode.value===`key`&&r.valueNode){let s=rs.create(e.uri,vl(e,o));if(t.push({name:yl(r.valueNode),kind:Gs.Function,location:s}),i--,i<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),t}}return t}let o=[{node:r,containerName:``}],s=0,c=!1,l=[],u=(t,n)=>{t.type===`array`?t.items.forEach(e=>{e&&o.push({node:e,containerName:n})}):t.type===`object`&&t.properties.forEach(t=>{let r=t.valueNode;if(r)if(i>0){i--;let a=rs.create(e.uri,vl(e,t)),s=n?n+`.`+t.keyNode.value:t.keyNode.value;l.push({name:this.getKeyLabel(t),kind:this.getSymbolKind(r.type),location:a,containerName:n}),o.push({node:r,containerName:s})}else c=!0})};for(;s<o.length;){let e=o[s++];u(e.node,e.containerName)}return c&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),l}findDocumentSymbols2(e,t,n={resultLimit:Number.MAX_VALUE}){let r=t.root;if(!r)return[];let i=n.resultLimit||Number.MAX_VALUE,a=e.uri;if((a===`vscode://defaultsettings/keybindings.json`||Xo(a.toLowerCase(),`/user/keybindings.json`))&&r.type===`array`){let t=[];for(let o of r.items)if(o.type===`object`){for(let r of o.properties)if(r.keyNode.value===`key`&&r.valueNode){let s=vl(e,o),c=vl(e,r.keyNode);if(t.push({name:yl(r.valueNode),kind:Gs.Function,range:s,selectionRange:c}),i--,i<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),t}}return t}let o=[],s=[{node:r,result:o}],c=0,l=!1,u=(t,n)=>{t.type===`array`?t.items.forEach((t,r)=>{if(t)if(i>0){i--;let a=vl(e,t),o=a,c={name:String(r),kind:this.getSymbolKind(t.type),range:a,selectionRange:o,children:[]};n.push(c),s.push({result:c.children,node:t})}else l=!0}):t.type===`object`&&t.properties.forEach(t=>{let r=t.valueNode;if(r)if(i>0){i--;let a=vl(e,t),o=vl(e,t.keyNode),c=[],l={name:this.getKeyLabel(t),kind:this.getSymbolKind(r.type),range:a,selectionRange:o,children:c,detail:this.getDetail(r)};n.push(l),s.push({result:c,node:r})}else l=!0})};for(;c<s.length;){let e=s[c++];u(e.node,e.result)}return l&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),o}getSymbolKind(e){switch(e){case`object`:return Gs.Module;case`string`:return Gs.String;case`number`:return Gs.Number;case`array`:return Gs.Array;case`boolean`:return Gs.Boolean;default:return Gs.Variable}}getKeyLabel(e){let t=e.keyNode.value;return t&&=t.replace(/[\n]/g,`↵`),t&&t.trim()?t:`"${t}"`}getDetail(e){if(e){if(e.type===`boolean`||e.type===`number`||e.type===`null`||e.type===`string`)return String(e.value);if(e.type===`array`)return e.children.length?void 0:`[]`;if(e.type===`object`)return e.children.length?void 0:`{}`}}findDocumentColors(e,t,n){return this.schemaService.getSchemaForResource(e.uri,t).then(r=>{let i=[];if(r){let a=n&&typeof n.resultLimit==`number`?n.resultLimit:Number.MAX_VALUE,o=t.getMatchingSchemas(r.schema),s={};for(let t of o)if(!t.inverted&&t.schema&&(t.schema.format===`color`||t.schema.format===`color-hex`)&&t.node&&t.node.type===`string`){let r=String(t.node.offset);if(!s[r]){let o=gl(Yc(t.node));if(o){let n=vl(e,t.node);i.push({color:o,range:n})}if(s[r]=!0,a--,a<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(e.uri),i}}}return i})}getColorPresentations(e,t,n,r){let i=[],a=Math.round(n.red*255),o=Math.round(n.green*255),s=Math.round(n.blue*255);function c(e){let t=e.toString(16);return t.length===2?t:`0`+t}let l;return l=n.alpha===1?`#${c(a)}${c(o)}${c(s)}`:`#${c(a)}${c(o)}${c(s)}${c(Math.round(n.alpha*255))}`,i.push({label:l,textEdit:_s.replace(r,JSON.stringify(l))}),i}};function vl(e,t){return U.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length))}function yl(e){return Yc(e)||J(`<empty>`)}var bl={schemaAssociations:[],schemas:{"http://json-schema.org/draft-04/schema#":{$schema:`http://json-schema.org/draft-04/schema#`,definitions:{schemaArray:{type:`array`,minItems:1,items:{$ref:`#`}},positiveInteger:{type:`integer`,minimum:0},positiveIntegerDefault0:{allOf:[{$ref:`#/definitions/positiveInteger`},{default:0}]},simpleTypes:{type:`string`,enum:[`array`,`boolean`,`integer`,`null`,`number`,`object`,`string`]},stringArray:{type:`array`,items:{type:`string`},minItems:1,uniqueItems:!0}},type:`object`,properties:{id:{type:`string`,format:`uri`},$schema:{type:`string`,format:`uri`},title:{type:`string`},description:{type:`string`},default:{},multipleOf:{type:`number`,minimum:0,exclusiveMinimum:!0},maximum:{type:`number`},exclusiveMaximum:{type:`boolean`,default:!1},minimum:{type:`number`},exclusiveMinimum:{type:`boolean`,default:!1},maxLength:{allOf:[{$ref:`#/definitions/positiveInteger`}]},minLength:{allOf:[{$ref:`#/definitions/positiveIntegerDefault0`}]},pattern:{type:`string`,format:`regex`},additionalItems:{anyOf:[{type:`boolean`},{$ref:`#`}],default:{}},items:{anyOf:[{$ref:`#`},{$ref:`#/definitions/schemaArray`}],default:{}},maxItems:{allOf:[{$ref:`#/definitions/positiveInteger`}]},minItems:{allOf:[{$ref:`#/definitions/positiveIntegerDefault0`}]},uniqueItems:{type:`boolean`,default:!1},maxProperties:{allOf:[{$ref:`#/definitions/positiveInteger`}]},minProperties:{allOf:[{$ref:`#/definitions/positiveIntegerDefault0`}]},required:{allOf:[{$ref:`#/definitions/stringArray`}]},additionalProperties:{anyOf:[{type:`boolean`},{$ref:`#`}],default:{}},definitions:{type:`object`,additionalProperties:{$ref:`#`},default:{}},properties:{type:`object`,additionalProperties:{$ref:`#`},default:{}},patternProperties:{type:`object`,additionalProperties:{$ref:`#`},default:{}},dependencies:{type:`object`,additionalProperties:{anyOf:[{$ref:`#`},{$ref:`#/definitions/stringArray`}]}},enum:{type:`array`,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:`#/definitions/simpleTypes`},{type:`array`,items:{$ref:`#/definitions/simpleTypes`},minItems:1,uniqueItems:!0}]},format:{anyOf:[{type:`string`,enum:[`date-time`,`uri`,`email`,`hostname`,`ipv4`,`ipv6`,`regex`]},{type:`string`}]},allOf:{allOf:[{$ref:`#/definitions/schemaArray`}]},anyOf:{allOf:[{$ref:`#/definitions/schemaArray`}]},oneOf:{allOf:[{$ref:`#/definitions/schemaArray`}]},not:{allOf:[{$ref:`#`}]}},dependencies:{exclusiveMaximum:[`maximum`],exclusiveMinimum:[`minimum`]},default:{}},"http://json-schema.org/draft-07/schema#":{definitions:{schemaArray:{type:`array`,minItems:1,items:{$ref:`#`}},nonNegativeInteger:{type:`integer`,minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:`#/definitions/nonNegativeInteger`},{default:0}]},simpleTypes:{enum:[`array`,`boolean`,`integer`,`null`,`number`,`object`,`string`]},stringArray:{type:`array`,items:{type:`string`},uniqueItems:!0,default:[]}},type:[`object`,`boolean`],properties:{$id:{type:`string`,format:`uri-reference`},$schema:{type:`string`,format:`uri`},$ref:{type:`string`,format:`uri-reference`},$comment:{type:`string`},title:{type:`string`},description:{type:`string`},default:!0,readOnly:{type:`boolean`,default:!1},examples:{type:`array`,items:!0},multipleOf:{type:`number`,exclusiveMinimum:0},maximum:{type:`number`},exclusiveMaximum:{type:`number`},minimum:{type:`number`},exclusiveMinimum:{type:`number`},maxLength:{$ref:`#/definitions/nonNegativeInteger`},minLength:{$ref:`#/definitions/nonNegativeIntegerDefault0`},pattern:{type:`string`,format:`regex`},additionalItems:{$ref:`#`},items:{anyOf:[{$ref:`#`},{$ref:`#/definitions/schemaArray`}],default:!0},maxItems:{$ref:`#/definitions/nonNegativeInteger`},minItems:{$ref:`#/definitions/nonNegativeIntegerDefault0`},uniqueItems:{type:`boolean`,default:!1},contains:{$ref:`#`},maxProperties:{$ref:`#/definitions/nonNegativeInteger`},minProperties:{$ref:`#/definitions/nonNegativeIntegerDefault0`},required:{$ref:`#/definitions/stringArray`},additionalProperties:{$ref:`#`},definitions:{type:`object`,additionalProperties:{$ref:`#`},default:{}},properties:{type:`object`,additionalProperties:{$ref:`#`},default:{}},patternProperties:{type:`object`,additionalProperties:{$ref:`#`},propertyNames:{format:`regex`},default:{}},dependencies:{type:`object`,additionalProperties:{anyOf:[{$ref:`#`},{$ref:`#/definitions/stringArray`}]}},propertyNames:{$ref:`#`},const:!0,enum:{type:`array`,items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:`#/definitions/simpleTypes`},{type:`array`,items:{$ref:`#/definitions/simpleTypes`},minItems:1,uniqueItems:!0}]},format:{type:`string`},contentMediaType:{type:`string`},contentEncoding:{type:`string`},if:{$ref:`#`},then:{$ref:`#`},else:{$ref:`#`},allOf:{$ref:`#/definitions/schemaArray`},anyOf:{$ref:`#/definitions/schemaArray`},oneOf:{$ref:`#/definitions/schemaArray`},not:{$ref:`#`}},default:!0}}},xl={id:J(`A unique identifier for the schema.`),$schema:J(`The schema to verify this document against.`),title:J(`A descriptive title of the element.`),description:J(`A long description of the element. Used in hover menus and suggestions.`),default:J(`A default value. Used by suggestions.`),multipleOf:J(`A number that should cleanly divide the current value (i.e. have no remainder).`),maximum:J(`The maximum numerical value, inclusive by default.`),exclusiveMaximum:J(`Makes the maximum property exclusive.`),minimum:J(`The minimum numerical value, inclusive by default.`),exclusiveMinimum:J(`Makes the minimum property exclusive.`),maxLength:J(`The maximum length of a string.`),minLength:J(`The minimum length of a string.`),pattern:J(`A regular expression to match the string against. It is not implicitly anchored.`),additionalItems:J(`For arrays, only when items is set as an array. If it is a schema, then this schema validates items after the ones specified by the items array. If it is false, then additional items will cause validation to fail.`),items:J(`For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on.`),maxItems:J(`The maximum number of items that can be inside an array. Inclusive.`),minItems:J(`The minimum number of items that can be inside an array. Inclusive.`),uniqueItems:J(`If all of the items in the array must be unique. Defaults to false.`),maxProperties:J(`The maximum number of properties an object can have. Inclusive.`),minProperties:J(`The minimum number of properties an object can have. Inclusive.`),required:J(`An array of strings that lists the names of all properties required on this object.`),additionalProperties:J(`Either a schema or a boolean. If a schema, then used to validate all properties not matched by 'properties' or 'patternProperties'. If false, then any properties not matched by either will cause this schema to fail.`),definitions:J(`Not used for validation. Place subschemas here that you wish to reference inline with $ref.`),properties:J(`A map of property names to schemas for each property.`),patternProperties:J(`A map of regular expressions on property names to schemas for matching properties.`),dependencies:J(`A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object.`),enum:J(`The set of literal values that are valid.`),type:J(`Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types.`),format:J(`Describes the format expected for the value.`),allOf:J(`An array of schemas, all of which must match.`),anyOf:J(`An array of schemas, where at least one must match.`),oneOf:J(`An array of schemas, exactly one of which must match.`),not:J(`A schema which must not match.`),$id:J(`A unique identifier for the schema.`),$ref:J(`Reference a definition hosted on any location.`),$comment:J(`Comments from schema authors to readers or maintainers of the schema.`),readOnly:J(`Indicates that the value of the instance is managed exclusively by the owning authority.`),examples:J(`Sample JSON values associated with a particular schema, for the purpose of illustrating usage.`),contains:J(`An array instance is valid against "contains" if at least one of its elements is valid against the given schema.`),propertyNames:J(`If the instance is an object, this keyword validates if every property name in the instance validates against the provided schema.`),const:J(`An instance validates successfully against this keyword if its value is equal to the value of the keyword.`),contentMediaType:J(`Describes the media type of a string property.`),contentEncoding:J(`Describes the content encoding of a string property.`),if:J(`The validation outcome of the "if" subschema controls which of the "then" or "else" keywords are evaluated.`),then:J(`The "if" subschema is used for validation when the "if" subschema succeeds.`),else:J(`The "else" subschema is used for validation when the "if" subschema fails.`)};for(let e in bl.schemas){let t=bl.schemas[e];for(let e in t.properties){let n=t.properties[e];typeof n==`boolean`&&(n=t.properties[e]={});let r=xl[e];r&&(n.description=r)}}var Sl;(()=>{var e={470:e=>{function t(e){if(typeof e!=`string`)throw TypeError(`Path must be a string. Received `+JSON.stringify(e))}function n(e,t){for(var n,r=``,i=0,a=-1,o=0,s=0;s<=e.length;++s){if(s<e.length)n=e.charCodeAt(s);else{if(n===47)break;n=47}if(n===47){if(!(a===s-1||o===1))if(a!==s-1&&o===2){if(r.length<2||i!==2||r.charCodeAt(r.length-1)!==46||r.charCodeAt(r.length-2)!==46){if(r.length>2){var c=r.lastIndexOf(`/`);if(c!==r.length-1){c===-1?(r=``,i=0):i=(r=r.slice(0,c)).length-1-r.lastIndexOf(`/`),a=s,o=0;continue}}else if(r.length===2||r.length===1){r=``,i=0,a=s,o=0;continue}}t&&(r.length>0?r+=`/..`:r=`..`,i=2)}else r.length>0?r+=`/`+e.slice(a+1,s):r=e.slice(a+1,s),i=s-a-1;a=s,o=0}else n===46&&o!==-1?++o:o=-1}return r}var r={resolve:function(){for(var e,r=``,i=!1,a=arguments.length-1;a>=-1&&!i;a--){var o;a>=0?o=arguments[a]:(e===void 0&&(e=process.cwd()),o=e),t(o),o.length!==0&&(r=o+`/`+r,i=o.charCodeAt(0)===47)}return r=n(r,!i),i?r.length>0?`/`+r:`/`:r.length>0?r:`.`},normalize:function(e){if(t(e),e.length===0)return`.`;var r=e.charCodeAt(0)===47,i=e.charCodeAt(e.length-1)===47;return(e=n(e,!r)).length!==0||r||(e=`.`),e.length>0&&i&&(e+=`/`),r?`/`+e:e},isAbsolute:function(e){return t(e),e.length>0&&e.charCodeAt(0)===47},join:function(){if(arguments.length===0)return`.`;for(var e,n=0;n<arguments.length;++n){var i=arguments[n];t(i),i.length>0&&(e===void 0?e=i:e+=`/`+i)}return e===void 0?`.`:r.normalize(e)},relative:function(e,n){if(t(e),t(n),e===n||(e=r.resolve(e))===(n=r.resolve(n)))return``;for(var i=1;i<e.length&&e.charCodeAt(i)===47;++i);for(var a=e.length,o=a-i,s=1;s<n.length&&n.charCodeAt(s)===47;++s);for(var c=n.length-s,l=o<c?o:c,u=-1,d=0;d<=l;++d){if(d===l){if(c>l){if(n.charCodeAt(s+d)===47)return n.slice(s+d+1);if(d===0)return n.slice(s+d)}else o>l&&(e.charCodeAt(i+d)===47?u=d:d===0&&(u=0));break}var f=e.charCodeAt(i+d);if(f!==n.charCodeAt(s+d))break;f===47&&(u=d)}var p=``;for(d=i+u+1;d<=a;++d)d!==a&&e.charCodeAt(d)!==47||(p.length===0?p+=`..`:p+=`/..`);return p.length>0?p+n.slice(s+u):(s+=u,n.charCodeAt(s)===47&&++s,n.slice(s))},_makeLong:function(e){return e},dirname:function(e){if(t(e),e.length===0)return`.`;for(var n=e.charCodeAt(0),r=n===47,i=-1,a=!0,o=e.length-1;o>=1;--o)if((n=e.charCodeAt(o))===47){if(!a){i=o;break}}else a=!1;return i===-1?r?`/`:`.`:r&&i===1?`//`:e.slice(0,i)},basename:function(e,n){if(n!==void 0&&typeof n!=`string`)throw TypeError(`"ext" argument must be a string`);t(e);var r,i=0,a=-1,o=!0;if(n!==void 0&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return``;var s=n.length-1,c=-1;for(r=e.length-1;r>=0;--r){var l=e.charCodeAt(r);if(l===47){if(!o){i=r+1;break}}else c===-1&&(o=!1,c=r+1),s>=0&&(l===n.charCodeAt(s)?--s==-1&&(a=r):(s=-1,a=c))}return i===a?a=c:a===-1&&(a=e.length),e.slice(i,a)}for(r=e.length-1;r>=0;--r)if(e.charCodeAt(r)===47){if(!o){i=r+1;break}}else a===-1&&(o=!1,a=r+1);return a===-1?``:e.slice(i,a)},extname:function(e){t(e);for(var n=-1,r=0,i=-1,a=!0,o=0,s=e.length-1;s>=0;--s){var c=e.charCodeAt(s);if(c!==47)i===-1&&(a=!1,i=s+1),c===46?n===-1?n=s:o!==1&&(o=1):n!==-1&&(o=-1);else if(!a){r=s+1;break}}return n===-1||i===-1||o===0||o===1&&n===i-1&&n===r+1?``:e.slice(n,i)},format:function(e){if(typeof e!=`object`||!e)throw TypeError(`The "pathObject" argument must be of type Object. Received type `+typeof e);return function(e,t){var n=t.dir||t.root,r=t.base||(t.name||``)+(t.ext||``);return n?n===t.root?n+r:n+`/`+r:r}(0,e)},parse:function(e){t(e);var n={root:``,dir:``,base:``,ext:``,name:``};if(e.length===0)return n;var r,i=e.charCodeAt(0),a=i===47;a?(n.root=`/`,r=1):r=0;for(var o=-1,s=0,c=-1,l=!0,u=e.length-1,d=0;u>=r;--u)if((i=e.charCodeAt(u))!==47)c===-1&&(l=!1,c=u+1),i===46?o===-1?o=u:d!==1&&(d=1):o!==-1&&(d=-1);else if(!l){s=u+1;break}return o===-1||c===-1||d===0||d===1&&o===c-1&&o===s+1?c!==-1&&(n.base=n.name=s===0&&a?e.slice(1,c):e.slice(s,c)):(s===0&&a?(n.name=e.slice(1,o),n.base=e.slice(1,c)):(n.name=e.slice(s,o),n.base=e.slice(s,c)),n.ext=e.slice(o,c)),s>0?n.dir=e.slice(0,s-1):a&&(n.dir=`/`),n},sep:`/`,delimiter:`:`,win32:null,posix:null};r.posix=r,e.exports=r}},t={};function n(r){var i=t[r];if(i!==void 0)return i.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{typeof Symbol<`u`&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:`Module`}),Object.defineProperty(e,`__esModule`,{value:!0})};var r={};(()=>{let e;n.r(r),n.d(r,{URI:()=>c,Utils:()=>x}),typeof process==`object`?e=process.platform===`win32`:typeof navigator==`object`&&(e=navigator.userAgent.indexOf(`Windows`)>=0);let t=/^\w[\w\d+.-]*$/,i=/^\//,a=/^\/\//;function o(e,n){if(!e.scheme&&n)throw Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!t.test(e.scheme))throw Error(`[UriError]: Scheme contains illegal characters.`);if(e.path){if(e.authority){if(!i.test(e.path))throw Error(`[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character`)}else if(a.test(e.path))throw Error(`[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")`)}}let s=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class c{static isUri(e){return e instanceof c||!!e&&typeof e.authority==`string`&&typeof e.fragment==`string`&&typeof e.path==`string`&&typeof e.query==`string`&&typeof e.scheme==`string`&&typeof e.fsPath==`string`&&typeof e.with==`function`&&typeof e.toString==`function`}scheme;authority;path;query;fragment;constructor(e,t,n,r,i,a=!1){typeof e==`object`?(this.scheme=e.scheme||``,this.authority=e.authority||``,this.path=e.path||``,this.query=e.query||``,this.fragment=e.fragment||``):(this.scheme=function(e,t){return e||t?e:`file`}(e,a),this.authority=t||``,this.path=function(e,t){switch(e){case`https`:case`http`:case`file`:t?t[0]!==`/`&&(t=`/`+t):t=`/`}return t}(this.scheme,n||``),this.query=r||``,this.fragment=i||``,o(this,a))}get fsPath(){return m(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:r,query:i,fragment:a}=e;return t===void 0?t=this.scheme:t===null&&(t=``),n===void 0?n=this.authority:n===null&&(n=``),r===void 0?r=this.path:r===null&&(r=``),i===void 0?i=this.query:i===null&&(i=``),a===void 0?a=this.fragment:a===null&&(a=``),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&a===this.fragment?this:new u(t,n,r,i,a)}static parse(e,t=!1){let n=s.exec(e);return n?new u(n[2]||``,v(n[4]||``),v(n[5]||``),v(n[7]||``),v(n[9]||``),t):new u(``,``,``,``,``)}static file(t){let n=``;if(e&&(t=t.replace(/\\/g,`/`)),t[0]===`/`&&t[1]===`/`){let e=t.indexOf(`/`,2);e===-1?(n=t.substring(2),t=`/`):(n=t.substring(2,e),t=t.substring(e)||`/`)}return new u(`file`,n,t,``,``)}static from(e){let t=new u(e.scheme,e.authority,e.path,e.query,e.fragment);return o(t,!0),t}toString(e=!1){return h(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof c)return e;{let t=new u(e);return t._formatted=e.external,t._fsPath=e._sep===l?e.fsPath:null,t}}return e}}let l=e?1:void 0;class u extends c{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||=m(this,!1),this._fsPath}toString(e=!1){return e?h(this,!0):(this._formatted||=h(this,!1),this._formatted)}toJSON(){let e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=l),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}let d={58:`%3A`,47:`%2F`,63:`%3F`,35:`%23`,91:`%5B`,93:`%5D`,64:`%40`,33:`%21`,36:`%24`,38:`%26`,39:`%27`,40:`%28`,41:`%29`,42:`%2A`,43:`%2B`,44:`%2C`,59:`%3B`,61:`%3D`,32:`%20`};function f(e,t,n){let r,i=-1;for(let a=0;a<e.length;a++){let o=e.charCodeAt(a);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||o===45||o===46||o===95||o===126||t&&o===47||n&&o===91||n&&o===93||n&&o===58)i!==-1&&(r+=encodeURIComponent(e.substring(i,a)),i=-1),r!==void 0&&(r+=e.charAt(a));else{r===void 0&&(r=e.substr(0,a));let t=d[o];t===void 0?i===-1&&(i=a):(i!==-1&&(r+=encodeURIComponent(e.substring(i,a)),i=-1),r+=t)}}return i!==-1&&(r+=encodeURIComponent(e.substring(i))),r===void 0?e:r}function p(e){let t;for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);r===35||r===63?(t===void 0&&(t=e.substr(0,n)),t+=d[r]):t!==void 0&&(t+=e[n])}return t===void 0?e:t}function m(t,n){let r;return r=t.authority&&t.path.length>1&&t.scheme===`file`?`//${t.authority}${t.path}`:t.path.charCodeAt(0)===47&&(t.path.charCodeAt(1)>=65&&t.path.charCodeAt(1)<=90||t.path.charCodeAt(1)>=97&&t.path.charCodeAt(1)<=122)&&t.path.charCodeAt(2)===58?n?t.path.substr(1):t.path[1].toLowerCase()+t.path.substr(2):t.path,e&&(r=r.replace(/\//g,`\\`)),r}function h(e,t){let n=t?p:f,r=``,{scheme:i,authority:a,path:o,query:s,fragment:c}=e;if(i&&(r+=i,r+=`:`),(a||i===`file`)&&(r+=`/`,r+=`/`),a){let e=a.indexOf(`@`);if(e!==-1){let t=a.substr(0,e);a=a.substr(e+1),e=t.lastIndexOf(`:`),e===-1?r+=n(t,!1,!1):(r+=n(t.substr(0,e),!1,!1),r+=`:`,r+=n(t.substr(e+1),!1,!0)),r+=`@`}a=a.toLowerCase(),e=a.lastIndexOf(`:`),e===-1?r+=n(a,!1,!0):(r+=n(a.substr(0,e),!1,!0),r+=a.substr(e))}if(o){if(o.length>=3&&o.charCodeAt(0)===47&&o.charCodeAt(2)===58){let e=o.charCodeAt(1);e>=65&&e<=90&&(o=`/${String.fromCharCode(e+32)}:${o.substr(3)}`)}else if(o.length>=2&&o.charCodeAt(1)===58){let e=o.charCodeAt(0);e>=65&&e<=90&&(o=`${String.fromCharCode(e+32)}:${o.substr(2)}`)}r+=n(o,!0,!1)}return s&&(r+=`?`,r+=n(s,!1,!1)),c&&(r+=`#`,r+=t?c:f(c,!1,!1)),r}function g(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+g(e.substr(3)):e}}let _=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function v(e){return e.match(_)?e.replace(_,e=>g(e)):e}var y=n(470);let b=y.posix||y;var x;(function(e){e.joinPath=function(e,...t){return e.with({path:b.join(e.path,...t)})},e.resolvePath=function(e,...t){let n=e.path,r=!1;n[0]!==`/`&&(n=`/`+n,r=!0);let i=b.resolve(n,...t);return r&&i[0]===`/`&&!e.authority&&(i=i.substring(1)),e.with({path:i})},e.dirname=function(e){if(e.path.length===0||e.path===`/`)return e;let t=b.dirname(e.path);return t.length===1&&t.charCodeAt(0)===46&&(t=``),e.with({path:t})},e.basename=function(e){return b.basename(e.path)},e.extname=function(e){return b.extname(e.path)}})(x||={})})(),Sl=r})();var{URI:Cl,Utils:wl}=Sl;function Tl(e,t){if(typeof e!=`string`)throw TypeError(`Expected a string`);let n=String(e),r=``,i=t?!!t.extended:!1,a=t?!!t.globstar:!1,o=!1,s=t&&typeof t.flags==`string`?t.flags:``,c;for(let e=0,t=n.length;e<t;e++)switch(c=n[e],c){case`/`:case`$`:case`^`:case`+`:case`.`:case`(`:case`)`:case`=`:case`!`:case`|`:r+=`\\`+c;break;case`?`:if(i){r+=`.`;break}case`[`:case`]`:if(i){r+=c;break}case`{`:if(i){o=!0,r+=`(`;break}case`}`:if(i){o=!1,r+=`)`;break}case`,`:if(o){r+=`|`;break}r+=`\\`+c;break;case`*`:let t=n[e-1],s=1;for(;n[e+1]===`*`;)s++,e++;let l=n[e+1];a?s>1&&(t===`/`||t===void 0||t===`{`||t===`,`)&&(l===`/`||l===void 0||l===`,`||l===`}`)?(l===`/`?e++:t===`/`&&r.endsWith(`\\/`)&&(r=r.substr(0,r.length-2)),r+=`((?:[^/]*(?:/|$))*)`):r+=`([^/]*)`:r+=`.*`;break;default:r+=c}return(!s||!~s.indexOf(`g`))&&(r=`^`+r+`$`),new RegExp(r,s)}var El=`!`,Dl=`/`,Ol=class{constructor(e,t,n){this.folderUri=t,this.uris=n,this.globWrappers=[];try{for(let t of e){let e=t[0]!==El;e||(t=t.substring(1)),t.length>0&&(t[0]===Dl&&(t=t.substring(1)),this.globWrappers.push({regexp:Tl(`**/`+t,{extended:!0,globstar:!0}),include:e}))}t&&(t=Fl(t),t.endsWith(`/`)||(t+=`/`),this.folderUri=t)}catch{this.globWrappers.length=0,this.uris=[]}}matchesPattern(e){if(this.folderUri&&!e.startsWith(this.folderUri))return!1;let t=!1;for(let{regexp:n,include:r}of this.globWrappers)n.test(e)&&(t=r);return t}getURIs(){return this.uris}},kl=class{constructor(e,t,n){this.service=e,this.uri=t,this.dependencies=new Set,this.anchors=void 0,n&&(this.unresolvedSchema=this.service.promise.resolve(new Al(n)))}getUnresolvedSchema(){return this.unresolvedSchema||=this.service.loadSchema(this.uri),this.unresolvedSchema}getResolvedSchema(){return this.resolvedSchema||=this.getUnresolvedSchema().then(e=>this.service.resolveSchemaContent(e,this)),this.resolvedSchema}clearSchema(){let e=!!this.unresolvedSchema;return this.resolvedSchema=void 0,this.unresolvedSchema=void 0,this.dependencies.clear(),this.anchors=void 0,e}},Al=class{constructor(e,t=[]){this.schema=e,this.errors=t}},jl=class{constructor(e,t=[],n=[],r){this.schema=e,this.errors=t,this.warnings=n,this.schemaDraft=r}getSection(e){let t=this.getSectionRecursive(e,this.schema);if(t)return Y(t)}getSectionRecursive(e,t){if(!t||typeof t==`boolean`||e.length===0)return t;let n=e.shift();if(t.properties&&typeof t.properties[n])return this.getSectionRecursive(e,t.properties[n]);if(t.patternProperties){for(let r of Object.keys(t.patternProperties))if(Zo(r)?.test(n))return this.getSectionRecursive(e,t.patternProperties[r])}else if(typeof t.additionalProperties==`object`)return this.getSectionRecursive(e,t.additionalProperties);else if(n.match(`[0-9]+`)){if(Array.isArray(t.items)){let r=parseInt(n,10);if(!isNaN(r)&&t.items[r])return this.getSectionRecursive(e,t.items[r])}else if(t.items)return this.getSectionRecursive(e,t.items)}}},Ml=class{constructor(e,t,n){this.contextService=t,this.requestService=e,this.promiseConstructor=n||Promise,this.callOnDispose=[],this.contributionSchemas={},this.contributionAssociations=[],this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={}}getRegisteredSchemaIds(e){return Object.keys(this.registeredSchemasIds).filter(t=>{let n=Cl.parse(t).scheme;return n!==`schemaservice`&&(!e||e(n))})}get promise(){return this.promiseConstructor}dispose(){for(;this.callOnDispose.length>0;)this.callOnDispose.pop()()}onResourceChange(e){this.cachedSchemaForResource=void 0;let t=!1;e=Pl(e);let n=[e],r=Object.keys(this.schemasById).map(e=>this.schemasById[e]);for(;n.length;){let e=n.pop();for(let i=0;i<r.length;i++){let a=r[i];a&&(a.uri===e||a.dependencies.has(e))&&(a.uri!==e&&n.push(a.uri),a.clearSchema()&&(t=!0),r[i]=void 0)}}return t}setSchemaContributions(e){if(e.schemas){let t=e.schemas;for(let e in t){let n=Pl(e);this.contributionSchemas[n]=this.addSchemaHandle(n,t[e])}}if(Array.isArray(e.schemaAssociations)){let t=e.schemaAssociations;for(let e of t){let t=e.uris.map(Pl),n=this.addFilePatternAssociation(e.pattern,e.folderUri,t);this.contributionAssociations.push(n)}}}addSchemaHandle(e,t){let n=new kl(this,e,t);return this.schemasById[e]=n,n}getOrAddSchemaHandle(e,t){return this.schemasById[e]||this.addSchemaHandle(e,t)}addFilePatternAssociation(e,t,n){let r=new Ol(e,t,n);return this.filePatternAssociations.push(r),r}registerExternalSchema(e){let t=Pl(e.uri);return this.registeredSchemasIds[t]=!0,this.cachedSchemaForResource=void 0,e.fileMatch&&e.fileMatch.length&&this.addFilePatternAssociation(e.fileMatch,e.folderUri,[t]),e.schema?this.addSchemaHandle(t,e.schema):this.getOrAddSchemaHandle(t)}clearExternalSchemas(){for(let e in this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={},this.cachedSchemaForResource=void 0,this.contributionSchemas)this.schemasById[e]=this.contributionSchemas[e],this.registeredSchemasIds[e]=!0;for(let e of this.contributionAssociations)this.filePatternAssociations.push(e)}getResolvedSchema(e){let t=Pl(e),n=this.schemasById[t];return n?n.getResolvedSchema():this.promise.resolve(void 0)}loadSchema(e){if(!this.requestService){let t=J(`Unable to load schema from '{0}'. No schema request service available`,Il(e));return this.promise.resolve(new Al({},[t]))}return e.startsWith(`http://json-schema.org/`)&&(e=`https`+e.substring(4)),this.requestService(e).then(t=>{if(!t){let t=J(`Unable to load schema from '{0}': No content.`,Il(e));return new Al({},[t])}let n=[];t.charCodeAt(0)===65279&&(n.push(J(`Problem reading content from '{0}': UTF-8 with BOM detected, only UTF 8 is allowed.`,Il(e))),t=t.trimStart());let r={},i=[];return r=Ro(t,i),i.length&&n.push(J(`Unable to parse content from '{0}': Parse error at offset {1}.`,Il(e),i[0].offset)),new Al(r,n)},t=>{let n=t.toString(),r=t.toString().split(`Error: `);return r.length>1&&(n=r[1]),Xo(n,`.`)&&(n=n.substr(0,n.length-1)),new Al({},[J(`Unable to load schema from '{0}': {1}.`,Il(e),n)])})}resolveSchemaContent(e,t){let n=e.errors.slice(0),r=e.schema,i=r.$schema?Pl(r.$schema):void 0;if(i===`http://json-schema.org/draft-03/schema`)return this.promise.resolve(new jl({},[J(`Draft-03 schemas are not supported.`)],[],i));let a=new Set,o=this.contextService,s=(e,t)=>{t=decodeURIComponent(t);let n=e;return t[0]===`/`&&(t=t.substring(1)),t.split(`/`).some(e=>(e=e.replace(/~1/g,`/`).replace(/~0/g,`~`),n=n[e],!n)),n},c=(e,t,n)=>(t.anchors||=p(e),t.anchors.get(n)),l=(e,t)=>{for(let n in t)t.hasOwnProperty(n)&&n!==`id`&&n!==`$id`&&(e[n]=t[n])},u=(e,t,r,i)=>{let a;a=i===void 0||i.length===0?t:i.charAt(0)===`/`?s(t,i):c(t,r,i),a?l(e,a):n.push(J(`$ref '{0}' in '{1}' can not be resolved.`,i||``,r.uri))},d=(e,t,r,i)=>{o&&!/^[A-Za-z][A-Za-z0-9+\-.+]*:\/\/.*/.test(t)&&(t=o.resolveRelativePath(t,i.uri)),t=Pl(t);let a=this.getOrAddSchemaHandle(t);return a.getUnresolvedSchema().then(o=>{if(i.dependencies.add(t),o.errors.length){let e=r?t+`#`+r:t;n.push(J(`Problems loading reference '{0}': {1}`,e,o.errors[0]))}return u(e,o.schema,a,r),f(e,o.schema,a)})},f=(e,t,n)=>{let r=[];return this.traverseNodes(e,e=>{let i=new Set;for(;e.$ref;){let a=e.$ref,o=a.split(`#`,2);if(delete e.$ref,o[0].length>0){r.push(d(e,o[0],o[1],n));return}else if(!i.has(a)){let r=o[1];u(e,t,n,r),i.add(a)}}e.$recursiveRef&&a.add(`$recursiveRef`),e.$dynamicRef&&a.add(`$dynamicRef`)}),this.promise.all(r)},p=e=>{let t=new Map;return this.traverseNodes(e,e=>{let r=e.$id||e.id,i=qo(r)&&r.charAt(0)===`#`?r.substring(1):e.$anchor;i&&(t.has(i)?n.push(J(`Duplicate anchor declaration: '{0}'`,i)):t.set(i,e)),e.$recursiveAnchor&&a.add(`$recursiveAnchor`),e.$dynamicAnchor&&a.add(`$dynamicAnchor`)}),t};return f(r,r,t).then(e=>{let t=[];return a.size&&t.push(J(`The schema uses meta-schema features ({0}) that are not yet supported by the validator.`,Array.from(a.keys()).join(`, `))),new jl(r,n,t,i)})}traverseNodes(e,t){if(!e||typeof e!=`object`)return Promise.resolve(null);let n=new Set,r=(...e)=>{for(let t of e)Jo(t)&&s.push(t)},i=(...e)=>{for(let t of e)if(Jo(t))for(let e in t){let n=t[e];Jo(n)&&s.push(n)}},a=(...e)=>{for(let t of e)if(Array.isArray(t))for(let e of t)Jo(e)&&s.push(e)},o=e=>{if(Array.isArray(e))for(let t of e)Jo(t)&&s.push(t);else Jo(e)&&s.push(e)},s=[e],c=s.pop();for(;c;)n.has(c)||(n.add(c),t(c),r(c.additionalItems,c.additionalProperties,c.not,c.contains,c.propertyNames,c.if,c.then,c.else,c.unevaluatedItems,c.unevaluatedProperties),i(c.definitions,c.$defs,c.properties,c.patternProperties,c.dependencies,c.dependentSchemas),a(c.anyOf,c.allOf,c.oneOf,c.prefixItems),o(c.items)),c=s.pop()}getSchemaFromProperty(e,t){if(t.root?.type===`object`){for(let n of t.root.properties)if(n.keyNode.value===`$schema`&&n.valueNode?.type===`string`){let t=n.valueNode.value;return this.contextService&&!/^\w[\w\d+.-]*:/.test(t)&&(t=this.contextService.resolveRelativePath(t,e)),t}}}getAssociatedSchemas(e){let t=Object.create(null),n=[],r=Fl(e);for(let e of this.filePatternAssociations)if(e.matchesPattern(r))for(let r of e.getURIs())t[r]||(n.push(r),t[r]=!0);return n}getSchemaURIsForResource(e,t){let n=t&&this.getSchemaFromProperty(e,t);return n?[n]:this.getAssociatedSchemas(e)}getSchemaForResource(e,t){if(t){let n=this.getSchemaFromProperty(e,t);if(n){let e=Pl(n);return this.getOrAddSchemaHandle(e).getResolvedSchema()}}if(this.cachedSchemaForResource&&this.cachedSchemaForResource.resource===e)return this.cachedSchemaForResource.resolvedSchema;let n=this.getAssociatedSchemas(e),r=n.length>0?this.createCombinedSchema(e,n).getResolvedSchema():this.promise.resolve(void 0);return this.cachedSchemaForResource={resource:e,resolvedSchema:r},r}createCombinedSchema(e,t){if(t.length===1)return this.getOrAddSchemaHandle(t[0]);{let n=`schemaservice://combinedSchema/`+encodeURIComponent(e),r={allOf:t.map(e=>({$ref:e}))};return this.addSchemaHandle(n,r)}}getMatchingSchemas(e,t,n){if(n){let e=n.id||`schemaservice://untitled/matchingSchemas/`+ Nl++;return this.addSchemaHandle(e,n).getResolvedSchema().then(e=>t.getMatchingSchemas(e.schema).filter(e=>!e.inverted))}return this.getSchemaForResource(e.uri,t).then(e=>e?t.getMatchingSchemas(e.schema).filter(e=>!e.inverted):[])}},Nl=0;function Pl(e){try{return Cl.parse(e).toString(!0)}catch{return e}}function Fl(e){try{return Cl.parse(e).with({fragment:null,query:null}).toString(!0)}catch{return e}}function Il(e){try{let t=Cl.parse(e);if(t.scheme===`file`)return t.fsPath}catch{}return e}function Ll(e,t){let n=[],r=[],i=[],a=-1,o=Fo(e.getText(),!1),s=o.scan();function c(e){n.push(e),r.push(i.length)}for(;s!==17;){switch(s){case 1:case 3:{let t=e.positionAt(o.getTokenOffset()).line,n={startLine:t,endLine:t,kind:s===1?`object`:`array`};i.push(n);break}case 2:case 4:{let t=s===2?`object`:`array`;if(i.length>0&&i[i.length-1].kind===t){let t=i.pop(),n=e.positionAt(o.getTokenOffset()).line;t&&n>t.startLine+1&&a!==t.startLine&&(t.endLine=n-1,c(t),a=t.startLine)}break}case 13:{let t=e.positionAt(o.getTokenOffset()).line,n=e.positionAt(o.getTokenOffset()+o.getTokenLength()).line;o.getTokenError()===1&&t+1<e.lineCount?o.setPosition(e.offsetAt(H.create(t+1,0))):t<n&&(c({startLine:t,endLine:n,kind:us.Comment}),a=t);break}case 12:{let t=e.getText().substr(o.getTokenOffset(),o.getTokenLength()).match(/^\/\/\s*#(region\b)|(endregion\b)/);if(t){let n=e.positionAt(o.getTokenOffset()).line;if(t[1]){let e={startLine:n,endLine:n,kind:us.Region};i.push(e)}else{let e=i.length-1;for(;e>=0&&i[e].kind!==us.Region;)e--;if(e>=0){let t=i[e];i.length=e,n>t.startLine&&a!==t.startLine&&(t.endLine=n,c(t),a=t.startLine)}}}break}}s=o.scan()}let l=t&&t.rangeLimit;if(typeof l!=`number`||n.length<=l)return n;t&&t.onRangeLimitExceeded&&t.onRangeLimitExceeded(e.uri);let u=[];for(let e of r)e<30&&(u[e]=(u[e]||0)+1);let d=0,f=0;for(let e=0;e<u.length;e++){let t=u[e];if(t){if(t+d>l){f=e;break}d+=t}}let p=[];for(let e=0;e<n.length;e++){let t=r[e];typeof t==`number`&&(t<f||t===f&&d++<l)&&p.push(n[e])}return p}function Rl(e,t,n){function r(t){let r=e.offsetAt(t),a=n.getNodeFromOffset(r,!0),s=[];for(;a;){switch(a.type){case`string`:case`object`:case`array`:let e=a.offset+1,t=a.offset+a.length-1;e<t&&r>=e&&r<=t&&s.push(i(e,t)),s.push(i(a.offset,a.offset+a.length));break;case`number`:case`boolean`:case`null`:case`property`:s.push(i(a.offset,a.offset+a.length));break}if(a.type===`property`||a.parent&&a.parent.type===`array`){let e=o(a.offset+a.length,5);e!==-1&&s.push(i(a.offset,e))}a=a.parent}let c;for(let e=s.length-1;e>=0;e--)c=rc.create(s[e],c);return c||=rc.create(U.create(t,t)),c}function i(t,n){return U.create(e.positionAt(t),e.positionAt(n))}let a=Fo(e.getText(),!0);function o(e,t){return a.setPosition(e),a.scan()===t?a.getTokenOffset()+a.getTokenLength():-1}return t.map(r)}function zl(e,t,n){let r;if(n){let t=e.offsetAt(n.start),i=e.offsetAt(n.end)-t;r={offset:t,length:i}}let i={tabSize:t?t.tabSize:4,insertSpaces:t?.insertSpaces===!0,insertFinalNewline:t?.insertFinalNewline===!0,eol:`
`,keepLines:t?.keepLines===!0};return Uo(e.getText(),r,i).map(t=>_s.replace(U.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length)),t.content))}var $;(function(e){e[e.Object=0]=`Object`,e[e.Array=1]=`Array`})($||={});var Bl=class{constructor(e,t){this.propertyName=e??``,this.beginningLineNumber=t,this.childrenProperties=[],this.lastProperty=!1,this.noKeyName=!1}addChildProperty(e){if(e.parent=this,this.childrenProperties.length>0){let t=0;t=e.noKeyName?this.childrenProperties.length:Hl(this.childrenProperties,e,Vl),t<0&&(t=t*-1-1),this.childrenProperties.splice(t,0,e)}else this.childrenProperties.push(e);return e}};function Vl(e,t){let n=e.propertyName.toLowerCase(),r=t.propertyName.toLowerCase();return n<r?-1:n>r?1:0}function Hl(e,t,n){let r=t.propertyName.toLowerCase(),i=e[0].propertyName.toLowerCase(),a=e[e.length-1].propertyName.toLowerCase();if(r<i)return 0;if(r>a)return e.length;let o=0,s=e.length-1;for(;o<=s;){let r=s+o>>1,i=n(t,e[r]);if(i>0)o=r+1;else if(i<0)s=r-1;else return r}return-o-1}function Ul(e,t){let n={...t,keepLines:!1},r=wc.applyEdits(e,zl(e,n,void 0)),i=wc.create(`test://test.json`,`json`,0,r),a=Wl(i),o=Gl(i,a),s=zl(o,n,void 0),c=wc.applyEdits(o,s);return[_s.replace(U.create(H.create(0,0),e.positionAt(e.getText().length)),c)]}function Wl(e){let t=e.getText(),n=Fo(t,!1),r=new Bl,i=r,a=r,o=r,s,c=0,l=0,u,d,f=-1,p=-1,m=0,h=0,g=[],_=!1,v=!1;for(;(s=n.scan())!==17;){if(_===!0&&s!==14&&s!==15&&s!==12&&s!==13&&a.endLineNumber===void 0){let e=n.getTokenStartLine();d===2||d===4?o.endLineNumber=e-1:a.endLineNumber=e-1,m=e,_=!1}if(v===!0&&s!==14&&s!==15&&s!==12&&s!==13&&(m=n.getTokenStartLine(),v=!1),n.getTokenStartLine()!==c){for(let t=c;t<n.getTokenStartLine();t++){let n=e.getText(U.create(H.create(t,0),H.create(t+1,0))).length;l+=n}c=n.getTokenStartLine()}switch(s){case 10:if(u===void 0||u===1||u===5&&g[g.length-1]===$.Object){let e=new Bl(n.getTokenValue(),m);o=a,a=i.addChildProperty(e)}break;case 3:if(r.beginningLineNumber===void 0&&(r.beginningLineNumber=n.getTokenStartLine()),g[g.length-1]===$.Object)i=a;else if(g[g.length-1]===$.Array){let e=new Bl(n.getTokenValue(),m);e.noKeyName=!0,o=a,a=i.addChildProperty(e),i=a}g.push($.Array),a.type=$.Array,m=n.getTokenStartLine(),m++;break;case 1:if(r.beginningLineNumber===void 0)r.beginningLineNumber=n.getTokenStartLine();else if(g[g.length-1]===$.Array){let e=new Bl(n.getTokenValue(),m);e.noKeyName=!0,o=a,a=i.addChildProperty(e)}a.type=$.Object,g.push($.Object),i=a,m=n.getTokenStartLine(),m++;break;case 4:h=n.getTokenStartLine(),g.pop(),a.endLineNumber===void 0&&(u===2||u===4)&&(a.endLineNumber=h-1,a.lastProperty=!0,a.lineWhereToAddComma=f,a.indexWhereToAddComa=p,o=a,a=a?a.parent:void 0,i=a),r.endLineNumber=h,m=h+1;break;case 2:h=n.getTokenStartLine(),g.pop(),u!==1&&(a.endLineNumber===void 0&&(a.endLineNumber=h-1,a.lastProperty=!0,a.lineWhereToAddComma=f,a.indexWhereToAddComa=p),o=a,a=a?a.parent:void 0,i=a),r.endLineNumber=n.getTokenStartLine(),m=h+1;break;case 5:h=n.getTokenStartLine(),a.endLineNumber===void 0&&(g[g.length-1]===$.Object||g[g.length-1]===$.Array&&(u===2||u===4))&&(a.endLineNumber=h,a.commaIndex=n.getTokenOffset()-l,a.commaLine=h),(u===2||u===4)&&(o=a,a=a?a.parent:void 0,i=a),m=h+1;break;case 13:u===5&&f===n.getTokenStartLine()&&(g[g.length-1]===$.Array&&(d===2||d===4)||g[g.length-1]===$.Object)&&(g[g.length-1]===$.Array&&(d===2||d===4)||g[g.length-1]===$.Object)&&(a.endLineNumber=void 0,_=!0),(u===1||u===3)&&f===n.getTokenStartLine()&&(v=!0);break}s!==14&&s!==13&&s!==12&&s!==15&&(d=u,u=s,f=n.getTokenStartLine(),p=n.getTokenOffset()+n.getTokenLength()-l)}return r}function Gl(e,t){if(t.childrenProperties.length===0)return e;let n=wc.create(`test://test.json`,`json`,0,e.getText()),r=[];for(Kl(r,t,t.beginningLineNumber);r.length>0;){let t=r.shift(),i=t.propertyTreeArray,a=t.beginningLineNumber;for(let t=0;t<i.length;t++){let o=i[t],s=U.create(H.create(o.beginningLineNumber,0),H.create(o.endLineNumber+1,0)),c=e.getText(s),l=wc.create(`test://test.json`,`json`,0,c);if(o.lastProperty===!0&&t!==i.length-1){let e=o.lineWhereToAddComma-o.beginningLineNumber,t=o.indexWhereToAddComa,n={range:U.create(H.create(e,t),H.create(e,t)),text:`,`};wc.update(l,[n],1)}else if(o.lastProperty===!1&&t===i.length-1){let e=o.commaIndex,t=o.commaLine-o.beginningLineNumber,n={range:U.create(H.create(t,e),H.create(t,e+1)),text:``};wc.update(l,[n],1)}let u=o.endLineNumber-o.beginningLineNumber+1,d={range:U.create(H.create(a,0),H.create(a+u,0)),text:l.getText()};wc.update(n,[d],1),Kl(r,o,a),a+=u}}return n}function Kl(e,t,n){if(t.childrenProperties.length!==0)if(t.type===$.Object){let r=1/0;for(let e of t.childrenProperties)e.beginningLineNumber<r&&(r=e.beginningLineNumber);let i=r-t.beginningLineNumber;n+=i,e.push(new Jl(n,t.childrenProperties))}else t.type===$.Array&&ql(e,t,n)}function ql(e,t,n){for(let r of t.childrenProperties){if(r.type===$.Object){let i=1/0;for(let e of r.childrenProperties)e.beginningLineNumber<i&&(i=e.beginningLineNumber);let a=i-r.beginningLineNumber;e.push(new Jl(n+r.beginningLineNumber-t.beginningLineNumber+a,r.childrenProperties))}r.type===$.Array&&ql(e,r,n+r.beginningLineNumber-t.beginningLineNumber)}}var Jl=class{constructor(e,t){this.beginningLineNumber=e,this.propertyTreeArray=t}};function Yl(e,t){let n=[];return t.visit(r=>{if(r.type===`property`&&r.keyNode.value===`$ref`&&r.valueNode?.type===`string`){let i=r.valueNode.value,a=Zl(t,i);if(a){let t=e.positionAt(a.offset);n.push({target:`${e.uri}#${t.line+1},${t.character+1}`,range:Xl(e,r.valueNode)})}}return!0}),Promise.resolve(n)}function Xl(e,t){return U.create(e.positionAt(t.offset+1),e.positionAt(t.offset+t.length-1))}function Zl(e,t){let n=$l(t);return n?Ql(n,e.root):null}function Ql(e,t){if(!t)return null;if(e.length===0)return t;let n=e.shift();if(t&&t.type===`object`){let r=t.properties.find(e=>e.keyNode.value===n);return r?Ql(e,r.valueNode):null}else if(t&&t.type===`array`&&n.match(/^(0|[1-9][0-9]*)$/)){let r=Number.parseInt(n),i=t.items[r];return i?Ql(e,i):null}return null}function $l(e){return e===`#`?[]:e[0]!==`#`||e[1]!==`/`?null:e.substring(2).split(/\//).map(eu)}function eu(e){return e.replace(/~1/g,`/`).replace(/~0/g,`~`)}function tu(e){let t=e.promiseConstructor||Promise,n=new Ml(e.schemaRequestService,e.workspaceContext,t);n.setSchemaContributions(bl);let r=new nl(n,e.contributions,t,e.clientCapabilities),i=new rl(n,e.contributions,t),a=new _l(n),o=new ol(n,t);return{configure:e=>{n.clearExternalSchemas(),e.schemas?.forEach(n.registerExternalSchema.bind(n)),o.configure(e)},resetSchema:e=>n.onResourceChange(e),doValidation:o.doValidation.bind(o),getLanguageStatus:o.getLanguageStatus.bind(o),parseJSONDocument:e=>el(e,{collectComments:!0}),newJSONDocument:(e,t)=>Jc(e,t),getMatchingSchemas:n.getMatchingSchemas.bind(n),doResolve:r.doResolve.bind(r),doComplete:r.doComplete.bind(r),findDocumentSymbols:a.findDocumentSymbols.bind(a),findDocumentSymbols2:a.findDocumentSymbols2.bind(a),findDocumentColors:a.findDocumentColors.bind(a),getColorPresentations:a.getColorPresentations.bind(a),doHover:i.doHover.bind(i),getFoldingRanges:Ll,getSelectionRanges:Rl,findDefinition:()=>Promise.resolve([]),findLinks:Yl,format:(e,t,n)=>zl(e,n,t),sort:(e,t)=>Ul(e,t)}}var nu;typeof fetch<`u`&&(nu=function(e){return fetch(e).then(e=>e.text())});var ru=class{constructor(e,t){this._ctx=e,this._languageSettings=t.languageSettings,this._languageId=t.languageId,this._languageService=tu({workspaceContext:{resolveRelativePath:(e,t)=>{let n=t.substr(0,t.lastIndexOf(`/`)+1);return su(n,e)}},schemaRequestService:t.enableSchemaRequest?nu:void 0,clientCapabilities:Ac.LATEST}),this._languageService.configure(this._languageSettings)}async doValidation(e){let t=this._getTextDocument(e);if(t){let e=this._languageService.parseJSONDocument(t);return this._languageService.doValidation(t,e,this._languageSettings)}return Promise.resolve([])}async doComplete(e,t){let n=this._getTextDocument(e);if(!n)return null;let r=this._languageService.parseJSONDocument(n);return this._languageService.doComplete(n,t,r)}async doResolve(e){return this._languageService.doResolve(e)}async doHover(e,t){let n=this._getTextDocument(e);if(!n)return null;let r=this._languageService.parseJSONDocument(n);return this._languageService.doHover(n,t,r)}async format(e,t,n){let r=this._getTextDocument(e);if(!r)return[];let i=this._languageService.format(r,t,n);return Promise.resolve(i)}async resetSchema(e){return Promise.resolve(this._languageService.resetSchema(e))}async findDocumentSymbols(e){let t=this._getTextDocument(e);if(!t)return[];let n=this._languageService.parseJSONDocument(t),r=this._languageService.findDocumentSymbols2(t,n);return Promise.resolve(r)}async findDocumentColors(e){let t=this._getTextDocument(e);if(!t)return[];let n=this._languageService.parseJSONDocument(t),r=this._languageService.findDocumentColors(t,n);return Promise.resolve(r)}async getColorPresentations(e,t,n){let r=this._getTextDocument(e);if(!r)return[];let i=this._languageService.parseJSONDocument(r),a=this._languageService.getColorPresentations(r,i,t,n);return Promise.resolve(a)}async getFoldingRanges(e,t){let n=this._getTextDocument(e);if(!n)return[];let r=this._languageService.getFoldingRanges(n,t);return Promise.resolve(r)}async getSelectionRanges(e,t){let n=this._getTextDocument(e);if(!n)return[];let r=this._languageService.parseJSONDocument(n),i=this._languageService.getSelectionRanges(n,t,r);return Promise.resolve(i)}async parseJSONDocument(e){let t=this._getTextDocument(e);if(!t)return null;let n=this._languageService.parseJSONDocument(t);return Promise.resolve(n)}async getMatchingSchemas(e){let t=this._getTextDocument(e);if(!t)return[];let n=this._languageService.parseJSONDocument(t);return Promise.resolve(this._languageService.getMatchingSchemas(t,n))}_getTextDocument(e){let t=this._ctx.getMirrorModels();for(let n of t)if(n.uri.toString()===e)return wc.create(e,this._languageId,n.version,n.getValue());return null}},iu=47,au=46;function ou(e){return e.charCodeAt(0)===iu}function su(e,t){if(ou(t)){let n=Cl.parse(e),r=t.split(`/`);return n.with({path:cu(r)}).toString()}return lu(e,t)}function cu(e){let t=[];for(let n of e)n.length===0||n.length===1&&n.charCodeAt(0)===au||(n.length===2&&n.charCodeAt(0)===au&&n.charCodeAt(1)===au?t.pop():t.push(n));e.length>1&&e[e.length-1].length===0&&t.push(``);let n=t.join(`/`);return e[0].length===0&&(n=`/`+n),n}function lu(e,...t){let n=Cl.parse(e),r=n.path.split(`/`);for(let e of t)r.push(...e.split(`/`));return n.with({path:cu(r)}).toString()}self.onmessage=()=>{mo((e,t)=>new ru(e,t))};
//# sourceMappingURL=json.worker-C--MAvZN.js.map