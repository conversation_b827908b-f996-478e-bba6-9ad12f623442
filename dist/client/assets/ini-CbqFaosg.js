/*!-----------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation. All rights reserved.
* Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
* Released under the MIT license
* https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
*-----------------------------------------------------------------------------*/
var e={comments:{lineComment:`#`},brackets:[[`{`,`}`],[`[`,`]`],[`(`,`)`]],autoClosingPairs:[{open:`{`,close:`}`},{open:`[`,close:`]`},{open:`(`,close:`)`},{open:`"`,close:`"`},{open:`'`,close:`'`}],surroundingPairs:[{open:`{`,close:`}`},{open:`[`,close:`]`},{open:`(`,close:`)`},{open:`"`,close:`"`},{open:`'`,close:`'`}]},t={defaultToken:``,tokenPostfix:`.ini`,escapes:/\\(?:[abfnrtv\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,tokenizer:{root:[[/^\[[^\]]*\]/,`metatag`],[/(^\w+)(\s*)(\=)/,[`key`,``,`delimiter`]],{include:`@whitespace`},[/\d+/,`number`],[/"([^"\\]|\\.)*$/,`string.invalid`],[/'([^'\\]|\\.)*$/,`string.invalid`],[/"/,`string`,`@string."`],[/'/,`string`,`@string.'`]],whitespace:[[/[ \t\r\n]+/,``],[/^\s*[#;].*$/,`comment`]],string:[[/[^\\"']+/,`string`],[/@escapes/,`string.escape`],[/\\./,`string.escape.invalid`],[/["']/,{cases:{"$#==$S2":{token:`string`,next:`@pop`},"@default":`string`}}]]}};export{e as conf,t as language};
//# sourceMappingURL=ini-CbqFaosg.js.map