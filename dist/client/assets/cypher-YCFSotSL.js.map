{"version": 3, "file": "cypher-YCFSotSL.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/cypher/cypher.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/cypher/cypher.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: `.cypher`,\n  ignoreCase: true,\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \"ALL\",\n    \"AND\",\n    \"AS\",\n    \"ASC\",\n    \"ASCENDING\",\n    \"BY\",\n    \"CALL\",\n    \"CASE\",\n    \"CONTAINS\",\n    \"CREATE\",\n    \"DELETE\",\n    \"DESC\",\n    \"DESCENDING\",\n    \"DETACH\",\n    \"DISTINCT\",\n    \"ELSE\",\n    \"END\",\n    \"ENDS\",\n    \"EXISTS\",\n    \"IN\",\n    \"IS\",\n    \"LIMIT\",\n    \"MANDATORY\",\n    \"MATCH\",\n    \"MERGE\",\n    \"NOT\",\n    \"ON\",\n    \"ON\",\n    \"OPTIONAL\",\n    \"OR\",\n    \"ORDER\",\n    \"REMOVE\",\n    \"RETURN\",\n    \"SET\",\n    \"SKIP\",\n    \"STARTS\",\n    \"THEN\",\n    \"UNION\",\n    \"UNWIND\",\n    \"WHEN\",\n    \"WHERE\",\n    \"WITH\",\n    \"XOR\",\n    \"YIELD\"\n  ],\n  builtinLiterals: [\"true\", \"TRUE\", \"false\", \"FALSE\", \"null\", \"NULL\"],\n  builtinFunctions: [\n    \"abs\",\n    \"acos\",\n    \"asin\",\n    \"atan\",\n    \"atan2\",\n    \"avg\",\n    \"ceil\",\n    \"coalesce\",\n    \"collect\",\n    \"cos\",\n    \"cot\",\n    \"count\",\n    \"degrees\",\n    \"e\",\n    \"endNode\",\n    \"exists\",\n    \"exp\",\n    \"floor\",\n    \"head\",\n    \"id\",\n    \"keys\",\n    \"labels\",\n    \"last\",\n    \"left\",\n    \"length\",\n    \"log\",\n    \"log10\",\n    \"lTrim\",\n    \"max\",\n    \"min\",\n    \"nodes\",\n    \"percentileCont\",\n    \"percentileDisc\",\n    \"pi\",\n    \"properties\",\n    \"radians\",\n    \"rand\",\n    \"range\",\n    \"relationships\",\n    \"replace\",\n    \"reverse\",\n    \"right\",\n    \"round\",\n    \"rTrim\",\n    \"sign\",\n    \"sin\",\n    \"size\",\n    \"split\",\n    \"sqrt\",\n    \"startNode\",\n    \"stDev\",\n    \"stDevP\",\n    \"substring\",\n    \"sum\",\n    \"tail\",\n    \"tan\",\n    \"timestamp\",\n    \"toBoolean\",\n    \"toFloat\",\n    \"toInteger\",\n    \"toLower\",\n    \"toString\",\n    \"toUpper\",\n    \"trim\",\n    \"type\"\n  ],\n  operators: [\n    // Math operators\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"^\",\n    // Comparison operators\n    \"=\",\n    \"<>\",\n    \"<\",\n    \">\",\n    \"<=\",\n    \">=\",\n    // Pattern operators\n    \"->\",\n    \"<-\",\n    \"-->\",\n    \"<--\"\n  ],\n  escapes: /\\\\(?:[tbnrf\\\\\"'`]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+/,\n  octaldigits: /[0-7]+/,\n  hexdigits: /[0-9a-fA-F]+/,\n  tokenizer: {\n    root: [[/[{}[\\]()]/, \"@brackets\"], { include: \"common\" }],\n    common: [\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      // Cypher labels on nodes/relationships, e.g. (n:NodeLabel)-[e:RelationshipLabel]\n      [/:[a-zA-Z_][\\w]*/, \"type.identifier\"],\n      [\n        /[a-zA-Z_][\\w]*(?=\\()/,\n        {\n          cases: {\n            \"@builtinFunctions\": \"predefined.function\"\n          }\n        }\n      ],\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@builtinLiterals\": \"predefined.literal\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/`/, \"identifier.escape\", \"@identifierBacktick\"],\n      // delimiter and operator after number because of `.\\d` floats and `:` in labels\n      [/[;,.:|]/, \"delimiter\"],\n      [\n        /[<>=%+\\-*/^]+/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/-?(@digits)[eE](-?(@digits))?/, \"number.float\"],\n      [/-?(@digits)?\\.(@digits)([eE]-?(@digits))?/, \"number.float\"],\n      [/-?0x(@hexdigits)/, \"number.hex\"],\n      [/-?0(@octaldigits)/, \"number.octal\"],\n      [/-?(@digits)/, \"number\"]\n    ],\n    strings: [\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@stringDouble\"],\n      [/'/, \"string\", \"@stringSingle\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/\\/\\/.*/, \"comment\"],\n      [/[^/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[/*]/, \"comment\"]\n    ],\n    stringDouble: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string\"],\n      [/\\\\./, \"string.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    stringSingle: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string\"],\n      [/\\\\./, \"string.invalid\"],\n      [/'/, \"string\", \"@pop\"]\n    ],\n    identifierBacktick: [\n      [/[^\\\\`]+/, \"identifier.escape\"],\n      [/@escapes/, \"identifier.escape\"],\n      [/\\\\./, \"identifier.escape.invalid\"],\n      [/`/, \"identifier.escape\", \"@pop\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,YAAa,KACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,UACd,WAAY,GACZ,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,oBAAqB,CACrD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CAC1D,CACD,SAAU,sQA6CT,CACD,gBAAiB,CAAC,OAAQ,OAAQ,QAAS,QAAS,OAAQ,OAAO,CACnE,iBAAkB,kbAkEjB,CACD,UAAW,CAET,IACA,IACA,IACA,IACA,IACA,IAEA,IACA,KACA,IACA,IACA,KACA,KAEA,KACA,KACA,MACA,MACD,CACD,QAAS,qDACT,OAAQ,MACR,YAAa,SACb,UAAW,eACX,UAAW,CACT,KAAM,CAAC,CAAC,YAAa,YAAY,CAAE,CAAE,QAAS,SAAU,CAAC,CACzD,OAAQ,CACN,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,WAAY,CACvB,CAAE,QAAS,WAAY,CAEvB,CAAC,kBAAmB,kBAAkB,CACtC,CACE,uBACA,CACE,MAAO,CACL,oBAAqB,sBACtB,CACF,CACF,CACD,CACE,mBACA,CACE,MAAO,CACL,YAAa,UACb,mBAAoB,qBACpB,WAAY,aACb,CACF,CACF,CACD,CAAC,IAAK,oBAAqB,sBAAsB,CAEjD,CAAC,UAAW,YAAY,CACxB,CACE,gBACA,CACE,MAAO,CACL,aAAc,YACd,WAAY,GACb,CACF,CACF,CACF,CACD,QAAS,CACP,CAAC,gCAAiC,eAAe,CACjD,CAAC,4CAA6C,eAAe,CAC7D,CAAC,mBAAoB,aAAa,CAClC,CAAC,oBAAqB,eAAe,CACrC,CAAC,cAAe,SAAS,CAC1B,CACD,QAAS,CACP,CAAC,kBAAmB,iBAAiB,CAErC,CAAC,kBAAmB,iBAAiB,CAErC,CAAC,IAAK,SAAU,gBAAgB,CAChC,CAAC,IAAK,SAAU,gBAAgB,CACjC,CACD,WAAY,CACV,CAAC,aAAc,QAAQ,CACvB,CAAC,OAAQ,UAAW,WAAW,CAC/B,CAAC,UAAW,UAAU,CACvB,CACD,QAAS,CACP,CAAC,SAAU,UAAU,CACrB,CAAC,SAAU,UAAU,CACrB,CAAC,OAAQ,UAAW,OAAO,CAC3B,CAAC,OAAQ,UAAU,CACpB,CACD,aAAc,CACZ,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,SAAS,CACtB,CAAC,MAAO,iBAAiB,CACzB,CAAC,IAAK,SAAU,OAAO,CACxB,CACD,aAAc,CACZ,CAAC,UAAW,SAAS,CACrB,CAAC,WAAY,SAAS,CACtB,CAAC,MAAO,iBAAiB,CACzB,CAAC,IAAK,SAAU,OAAO,CACxB,CACD,mBAAoB,CAClB,CAAC,UAAW,oBAAoB,CAChC,CAAC,WAAY,oBAAoB,CACjC,CAAC,MAAO,4BAA4B,CACpC,CAAC,IAAK,oBAAqB,OAAO,CACnC,CACF,CACF"}