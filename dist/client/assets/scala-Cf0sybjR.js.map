{"version": 3, "file": "scala-Cf0sybjR.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/scala/scala.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scala/scala.ts\nvar conf = {\n  /*\n   * `...` is allowed as an identifier.\n   * $ is allowed in identifiers.\n   * unary_<op> is allowed as an identifier.\n   * <name>_= is allowed as an identifier.\n   */\n  wordPattern: /(unary_[@~!#%^&*()\\-=+\\\\|:<>\\/?]+)|([a-zA-Z_$][\\w$]*?_=)|(`[^`]+`)|([a-zA-Z_$][\\w$]*)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?region\\\\b)|(?:<editor-fold\\\\b))\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?endregion\\\\b)|(?:</editor-fold>))\")\n    }\n  }\n};\nvar language = {\n  tokenPostfix: \".scala\",\n  // We can't easily add everything from Dotty, but we can at least add some of its keywords\n  keywords: [\n    \"asInstanceOf\",\n    \"catch\",\n    \"class\",\n    \"classOf\",\n    \"def\",\n    \"do\",\n    \"else\",\n    \"extends\",\n    \"finally\",\n    \"for\",\n    \"foreach\",\n    \"forSome\",\n    \"if\",\n    \"import\",\n    \"isInstanceOf\",\n    \"macro\",\n    \"match\",\n    \"new\",\n    \"object\",\n    \"package\",\n    \"return\",\n    \"throw\",\n    \"trait\",\n    \"try\",\n    \"type\",\n    \"until\",\n    \"val\",\n    \"var\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    // Dotty-specific:\n    \"given\",\n    \"enum\",\n    \"then\"\n  ],\n  // Dotty-specific:\n  softKeywords: [\"as\", \"export\", \"extension\", \"end\", \"derives\", \"on\"],\n  constants: [\"true\", \"false\", \"null\", \"this\", \"super\"],\n  modifiers: [\n    \"abstract\",\n    \"final\",\n    \"implicit\",\n    \"lazy\",\n    \"override\",\n    \"private\",\n    \"protected\",\n    \"sealed\"\n  ],\n  // Dotty-specific:\n  softModifiers: [\"inline\", \"opaque\", \"open\", \"transparent\", \"using\"],\n  name: /(?:[a-z_$][\\w$]*|`[^`]+`)/,\n  type: /(?:[A-Z][\\w$]*)/,\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/^\\\\%@#]+/,\n  digits: /\\d+(_+\\d+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  // C# style strings\n  escapes: /\\\\(?:[btnfr\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  fstring_conv: /[bBhHsScCdoxXeEfgGaAt]|[Tn](?:[HIklMSLNpzZsQ]|[BbhAaCYyjmde]|[RTrDFC])/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // strings\n      [/\\braw\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@rawstringt\" }],\n      [/\\braw\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@rawstring\" }],\n      [/\\bs\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstringt\" }],\n      [/\\bs\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstring\" }],\n      [/\\bf\"\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@fstringt\" }],\n      [/\\bf\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@fstring\" }],\n      [/\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@stringt\" }],\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?[fFdD]?/, \"number.float\", \"@allowMethod\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?[fFdD]?/, \"number.float\", \"@allowMethod\"],\n      [/0[xX](@hexdigits)[Ll]?/, \"number.hex\", \"@allowMethod\"],\n      [/(@digits)[fFdD]/, \"number.float\", \"@allowMethod\"],\n      [/(@digits)[lL]?/, \"number\", \"@allowMethod\"],\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_)\\b/, \"keyword\", \"@allowMethod\"],\n      // identifiers and keywords\n      [/\\bimport\\b/, \"keyword\", \"@import\"],\n      [/\\b(case)([ \\t]+)(class)\\b/, [\"keyword.modifier\", \"white\", \"keyword\"]],\n      [/\\bcase\\b/, \"keyword\", \"@case\"],\n      [/\\bva[lr]\\b/, \"keyword\", \"@vardef\"],\n      [\n        /\\b(def)([ \\t]+)((?:unary_)?@symbols|@name(?:_=)|@name)/,\n        [\"keyword\", \"white\", \"identifier\"]\n      ],\n      [/@name(?=[ \\t]*:(?!:))/, \"variable\"],\n      [/(\\.)(@name|@symbols)/, [\"operator\", { token: \"@rematch\", next: \"@allowMethod\" }]],\n      [/([{(])(\\s*)(@name(?=\\s*=>))/, [\"@brackets\", \"white\", \"variable\"]],\n      [\n        /@name/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@softKeywords\": \"keyword\",\n            \"@modifiers\": \"keyword.modifier\",\n            \"@softModifiers\": \"keyword.modifier\",\n            \"@constants\": {\n              token: \"constant\",\n              next: \"@allowMethod\"\n            },\n            \"@default\": {\n              token: \"identifier\",\n              next: \"@allowMethod\"\n            }\n          }\n        }\n      ],\n      [/@type/, \"type\", \"@allowMethod\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // @ annotations.\n      [/@[a-zA-Z_$][\\w$]*(?:\\.[a-zA-Z_$][\\w$]*)*/, \"annotation\"],\n      // delimiters and operators\n      [/[{(]/, \"@brackets\"],\n      [/[})]/, \"@brackets\", \"@allowMethod\"],\n      [/\\[/, \"operator.square\"],\n      [/](?!\\s*(?:va[rl]|def|type)\\b)/, \"operator.square\", \"@allowMethod\"],\n      [/]/, \"operator.square\"],\n      [/([=-]>|<-|>:|<:|:>|<%)(?=[\\s\\w()[\\]{},\\.\"'`])/, \"keyword\"],\n      [/@symbols/, \"operator\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,\\.]/, \"delimiter\"],\n      // symbols\n      [/'[a-zA-Z$][\\w$]*(?!')/, \"attribute.name\"],\n      // characters\n      [/'[^\\\\']'/, \"string\", \"@allowMethod\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", { token: \"string\", next: \"@allowMethod\" }]],\n      [/'/, \"string.invalid\"]\n    ],\n    import: [\n      [/;/, \"delimiter\", \"@pop\"],\n      [/^|$/, \"\", \"@pop\"],\n      [/[ \\t]+/, \"white\"],\n      [/[\\n\\r]+/, \"white\", \"@pop\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/@name|@type/, \"type\"],\n      [/[(){}]/, \"@brackets\"],\n      [/[[\\]]/, \"operator.square\"],\n      [/[\\.,]/, \"delimiter\"]\n    ],\n    allowMethod: [\n      [/^|$/, \"\", \"@pop\"],\n      [/[ \\t]+/, \"white\"],\n      [/[\\n\\r]+/, \"white\", \"@pop\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/(?==>[\\s\\w([{])/, \"keyword\", \"@pop\"],\n      [\n        /(@name|@symbols)(?=[ \\t]*[[({\"'`]|[ \\t]+(?:[+-]?\\.?\\d|\\w))/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword\", next: \"@pop\" },\n            \"->|<-|>:|<:|<%\": { token: \"keyword\", next: \"@pop\" },\n            \"@default\": { token: \"@rematch\", next: \"@pop\" }\n          }\n        }\n      ],\n      [\"\", \"\", \"@pop\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    case: [\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_|true|false|null|this|super)\\b/, \"keyword\", \"@allowMethod\"],\n      [/\\bif\\b|=>/, \"keyword\", \"@pop\"],\n      [/`[^`]+`/, \"identifier\", \"@allowMethod\"],\n      [/@name/, \"variable\", \"@allowMethod\"],\n      [/:::?|\\||@(?![a-z_$])/, \"keyword\"],\n      { include: \"@root\" }\n    ],\n    vardef: [\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_|true|false|null|this|super)\\b/, \"keyword\"],\n      [/@name/, \"variable\"],\n      [/:::?|\\||@(?![a-z_$])/, \"keyword\"],\n      [/=|:(?!:)/, \"operator\", \"@pop\"],\n      [/$/, \"white\", \"@pop\"],\n      { include: \"@root\" }\n    ],\n    string: [\n      [/[^\\\\\"\\n\\r]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ]\n    ],\n    stringt: [\n      [/[^\\\\\"\\n\\r]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\"/, \"string\"]\n    ],\n    fstring: [\n      [/@escapes/, \"string.escape\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/%%/, \"string\"],\n      [\n        /(%)([\\-#+ 0,(])(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/,\n        [\"metatag\", \"keyword.modifier\", \"number\", \"metatag\"]\n      ],\n      [/(%)(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/, [\"metatag\", \"number\", \"metatag\"]],\n      [/(%)([\\-#+ 0,(])(@fstring_conv)/, [\"metatag\", \"keyword.modifier\", \"metatag\"]],\n      [/(%)(@fstring_conv)/, [\"metatag\", \"metatag\"]],\n      [/./, \"string\"]\n    ],\n    fstringt: [\n      [/@escapes/, \"string.escape\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/%%/, \"string\"],\n      [\n        /(%)([\\-#+ 0,(])(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/,\n        [\"metatag\", \"keyword.modifier\", \"number\", \"metatag\"]\n      ],\n      [/(%)(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/, [\"metatag\", \"number\", \"metatag\"]],\n      [/(%)([\\-#+ 0,(])(@fstring_conv)/, [\"metatag\", \"keyword.modifier\", \"metatag\"]],\n      [/(%)(@fstring_conv)/, [\"metatag\", \"metatag\"]],\n      [/./, \"string\"]\n    ],\n    sstring: [\n      [/@escapes/, \"string.escape\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/./, \"string\"]\n    ],\n    sstringt: [\n      [/@escapes/, \"string.escape\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/./, \"string\"]\n    ],\n    interp: [[/{/, \"operator\", \"@push\"], [/}/, \"operator\", \"@pop\"], { include: \"@root\" }],\n    rawstring: [\n      [/[^\"]/, \"string\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ]\n    ],\n    rawstringt: [\n      [/[^\"]/, \"string\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\"/, \"string\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CAOT,YAAa,yFACb,SAAU,CACR,YAAa,KACb,aAAc,CAAC,KAAM,KAAK,CAC3B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,qDAAqD,CACvE,IAAS,OAAO,uDAAuD,CACxE,CACF,CACF,CACG,EAAW,CACb,aAAc,SAEd,SAAU,8NAoCT,CAED,aAAc,CAAC,KAAM,SAAU,YAAa,MAAO,UAAW,KAAK,CACnE,UAAW,CAAC,OAAQ,QAAS,OAAQ,OAAQ,QAAQ,CACrD,UAAW,CACT,WACA,QACA,WACA,OACA,WACA,UACA,YACA,SACD,CAED,cAAe,CAAC,SAAU,SAAU,OAAQ,cAAe,QAAQ,CACnE,KAAM,4BACN,KAAM,kBAEN,QAAS,2BACT,OAAQ,cACR,UAAW,iCAEX,QAAS,sEACT,aAAc,yEAEd,UAAW,CACT,KAAM,CAEJ,CAAC,WAAY,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,cAAe,CAAC,CAC9E,CAAC,SAAU,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,aAAc,CAAC,CAC3E,CAAC,SAAU,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,YAAa,CAAC,CAC1E,CAAC,OAAQ,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,WAAY,CAAC,CACvE,CAAC,UAAW,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,YAAa,CAAC,CAC3E,CAAC,OAAQ,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,WAAY,CAAC,CACvE,CAAC,MAAO,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,WAAY,CAAC,CACtE,CAAC,IAAK,CAAE,MAAO,eAAgB,QAAS,QAAS,KAAM,UAAW,CAAC,CAEnE,CAAC,yCAA0C,eAAgB,eAAe,CAC1E,CAAC,oDAAqD,eAAgB,eAAe,CACrF,CAAC,yBAA0B,aAAc,eAAe,CACxD,CAAC,kBAAmB,eAAgB,eAAe,CACnD,CAAC,iBAAkB,SAAU,eAAe,CAC5C,CAAC,QAAS,MAAM,CAChB,CAAC,UAAW,UAAW,eAAe,CAEtC,CAAC,aAAc,UAAW,UAAU,CACpC,CAAC,4BAA6B,CAAC,mBAAoB,QAAS,UAAU,CAAC,CACvE,CAAC,WAAY,UAAW,QAAQ,CAChC,CAAC,aAAc,UAAW,UAAU,CACpC,CACE,yDACA,CAAC,UAAW,QAAS,aAAa,CACnC,CACD,CAAC,wBAAyB,WAAW,CACrC,CAAC,uBAAwB,CAAC,WAAY,CAAE,MAAO,WAAY,KAAM,eAAgB,CAAC,CAAC,CACnF,CAAC,8BAA+B,CAAC,YAAa,QAAS,WAAW,CAAC,CACnE,CACE,QACA,CACE,MAAO,CACL,YAAa,UACb,gBAAiB,UACjB,aAAc,mBACd,iBAAkB,mBAClB,aAAc,CACZ,MAAO,WACP,KAAM,eACP,CACD,WAAY,CACV,MAAO,aACP,KAAM,eACP,CACF,CACF,CACF,CACD,CAAC,QAAS,OAAQ,eAAe,CAEjC,CAAE,QAAS,cAAe,CAE1B,CAAC,2CAA4C,aAAa,CAE1D,CAAC,OAAQ,YAAY,CACrB,CAAC,OAAQ,YAAa,eAAe,CACrC,CAAC,KAAM,kBAAkB,CACzB,CAAC,gCAAiC,kBAAmB,eAAe,CACpE,CAAC,IAAK,kBAAkB,CACxB,CAAC,gDAAiD,UAAU,CAC5D,CAAC,WAAY,WAAW,CAExB,CAAC,SAAU,YAAY,CAEvB,CAAC,wBAAyB,iBAAiB,CAE3C,CAAC,WAAY,SAAU,eAAe,CACtC,CAAC,mBAAoB,CAAC,SAAU,gBAAiB,CAAE,MAAO,SAAU,KAAM,eAAgB,CAAC,CAAC,CAC5F,CAAC,IAAK,iBAAiB,CACxB,CACD,OAAQ,CACN,CAAC,IAAK,YAAa,OAAO,CAC1B,CAAC,MAAO,GAAI,OAAO,CACnB,CAAC,SAAU,QAAQ,CACnB,CAAC,UAAW,QAAS,OAAO,CAC5B,CAAC,OAAQ,UAAW,WAAW,CAC/B,CAAC,cAAe,OAAO,CACvB,CAAC,SAAU,YAAY,CACvB,CAAC,QAAS,kBAAkB,CAC5B,CAAC,QAAS,YAAY,CACvB,CACD,YAAa,CACX,CAAC,MAAO,GAAI,OAAO,CACnB,CAAC,SAAU,QAAQ,CACnB,CAAC,UAAW,QAAS,OAAO,CAC5B,CAAC,OAAQ,UAAW,WAAW,CAC/B,CAAC,kBAAmB,UAAW,OAAO,CACtC,CACE,6DACA,CACE,MAAO,CACL,YAAa,CAAE,MAAO,UAAW,KAAM,OAAQ,CAC/C,iBAAkB,CAAE,MAAO,UAAW,KAAM,OAAQ,CACpD,WAAY,CAAE,MAAO,WAAY,KAAM,OAAQ,CAChD,CACF,CACF,CACD,CAAC,GAAI,GAAI,OAAO,CACjB,CACD,QAAS,CACP,CAAC,UAAW,UAAU,CACtB,CAAC,OAAQ,UAAW,QAAQ,CAE5B,CAAC,OAAQ,UAAW,OAAO,CAC3B,CAAC,QAAS,UAAU,CACrB,CACD,KAAM,CACJ,CAAC,QAAS,MAAM,CAChB,CAAC,qCAAsC,UAAW,eAAe,CACjE,CAAC,YAAa,UAAW,OAAO,CAChC,CAAC,UAAW,aAAc,eAAe,CACzC,CAAC,QAAS,WAAY,eAAe,CACrC,CAAC,uBAAwB,UAAU,CACnC,CAAE,QAAS,QAAS,CACrB,CACD,OAAQ,CACN,CAAC,QAAS,MAAM,CAChB,CAAC,qCAAsC,UAAU,CACjD,CAAC,QAAS,WAAW,CACrB,CAAC,uBAAwB,UAAU,CACnC,CAAC,WAAY,WAAY,OAAO,CAChC,CAAC,IAAK,QAAS,OAAO,CACtB,CAAE,QAAS,QAAS,CACrB,CACD,OAAQ,CACN,CAAC,cAAe,SAAS,CACzB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CACE,IACA,CACE,MAAO,eACP,QAAS,SACT,SAAU,eACX,CACF,CACF,CACD,QAAS,CACP,CAAC,cAAe,SAAS,CACzB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,MAAO,wBAAwB,CAChC,CAAC,WAAY,SAAS,CACtB,CACE,MACA,CACE,MAAO,eACP,QAAS,SACT,SAAU,eACX,CACF,CACD,CAAC,IAAK,SAAS,CAChB,CACD,QAAS,CACP,CAAC,WAAY,gBAAgB,CAC7B,CACE,IACA,CACE,MAAO,eACP,QAAS,SACT,SAAU,eACX,CACF,CACD,CAAC,OAAQ,SAAS,CAClB,CAAC,kBAAmB,CAAC,WAAY,aAAa,CAAC,CAC/C,CAAC,OAAQ,WAAY,UAAU,CAC/B,CAAC,KAAM,SAAS,CAChB,CACE,qDACA,CAAC,UAAW,mBAAoB,SAAU,UAAU,CACrD,CACD,CAAC,yCAA0C,CAAC,UAAW,SAAU,UAAU,CAAC,CAC5E,CAAC,iCAAkC,CAAC,UAAW,mBAAoB,UAAU,CAAC,CAC9E,CAAC,qBAAsB,CAAC,UAAW,UAAU,CAAC,CAC9C,CAAC,IAAK,SAAS,CAChB,CACD,SAAU,CACR,CAAC,WAAY,gBAAgB,CAC7B,CAAC,WAAY,SAAS,CACtB,CACE,MACA,CACE,MAAO,eACP,QAAS,SACT,SAAU,eACX,CACF,CACD,CAAC,OAAQ,SAAS,CAClB,CAAC,kBAAmB,CAAC,WAAY,aAAa,CAAC,CAC/C,CAAC,OAAQ,WAAY,UAAU,CAC/B,CAAC,KAAM,SAAS,CAChB,CACE,qDACA,CAAC,UAAW,mBAAoB,SAAU,UAAU,CACrD,CACD,CAAC,yCAA0C,CAAC,UAAW,SAAU,UAAU,CAAC,CAC5E,CAAC,iCAAkC,CAAC,UAAW,mBAAoB,UAAU,CAAC,CAC9E,CAAC,qBAAsB,CAAC,UAAW,UAAU,CAAC,CAC9C,CAAC,IAAK,SAAS,CAChB,CACD,QAAS,CACP,CAAC,WAAY,gBAAgB,CAC7B,CACE,IACA,CACE,MAAO,eACP,QAAS,SACT,SAAU,eACX,CACF,CACD,CAAC,OAAQ,SAAS,CAClB,CAAC,kBAAmB,CAAC,WAAY,aAAa,CAAC,CAC/C,CAAC,OAAQ,WAAY,UAAU,CAC/B,CAAC,IAAK,SAAS,CAChB,CACD,SAAU,CACR,CAAC,WAAY,gBAAgB,CAC7B,CAAC,WAAY,SAAS,CACtB,CACE,MACA,CACE,MAAO,eACP,QAAS,SACT,SAAU,eACX,CACF,CACD,CAAC,OAAQ,SAAS,CAClB,CAAC,kBAAmB,CAAC,WAAY,aAAa,CAAC,CAC/C,CAAC,OAAQ,WAAY,UAAU,CAC/B,CAAC,IAAK,SAAS,CAChB,CACD,OAAQ,CAAC,CAAC,IAAK,WAAY,QAAQ,CAAE,CAAC,IAAK,WAAY,OAAO,CAAE,CAAE,QAAS,QAAS,CAAC,CACrF,UAAW,CACT,CAAC,OAAQ,SAAS,CAClB,CACE,IACA,CACE,MAAO,eACP,QAAS,SACT,SAAU,eACX,CACF,CACF,CACD,WAAY,CACV,CAAC,OAAQ,SAAS,CAClB,CAAC,WAAY,SAAS,CACtB,CACE,MACA,CACE,MAAO,eACP,QAAS,SACT,SAAU,eACX,CACF,CACD,CAAC,IAAK,SAAS,CAChB,CACD,WAAY,CACV,CAAC,aAAc,QAAQ,CACvB,CAAC,OAAQ,UAAW,WAAW,CAC/B,CAAC,UAAW,UAAU,CACvB,CACF,CACF"}