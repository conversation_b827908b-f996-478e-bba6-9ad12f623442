import{n as e}from"./index-BtNCOo_D.js";
/*!-----------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation. All rights reserved.
* Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
* Released under the MIT license
* https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
*-----------------------------------------------------------------------------*/
var t=Object.defineProperty,n=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a=(e,a,o,s)=>{if(a&&typeof a==`object`||typeof a==`function`)for(let c of r(a))!i.call(e,c)&&c!==o&&t(e,c,{get:()=>a[c],enumerable:!(s=n(a,c))||s.enumerable});return e},o=(e,t,n)=>(a(e,t,`default`),n&&a(n,t,`default`)),s={};o(s,e);var c=[`area`,`base`,`br`,`col`,`embed`,`hr`,`img`,`input`,`keygen`,`link`,`menuitem`,`meta`,`param`,`source`,`track`,`wbr`],l={wordPattern:/(-?\d*\.\d\w*)|([^\`\~\!\@\$\^\&\*\(\)\=\+\[\{\]\}\\\|\;\:\'\"\,\.\<\>\/\s]+)/g,comments:{blockComment:[`<!--`,`-->`]},brackets:[[`<!--`,`-->`],[`<`,`>`],[`{`,`}`],[`(`,`)`]],autoClosingPairs:[{open:`{`,close:`}`},{open:`[`,close:`]`},{open:`(`,close:`)`},{open:`"`,close:`"`},{open:`'`,close:`'`}],surroundingPairs:[{open:`"`,close:`"`},{open:`'`,close:`'`},{open:`{`,close:`}`},{open:`[`,close:`]`},{open:`(`,close:`)`},{open:`<`,close:`>`}],onEnterRules:[{beforeText:RegExp(`<(?!(?:${c.join(`|`)}))([_:\\w][_:\\w-.\\d]*)([^/>]*(?!/)>)[^<]*$`,`i`),afterText:/^<\/([_:\w][_:\w-.\d]*)\s*>$/i,action:{indentAction:s.languages.IndentAction.IndentOutdent}},{beforeText:RegExp(`<(?!(?:${c.join(`|`)}))(\\w[\\w\\d]*)([^/>]*(?!/)>)[^<]*$`,`i`),action:{indentAction:s.languages.IndentAction.Indent}}],folding:{markers:{start:RegExp(`^\\s*<!--\\s*#region\\b.*-->`),end:RegExp(`^\\s*<!--\\s*#endregion\\b.*-->`)}}},u={defaultToken:``,tokenPostfix:`.html`,ignoreCase:!0,tokenizer:{root:[[/<!DOCTYPE/,`metatag`,`@doctype`],[/<!--/,`comment`,`@comment`],[/(<)((?:[\w\-]+:)?[\w\-]+)(\s*)(\/>)/,[`delimiter`,`tag`,``,`delimiter`]],[/(<)(script)/,[`delimiter`,{token:`tag`,next:`@script`}]],[/(<)(style)/,[`delimiter`,{token:`tag`,next:`@style`}]],[/(<)((?:[\w\-]+:)?[\w\-]+)/,[`delimiter`,{token:`tag`,next:`@otherTag`}]],[/(<\/)((?:[\w\-]+:)?[\w\-]+)/,[`delimiter`,{token:`tag`,next:`@otherTag`}]],[/</,`delimiter`],[/[^<]+/]],doctype:[[/[^>]+/,`metatag.content`],[/>/,`metatag`,`@pop`]],comment:[[/-->/,`comment`,`@pop`],[/[^-]+/,`comment.content`],[/./,`comment.content`]],otherTag:[[/\/?>/,`delimiter`,`@pop`],[/"([^"]*)"/,`attribute.value`],[/'([^']*)'/,`attribute.value`],[/[\w\-]+/,`attribute.name`],[/=/,`delimiter`],[/[ \t\r\n]+/]],script:[[/type/,`attribute.name`,`@scriptAfterType`],[/"([^"]*)"/,`attribute.value`],[/'([^']*)'/,`attribute.value`],[/[\w\-]+/,`attribute.name`],[/=/,`delimiter`],[/>/,{token:`delimiter`,next:`@scriptEmbedded`,nextEmbedded:`text/javascript`}],[/[ \t\r\n]+/],[/(<\/)(script\s*)(>)/,[`delimiter`,`tag`,{token:`delimiter`,next:`@pop`}]]],scriptAfterType:[[/=/,`delimiter`,`@scriptAfterTypeEquals`],[/>/,{token:`delimiter`,next:`@scriptEmbedded`,nextEmbedded:`text/javascript`}],[/[ \t\r\n]+/],[/<\/script\s*>/,{token:`@rematch`,next:`@pop`}]],scriptAfterTypeEquals:[[/"module"/,{token:`attribute.value`,switchTo:`@scriptWithCustomType.text/javascript`}],[/'module'/,{token:`attribute.value`,switchTo:`@scriptWithCustomType.text/javascript`}],[/"([^"]*)"/,{token:`attribute.value`,switchTo:`@scriptWithCustomType.$1`}],[/'([^']*)'/,{token:`attribute.value`,switchTo:`@scriptWithCustomType.$1`}],[/>/,{token:`delimiter`,next:`@scriptEmbedded`,nextEmbedded:`text/javascript`}],[/[ \t\r\n]+/],[/<\/script\s*>/,{token:`@rematch`,next:`@pop`}]],scriptWithCustomType:[[/>/,{token:`delimiter`,next:`@scriptEmbedded.$S2`,nextEmbedded:`$S2`}],[/"([^"]*)"/,`attribute.value`],[/'([^']*)'/,`attribute.value`],[/[\w\-]+/,`attribute.name`],[/=/,`delimiter`],[/[ \t\r\n]+/],[/<\/script\s*>/,{token:`@rematch`,next:`@pop`}]],scriptEmbedded:[[/<\/script/,{token:`@rematch`,next:`@pop`,nextEmbedded:`@pop`}],[/[^<]+/,``]],style:[[/type/,`attribute.name`,`@styleAfterType`],[/"([^"]*)"/,`attribute.value`],[/'([^']*)'/,`attribute.value`],[/[\w\-]+/,`attribute.name`],[/=/,`delimiter`],[/>/,{token:`delimiter`,next:`@styleEmbedded`,nextEmbedded:`text/css`}],[/[ \t\r\n]+/],[/(<\/)(style\s*)(>)/,[`delimiter`,`tag`,{token:`delimiter`,next:`@pop`}]]],styleAfterType:[[/=/,`delimiter`,`@styleAfterTypeEquals`],[/>/,{token:`delimiter`,next:`@styleEmbedded`,nextEmbedded:`text/css`}],[/[ \t\r\n]+/],[/<\/style\s*>/,{token:`@rematch`,next:`@pop`}]],styleAfterTypeEquals:[[/"([^"]*)"/,{token:`attribute.value`,switchTo:`@styleWithCustomType.$1`}],[/'([^']*)'/,{token:`attribute.value`,switchTo:`@styleWithCustomType.$1`}],[/>/,{token:`delimiter`,next:`@styleEmbedded`,nextEmbedded:`text/css`}],[/[ \t\r\n]+/],[/<\/style\s*>/,{token:`@rematch`,next:`@pop`}]],styleWithCustomType:[[/>/,{token:`delimiter`,next:`@styleEmbedded.$S2`,nextEmbedded:`$S2`}],[/"([^"]*)"/,`attribute.value`],[/'([^']*)'/,`attribute.value`],[/[\w\-]+/,`attribute.name`],[/=/,`delimiter`],[/[ \t\r\n]+/],[/<\/style\s*>/,{token:`@rematch`,next:`@pop`}]],styleEmbedded:[[/<\/style/,{token:`@rematch`,next:`@pop`,nextEmbedded:`@pop`}],[/[^<]+/,``]]}};export{l as conf,u as language};
//# sourceMappingURL=html-B4zft6rn.js.map