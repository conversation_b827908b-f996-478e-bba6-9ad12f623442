import{n as e,t}from"./index-BtNCOo_D.js";
/*!-----------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation. All rights reserved.
* Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
* Released under the MIT license
* https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
*-----------------------------------------------------------------------------*/
var n=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,o=(e,t,o,s)=>{if(t&&typeof t==`object`||typeof t==`function`)for(let c of i(t))!a.call(e,c)&&c!==o&&n(e,c,{get:()=>t[c],enumerable:!(s=r(t,c))||s.enumerable});return e},s=(e,t,n)=>(o(e,t,`default`),n&&o(n,t,`default`)),c={};s(c,e);var l=class{constructor(e,t){this._modeId=e,this._defaults=t,this._worker=null,this._client=null,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker()),this._updateExtraLibsToken=0,this._extraLibsChangeListener=this._defaults.onDidExtraLibsChange(()=>this._updateExtraLibs())}dispose(){this._configChangeListener.dispose(),this._extraLibsChangeListener.dispose(),this._stopWorker()}_stopWorker(){this._worker&&=(this._worker.dispose(),null),this._client=null}async _updateExtraLibs(){if(!this._worker)return;let e=++this._updateExtraLibsToken,t=await this._worker.getProxy();this._updateExtraLibsToken===e&&t.updateExtraLibs(this._defaults.getExtraLibs())}_getClient(){return this._client||=(async()=>(this._worker=c.editor.createWebWorker({moduleId:`vs/language/typescript/tsWorker`,label:this._modeId,keepIdleModels:!0,createData:{compilerOptions:this._defaults.getCompilerOptions(),extraLibs:this._defaults.getExtraLibs(),customWorkerPath:this._defaults.workerOptions.customWorkerPath,inlayHintsOptions:this._defaults.inlayHintsOptions}}),this._defaults.getEagerModelSync()?await this._worker.withSyncedResources(c.editor.getModels().filter(e=>e.getLanguageId()===this._modeId).map(e=>e.uri)):await this._worker.getProxy()))(),this._client}async getLanguageServiceWorker(...e){let t=await this._getClient();return this._worker&&await this._worker.withSyncedResources(e),t}},u={};u[`lib.d.ts`]=!0,u[`lib.decorators.d.ts`]=!0,u[`lib.decorators.legacy.d.ts`]=!0,u[`lib.dom.asynciterable.d.ts`]=!0,u[`lib.dom.d.ts`]=!0,u[`lib.dom.iterable.d.ts`]=!0,u[`lib.es2015.collection.d.ts`]=!0,u[`lib.es2015.core.d.ts`]=!0,u[`lib.es2015.d.ts`]=!0,u[`lib.es2015.generator.d.ts`]=!0,u[`lib.es2015.iterable.d.ts`]=!0,u[`lib.es2015.promise.d.ts`]=!0,u[`lib.es2015.proxy.d.ts`]=!0,u[`lib.es2015.reflect.d.ts`]=!0,u[`lib.es2015.symbol.d.ts`]=!0,u[`lib.es2015.symbol.wellknown.d.ts`]=!0,u[`lib.es2016.array.include.d.ts`]=!0,u[`lib.es2016.d.ts`]=!0,u[`lib.es2016.full.d.ts`]=!0,u[`lib.es2016.intl.d.ts`]=!0,u[`lib.es2017.d.ts`]=!0,u[`lib.es2017.date.d.ts`]=!0,u[`lib.es2017.full.d.ts`]=!0,u[`lib.es2017.intl.d.ts`]=!0,u[`lib.es2017.object.d.ts`]=!0,u[`lib.es2017.sharedmemory.d.ts`]=!0,u[`lib.es2017.string.d.ts`]=!0,u[`lib.es2017.typedarrays.d.ts`]=!0,u[`lib.es2018.asyncgenerator.d.ts`]=!0,u[`lib.es2018.asynciterable.d.ts`]=!0,u[`lib.es2018.d.ts`]=!0,u[`lib.es2018.full.d.ts`]=!0,u[`lib.es2018.intl.d.ts`]=!0,u[`lib.es2018.promise.d.ts`]=!0,u[`lib.es2018.regexp.d.ts`]=!0,u[`lib.es2019.array.d.ts`]=!0,u[`lib.es2019.d.ts`]=!0,u[`lib.es2019.full.d.ts`]=!0,u[`lib.es2019.intl.d.ts`]=!0,u[`lib.es2019.object.d.ts`]=!0,u[`lib.es2019.string.d.ts`]=!0,u[`lib.es2019.symbol.d.ts`]=!0,u[`lib.es2020.bigint.d.ts`]=!0,u[`lib.es2020.d.ts`]=!0,u[`lib.es2020.date.d.ts`]=!0,u[`lib.es2020.full.d.ts`]=!0,u[`lib.es2020.intl.d.ts`]=!0,u[`lib.es2020.number.d.ts`]=!0,u[`lib.es2020.promise.d.ts`]=!0,u[`lib.es2020.sharedmemory.d.ts`]=!0,u[`lib.es2020.string.d.ts`]=!0,u[`lib.es2020.symbol.wellknown.d.ts`]=!0,u[`lib.es2021.d.ts`]=!0,u[`lib.es2021.full.d.ts`]=!0,u[`lib.es2021.intl.d.ts`]=!0,u[`lib.es2021.promise.d.ts`]=!0,u[`lib.es2021.string.d.ts`]=!0,u[`lib.es2021.weakref.d.ts`]=!0,u[`lib.es2022.array.d.ts`]=!0,u[`lib.es2022.d.ts`]=!0,u[`lib.es2022.error.d.ts`]=!0,u[`lib.es2022.full.d.ts`]=!0,u[`lib.es2022.intl.d.ts`]=!0,u[`lib.es2022.object.d.ts`]=!0,u[`lib.es2022.regexp.d.ts`]=!0,u[`lib.es2022.sharedmemory.d.ts`]=!0,u[`lib.es2022.string.d.ts`]=!0,u[`lib.es2023.array.d.ts`]=!0,u[`lib.es2023.collection.d.ts`]=!0,u[`lib.es2023.d.ts`]=!0,u[`lib.es2023.full.d.ts`]=!0,u[`lib.es5.d.ts`]=!0,u[`lib.es6.d.ts`]=!0,u[`lib.esnext.collection.d.ts`]=!0,u[`lib.esnext.d.ts`]=!0,u[`lib.esnext.decorators.d.ts`]=!0,u[`lib.esnext.disposable.d.ts`]=!0,u[`lib.esnext.full.d.ts`]=!0,u[`lib.esnext.intl.d.ts`]=!0,u[`lib.esnext.object.d.ts`]=!0,u[`lib.esnext.promise.d.ts`]=!0,u[`lib.scripthost.d.ts`]=!0,u[`lib.webworker.asynciterable.d.ts`]=!0,u[`lib.webworker.d.ts`]=!0,u[`lib.webworker.importscripts.d.ts`]=!0,u[`lib.webworker.iterable.d.ts`]=!0;function d(e,t,n=0){if(typeof e==`string`)return e;if(e===void 0)return``;let r=``;if(n){r+=t;for(let e=0;e<n;e++)r+=`  `}if(r+=e.messageText,n++,e.next)for(let i of e.next)r+=d(i,t,n);return r}function f(e){return e?e.map(e=>e.text).join(``):``}var p=class{constructor(e){this._worker=e}_textSpanToRange(e,t){let n=e.getPositionAt(t.start),r=e.getPositionAt(t.start+t.length),{lineNumber:i,column:a}=n,{lineNumber:o,column:s}=r;return{startLineNumber:i,startColumn:a,endLineNumber:o,endColumn:s}}},m=class{constructor(e){this._worker=e,this._libFiles={},this._hasFetchedLibFiles=!1,this._fetchLibFilesPromise=null}isLibFile(e){return e&&e.path.indexOf(`/lib.`)===0?!!u[e.path.slice(1)]:!1}getOrCreateModel(e){let n=c.Uri.parse(e),r=c.editor.getModel(n);if(r)return r;if(this.isLibFile(n)&&this._hasFetchedLibFiles)return c.editor.createModel(this._libFiles[n.path.slice(1)],`typescript`,n);let i=t.getExtraLibs()[e];return i?c.editor.createModel(i.content,`typescript`,n):null}_containsLibFile(e){for(let t of e)if(this.isLibFile(t))return!0;return!1}async fetchLibFilesIfNecessary(e){this._containsLibFile(e)&&await this._fetchLibFiles()}_fetchLibFiles(){return this._fetchLibFilesPromise||=this._worker().then(e=>e.getLibFiles()).then(e=>{this._hasFetchedLibFiles=!0,this._libFiles=e}),this._fetchLibFilesPromise}},h=class extends p{constructor(e,t,n,r){super(r),this._libFiles=e,this._defaults=t,this._selector=n,this._disposables=[],this._listener=Object.create(null);let i=e=>{if(e.getLanguageId()!==n)return;let t=()=>{let{onlyVisible:t}=this._defaults.getDiagnosticsOptions();t?e.isAttachedToEditor()&&this._doValidate(e):this._doValidate(e)},r,i=e.onDidChangeContent(()=>{clearTimeout(r),r=window.setTimeout(t,500)}),a=e.onDidChangeAttached(()=>{let{onlyVisible:n}=this._defaults.getDiagnosticsOptions();n&&(e.isAttachedToEditor()?t():c.editor.setModelMarkers(e,this._selector,[]))});this._listener[e.uri.toString()]={dispose(){i.dispose(),a.dispose(),clearTimeout(r)}},t()},a=e=>{c.editor.setModelMarkers(e,this._selector,[]);let t=e.uri.toString();this._listener[t]&&(this._listener[t].dispose(),delete this._listener[t])};this._disposables.push(c.editor.onDidCreateModel(e=>i(e))),this._disposables.push(c.editor.onWillDisposeModel(a)),this._disposables.push(c.editor.onDidChangeModelLanguage(e=>{a(e.model),i(e.model)})),this._disposables.push({dispose(){for(let e of c.editor.getModels())a(e)}});let o=()=>{for(let e of c.editor.getModels())a(e),i(e)};this._disposables.push(this._defaults.onDidChange(o)),this._disposables.push(this._defaults.onDidExtraLibsChange(o)),c.editor.getModels().forEach(e=>i(e))}dispose(){this._disposables.forEach(e=>e&&e.dispose()),this._disposables=[]}async _doValidate(e){let t=await this._worker(e.uri);if(e.isDisposed())return;let n=[],{noSyntaxValidation:r,noSemanticValidation:i,noSuggestionDiagnostics:a}=this._defaults.getDiagnosticsOptions();r||n.push(t.getSyntacticDiagnostics(e.uri.toString())),i||n.push(t.getSemanticDiagnostics(e.uri.toString())),a||n.push(t.getSuggestionDiagnostics(e.uri.toString()));let o=await Promise.all(n);if(!o||e.isDisposed())return;let s=o.reduce((e,t)=>t.concat(e),[]).filter(e=>(this._defaults.getDiagnosticsOptions().diagnosticCodesToIgnore||[]).indexOf(e.code)===-1),l=s.map(e=>e.relatedInformation||[]).reduce((e,t)=>t.concat(e),[]).map(e=>e.file?c.Uri.parse(e.file.fileName):null);await this._libFiles.fetchLibFilesIfNecessary(l),!e.isDisposed()&&c.editor.setModelMarkers(e,this._selector,s.map(t=>this._convertDiagnostics(e,t)))}_convertDiagnostics(e,t){let n=t.start||0,r=t.length||1,{lineNumber:i,column:a}=e.getPositionAt(n),{lineNumber:o,column:s}=e.getPositionAt(n+r),l=[];return t.reportsUnnecessary&&l.push(c.MarkerTag.Unnecessary),t.reportsDeprecated&&l.push(c.MarkerTag.Deprecated),{severity:this._tsDiagnosticCategoryToMarkerSeverity(t.category),startLineNumber:i,startColumn:a,endLineNumber:o,endColumn:s,message:d(t.messageText,`
`),code:t.code.toString(),tags:l,relatedInformation:this._convertRelatedInformation(e,t.relatedInformation)}}_convertRelatedInformation(e,t){if(!t)return[];let n=[];return t.forEach(t=>{let r=e;if(t.file&&(r=this._libFiles.getOrCreateModel(t.file.fileName)),!r)return;let i=t.start||0,a=t.length||1,{lineNumber:o,column:s}=r.getPositionAt(i),{lineNumber:c,column:l}=r.getPositionAt(i+a);n.push({resource:r.uri,startLineNumber:o,startColumn:s,endLineNumber:c,endColumn:l,message:d(t.messageText,`
`)})}),n}_tsDiagnosticCategoryToMarkerSeverity(e){switch(e){case 1:return c.MarkerSeverity.Error;case 3:return c.MarkerSeverity.Info;case 0:return c.MarkerSeverity.Warning;case 2:return c.MarkerSeverity.Hint}return c.MarkerSeverity.Info}},g=class e extends p{get triggerCharacters(){return[`.`]}async provideCompletionItems(t,n,r,i){let a=t.getWordUntilPosition(n),o=new c.Range(n.lineNumber,a.startColumn,n.lineNumber,a.endColumn),s=t.uri,l=t.getOffsetAt(n),u=await this._worker(s);if(t.isDisposed())return;let d=await u.getCompletionsAtPosition(s.toString(),l);if(!(!d||t.isDisposed()))return{suggestions:d.entries.map(r=>{let i=o;if(r.replacementSpan){let e=t.getPositionAt(r.replacementSpan.start),n=t.getPositionAt(r.replacementSpan.start+r.replacementSpan.length);i=new c.Range(e.lineNumber,e.column,n.lineNumber,n.column)}let a=[];return r.kindModifiers!==void 0&&r.kindModifiers.indexOf(`deprecated`)!==-1&&a.push(c.languages.CompletionItemTag.Deprecated),{uri:s,position:n,offset:l,range:i,label:r.name,insertText:r.name,sortText:r.sortText,kind:e.convertKind(r.kind),tags:a}})}}async resolveCompletionItem(t,n){let r=t,i=r.uri,a=r.position,o=r.offset,s=await(await this._worker(i)).getCompletionEntryDetails(i.toString(),o,r.label);return s?{uri:i,position:a,label:s.name,kind:e.convertKind(s.kind),detail:f(s.displayParts),documentation:{value:e.createDocumentationString(s)}}:r}static convertKind(e){switch(e){case w.primitiveType:case w.keyword:return c.languages.CompletionItemKind.Keyword;case w.variable:case w.localVariable:return c.languages.CompletionItemKind.Variable;case w.memberVariable:case w.memberGetAccessor:case w.memberSetAccessor:return c.languages.CompletionItemKind.Field;case w.function:case w.memberFunction:case w.constructSignature:case w.callSignature:case w.indexSignature:return c.languages.CompletionItemKind.Function;case w.enum:return c.languages.CompletionItemKind.Enum;case w.module:return c.languages.CompletionItemKind.Module;case w.class:return c.languages.CompletionItemKind.Class;case w.interface:return c.languages.CompletionItemKind.Interface;case w.warning:return c.languages.CompletionItemKind.File}return c.languages.CompletionItemKind.Property}static createDocumentationString(e){let t=f(e.documentation);if(e.tags)for(let n of e.tags)t+=`

${_(n)}`;return t}};function _(e){let t=`*@${e.name}*`;if(e.name===`param`&&e.text){let[n,...r]=e.text;t+=`\`${n.text}\``,r.length>0&&(t+=` \u2014 ${r.map(e=>e.text).join(` `)}`)}else Array.isArray(e.text)?t+=` \u2014 ${e.text.map(e=>e.text).join(` `)}`:e.text&&(t+=` \u2014 ${e.text}`);return t}var v=class e extends p{constructor(){super(...arguments),this.signatureHelpTriggerCharacters=[`(`,`,`]}static _toSignatureHelpTriggerReason(e){switch(e.triggerKind){case c.languages.SignatureHelpTriggerKind.TriggerCharacter:return e.triggerCharacter?e.isRetrigger?{kind:`retrigger`,triggerCharacter:e.triggerCharacter}:{kind:`characterTyped`,triggerCharacter:e.triggerCharacter}:{kind:`invoked`};case c.languages.SignatureHelpTriggerKind.ContentChange:return e.isRetrigger?{kind:`retrigger`}:{kind:`invoked`};case c.languages.SignatureHelpTriggerKind.Invoke:default:return{kind:`invoked`}}}async provideSignatureHelp(t,n,r,i){let a=t.uri,o=t.getOffsetAt(n),s=await this._worker(a);if(t.isDisposed())return;let c=await s.getSignatureHelpItems(a.toString(),o,{triggerReason:e._toSignatureHelpTriggerReason(i)});if(!c||t.isDisposed())return;let l={activeSignature:c.selectedItemIndex,activeParameter:c.argumentIndex,signatures:[]};return c.items.forEach(e=>{let t={label:``,parameters:[]};t.documentation={value:f(e.documentation)},t.label+=f(e.prefixDisplayParts),e.parameters.forEach((n,r,i)=>{let a=f(n.displayParts),o={label:a,documentation:{value:f(n.documentation)}};t.label+=a,t.parameters.push(o),r<i.length-1&&(t.label+=f(e.separatorDisplayParts))}),t.label+=f(e.suffixDisplayParts),l.signatures.push(t)}),{value:l,dispose(){}}}},y=class extends p{async provideHover(e,t,n){let r=e.uri,i=e.getOffsetAt(t),a=await this._worker(r);if(e.isDisposed())return;let o=await a.getQuickInfoAtPosition(r.toString(),i);if(!o||e.isDisposed())return;let s=f(o.documentation),c=o.tags?o.tags.map(e=>_(e)).join(`  

`):``,l=f(o.displayParts);return{range:this._textSpanToRange(e,o.textSpan),contents:[{value:"```typescript\n"+l+"\n```\n"},{value:s+(c?`

`+c:``)}]}}},b=class extends p{async provideDocumentHighlights(e,t,n){let r=e.uri,i=e.getOffsetAt(t),a=await this._worker(r);if(e.isDisposed())return;let o=await a.getDocumentHighlights(r.toString(),i,[r.toString()]);if(!(!o||e.isDisposed()))return o.flatMap(t=>t.highlightSpans.map(t=>({range:this._textSpanToRange(e,t.textSpan),kind:t.kind===`writtenReference`?c.languages.DocumentHighlightKind.Write:c.languages.DocumentHighlightKind.Text})))}},x=class extends p{constructor(e,t){super(t),this._libFiles=e}async provideDefinition(e,t,n){let r=e.uri,i=e.getOffsetAt(t),a=await this._worker(r);if(e.isDisposed())return;let o=await a.getDefinitionAtPosition(r.toString(),i);if(!o||e.isDisposed()||(await this._libFiles.fetchLibFilesIfNecessary(o.map(e=>c.Uri.parse(e.fileName))),e.isDisposed()))return;let s=[];for(let e of o){let t=this._libFiles.getOrCreateModel(e.fileName);t&&s.push({uri:t.uri,range:this._textSpanToRange(t,e.textSpan)})}return s}},S=class extends p{constructor(e,t){super(t),this._libFiles=e}async provideReferences(e,t,n,r){let i=e.uri,a=e.getOffsetAt(t),o=await this._worker(i);if(e.isDisposed())return;let s=await o.getReferencesAtPosition(i.toString(),a);if(!s||e.isDisposed()||(await this._libFiles.fetchLibFilesIfNecessary(s.map(e=>c.Uri.parse(e.fileName))),e.isDisposed()))return;let l=[];for(let e of s){let t=this._libFiles.getOrCreateModel(e.fileName);t&&l.push({uri:t.uri,range:this._textSpanToRange(t,e.textSpan)})}return l}},C=class extends p{async provideDocumentSymbols(e,t){let n=e.uri,r=await this._worker(n);if(e.isDisposed())return;let i=await r.getNavigationTree(n.toString());if(!i||e.isDisposed())return;let a=(t,n)=>({name:t.text,detail:``,kind:T[t.kind]||c.languages.SymbolKind.Variable,range:this._textSpanToRange(e,t.spans[0]),selectionRange:this._textSpanToRange(e,t.spans[0]),tags:[],children:t.childItems?.map(e=>a(e,t.text)),containerName:n});return i.childItems?i.childItems.map(e=>a(e)):[]}},w=class{static{this.unknown=``}static{this.keyword=`keyword`}static{this.script=`script`}static{this.module=`module`}static{this.class=`class`}static{this.interface=`interface`}static{this.type=`type`}static{this.enum=`enum`}static{this.variable=`var`}static{this.localVariable=`local var`}static{this.function=`function`}static{this.localFunction=`local function`}static{this.memberFunction=`method`}static{this.memberGetAccessor=`getter`}static{this.memberSetAccessor=`setter`}static{this.memberVariable=`property`}static{this.constructorImplementation=`constructor`}static{this.callSignature=`call`}static{this.indexSignature=`index`}static{this.constructSignature=`construct`}static{this.parameter=`parameter`}static{this.typeParameter=`type parameter`}static{this.primitiveType=`primitive type`}static{this.label=`label`}static{this.alias=`alias`}static{this.const=`const`}static{this.let=`let`}static{this.warning=`warning`}},T=Object.create(null);T[w.module]=c.languages.SymbolKind.Module,T[w.class]=c.languages.SymbolKind.Class,T[w.enum]=c.languages.SymbolKind.Enum,T[w.interface]=c.languages.SymbolKind.Interface,T[w.memberFunction]=c.languages.SymbolKind.Method,T[w.memberVariable]=c.languages.SymbolKind.Property,T[w.memberGetAccessor]=c.languages.SymbolKind.Property,T[w.memberSetAccessor]=c.languages.SymbolKind.Property,T[w.variable]=c.languages.SymbolKind.Variable,T[w.const]=c.languages.SymbolKind.Variable,T[w.localVariable]=c.languages.SymbolKind.Variable,T[w.variable]=c.languages.SymbolKind.Variable,T[w.function]=c.languages.SymbolKind.Function,T[w.localFunction]=c.languages.SymbolKind.Function;var E=class extends p{static _convertOptions(e){return{ConvertTabsToSpaces:e.insertSpaces,TabSize:e.tabSize,IndentSize:e.tabSize,IndentStyle:2,NewLineCharacter:`
`,InsertSpaceAfterCommaDelimiter:!0,InsertSpaceAfterSemicolonInForStatements:!0,InsertSpaceBeforeAndAfterBinaryOperators:!0,InsertSpaceAfterKeywordsInControlFlowStatements:!0,InsertSpaceAfterFunctionKeywordForAnonymousFunctions:!0,InsertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis:!1,InsertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets:!1,InsertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces:!1,PlaceOpenBraceOnNewLineForControlBlocks:!1,PlaceOpenBraceOnNewLineForFunctions:!1}}_convertTextChanges(e,t){return{text:t.newText,range:this._textSpanToRange(e,t.span)}}},D=class extends E{constructor(){super(...arguments),this.canFormatMultipleRanges=!1}async provideDocumentRangeFormattingEdits(e,t,n,r){let i=e.uri,a=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),o=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),s=await this._worker(i);if(e.isDisposed())return;let c=await s.getFormattingEditsForRange(i.toString(),a,o,E._convertOptions(n));if(!(!c||e.isDisposed()))return c.map(t=>this._convertTextChanges(e,t))}},O=class extends E{get autoFormatTriggerCharacters(){return[`;`,`}`,`
`]}async provideOnTypeFormattingEdits(e,t,n,r,i){let a=e.uri,o=e.getOffsetAt(t),s=await this._worker(a);if(e.isDisposed())return;let c=await s.getFormattingEditsAfterKeystroke(a.toString(),o,n,E._convertOptions(r));if(!(!c||e.isDisposed()))return c.map(t=>this._convertTextChanges(e,t))}},k=class extends E{async provideCodeActions(e,t,n,r){let i=e.uri,a=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),o=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),s=E._convertOptions(e.getOptions()),c=n.markers.filter(e=>e.code).map(e=>e.code).map(Number),l=await this._worker(i);if(e.isDisposed())return;let u=await l.getCodeFixesAtPosition(i.toString(),a,o,c,s);return!u||e.isDisposed()?{actions:[],dispose:()=>{}}:{actions:u.filter(e=>e.changes.filter(e=>e.isNewFile).length===0).map(t=>this._tsCodeFixActionToMonacoCodeAction(e,n,t)),dispose:()=>{}}}_tsCodeFixActionToMonacoCodeAction(e,t,n){let r=[];for(let t of n.changes)for(let n of t.textChanges)r.push({resource:e.uri,versionId:void 0,textEdit:{range:this._textSpanToRange(e,n.span),text:n.newText}});return{title:n.description,edit:{edits:r},diagnostics:t.markers,kind:`quickfix`}}},A=class extends p{constructor(e,t){super(t),this._libFiles=e}async provideRenameEdits(e,t,n,r){let i=e.uri,a=i.toString(),o=e.getOffsetAt(t),s=await this._worker(i);if(e.isDisposed())return;let c=await s.getRenameInfo(a,o,{allowRenameOfImportPath:!1});if(c.canRename===!1)return{edits:[],rejectReason:c.localizedErrorMessage};if(c.fileToRename!==void 0)throw Error(`Renaming files is not supported.`);let l=await s.findRenameLocations(a,o,!1,!1,!1);if(!l||e.isDisposed())return;let u=[];for(let e of l){let t=this._libFiles.getOrCreateModel(e.fileName);if(t)u.push({resource:t.uri,versionId:void 0,textEdit:{range:this._textSpanToRange(t,e.textSpan),text:n}});else throw Error(`Unknown file ${e.fileName}.`)}return{edits:u}}},j=class extends p{async provideInlayHints(e,t,n){let r=e.uri,i=r.toString(),a=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),o=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),s=await this._worker(r);return e.isDisposed()?null:{hints:(await s.provideInlayHints(i,a,o)).map(t=>({...t,label:t.text,position:e.getPositionAt(t.position),kind:this._convertHintKind(t.kind)})),dispose:()=>{}}}_convertHintKind(e){switch(e){case`Parameter`:return c.languages.InlayHintKind.Parameter;case`Type`:return c.languages.InlayHintKind.Type;default:return c.languages.InlayHintKind.Type}}},M,N;function P(e){N=R(e,`typescript`)}function F(e){M=R(e,`javascript`)}function I(){return new Promise((e,t)=>{if(!M)return t(`JavaScript not registered!`);e(M)})}function L(){return new Promise((e,t)=>{if(!N)return t(`TypeScript not registered!`);e(N)})}function R(e,t){let n=[],r=[],i=new l(t,e);n.push(i);let a=(...e)=>i.getLanguageServiceWorker(...e),o=new m(a);function s(){let{modeConfiguration:n}=e;B(r),n.completionItems&&r.push(c.languages.registerCompletionItemProvider(t,new g(a))),n.signatureHelp&&r.push(c.languages.registerSignatureHelpProvider(t,new v(a))),n.hovers&&r.push(c.languages.registerHoverProvider(t,new y(a))),n.documentHighlights&&r.push(c.languages.registerDocumentHighlightProvider(t,new b(a))),n.definitions&&r.push(c.languages.registerDefinitionProvider(t,new x(o,a))),n.references&&r.push(c.languages.registerReferenceProvider(t,new S(o,a))),n.documentSymbols&&r.push(c.languages.registerDocumentSymbolProvider(t,new C(a))),n.rename&&r.push(c.languages.registerRenameProvider(t,new A(o,a))),n.documentRangeFormattingEdits&&r.push(c.languages.registerDocumentRangeFormattingEditProvider(t,new D(a))),n.onTypeFormattingEdits&&r.push(c.languages.registerOnTypeFormattingEditProvider(t,new O(a))),n.codeActions&&r.push(c.languages.registerCodeActionProvider(t,new k(a))),n.inlayHints&&r.push(c.languages.registerInlayHintsProvider(t,new j(a))),n.diagnostics&&r.push(new h(o,e,t,a))}return s(),n.push(z(r)),a}function z(e){return{dispose:()=>B(e)}}function B(e){for(;e.length;)e.pop().dispose()}export{p as Adapter,k as CodeActionAdaptor,x as DefinitionAdapter,h as DiagnosticsAdapter,b as DocumentHighlightAdapter,D as FormatAdapter,E as FormatHelper,O as FormatOnTypeAdapter,j as InlayHintsAdapter,w as Kind,m as LibFiles,C as OutlineAdapter,y as QuickInfoAdapter,S as ReferenceAdapter,A as RenameAdapter,v as SignatureHelpAdapter,g as SuggestAdapter,l as WorkerManager,d as flattenDiagnosticMessageText,I as getJavaScriptWorker,L as getTypeScriptWorker,F as setupJavaScript,P as setupTypeScript};
//# sourceMappingURL=tsMode-BoGhhfE6.js.map