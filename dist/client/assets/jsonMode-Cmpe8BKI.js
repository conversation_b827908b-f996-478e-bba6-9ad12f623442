import{n as e}from"./index-BtNCOo_D.js";
/*!-----------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation. All rights reserved.
* Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
* Released under the MIT license
* https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
*-----------------------------------------------------------------------------*/
var t=Object.defineProperty,n=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a=(e,a,o,s)=>{if(a&&typeof a==`object`||typeof a==`function`)for(let c of r(a))!i.call(e,c)&&c!==o&&t(e,c,{get:()=>a[c],enumerable:!(s=n(a,c))||s.enumerable});return e},o=(e,t,n)=>(a(e,t,`default`),n&&a(n,t,`default`)),s={};o(s,e);var c=class{constructor(e){this._defaults=e,this._worker=null,this._client=null,this._idleCheckInterval=window.setInterval(()=>this._checkIfIdle(),30*1e3),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker())}_stopWorker(){this._worker&&=(this._worker.dispose(),null),this._client=null}dispose(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()}_checkIfIdle(){this._worker&&Date.now()-this._lastUsedTime>12e4&&this._stopWorker()}_getClient(){return this._lastUsedTime=Date.now(),this._client||=(this._worker=s.editor.createWebWorker({moduleId:`vs/language/json/jsonWorker`,label:this._defaults.languageId,createData:{languageSettings:this._defaults.diagnosticsOptions,languageId:this._defaults.languageId,enableSchemaRequest:this._defaults.diagnosticsOptions.enableSchemaRequest}}),this._worker.getProxy()),this._client}getLanguageServiceWorker(...e){let t;return this._getClient().then(e=>{t=e}).then(t=>{if(this._worker)return this._worker.withSyncedResources(e)}).then(e=>t)}},l;(function(e){function t(e){return typeof e==`string`}e.is=t})(l||={});var u;(function(e){function t(e){return typeof e==`string`}e.is=t})(u||={});var d;(function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647;function t(t){return typeof t==`number`&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}e.is=t})(d||={});var f;(function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647;function t(t){return typeof t==`number`&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}e.is=t})(f||={});var p;(function(e){function t(e,t){return e===Number.MAX_VALUE&&(e=f.MAX_VALUE),t===Number.MAX_VALUE&&(t=f.MAX_VALUE),{line:e,character:t}}e.create=t;function n(e){let t=e;return L.objectLiteral(t)&&L.uinteger(t.line)&&L.uinteger(t.character)}e.is=n})(p||={});var m;(function(e){function t(e,t,n,r){if(L.uinteger(e)&&L.uinteger(t)&&L.uinteger(n)&&L.uinteger(r))return{start:p.create(e,t),end:p.create(n,r)};if(p.is(e)&&p.is(t))return{start:e,end:t};throw Error(`Range#create called with invalid arguments[${e}, ${t}, ${n}, ${r}]`)}e.create=t;function n(e){let t=e;return L.objectLiteral(t)&&p.is(t.start)&&p.is(t.end)}e.is=n})(m||={});var h;(function(e){function t(e,t){return{uri:e,range:t}}e.create=t;function n(e){let t=e;return L.objectLiteral(t)&&m.is(t.range)&&(L.string(t.uri)||L.undefined(t.uri))}e.is=n})(h||={});var g;(function(e){function t(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}}e.create=t;function n(e){let t=e;return L.objectLiteral(t)&&m.is(t.targetRange)&&L.string(t.targetUri)&&m.is(t.targetSelectionRange)&&(m.is(t.originSelectionRange)||L.undefined(t.originSelectionRange))}e.is=n})(g||={});var _;(function(e){function t(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}}e.create=t;function n(e){let t=e;return L.objectLiteral(t)&&L.numberRange(t.red,0,1)&&L.numberRange(t.green,0,1)&&L.numberRange(t.blue,0,1)&&L.numberRange(t.alpha,0,1)}e.is=n})(_||={});var v;(function(e){function t(e,t){return{range:e,color:t}}e.create=t;function n(e){let t=e;return L.objectLiteral(t)&&m.is(t.range)&&_.is(t.color)}e.is=n})(v||={});var ee;(function(e){function t(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}}e.create=t;function n(e){let t=e;return L.objectLiteral(t)&&L.string(t.label)&&(L.undefined(t.textEdit)||w.is(t))&&(L.undefined(t.additionalTextEdits)||L.typedArray(t.additionalTextEdits,w.is))}e.is=n})(ee||={});var y;(function(e){e.Comment=`comment`,e.Imports=`imports`,e.Region=`region`})(y||={});var te;(function(e){function t(e,t,n,r,i,a){let o={startLine:e,endLine:t};return L.defined(n)&&(o.startCharacter=n),L.defined(r)&&(o.endCharacter=r),L.defined(i)&&(o.kind=i),L.defined(a)&&(o.collapsedText=a),o}e.create=t;function n(e){let t=e;return L.objectLiteral(t)&&L.uinteger(t.startLine)&&L.uinteger(t.startLine)&&(L.undefined(t.startCharacter)||L.uinteger(t.startCharacter))&&(L.undefined(t.endCharacter)||L.uinteger(t.endCharacter))&&(L.undefined(t.kind)||L.string(t.kind))}e.is=n})(te||={});var b;(function(e){function t(e,t){return{location:e,message:t}}e.create=t;function n(e){let t=e;return L.defined(t)&&h.is(t.location)&&L.string(t.message)}e.is=n})(b||={});var x;(function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4})(x||={});var ne;(function(e){e.Unnecessary=1,e.Deprecated=2})(ne||={});var re;(function(e){function t(e){let t=e;return L.objectLiteral(t)&&L.string(t.href)}e.is=t})(re||={});var S;(function(e){function t(e,t,n,r,i,a){let o={range:e,message:t};return L.defined(n)&&(o.severity=n),L.defined(r)&&(o.code=r),L.defined(i)&&(o.source=i),L.defined(a)&&(o.relatedInformation=a),o}e.create=t;function n(e){let t=e;return L.defined(t)&&m.is(t.range)&&L.string(t.message)&&(L.number(t.severity)||L.undefined(t.severity))&&(L.integer(t.code)||L.string(t.code)||L.undefined(t.code))&&(L.undefined(t.codeDescription)||L.string(t.codeDescription?.href))&&(L.string(t.source)||L.undefined(t.source))&&(L.undefined(t.relatedInformation)||L.typedArray(t.relatedInformation,b.is))}e.is=n})(S||={});var C;(function(e){function t(e,t,...n){let r={title:e,command:t};return L.defined(n)&&n.length>0&&(r.arguments=n),r}e.create=t;function n(e){let t=e;return L.defined(t)&&L.string(t.title)&&L.string(t.command)}e.is=n})(C||={});var w;(function(e){function t(e,t){return{range:e,newText:t}}e.replace=t;function n(e,t){return{range:{start:e,end:e},newText:t}}e.insert=n;function r(e){return{range:e,newText:``}}e.del=r;function i(e){let t=e;return L.objectLiteral(t)&&L.string(t.newText)&&m.is(t.range)}e.is=i})(w||={});var T;(function(e){function t(e,t,n){let r={label:e};return t!==void 0&&(r.needsConfirmation=t),n!==void 0&&(r.description=n),r}e.create=t;function n(e){let t=e;return L.objectLiteral(t)&&L.string(t.label)&&(L.boolean(t.needsConfirmation)||t.needsConfirmation===void 0)&&(L.string(t.description)||t.description===void 0)}e.is=n})(T||={});var E;(function(e){function t(e){let t=e;return L.string(t)}e.is=t})(E||={});var ie;(function(e){function t(e,t,n){return{range:e,newText:t,annotationId:n}}e.replace=t;function n(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}}e.insert=n;function r(e,t){return{range:e,newText:``,annotationId:t}}e.del=r;function i(e){let t=e;return w.is(t)&&(T.is(t.annotationId)||E.is(t.annotationId))}e.is=i})(ie||={});var D;(function(e){function t(e,t){return{textDocument:e,edits:t}}e.create=t;function n(e){let t=e;return L.defined(t)&&ue.is(t.textDocument)&&Array.isArray(t.edits)}e.is=n})(D||={});var O;(function(e){function t(e,t,n){let r={kind:`create`,uri:e};return t!==void 0&&(t.overwrite!==void 0||t.ignoreIfExists!==void 0)&&(r.options=t),n!==void 0&&(r.annotationId=n),r}e.create=t;function n(e){let t=e;return t&&t.kind===`create`&&L.string(t.uri)&&(t.options===void 0||(t.options.overwrite===void 0||L.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===void 0||L.boolean(t.options.ignoreIfExists)))&&(t.annotationId===void 0||E.is(t.annotationId))}e.is=n})(O||={});var ae;(function(e){function t(e,t,n,r){let i={kind:`rename`,oldUri:e,newUri:t};return n!==void 0&&(n.overwrite!==void 0||n.ignoreIfExists!==void 0)&&(i.options=n),r!==void 0&&(i.annotationId=r),i}e.create=t;function n(e){let t=e;return t&&t.kind===`rename`&&L.string(t.oldUri)&&L.string(t.newUri)&&(t.options===void 0||(t.options.overwrite===void 0||L.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===void 0||L.boolean(t.options.ignoreIfExists)))&&(t.annotationId===void 0||E.is(t.annotationId))}e.is=n})(ae||={});var oe;(function(e){function t(e,t,n){let r={kind:`delete`,uri:e};return t!==void 0&&(t.recursive!==void 0||t.ignoreIfNotExists!==void 0)&&(r.options=t),n!==void 0&&(r.annotationId=n),r}e.create=t;function n(e){let t=e;return t&&t.kind===`delete`&&L.string(t.uri)&&(t.options===void 0||(t.options.recursive===void 0||L.boolean(t.options.recursive))&&(t.options.ignoreIfNotExists===void 0||L.boolean(t.options.ignoreIfNotExists)))&&(t.annotationId===void 0||E.is(t.annotationId))}e.is=n})(oe||={});var se;(function(e){function t(e){let t=e;return t&&(t.changes!==void 0||t.documentChanges!==void 0)&&(t.documentChanges===void 0||t.documentChanges.every(e=>L.string(e.kind)?O.is(e)||ae.is(e)||oe.is(e):D.is(e)))}e.is=t})(se||={});var ce;(function(e){function t(e){return{uri:e}}e.create=t;function n(e){let t=e;return L.defined(t)&&L.string(t.uri)}e.is=n})(ce||={});var le;(function(e){function t(e,t){return{uri:e,version:t}}e.create=t;function n(e){let t=e;return L.defined(t)&&L.string(t.uri)&&L.integer(t.version)}e.is=n})(le||={});var ue;(function(e){function t(e,t){return{uri:e,version:t}}e.create=t;function n(e){let t=e;return L.defined(t)&&L.string(t.uri)&&(t.version===null||L.integer(t.version))}e.is=n})(ue||={});var de;(function(e){function t(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}}e.create=t;function n(e){let t=e;return L.defined(t)&&L.string(t.uri)&&L.string(t.languageId)&&L.integer(t.version)&&L.string(t.text)}e.is=n})(de||={});var k;(function(e){e.PlainText=`plaintext`,e.Markdown=`markdown`;function t(t){let n=t;return n===e.PlainText||n===e.Markdown}e.is=t})(k||={});var A;(function(e){function t(e){let t=e;return L.objectLiteral(e)&&k.is(t.kind)&&L.string(t.value)}e.is=t})(A||={});var j;(function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25})(j||={});var fe;(function(e){e.PlainText=1,e.Snippet=2})(fe||={});var pe;(function(e){e.Deprecated=1})(pe||={});var me;(function(e){function t(e,t,n){return{newText:e,insert:t,replace:n}}e.create=t;function n(e){let t=e;return t&&L.string(t.newText)&&m.is(t.insert)&&m.is(t.replace)}e.is=n})(me||={});var he;(function(e){e.asIs=1,e.adjustIndentation=2})(he||={});var ge;(function(e){function t(e){let t=e;return t&&(L.string(t.detail)||t.detail===void 0)&&(L.string(t.description)||t.description===void 0)}e.is=t})(ge||={});var _e;(function(e){function t(e){return{label:e}}e.create=t})(_e||={});var ve;(function(e){function t(e,t){return{items:e||[],isIncomplete:!!t}}e.create=t})(ve||={});var M;(function(e){function t(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,`\\$&`)}e.fromPlainText=t;function n(e){let t=e;return L.string(t)||L.objectLiteral(t)&&L.string(t.language)&&L.string(t.value)}e.is=n})(M||={});var ye;(function(e){function t(e){let t=e;return!!t&&L.objectLiteral(t)&&(A.is(t.contents)||M.is(t.contents)||L.typedArray(t.contents,M.is))&&(e.range===void 0||m.is(e.range))}e.is=t})(ye||={});var be;(function(e){function t(e,t){return t?{label:e,documentation:t}:{label:e}}e.create=t})(be||={});var xe;(function(e){function t(e,t,...n){let r={label:e};return L.defined(t)&&(r.documentation=t),L.defined(n)?r.parameters=n:r.parameters=[],r}e.create=t})(xe||={});var N;(function(e){e.Text=1,e.Read=2,e.Write=3})(N||={});var Se;(function(e){function t(e,t){let n={range:e};return L.number(t)&&(n.kind=t),n}e.create=t})(Se||={});var P;(function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26})(P||={});var Ce;(function(e){e.Deprecated=1})(Ce||={});var we;(function(e){function t(e,t,n,r,i){let a={name:e,kind:t,location:{uri:r,range:n}};return i&&(a.containerName=i),a}e.create=t})(we||={});var Te;(function(e){function t(e,t,n,r){return r===void 0?{name:e,kind:t,location:{uri:n}}:{name:e,kind:t,location:{uri:n,range:r}}}e.create=t})(Te||={});var Ee;(function(e){function t(e,t,n,r,i,a){let o={name:e,detail:t,kind:n,range:r,selectionRange:i};return a!==void 0&&(o.children=a),o}e.create=t;function n(e){let t=e;return t&&L.string(t.name)&&L.number(t.kind)&&m.is(t.range)&&m.is(t.selectionRange)&&(t.detail===void 0||L.string(t.detail))&&(t.deprecated===void 0||L.boolean(t.deprecated))&&(t.children===void 0||Array.isArray(t.children))&&(t.tags===void 0||Array.isArray(t.tags))}e.is=n})(Ee||={});var De;(function(e){e.Empty=``,e.QuickFix=`quickfix`,e.Refactor=`refactor`,e.RefactorExtract=`refactor.extract`,e.RefactorInline=`refactor.inline`,e.RefactorRewrite=`refactor.rewrite`,e.Source=`source`,e.SourceOrganizeImports=`source.organizeImports`,e.SourceFixAll=`source.fixAll`})(De||={});var F;(function(e){e.Invoked=1,e.Automatic=2})(F||={});var Oe;(function(e){function t(e,t,n){let r={diagnostics:e};return t!=null&&(r.only=t),n!=null&&(r.triggerKind=n),r}e.create=t;function n(e){let t=e;return L.defined(t)&&L.typedArray(t.diagnostics,S.is)&&(t.only===void 0||L.typedArray(t.only,L.string))&&(t.triggerKind===void 0||t.triggerKind===F.Invoked||t.triggerKind===F.Automatic)}e.is=n})(Oe||={});var ke;(function(e){function t(e,t,n){let r={title:e},i=!0;return typeof t==`string`?(i=!1,r.kind=t):C.is(t)?r.command=t:r.edit=t,i&&n!==void 0&&(r.kind=n),r}e.create=t;function n(e){let t=e;return t&&L.string(t.title)&&(t.diagnostics===void 0||L.typedArray(t.diagnostics,S.is))&&(t.kind===void 0||L.string(t.kind))&&(t.edit!==void 0||t.command!==void 0)&&(t.command===void 0||C.is(t.command))&&(t.isPreferred===void 0||L.boolean(t.isPreferred))&&(t.edit===void 0||se.is(t.edit))}e.is=n})(ke||={});var Ae;(function(e){function t(e,t){let n={range:e};return L.defined(t)&&(n.data=t),n}e.create=t;function n(e){let t=e;return L.defined(t)&&m.is(t.range)&&(L.undefined(t.command)||C.is(t.command))}e.is=n})(Ae||={});var je;(function(e){function t(e,t){return{tabSize:e,insertSpaces:t}}e.create=t;function n(e){let t=e;return L.defined(t)&&L.uinteger(t.tabSize)&&L.boolean(t.insertSpaces)}e.is=n})(je||={});var Me;(function(e){function t(e,t,n){return{range:e,target:t,data:n}}e.create=t;function n(e){let t=e;return L.defined(t)&&m.is(t.range)&&(L.undefined(t.target)||L.string(t.target))}e.is=n})(Me||={});var Ne;(function(e){function t(e,t){return{range:e,parent:t}}e.create=t;function n(t){let n=t;return L.objectLiteral(n)&&m.is(n.range)&&(n.parent===void 0||e.is(n.parent))}e.is=n})(Ne||={});var Pe;(function(e){e.namespace=`namespace`,e.type=`type`,e.class=`class`,e.enum=`enum`,e.interface=`interface`,e.struct=`struct`,e.typeParameter=`typeParameter`,e.parameter=`parameter`,e.variable=`variable`,e.property=`property`,e.enumMember=`enumMember`,e.event=`event`,e.function=`function`,e.method=`method`,e.macro=`macro`,e.keyword=`keyword`,e.modifier=`modifier`,e.comment=`comment`,e.string=`string`,e.number=`number`,e.regexp=`regexp`,e.operator=`operator`,e.decorator=`decorator`})(Pe||={});var Fe;(function(e){e.declaration=`declaration`,e.definition=`definition`,e.readonly=`readonly`,e.static=`static`,e.deprecated=`deprecated`,e.abstract=`abstract`,e.async=`async`,e.modification=`modification`,e.documentation=`documentation`,e.defaultLibrary=`defaultLibrary`})(Fe||={});var Ie;(function(e){function t(e){let t=e;return L.objectLiteral(t)&&(t.resultId===void 0||typeof t.resultId==`string`)&&Array.isArray(t.data)&&(t.data.length===0||typeof t.data[0]==`number`)}e.is=t})(Ie||={});var Le;(function(e){function t(e,t){return{range:e,text:t}}e.create=t;function n(e){let t=e;return t!=null&&m.is(t.range)&&L.string(t.text)}e.is=n})(Le||={});var Re;(function(e){function t(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}}e.create=t;function n(e){let t=e;return t!=null&&m.is(t.range)&&L.boolean(t.caseSensitiveLookup)&&(L.string(t.variableName)||t.variableName===void 0)}e.is=n})(Re||={});var ze;(function(e){function t(e,t){return{range:e,expression:t}}e.create=t;function n(e){let t=e;return t!=null&&m.is(t.range)&&(L.string(t.expression)||t.expression===void 0)}e.is=n})(ze||={});var Be;(function(e){function t(e,t){return{frameId:e,stoppedLocation:t}}e.create=t;function n(e){let t=e;return L.defined(t)&&m.is(e.stoppedLocation)}e.is=n})(Be||={});var Ve;(function(e){e.Type=1,e.Parameter=2;function t(e){return e===1||e===2}e.is=t})(Ve||={});var I;(function(e){function t(e){return{value:e}}e.create=t;function n(e){let t=e;return L.objectLiteral(t)&&(t.tooltip===void 0||L.string(t.tooltip)||A.is(t.tooltip))&&(t.location===void 0||h.is(t.location))&&(t.command===void 0||C.is(t.command))}e.is=n})(I||={});var He;(function(e){function t(e,t,n){let r={position:e,label:t};return n!==void 0&&(r.kind=n),r}e.create=t;function n(e){let t=e;return L.objectLiteral(t)&&p.is(t.position)&&(L.string(t.label)||L.typedArray(t.label,I.is))&&(t.kind===void 0||Ve.is(t.kind))&&t.textEdits===void 0||L.typedArray(t.textEdits,w.is)&&(t.tooltip===void 0||L.string(t.tooltip)||A.is(t.tooltip))&&(t.paddingLeft===void 0||L.boolean(t.paddingLeft))&&(t.paddingRight===void 0||L.boolean(t.paddingRight))}e.is=n})(He||={});var Ue;(function(e){function t(e){return{kind:`snippet`,value:e}}e.createSnippet=t})(Ue||={});var We;(function(e){function t(e,t,n,r){return{insertText:e,filterText:t,range:n,command:r}}e.create=t})(We||={});var Ge;(function(e){function t(e){return{items:e}}e.create=t})(Ge||={});var Ke;(function(e){e.Invoked=0,e.Automatic=1})(Ke||={});var qe;(function(e){function t(e,t){return{range:e,text:t}}e.create=t})(qe||={});var Je;(function(e){function t(e,t){return{triggerKind:e,selectedCompletionInfo:t}}e.create=t})(Je||={});var Ye;(function(e){function t(e){let t=e;return L.objectLiteral(t)&&u.is(t.uri)&&L.string(t.name)}e.is=t})(Ye||={});var Xe;(function(e){function t(e,t,n,r){return new Ze(e,t,n,r)}e.create=t;function n(e){let t=e;return!!(L.defined(t)&&L.string(t.uri)&&(L.undefined(t.languageId)||L.string(t.languageId))&&L.uinteger(t.lineCount)&&L.func(t.getText)&&L.func(t.positionAt)&&L.func(t.offsetAt))}e.is=n;function r(e,t){let n=e.getText(),r=i(t,(e,t)=>{let n=e.range.start.line-t.range.start.line;return n===0?e.range.start.character-t.range.start.character:n}),a=n.length;for(let t=r.length-1;t>=0;t--){let i=r[t],o=e.offsetAt(i.range.start),s=e.offsetAt(i.range.end);if(s<=a)n=n.substring(0,o)+i.newText+n.substring(s,n.length);else throw Error(`Overlapping edit`);a=o}return n}e.applyEdits=r;function i(e,t){if(e.length<=1)return e;let n=e.length/2|0,r=e.slice(0,n),a=e.slice(n);i(r,t),i(a,t);let o=0,s=0,c=0;for(;o<r.length&&s<a.length;)t(r[o],a[s])<=0?e[c++]=r[o++]:e[c++]=a[s++];for(;o<r.length;)e[c++]=r[o++];for(;s<a.length;)e[c++]=a[s++];return e}})(Xe||={});var Ze=class{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],t=this._content,n=!0;for(let r=0;r<t.length;r++){n&&=(e.push(r),!1);let i=t.charAt(r);n=i===`\r`||i===`
`,i===`\r`&&r+1<t.length&&t.charAt(r+1)===`
`&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(r===0)return p.create(0,e);for(;n<r;){let i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}let i=n-1;return p.create(i,e-t[i])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}},L;(function(e){let t=Object.prototype.toString;function n(e){return e!==void 0}e.defined=n;function r(e){return e===void 0}e.undefined=r;function i(e){return e===!0||e===!1}e.boolean=i;function a(e){return t.call(e)===`[object String]`}e.string=a;function o(e){return t.call(e)===`[object Number]`}e.number=o;function s(e,n,r){return t.call(e)===`[object Number]`&&n<=e&&e<=r}e.numberRange=s;function c(e){return t.call(e)===`[object Number]`&&-2147483648<=e&&e<=2147483647}e.integer=c;function l(e){return t.call(e)===`[object Number]`&&0<=e&&e<=2147483647}e.uinteger=l;function u(e){return t.call(e)===`[object Function]`}e.func=u;function d(e){return typeof e==`object`&&!!e}e.objectLiteral=d;function f(e,t){return Array.isArray(e)&&e.every(t)}e.typedArray=f})(L||={});var R=class{constructor(e,t,n){this._languageId=e,this._worker=t,this._disposables=[],this._listener=Object.create(null);let r=e=>{let t=e.getLanguageId();if(t!==this._languageId)return;let n;this._listener[e.uri.toString()]=e.onDidChangeContent(()=>{window.clearTimeout(n),n=window.setTimeout(()=>this._doValidate(e.uri,t),500)}),this._doValidate(e.uri,t)},i=e=>{s.editor.setModelMarkers(e,this._languageId,[]);let t=e.uri.toString(),n=this._listener[t];n&&(n.dispose(),delete this._listener[t])};this._disposables.push(s.editor.onDidCreateModel(r)),this._disposables.push(s.editor.onWillDisposeModel(i)),this._disposables.push(s.editor.onDidChangeModelLanguage(e=>{i(e.model),r(e.model)})),this._disposables.push(n(e=>{s.editor.getModels().forEach(e=>{e.getLanguageId()===this._languageId&&(i(e),r(e))})})),this._disposables.push({dispose:()=>{for(let e in s.editor.getModels().forEach(i),this._listener)this._listener[e].dispose()}}),s.editor.getModels().forEach(r)}dispose(){this._disposables.forEach(e=>e&&e.dispose()),this._disposables.length=0}_doValidate(e,t){this._worker(e).then(t=>t.doValidation(e.toString())).then(n=>{let r=n.map(t=>$e(e,t)),i=s.editor.getModel(e);i&&i.getLanguageId()===t&&s.editor.setModelMarkers(i,t,r)}).then(void 0,e=>{console.error(e)})}};function Qe(e){switch(e){case x.Error:return s.MarkerSeverity.Error;case x.Warning:return s.MarkerSeverity.Warning;case x.Information:return s.MarkerSeverity.Info;case x.Hint:return s.MarkerSeverity.Hint;default:return s.MarkerSeverity.Info}}function $e(e,t){let n=typeof t.code==`number`?String(t.code):t.code;return{severity:Qe(t.severity),startLineNumber:t.range.start.line+1,startColumn:t.range.start.character+1,endLineNumber:t.range.end.line+1,endColumn:t.range.end.character+1,message:t.message,code:n,source:t.source}}var z=class{constructor(e,t){this._worker=e,this._triggerCharacters=t}get triggerCharacters(){return this._triggerCharacters}provideCompletionItems(e,t,n,r){let i=e.uri;return this._worker(i).then(e=>e.doComplete(i.toString(),B(t))).then(n=>{if(!n)return;let r=e.getWordUntilPosition(t),i=new s.Range(t.lineNumber,r.startColumn,t.lineNumber,r.endColumn),a=n.items.map(e=>{let t={label:e.label,insertText:e.insertText||e.label,sortText:e.sortText,filterText:e.filterText,documentation:e.documentation,detail:e.detail,command:nt(e.command),range:i,kind:tt(e.kind)};return e.textEdit&&(et(e.textEdit)?t.range={insert:H(e.textEdit.insert),replace:H(e.textEdit.replace)}:t.range=H(e.textEdit.range),t.insertText=e.textEdit.newText),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(U)),e.insertTextFormat===fe.Snippet&&(t.insertTextRules=s.languages.CompletionItemInsertTextRule.InsertAsSnippet),t});return{isIncomplete:n.isIncomplete,suggestions:a}})}};function B(e){if(e)return{character:e.column-1,line:e.lineNumber-1}}function V(e){if(e)return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function H(e){if(e)return new s.Range(e.start.line+1,e.start.character+1,e.end.line+1,e.end.character+1)}function et(e){return e.insert!==void 0&&e.replace!==void 0}function tt(e){let t=s.languages.CompletionItemKind;switch(e){case j.Text:return t.Text;case j.Method:return t.Method;case j.Function:return t.Function;case j.Constructor:return t.Constructor;case j.Field:return t.Field;case j.Variable:return t.Variable;case j.Class:return t.Class;case j.Interface:return t.Interface;case j.Module:return t.Module;case j.Property:return t.Property;case j.Unit:return t.Unit;case j.Value:return t.Value;case j.Enum:return t.Enum;case j.Keyword:return t.Keyword;case j.Snippet:return t.Snippet;case j.Color:return t.Color;case j.File:return t.File;case j.Reference:return t.Reference}return t.Property}function U(e){if(e)return{range:H(e.range),text:e.newText}}function nt(e){return e&&e.command===`editor.action.triggerSuggest`?{id:e.command,title:e.title,arguments:e.arguments}:void 0}var W=class{constructor(e){this._worker=e}provideHover(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.doHover(r.toString(),B(t))).then(e=>{if(e)return{range:H(e.range),contents:it(e.contents)}})}};function rt(e){return e&&typeof e==`object`&&typeof e.kind==`string`}function G(e){return typeof e==`string`?{value:e}:rt(e)?e.kind===`plaintext`?{value:e.value.replace(/[\\`*_{}[\]()#+\-.!]/g,`\\$&`)}:{value:e.value}:{value:"```"+e.language+`
`+e.value+"\n```\n"}}function it(e){if(e)return Array.isArray(e)?e.map(G):[G(e)]}var at=class{constructor(e){this._worker=e}provideDocumentHighlights(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.findDocumentHighlights(r.toString(),B(t))).then(e=>{if(e)return e.map(e=>({range:H(e.range),kind:ot(e.kind)}))})}};function ot(e){switch(e){case N.Read:return s.languages.DocumentHighlightKind.Read;case N.Write:return s.languages.DocumentHighlightKind.Write;case N.Text:return s.languages.DocumentHighlightKind.Text}return s.languages.DocumentHighlightKind.Text}var st=class{constructor(e){this._worker=e}provideDefinition(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.findDefinition(r.toString(),B(t))).then(e=>{if(e)return[K(e)]})}};function K(e){return{uri:s.Uri.parse(e.uri),range:H(e.range)}}var ct=class{constructor(e){this._worker=e}provideReferences(e,t,n,r){let i=e.uri;return this._worker(i).then(e=>e.findReferences(i.toString(),B(t))).then(e=>{if(e)return e.map(K)})}},lt=class{constructor(e){this._worker=e}provideRenameEdits(e,t,n,r){let i=e.uri;return this._worker(i).then(e=>e.doRename(i.toString(),B(t),n)).then(e=>ut(e))}};function ut(e){if(!e||!e.changes)return;let t=[];for(let n in e.changes){let r=s.Uri.parse(n);for(let i of e.changes[n])t.push({resource:r,versionId:void 0,textEdit:{range:H(i.range),text:i.newText}})}return{edits:t}}var q=class{constructor(e){this._worker=e}provideDocumentSymbols(e,t){let n=e.uri;return this._worker(n).then(e=>e.findDocumentSymbols(n.toString())).then(e=>{if(e)return e.map(e=>dt(e)?J(e):{name:e.name,detail:``,containerName:e.containerName,kind:ft(e.kind),range:H(e.location.range),selectionRange:H(e.location.range),tags:[]})})}};function dt(e){return`children`in e}function J(e){return{name:e.name,detail:e.detail??``,kind:ft(e.kind),range:H(e.range),selectionRange:H(e.selectionRange),tags:e.tags??[],children:(e.children??[]).map(e=>J(e))}}function ft(e){let t=s.languages.SymbolKind;switch(e){case P.File:return t.File;case P.Module:return t.Module;case P.Namespace:return t.Namespace;case P.Package:return t.Package;case P.Class:return t.Class;case P.Method:return t.Method;case P.Property:return t.Property;case P.Field:return t.Field;case P.Constructor:return t.Constructor;case P.Enum:return t.Enum;case P.Interface:return t.Interface;case P.Function:return t.Function;case P.Variable:return t.Variable;case P.Constant:return t.Constant;case P.String:return t.String;case P.Number:return t.Number;case P.Boolean:return t.Boolean;case P.Array:return t.Array}return t.Function}var pt=class{constructor(e){this._worker=e}provideLinks(e,t){let n=e.uri;return this._worker(n).then(e=>e.findDocumentLinks(n.toString())).then(e=>{if(e)return{links:e.map(e=>({range:H(e.range),url:e.target}))}})}},mt=class{constructor(e){this._worker=e}provideDocumentFormattingEdits(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.format(r.toString(),null,gt(t)).then(e=>{if(!(!e||e.length===0))return e.map(U)}))}},ht=class{constructor(e){this._worker=e,this.canFormatMultipleRanges=!1}provideDocumentRangeFormattingEdits(e,t,n,r){let i=e.uri;return this._worker(i).then(e=>e.format(i.toString(),V(t),gt(n)).then(e=>{if(!(!e||e.length===0))return e.map(U)}))}};function gt(e){return{tabSize:e.tabSize,insertSpaces:e.insertSpaces}}var _t=class{constructor(e){this._worker=e}provideDocumentColors(e,t){let n=e.uri;return this._worker(n).then(e=>e.findDocumentColors(n.toString())).then(e=>{if(e)return e.map(e=>({color:e.color,range:H(e.range)}))})}provideColorPresentations(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.getColorPresentations(r.toString(),t.color,V(t.range))).then(e=>{if(e)return e.map(e=>{let t={label:e.label};return e.textEdit&&(t.textEdit=U(e.textEdit)),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(U)),t})})}},vt=class{constructor(e){this._worker=e}provideFoldingRanges(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.getFoldingRanges(r.toString(),t)).then(e=>{if(e)return e.map(e=>{let t={start:e.startLine+1,end:e.endLine+1};return e.kind!==void 0&&(t.kind=yt(e.kind)),t})})}};function yt(e){switch(e){case y.Comment:return s.languages.FoldingRangeKind.Comment;case y.Imports:return s.languages.FoldingRangeKind.Imports;case y.Region:return s.languages.FoldingRangeKind.Region}}var bt=class{constructor(e){this._worker=e}provideSelectionRanges(e,t,n){let r=e.uri;return this._worker(r).then(e=>e.getSelectionRanges(r.toString(),t.map(B))).then(e=>{if(e)return e.map(e=>{let t=[];for(;e;)t.push({range:H(e.range)}),e=e.parent;return t})})}};function xt(e,t=!1){let n=e.length,r=0,i=``,a=0,o=16,s=0,c=0,l=0,u=0,d=0;function f(t,n){let i=0,a=0;for(;i<t||!n;){let t=e.charCodeAt(r);if(t>=48&&t<=57)a=a*16+t-48;else if(t>=65&&t<=70)a=a*16+t-65+10;else if(t>=97&&t<=102)a=a*16+t-97+10;else break;r++,i++}return i<t&&(a=-1),a}function p(e){r=e,i=``,a=0,o=16,d=0}function m(){let t=r;if(e.charCodeAt(r)===48)r++;else for(r++;r<e.length&&Z(e.charCodeAt(r));)r++;if(r<e.length&&e.charCodeAt(r)===46)if(r++,r<e.length&&Z(e.charCodeAt(r)))for(r++;r<e.length&&Z(e.charCodeAt(r));)r++;else return d=3,e.substring(t,r);let n=r;if(r<e.length&&(e.charCodeAt(r)===69||e.charCodeAt(r)===101))if(r++,(r<e.length&&e.charCodeAt(r)===43||e.charCodeAt(r)===45)&&r++,r<e.length&&Z(e.charCodeAt(r))){for(r++;r<e.length&&Z(e.charCodeAt(r));)r++;n=r}else d=3;return e.substring(t,n)}function h(){let t=``,i=r;for(;;){if(r>=n){t+=e.substring(i,r),d=2;break}let a=e.charCodeAt(r);if(a===34){t+=e.substring(i,r),r++;break}if(a===92){if(t+=e.substring(i,r),r++,r>=n){d=2;break}switch(e.charCodeAt(r++)){case 34:t+=`"`;break;case 92:t+=`\\`;break;case 47:t+=`/`;break;case 98:t+=`\b`;break;case 102:t+=`\f`;break;case 110:t+=`
`;break;case 114:t+=`\r`;break;case 116:t+=`	`;break;case 117:let e=f(4,!0);e>=0?t+=String.fromCharCode(e):d=4;break;default:d=5}i=r;continue}if(a>=0&&a<=31)if(X(a)){t+=e.substring(i,r),d=2;break}else d=6;r++}return t}function g(){if(i=``,d=0,a=r,c=s,u=l,r>=n)return a=n,o=17;let t=e.charCodeAt(r);if(Y(t)){do r++,i+=String.fromCharCode(t),t=e.charCodeAt(r);while(Y(t));return o=15}if(X(t))return r++,i+=String.fromCharCode(t),t===13&&e.charCodeAt(r)===10&&(r++,i+=`
`),s++,l=r,o=14;switch(t){case 123:return r++,o=1;case 125:return r++,o=2;case 91:return r++,o=3;case 93:return r++,o=4;case 58:return r++,o=6;case 44:return r++,o=5;case 34:return r++,i=h(),o=10;case 47:let c=r-1;if(e.charCodeAt(r+1)===47){for(r+=2;r<n&&!X(e.charCodeAt(r));)r++;return i=e.substring(c,r),o=12}if(e.charCodeAt(r+1)===42){r+=2;let t=n-1,a=!1;for(;r<t;){let t=e.charCodeAt(r);if(t===42&&e.charCodeAt(r+1)===47){r+=2,a=!0;break}r++,X(t)&&(t===13&&e.charCodeAt(r)===10&&r++,s++,l=r)}return a||(r++,d=1),i=e.substring(c,r),o=13}return i+=String.fromCharCode(t),r++,o=16;case 45:if(i+=String.fromCharCode(t),r++,r===n||!Z(e.charCodeAt(r)))return o=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return i+=m(),o=11;default:for(;r<n&&_(t);)r++,t=e.charCodeAt(r);if(a!==r){switch(i=e.substring(a,r),i){case`true`:return o=8;case`false`:return o=9;case`null`:return o=7}return o=16}return i+=String.fromCharCode(t),r++,o=16}}function _(e){if(Y(e)||X(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}function v(){let e;do e=g();while(e>=12&&e<=15);return e}return{setPosition:p,getPosition:()=>r,scan:t?v:g,getToken:()=>o,getTokenValue:()=>i,getTokenOffset:()=>a,getTokenLength:()=>r-a,getTokenStartLine:()=>c,getTokenStartCharacter:()=>a-u,getTokenError:()=>d}}function Y(e){return e===32||e===9}function X(e){return e===10||e===13}function Z(e){return e>=48&&e<=57}var St;(function(e){e[e.lineFeed=10]=`lineFeed`,e[e.carriageReturn=13]=`carriageReturn`,e[e.space=32]=`space`,e[e._0=48]=`_0`,e[e._1=49]=`_1`,e[e._2=50]=`_2`,e[e._3=51]=`_3`,e[e._4=52]=`_4`,e[e._5=53]=`_5`,e[e._6=54]=`_6`,e[e._7=55]=`_7`,e[e._8=56]=`_8`,e[e._9=57]=`_9`,e[e.a=97]=`a`,e[e.b=98]=`b`,e[e.c=99]=`c`,e[e.d=100]=`d`,e[e.e=101]=`e`,e[e.f=102]=`f`,e[e.g=103]=`g`,e[e.h=104]=`h`,e[e.i=105]=`i`,e[e.j=106]=`j`,e[e.k=107]=`k`,e[e.l=108]=`l`,e[e.m=109]=`m`,e[e.n=110]=`n`,e[e.o=111]=`o`,e[e.p=112]=`p`,e[e.q=113]=`q`,e[e.r=114]=`r`,e[e.s=115]=`s`,e[e.t=116]=`t`,e[e.u=117]=`u`,e[e.v=118]=`v`,e[e.w=119]=`w`,e[e.x=120]=`x`,e[e.y=121]=`y`,e[e.z=122]=`z`,e[e.A=65]=`A`,e[e.B=66]=`B`,e[e.C=67]=`C`,e[e.D=68]=`D`,e[e.E=69]=`E`,e[e.F=70]=`F`,e[e.G=71]=`G`,e[e.H=72]=`H`,e[e.I=73]=`I`,e[e.J=74]=`J`,e[e.K=75]=`K`,e[e.L=76]=`L`,e[e.M=77]=`M`,e[e.N=78]=`N`,e[e.O=79]=`O`,e[e.P=80]=`P`,e[e.Q=81]=`Q`,e[e.R=82]=`R`,e[e.S=83]=`S`,e[e.T=84]=`T`,e[e.U=85]=`U`,e[e.V=86]=`V`,e[e.W=87]=`W`,e[e.X=88]=`X`,e[e.Y=89]=`Y`,e[e.Z=90]=`Z`,e[e.asterisk=42]=`asterisk`,e[e.backslash=92]=`backslash`,e[e.closeBrace=125]=`closeBrace`,e[e.closeBracket=93]=`closeBracket`,e[e.colon=58]=`colon`,e[e.comma=44]=`comma`,e[e.dot=46]=`dot`,e[e.doubleQuote=34]=`doubleQuote`,e[e.minus=45]=`minus`,e[e.openBrace=123]=`openBrace`,e[e.openBracket=91]=`openBracket`,e[e.plus=43]=`plus`,e[e.slash=47]=`slash`,e[e.formFeed=12]=`formFeed`,e[e.tab=9]=`tab`})(St||={}),Array(20).fill(0).map((e,t)=>` `.repeat(t)),Array(200).fill(0).map((e,t)=>`
`+` `.repeat(t)),Array(200).fill(0).map((e,t)=>`\r`+` `.repeat(t)),Array(200).fill(0).map((e,t)=>`\r
`+` `.repeat(t)),Array(200).fill(0).map((e,t)=>`
`+`	`.repeat(t)),Array(200).fill(0).map((e,t)=>`\r`+`	`.repeat(t)),Array(200).fill(0).map((e,t)=>`\r
`+`	`.repeat(t));var Ct;(function(e){e.DEFAULT={allowTrailingComma:!1}})(Ct||={});var wt=xt,Tt;(function(e){e[e.None=0]=`None`,e[e.UnexpectedEndOfComment=1]=`UnexpectedEndOfComment`,e[e.UnexpectedEndOfString=2]=`UnexpectedEndOfString`,e[e.UnexpectedEndOfNumber=3]=`UnexpectedEndOfNumber`,e[e.InvalidUnicode=4]=`InvalidUnicode`,e[e.InvalidEscapeCharacter=5]=`InvalidEscapeCharacter`,e[e.InvalidCharacter=6]=`InvalidCharacter`})(Tt||={});var Et;(function(e){e[e.OpenBraceToken=1]=`OpenBraceToken`,e[e.CloseBraceToken=2]=`CloseBraceToken`,e[e.OpenBracketToken=3]=`OpenBracketToken`,e[e.CloseBracketToken=4]=`CloseBracketToken`,e[e.CommaToken=5]=`CommaToken`,e[e.ColonToken=6]=`ColonToken`,e[e.NullKeyword=7]=`NullKeyword`,e[e.TrueKeyword=8]=`TrueKeyword`,e[e.FalseKeyword=9]=`FalseKeyword`,e[e.StringLiteral=10]=`StringLiteral`,e[e.NumericLiteral=11]=`NumericLiteral`,e[e.LineCommentTrivia=12]=`LineCommentTrivia`,e[e.BlockCommentTrivia=13]=`BlockCommentTrivia`,e[e.LineBreakTrivia=14]=`LineBreakTrivia`,e[e.Trivia=15]=`Trivia`,e[e.Unknown=16]=`Unknown`,e[e.EOF=17]=`EOF`})(Et||={});var Dt;(function(e){e[e.InvalidSymbol=1]=`InvalidSymbol`,e[e.InvalidNumberFormat=2]=`InvalidNumberFormat`,e[e.PropertyNameExpected=3]=`PropertyNameExpected`,e[e.ValueExpected=4]=`ValueExpected`,e[e.ColonExpected=5]=`ColonExpected`,e[e.CommaExpected=6]=`CommaExpected`,e[e.CloseBraceExpected=7]=`CloseBraceExpected`,e[e.CloseBracketExpected=8]=`CloseBracketExpected`,e[e.EndOfFileExpected=9]=`EndOfFileExpected`,e[e.InvalidCommentToken=10]=`InvalidCommentToken`,e[e.UnexpectedEndOfComment=11]=`UnexpectedEndOfComment`,e[e.UnexpectedEndOfString=12]=`UnexpectedEndOfString`,e[e.UnexpectedEndOfNumber=13]=`UnexpectedEndOfNumber`,e[e.InvalidUnicode=14]=`InvalidUnicode`,e[e.InvalidEscapeCharacter=15]=`InvalidEscapeCharacter`,e[e.InvalidCharacter=16]=`InvalidCharacter`})(Dt||={});function Ot(e){return{getInitialState:()=>new Bt(null,null,!1,null),tokenize:(t,n)=>Vt(e,t,n)}}var kt=`delimiter.bracket.json`,At=`delimiter.array.json`,jt=`delimiter.colon.json`,Mt=`delimiter.comma.json`,Nt=`keyword.json`,Pt=`keyword.json`,Ft=`string.value.json`,It=`number.json`,Lt=`string.key.json`,Rt=`comment.block.json`,zt=`comment.line.json`,Q=class e{constructor(e,t){this.parent=e,this.type=t}static pop(e){return e?e.parent:null}static push(t,n){return new e(t,n)}static equals(e,t){if(!e&&!t)return!0;if(!e||!t)return!1;for(;e&&t;){if(e===t)return!0;if(e.type!==t.type)return!1;e=e.parent,t=t.parent}return!0}},Bt=class e{constructor(e,t,n,r){this._state=e,this.scanError=t,this.lastWasColon=n,this.parents=r}clone(){return new e(this._state,this.scanError,this.lastWasColon,this.parents)}equals(t){return t===this?!0:!t||!(t instanceof e)?!1:this.scanError===t.scanError&&this.lastWasColon===t.lastWasColon&&Q.equals(this.parents,t.parents)}getStateData(){return this._state}setStateData(e){this._state=e}};function Vt(e,t,n,r=0){let i=0,a=!1;switch(n.scanError){case 2:t=`"`+t,i=1;break;case 1:t=`/*`+t,i=2;break}let o=wt(t),s=n.lastWasColon,c=n.parents,l={tokens:[],endState:n.clone()};for(;;){let u=r+o.getPosition(),d=``,f=o.scan();if(f===17)break;if(u===r+o.getPosition())throw Error(`Scanner did not advance, next 3 characters are: `+t.substr(o.getPosition(),3));switch(a&&(u-=i),a=i>0,f){case 1:c=Q.push(c,0),d=kt,s=!1;break;case 2:c=Q.pop(c),d=kt,s=!1;break;case 3:c=Q.push(c,1),d=At,s=!1;break;case 4:c=Q.pop(c),d=At,s=!1;break;case 6:d=jt,s=!0;break;case 5:d=Mt,s=!1;break;case 8:case 9:d=Nt,s=!1;break;case 7:d=Pt,s=!1;break;case 10:let e=(c?c.type:0)===1;d=s||e?Ft:Lt,s=!1;break;case 11:d=It,s=!1;break}if(e)switch(f){case 12:d=zt;break;case 13:d=Rt;break}l.endState=new Bt(n.getStateData(),o.getTokenError(),s,c),l.tokens.push({startIndex:u,scopes:d})}return l}var $;function Ht(){return new Promise((e,t)=>{if(!$)return t(`JSON not registered!`);e($)})}var Ut=class extends R{constructor(e,t,n){super(e,t,n.onDidChange),this._disposables.push(s.editor.onWillDisposeModel(e=>{this._resetSchema(e.uri)})),this._disposables.push(s.editor.onDidChangeModelLanguage(e=>{this._resetSchema(e.model.uri)}))}_resetSchema(e){this._worker().then(t=>{t.resetSchema(e.toString())})}};function Wt(e){let t=[],n=[],r=new c(e);t.push(r),$=(...e)=>r.getLanguageServiceWorker(...e);function i(){let{languageId:t,modeConfiguration:r}=e;Kt(n),r.documentFormattingEdits&&n.push(s.languages.registerDocumentFormattingEditProvider(t,new mt($))),r.documentRangeFormattingEdits&&n.push(s.languages.registerDocumentRangeFormattingEditProvider(t,new ht($))),r.completionItems&&n.push(s.languages.registerCompletionItemProvider(t,new z($,[` `,`:`,`"`]))),r.hovers&&n.push(s.languages.registerHoverProvider(t,new W($))),r.documentSymbols&&n.push(s.languages.registerDocumentSymbolProvider(t,new q($))),r.tokens&&n.push(s.languages.setTokensProvider(t,Ot(!0))),r.colors&&n.push(s.languages.registerColorProvider(t,new _t($))),r.foldingRanges&&n.push(s.languages.registerFoldingRangeProvider(t,new vt($))),r.diagnostics&&n.push(new Ut(t,$,e)),r.selectionRanges&&n.push(s.languages.registerSelectionRangeProvider(t,new bt($)))}i(),t.push(s.languages.setLanguageConfiguration(e.languageId,qt));let a=e.modeConfiguration;return e.onDidChange(e=>{e.modeConfiguration!==a&&(a=e.modeConfiguration,i())}),t.push(Gt(n)),Gt(t)}function Gt(e){return{dispose:()=>Kt(e)}}function Kt(e){for(;e.length;)e.pop().dispose()}var qt={wordPattern:/(-?\d*\.\d\w*)|([^\[\{\]\}\:\"\,\s]+)/g,comments:{lineComment:`//`,blockComment:[`/*`,`*/`]},brackets:[[`{`,`}`],[`[`,`]`]],autoClosingPairs:[{open:`{`,close:`}`,notIn:[`string`]},{open:`[`,close:`]`,notIn:[`string`]},{open:`"`,close:`"`,notIn:[`string`]}]};export{z as CompletionAdapter,st as DefinitionAdapter,R as DiagnosticsAdapter,_t as DocumentColorAdapter,mt as DocumentFormattingEditProvider,at as DocumentHighlightAdapter,pt as DocumentLinkAdapter,ht as DocumentRangeFormattingEditProvider,q as DocumentSymbolAdapter,vt as FoldingRangeAdapter,W as HoverAdapter,ct as ReferenceAdapter,lt as RenameAdapter,bt as SelectionRangeAdapter,c as WorkerManager,B as fromPosition,V as fromRange,Ht as getWorker,Wt as setupMode,H as toRange,U as toTextEdit};
//# sourceMappingURL=jsonMode-Cmpe8BKI.js.map