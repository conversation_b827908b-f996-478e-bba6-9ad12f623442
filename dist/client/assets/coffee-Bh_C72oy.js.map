{"version": 3, "file": "coffee-Bh_C72oy.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/coffee/coffee.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/coffee/coffee.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#%\\^\\&\\*\\(\\)\\=\\$\\-\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    blockComment: [\"###\", \"###\"],\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: true,\n  tokenPostfix: \".coffee\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  regEx: /\\/(?!\\/\\/)(?:[^\\/\\\\]|\\\\.)*\\/[igm]*/,\n  keywords: [\n    \"and\",\n    \"or\",\n    \"is\",\n    \"isnt\",\n    \"not\",\n    \"on\",\n    \"yes\",\n    \"@\",\n    \"no\",\n    \"off\",\n    \"true\",\n    \"false\",\n    \"null\",\n    \"this\",\n    \"new\",\n    \"delete\",\n    \"typeof\",\n    \"in\",\n    \"instanceof\",\n    \"return\",\n    \"throw\",\n    \"break\",\n    \"continue\",\n    \"debugger\",\n    \"if\",\n    \"else\",\n    \"switch\",\n    \"for\",\n    \"while\",\n    \"do\",\n    \"try\",\n    \"catch\",\n    \"finally\",\n    \"class\",\n    \"extends\",\n    \"super\",\n    \"undefined\",\n    \"then\",\n    \"unless\",\n    \"until\",\n    \"loop\",\n    \"of\",\n    \"by\",\n    \"when\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?&%|+\\-*\\/\\^\\.,\\:]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"'$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [/\\@[a-zA-Z_]\\w*/, \"variable.predefined\"],\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            this: \"variable.predefined\",\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // whitespace\n      [/[ \\t\\r\\n]+/, \"\"],\n      // Comments\n      [/###/, \"comment\", \"@comment\"],\n      [/#.*$/, \"comment\"],\n      // regular expressions\n      [\"///\", { token: \"regexp\", next: \"@hereregexp\" }],\n      [/^(\\s*)(@regEx)/, [\"\", \"regexp\"]],\n      [/(\\()(\\s*)(@regEx)/, [\"@brackets\", \"\", \"regexp\"]],\n      [/(\\,)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\=)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\:)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\[)(\\s*)(@regEx)/, [\"@brackets\", \"\", \"regexp\"]],\n      [/(\\!)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\&)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\|)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\?)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\{)(\\s*)(@regEx)/, [\"@brackets\", \"\", \"regexp\"]],\n      [/(\\;)(\\s*)(@regEx)/, [\"\", \"\", \"regexp\"]],\n      // delimiters\n      [\n        /}/,\n        {\n          cases: {\n            \"$S2==interpolatedstring\": {\n              token: \"string\",\n              next: \"@pop\"\n            },\n            \"@default\": \"@brackets\"\n          }\n        }\n      ],\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d+[eE]([\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d+\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/0[0-7]+(?!\\d)/, \"number.octal\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[,.]/, \"delimiter\"],\n      // strings:\n      [/\"\"\"/, \"string\", '@herestring.\"\"\"'],\n      [/'''/, \"string\", \"@herestring.'''\"],\n      [\n        /\"/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: '@string.\"' }\n          }\n        }\n      ],\n      [\n        /'/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: \"@string.'\" }\n          }\n        }\n      ]\n    ],\n    string: [\n      [/[^\"'\\#\\\\]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\./, \"string.escape.invalid\"],\n      [/\\./, \"string.escape.invalid\"],\n      [\n        /#{/,\n        {\n          cases: {\n            '$S2==\"': {\n              token: \"string\",\n              next: \"root.interpolatedstring\"\n            },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/#/, \"string\"]\n    ],\n    herestring: [\n      [\n        /(\"\"\"|''')/,\n        {\n          cases: {\n            \"$1==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/[^#\\\\'\"]+/, \"string\"],\n      [/['\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\./, \"string.escape.invalid\"],\n      [/#{/, { token: \"string.quote\", next: \"root.interpolatedstring\" }],\n      [/#/, \"string\"]\n    ],\n    comment: [\n      [/[^#]+/, \"comment\"],\n      [/###/, \"comment\", \"@pop\"],\n      [/#/, \"comment\"]\n    ],\n    hereregexp: [\n      [/[^\\\\\\/#]+/, \"regexp\"],\n      [/\\\\./, \"regexp\"],\n      [/#.*$/, \"comment\"],\n      [\"///[igm]*\", { token: \"regexp\", next: \"@pop\" }],\n      [/\\//, \"regexp\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,YAAa,wFACb,SAAU,CACR,aAAc,CAAC,MAAO,MAAM,CAC5B,YAAa,IACd,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,kBAAkB,CACpC,IAAS,OAAO,qBAAqB,CACtC,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,WAAY,GACZ,aAAc,UACd,SAAU,CACR,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,kBAAmB,CACnD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,mBAAoB,CACpD,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,wBAAyB,CAC1D,CACD,MAAO,qCACP,SAAU,uPA6CT,CAED,QAAS,4BACT,QAAS,yEAET,UAAW,CACT,KAAM,CAEJ,CAAC,iBAAkB,sBAAsB,CACzC,CACE,eACA,CACE,MAAO,CACL,KAAM,sBACN,YAAa,CAAE,MAAO,aAAc,CACpC,WAAY,GACb,CACF,CACF,CAED,CAAC,aAAc,GAAG,CAElB,CAAC,MAAO,UAAW,WAAW,CAC9B,CAAC,OAAQ,UAAU,CAEnB,CAAC,MAAO,CAAE,MAAO,SAAU,KAAM,cAAe,CAAC,CACjD,CAAC,iBAAkB,CAAC,GAAI,SAAS,CAAC,CAClC,CAAC,oBAAqB,CAAC,YAAa,GAAI,SAAS,CAAC,CAClD,CAAC,oBAAqB,CAAC,YAAa,GAAI,SAAS,CAAC,CAClD,CAAC,oBAAqB,CAAC,YAAa,GAAI,SAAS,CAAC,CAClD,CAAC,oBAAqB,CAAC,YAAa,GAAI,SAAS,CAAC,CAClD,CAAC,oBAAqB,CAAC,YAAa,GAAI,SAAS,CAAC,CAClD,CAAC,oBAAqB,CAAC,YAAa,GAAI,SAAS,CAAC,CAClD,CAAC,oBAAqB,CAAC,YAAa,GAAI,SAAS,CAAC,CAClD,CAAC,oBAAqB,CAAC,YAAa,GAAI,SAAS,CAAC,CAClD,CAAC,oBAAqB,CAAC,YAAa,GAAI,SAAS,CAAC,CAClD,CAAC,oBAAqB,CAAC,YAAa,GAAI,SAAS,CAAC,CAClD,CAAC,oBAAqB,CAAC,GAAI,GAAI,SAAS,CAAC,CAEzC,CACE,IACA,CACE,MAAO,CACL,0BAA2B,CACzB,MAAO,SACP,KAAM,OACP,CACD,WAAY,YACb,CACF,CACF,CACD,CAAC,aAAc,YAAY,CAC3B,CAAC,WAAY,YAAY,CAEzB,CAAC,sBAAuB,eAAe,CACvC,CAAC,2BAA4B,eAAe,CAC5C,CAAC,oBAAqB,aAAa,CACnC,CAAC,gBAAiB,eAAe,CACjC,CAAC,MAAO,SAAS,CAEjB,CAAC,OAAQ,YAAY,CAErB,CAAC,MAAO,SAAU,kBAAkB,CACpC,CAAC,MAAO,SAAU,kBAAkB,CACpC,CACE,IACA,CACE,MAAO,CACL,OAAQ,SACR,WAAY,CAAE,MAAO,SAAU,KAAM,YAAa,CACnD,CACF,CACF,CACD,CACE,IACA,CACE,MAAO,CACL,OAAQ,SACR,WAAY,CAAE,MAAO,SAAU,KAAM,YAAa,CACnD,CACF,CACF,CACF,CACD,OAAQ,CACN,CAAC,aAAc,SAAS,CACxB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,KAAM,wBAAwB,CAC/B,CAAC,KAAM,wBAAwB,CAC/B,CACE,KACA,CACE,MAAO,CACL,SAAU,CACR,MAAO,SACP,KAAM,0BACP,CACD,WAAY,SACb,CACF,CACF,CACD,CACE,OACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,SAAU,KAAM,OAAQ,CAC5C,WAAY,SACb,CACF,CACF,CACD,CAAC,IAAK,SAAS,CAChB,CACD,WAAY,CACV,CACE,YACA,CACE,MAAO,CACL,UAAW,CAAE,MAAO,SAAU,KAAM,OAAQ,CAC5C,WAAY,SACb,CACF,CACF,CACD,CAAC,YAAa,SAAS,CACvB,CAAC,QAAS,SAAS,CACnB,CAAC,WAAY,gBAAgB,CAC7B,CAAC,KAAM,wBAAwB,CAC/B,CAAC,KAAM,CAAE,MAAO,eAAgB,KAAM,0BAA2B,CAAC,CAClE,CAAC,IAAK,SAAS,CAChB,CACD,QAAS,CACP,CAAC,QAAS,UAAU,CACpB,CAAC,MAAO,UAAW,OAAO,CAC1B,CAAC,IAAK,UAAU,CACjB,CACD,WAAY,CACV,CAAC,YAAa,SAAS,CACvB,CAAC,MAAO,SAAS,CACjB,CAAC,OAAQ,UAAU,CACnB,CAAC,YAAa,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAC,CAChD,CAAC,KAAM,SAAS,CACjB,CACF,CACF"}