{"version": 3, "file": "markdown-C-jn5vO9.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/markdown/markdown.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/markdown/markdown.ts\nvar conf = {\n  comments: {\n    blockComment: [\"<!--\", \"-->\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"(\", close: \")\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*<!--\\\\s*#?region\\\\b.*-->\"),\n      end: new RegExp(\"^\\\\s*<!--\\\\s*#?endregion\\\\b.*-->\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".md\",\n  // escape codes\n  control: /[\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  noncontrol: /[^\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  escapes: /\\\\(?:@control)/,\n  // escape codes for javascript/CSS strings\n  jsescapes: /\\\\(?:[btnfr\\\\\"']|[0-7][0-7]?|[0-3][0-7]{2})/,\n  // non matched elements\n  empty: [\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"link\",\n    \"meta\",\n    \"param\"\n  ],\n  tokenizer: {\n    root: [\n      // markdown tables\n      [/^\\s*\\|/, \"@rematch\", \"@table_header\"],\n      // headers (with #)\n      [/^(\\s{0,3})(#+)((?:[^\\\\#]|@escapes)+)((?:#+)?)/, [\"white\", \"keyword\", \"keyword\", \"keyword\"]],\n      // headers (with =)\n      [/^\\s*(=+|\\-+)\\s*$/, \"keyword\"],\n      // headers (with ***)\n      [/^\\s*((\\*[ ]?)+)\\s*$/, \"meta.separator\"],\n      // quote\n      [/^\\s*>+/, \"comment\"],\n      // list (starting with * or number)\n      [/^\\s*([\\*\\-+:]|\\d+\\.)\\s/, \"keyword\"],\n      // code block (4 spaces indent)\n      [/^(\\t|[ ]{4})[^ ].*$/, \"string\"],\n      // code block (3 tilde)\n      [/^\\s*~~~\\s*((?:\\w|[\\/\\-#])+)?\\s*$/, { token: \"string\", next: \"@codeblock\" }],\n      // github style code blocks (with backticks and language)\n      [\n        /^\\s*```\\s*((?:\\w|[\\/\\-#])+).*$/,\n        { token: \"string\", next: \"@codeblockgh\", nextEmbedded: \"$1\" }\n      ],\n      // github style code blocks (with backticks but no language)\n      [/^\\s*```\\s*$/, { token: \"string\", next: \"@codeblock\" }],\n      // markup within lines\n      { include: \"@linecontent\" }\n    ],\n    table_header: [\n      { include: \"@table_common\" },\n      [/[^\\|]+/, \"keyword.table.header\"]\n      // table header\n    ],\n    table_body: [{ include: \"@table_common\" }, { include: \"@linecontent\" }],\n    table_common: [\n      [/\\s*[\\-:]+\\s*/, { token: \"keyword\", switchTo: \"table_body\" }],\n      // header-divider\n      [/^\\s*\\|/, \"keyword.table.left\"],\n      // opening |\n      [/^\\s*[^\\|]/, \"@rematch\", \"@pop\"],\n      // exiting\n      [/^\\s*$/, \"@rematch\", \"@pop\"],\n      // exiting\n      [\n        /\\|/,\n        {\n          cases: {\n            \"@eos\": \"keyword.table.right\",\n            // closing |\n            \"@default\": \"keyword.table.middle\"\n            // inner |\n          }\n        }\n      ]\n    ],\n    codeblock: [\n      [/^\\s*~~~\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/^\\s*```\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    // github style code blocks\n    codeblockgh: [\n      [/```\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^`]+/, \"variable.source\"]\n    ],\n    linecontent: [\n      // escapes\n      [/&\\w+;/, \"string.escape\"],\n      [/@escapes/, \"escape\"],\n      // various markup\n      [/\\b__([^\\\\_]|@escapes|_(?!_))+__\\b/, \"strong\"],\n      [/\\*\\*([^\\\\*]|@escapes|\\*(?!\\*))+\\*\\*/, \"strong\"],\n      [/\\b_[^_]+_\\b/, \"emphasis\"],\n      [/\\*([^\\\\*]|@escapes)+\\*/, \"emphasis\"],\n      [/`([^\\\\`]|@escapes)+`/, \"variable\"],\n      // links\n      [/\\{+[^}]+\\}+/, \"string.target\"],\n      [/(!?\\[)((?:[^\\]\\\\]|@escapes)*)(\\]\\([^\\)]+\\))/, [\"string.link\", \"\", \"string.link\"]],\n      [/(!?\\[)((?:[^\\]\\\\]|@escapes)*)(\\])/, \"string.link\"],\n      // or html\n      { include: \"html\" }\n    ],\n    // Note: it is tempting to rather switch to the real HTML mode instead of building our own here\n    // but currently there is a limitation in Monarch that prevents us from doing it: The opening\n    // '<' would start the HTML mode, however there is no way to jump 1 character back to let the\n    // HTML mode also tokenize the opening angle bracket. Thus, even though we could jump to HTML,\n    // we cannot correctly tokenize it in that mode yet.\n    html: [\n      // html tags\n      [/<(\\w+)\\/>/, \"tag\"],\n      [\n        /<(\\w+)(\\-|\\w)*/,\n        {\n          cases: {\n            \"@empty\": { token: \"tag\", next: \"@tag.$1\" },\n            \"@default\": { token: \"tag\", next: \"@tag.$1\" }\n          }\n        }\n      ],\n      [/<\\/(\\w+)(\\-|\\w)*\\s*>/, { token: \"tag\" }],\n      [/<!--/, \"comment\", \"@comment\"]\n    ],\n    comment: [\n      [/[^<\\-]+/, \"comment.content\"],\n      [/-->/, \"comment\", \"@pop\"],\n      [/<!--/, \"comment.content.invalid\"],\n      [/[<\\-]/, \"comment.content\"]\n    ],\n    // Almost full HTML tag matching, complete with embedded scripts & styles\n    tag: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [\n        /(type)(\\s*=\\s*)(\")([^\"]+)(\")/,\n        [\n          \"attribute.name.html\",\n          \"delimiter.html\",\n          \"string.html\",\n          { token: \"string.html\", switchTo: \"@tag.$S2.$4\" },\n          \"string.html\"\n        ]\n      ],\n      [\n        /(type)(\\s*=\\s*)(')([^']+)(')/,\n        [\n          \"attribute.name.html\",\n          \"delimiter.html\",\n          \"string.html\",\n          { token: \"string.html\", switchTo: \"@tag.$S2.$4\" },\n          \"string.html\"\n        ]\n      ],\n      [/(\\w+)(\\s*=\\s*)(\"[^\"]*\"|'[^']*')/, [\"attribute.name.html\", \"delimiter.html\", \"string.html\"]],\n      [/\\w+/, \"attribute.name.html\"],\n      [/\\/>/, \"tag\", \"@pop\"],\n      [\n        />/,\n        {\n          cases: {\n            \"$S2==style\": {\n              token: \"tag\",\n              switchTo: \"embeddedStyle\",\n              nextEmbedded: \"text/css\"\n            },\n            \"$S2==script\": {\n              cases: {\n                $S3: {\n                  token: \"tag\",\n                  switchTo: \"embeddedScript\",\n                  nextEmbedded: \"$S3\"\n                },\n                \"@default\": {\n                  token: \"tag\",\n                  switchTo: \"embeddedScript\",\n                  nextEmbedded: \"text/javascript\"\n                }\n              }\n            },\n            \"@default\": { token: \"tag\", next: \"@pop\" }\n          }\n        }\n      ]\n    ],\n    embeddedStyle: [\n      [/[^<]+/, \"\"],\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/</, \"\"]\n    ],\n    embeddedScript: [\n      [/[^<]+/, \"\"],\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/</, \"\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,aAAc,CAAC,OAAQ,MAAM,CAC9B,CACD,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,MAAO,CAAC,SAAS,CAAE,CAC7C,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,QAAS,CACP,QAAS,CACP,MAAW,OAAO,gCAAgC,CAClD,IAAS,OAAO,mCAAmC,CACpD,CACF,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,MAEd,QAAS,yBACT,WAAY,0BACZ,QAAS,iBAET,UAAW,8CAEX,MAAO,CACL,OACA,OACA,WACA,KACA,MACA,QACA,KACA,MACA,QACA,UACA,OACA,OACA,QACD,CACD,UAAW,CACT,KAAM,CAEJ,CAAC,SAAU,WAAY,gBAAgB,CAEvC,CAAC,gDAAiD,CAAC,QAAS,UAAW,UAAW,UAAU,CAAC,CAE7F,CAAC,mBAAoB,UAAU,CAE/B,CAAC,sBAAuB,iBAAiB,CAEzC,CAAC,SAAU,UAAU,CAErB,CAAC,yBAA0B,UAAU,CAErC,CAAC,sBAAuB,SAAS,CAEjC,CAAC,mCAAoC,CAAE,MAAO,SAAU,KAAM,aAAc,CAAC,CAE7E,CACE,iCACA,CAAE,MAAO,SAAU,KAAM,eAAgB,aAAc,KAAM,CAC9D,CAED,CAAC,cAAe,CAAE,MAAO,SAAU,KAAM,aAAc,CAAC,CAExD,CAAE,QAAS,eAAgB,CAC5B,CACD,aAAc,CACZ,CAAE,QAAS,gBAAiB,CAC5B,CAAC,SAAU,uBAAuB,CAEnC,CACD,WAAY,CAAC,CAAE,QAAS,gBAAiB,CAAE,CAAE,QAAS,eAAgB,CAAC,CACvE,aAAc,CACZ,CAAC,eAAgB,CAAE,MAAO,UAAW,SAAU,aAAc,CAAC,CAE9D,CAAC,SAAU,qBAAqB,CAEhC,CAAC,YAAa,WAAY,OAAO,CAEjC,CAAC,QAAS,WAAY,OAAO,CAE7B,CACE,KACA,CACE,MAAO,CACL,OAAQ,sBAER,WAAY,uBAEb,CACF,CACF,CACF,CACD,UAAW,CACT,CAAC,cAAe,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAC,CAClD,CAAC,cAAe,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAC,CAClD,CAAC,MAAO,kBAAkB,CAC3B,CAED,YAAa,CACX,CAAC,UAAW,CAAE,MAAO,SAAU,KAAM,OAAQ,aAAc,OAAQ,CAAC,CACpE,CAAC,QAAS,kBAAkB,CAC7B,CACD,YAAa,CAEX,CAAC,QAAS,gBAAgB,CAC1B,CAAC,WAAY,SAAS,CAEtB,CAAC,oCAAqC,SAAS,CAC/C,CAAC,sCAAuC,SAAS,CACjD,CAAC,cAAe,WAAW,CAC3B,CAAC,yBAA0B,WAAW,CACtC,CAAC,uBAAwB,WAAW,CAEpC,CAAC,cAAe,gBAAgB,CAChC,CAAC,8CAA+C,CAAC,cAAe,GAAI,cAAc,CAAC,CACnF,CAAC,oCAAqC,cAAc,CAEpD,CAAE,QAAS,OAAQ,CACpB,CAMD,KAAM,CAEJ,CAAC,YAAa,MAAM,CACpB,CACE,iBACA,CACE,MAAO,CACL,SAAU,CAAE,MAAO,MAAO,KAAM,UAAW,CAC3C,WAAY,CAAE,MAAO,MAAO,KAAM,UAAW,CAC9C,CACF,CACF,CACD,CAAC,uBAAwB,CAAE,MAAO,MAAO,CAAC,CAC1C,CAAC,OAAQ,UAAW,WAAW,CAChC,CACD,QAAS,CACP,CAAC,UAAW,kBAAkB,CAC9B,CAAC,MAAO,UAAW,OAAO,CAC1B,CAAC,OAAQ,0BAA0B,CACnC,CAAC,QAAS,kBAAkB,CAC7B,CAED,IAAK,CACH,CAAC,aAAc,QAAQ,CACvB,CACE,+BACA,CACE,sBACA,iBACA,cACA,CAAE,MAAO,cAAe,SAAU,cAAe,CACjD,cACD,CACF,CACD,CACE,+BACA,CACE,sBACA,iBACA,cACA,CAAE,MAAO,cAAe,SAAU,cAAe,CACjD,cACD,CACF,CACD,CAAC,kCAAmC,CAAC,sBAAuB,iBAAkB,cAAc,CAAC,CAC7F,CAAC,MAAO,sBAAsB,CAC9B,CAAC,MAAO,MAAO,OAAO,CACtB,CACE,IACA,CACE,MAAO,CACL,aAAc,CACZ,MAAO,MACP,SAAU,gBACV,aAAc,WACf,CACD,cAAe,CACb,MAAO,CACL,IAAK,CACH,MAAO,MACP,SAAU,iBACV,aAAc,MACf,CACD,WAAY,CACV,MAAO,MACP,SAAU,iBACV,aAAc,kBACf,CACF,CACF,CACD,WAAY,CAAE,MAAO,MAAO,KAAM,OAAQ,CAC3C,CACF,CACF,CACF,CACD,cAAe,CACb,CAAC,QAAS,GAAG,CACb,CAAC,eAAgB,CAAE,MAAO,WAAY,KAAM,OAAQ,aAAc,OAAQ,CAAC,CAC3E,CAAC,IAAK,GAAG,CACV,CACD,eAAgB,CACd,CAAC,QAAS,GAAG,CACb,CAAC,gBAAiB,CAAE,MAAO,WAAY,KAAM,OAAQ,aAAc,OAAQ,CAAC,CAC5E,CAAC,IAAK,GAAG,CACV,CACF,CACF"}