{"version": 3, "file": "dockerfile-C5nNHZyt.js", "names": [], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/dockerfile/dockerfile.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/dockerfile/dockerfile.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".dockerfile\",\n  variable: /\\${?[\\w]+}?/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      [/(ONBUILD)(\\s+)/, [\"keyword\", \"\"]],\n      [/(ENV)(\\s+)([\\w]+)/, [\"keyword\", \"\", { token: \"variable\", next: \"@arguments\" }]],\n      [\n        /(FROM|MAINTAINER|RUN|EXPOSE|ENV|ADD|ARG|VOLUME|LABEL|USER|WORKDIR|COPY|CMD|STOPSIGNAL|SHELL|HEALTHCHECK|ENTRYPOINT)/,\n        { token: \"keyword\", next: \"@arguments\" }\n      ]\n    ],\n    arguments: [\n      { include: \"@whitespace\" },\n      { include: \"@strings\" },\n      [\n        /(@variable)/,\n        {\n          cases: {\n            \"@eos\": { token: \"variable\", next: \"@popall\" },\n            \"@default\": \"variable\"\n          }\n        }\n      ],\n      [\n        /\\\\/,\n        {\n          cases: {\n            \"@eos\": \"\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [\n        /./,\n        {\n          cases: {\n            \"@eos\": { token: \"\", next: \"@popall\" },\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    // Deal with white space, including comments\n    whitespace: [\n      [\n        /\\s+/,\n        {\n          cases: {\n            \"@eos\": { token: \"\", next: \"@popall\" },\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    comment: [[/(^#.*$)/, \"comment\", \"@popall\"]],\n    // Recognize strings, including those broken across lines with \\ (but not without)\n    strings: [\n      [/\\\\'$/, \"\", \"@popall\"],\n      // \\' leaves @arguments at eol\n      [/\\\\'/, \"\"],\n      // \\' is not a string\n      [/'$/, \"string\", \"@popall\"],\n      [/'/, \"string\", \"@stringBody\"],\n      [/\"$/, \"string\", \"@popall\"],\n      [/\"/, \"string\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [\n        /[^\\\\\\$']/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/\\\\./, \"string.escape\"],\n      [/'$/, \"string\", \"@popall\"],\n      [/'/, \"string\", \"@pop\"],\n      [/(@variable)/, \"variable\"],\n      [/\\\\$/, \"string\"],\n      [/$/, \"string\", \"@popall\"]\n    ],\n    dblStringBody: [\n      [\n        /[^\\\\\\$\"]/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/\\\\./, \"string.escape\"],\n      [/\"$/, \"string\", \"@popall\"],\n      [/\"/, \"string\", \"@pop\"],\n      [/(@variable)/, \"variable\"],\n      [/\\\\$/, \"string\"],\n      [/$/, \"string\", \"@popall\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AASA,IAAI,EAAO,CACT,SAAU,CACR,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACV,CAAC,IAAK,IAAI,CACX,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,cACd,SAAU,cACV,UAAW,CACT,KAAM,CACJ,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,WAAY,CACvB,CAAC,iBAAkB,CAAC,UAAW,GAAG,CAAC,CACnC,CAAC,oBAAqB,CAAC,UAAW,GAAI,CAAE,MAAO,WAAY,KAAM,aAAc,CAAC,CAAC,CACjF,CACE,sHACA,CAAE,MAAO,UAAW,KAAM,aAAc,CACzC,CACF,CACD,UAAW,CACT,CAAE,QAAS,cAAe,CAC1B,CAAE,QAAS,WAAY,CACvB,CACE,cACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,WAAY,KAAM,UAAW,CAC9C,WAAY,WACb,CACF,CACF,CACD,CACE,KACA,CACE,MAAO,CACL,OAAQ,GACR,WAAY,GACb,CACF,CACF,CACD,CACE,IACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,GAAI,KAAM,UAAW,CACtC,WAAY,GACb,CACF,CACF,CACF,CAED,WAAY,CACV,CACE,MACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,GAAI,KAAM,UAAW,CACtC,WAAY,GACb,CACF,CACF,CACF,CACD,QAAS,CAAC,CAAC,UAAW,UAAW,UAAU,CAAC,CAE5C,QAAS,CACP,CAAC,OAAQ,GAAI,UAAU,CAEvB,CAAC,MAAO,GAAG,CAEX,CAAC,KAAM,SAAU,UAAU,CAC3B,CAAC,IAAK,SAAU,cAAc,CAC9B,CAAC,KAAM,SAAU,UAAU,CAC3B,CAAC,IAAK,SAAU,iBAAiB,CAClC,CACD,WAAY,CACV,CACE,WACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,SAAU,KAAM,UAAW,CAC5C,WAAY,SACb,CACF,CACF,CACD,CAAC,MAAO,gBAAgB,CACxB,CAAC,KAAM,SAAU,UAAU,CAC3B,CAAC,IAAK,SAAU,OAAO,CACvB,CAAC,cAAe,WAAW,CAC3B,CAAC,MAAO,SAAS,CACjB,CAAC,IAAK,SAAU,UAAU,CAC3B,CACD,cAAe,CACb,CACE,WACA,CACE,MAAO,CACL,OAAQ,CAAE,MAAO,SAAU,KAAM,UAAW,CAC5C,WAAY,SACb,CACF,CACF,CACD,CAAC,MAAO,gBAAgB,CACxB,CAAC,KAAM,SAAU,UAAU,CAC3B,CAAC,IAAK,SAAU,OAAO,CACvB,CAAC,cAAe,WAAW,CAC3B,CAAC,MAAO,SAAS,CACjB,CAAC,IAAK,SAAU,UAAU,CAC3B,CACF,CACF"}