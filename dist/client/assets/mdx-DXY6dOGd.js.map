{"version": 3, "file": "mdx-DXY6dOGd.js", "names": ["monaco_editor_core_star"], "sources": ["../../../node_modules/monaco-editor/esm/vs/basic-languages/mdx/mdx.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/mdx/mdx.ts\nvar conf = {\n  comments: {\n    blockComment: [\"{/*\", \"*/}\"]\n  },\n  brackets: [[\"{\", \"}\"]],\n  autoClosingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"\\u201C\", close: \"\\u201D\" },\n    { open: \"\\u2018\", close: \"\\u2019\" },\n    { open: \"`\", close: \"`\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"_\", close: \"_\" },\n    { open: \"**\", close: \"**\" },\n    { open: \"<\", close: \">\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: /^\\s*- .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"- \" }\n    },\n    {\n      beforeText: /^\\s*\\+ .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"+ \" }\n    },\n    {\n      beforeText: /^\\s*\\* .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"* \" }\n    },\n    {\n      beforeText: /^> /,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"> \" }\n    },\n    {\n      beforeText: /<\\w+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    },\n    {\n      beforeText: /\\s+>\\s*$/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    },\n    {\n      beforeText: /<\\/\\w+>/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Outdent }\n    },\n    ...Array.from({ length: 100 }, (_, index) => ({\n      beforeText: new RegExp(`^${index}\\\\. .+`),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: `${index + 1}. ` }\n    }))\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".mdx\",\n  control: /[!#()*+.[\\\\\\]_`{}\\-]/,\n  escapes: /\\\\@control/,\n  tokenizer: {\n    root: [\n      [/^---$/, { token: \"meta.content\", next: \"@frontmatter\", nextEmbedded: \"yaml\" }],\n      [/^\\s*import/, { token: \"keyword\", next: \"@import\", nextEmbedded: \"js\" }],\n      [/^\\s*export/, { token: \"keyword\", next: \"@export\", nextEmbedded: \"js\" }],\n      [/<\\w+/, { token: \"type.identifier\", next: \"@jsx\" }],\n      [/<\\/?\\w+>/, \"type.identifier\"],\n      [\n        /^(\\s*)(>*\\s*)(#{1,6}\\s)/,\n        [{ token: \"white\" }, { token: \"comment\" }, { token: \"keyword\", next: \"@header\" }]\n      ],\n      [/^(\\s*)(>*\\s*)([*+-])(\\s+)/, [\"white\", \"comment\", \"keyword\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(\\d{1,9}\\.)(\\s+)/, [\"white\", \"comment\", \"number\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(\\d{1,9}\\.)(\\s+)/, [\"white\", \"comment\", \"number\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(-{3,}|\\*{3,}|_{3,})$/, [\"white\", \"comment\", \"keyword\"]],\n      [/`{3,}(\\s.*)?$/, { token: \"string\", next: \"@codeblock_backtick\" }],\n      [/~{3,}(\\s.*)?$/, { token: \"string\", next: \"@codeblock_tilde\" }],\n      [\n        /`{3,}(\\S+).*$/,\n        { token: \"string\", next: \"@codeblock_highlight_backtick\", nextEmbedded: \"$1\" }\n      ],\n      [\n        /~{3,}(\\S+).*$/,\n        { token: \"string\", next: \"@codeblock_highlight_tilde\", nextEmbedded: \"$1\" }\n      ],\n      [/^(\\s*)(-{4,})$/, [\"white\", \"comment\"]],\n      [/^(\\s*)(>+)/, [\"white\", \"comment\"]],\n      { include: \"content\" }\n    ],\n    content: [\n      [\n        /(\\[)(.+)(]\\()(.+)(\\s+\".*\")(\\))/,\n        [\"\", \"string.link\", \"\", \"type.identifier\", \"string.link\", \"\"]\n      ],\n      [/(\\[)(.+)(]\\()(.+)(\\))/, [\"\", \"type.identifier\", \"\", \"string.link\", \"\"]],\n      [/(\\[)(.+)(]\\[)(.+)(])/, [\"\", \"type.identifier\", \"\", \"type.identifier\", \"\"]],\n      [/(\\[)(.+)(]:\\s+)(\\S*)/, [\"\", \"type.identifier\", \"\", \"string.link\"]],\n      [/(\\[)(.+)(])/, [\"\", \"type.identifier\", \"\"]],\n      [/`.*`/, \"variable.source\"],\n      [/_/, { token: \"emphasis\", next: \"@emphasis_underscore\" }],\n      [/\\*(?!\\*)/, { token: \"emphasis\", next: \"@emphasis_asterisk\" }],\n      [/\\*\\*/, { token: \"strong\", next: \"@strong\" }],\n      [/{/, { token: \"delimiter.bracket\", next: \"@expression\", nextEmbedded: \"js\" }]\n    ],\n    import: [[/'\\s*(;|$)/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }]],\n    expression: [\n      [/{/, { token: \"delimiter.bracket\", next: \"@expression\" }],\n      [/}/, { token: \"delimiter.bracket\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    export: [[/^\\s*$/, { token: \"delimiter.bracket\", next: \"@pop\", nextEmbedded: \"@pop\" }]],\n    jsx: [\n      [/\\s+/, \"\"],\n      [/(\\w+)(=)(\"(?:[^\"\\\\]|\\\\.)*\")/, [\"attribute.name\", \"operator\", \"string\"]],\n      [/(\\w+)(=)('(?:[^'\\\\]|\\\\.)*')/, [\"attribute.name\", \"operator\", \"string\"]],\n      [/(\\w+(?=\\s|>|={|$))/, [\"attribute.name\"]],\n      [/={/, { token: \"delimiter.bracket\", next: \"@expression\", nextEmbedded: \"js\" }],\n      [/>/, { token: \"type.identifier\", next: \"@pop\" }]\n    ],\n    header: [\n      [/.$/, { token: \"keyword\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"keyword\" }]\n    ],\n    strong: [\n      [/\\*\\*/, { token: \"strong\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"strong\" }]\n    ],\n    emphasis_underscore: [\n      [/_/, { token: \"emphasis\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"emphasis\" }]\n    ],\n    emphasis_asterisk: [\n      [/\\*(?!\\*)/, { token: \"emphasis\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"emphasis\" }]\n    ],\n    frontmatter: [[/^---$/, { token: \"meta.content\", nextEmbedded: \"@pop\", next: \"@pop\" }]],\n    codeblock_highlight_backtick: [\n      [/\\s*`{3,}\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_highlight_tilde: [\n      [/\\s*~{3,}\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_backtick: [\n      [/\\s*`{3,}\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_tilde: [\n      [/\\s*~{3,}\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;;AAOA,IAAI,EAAY,OAAO,eACnB,EAAmB,OAAO,yBAC1B,EAAoB,OAAO,oBAC3B,EAAe,OAAO,UAAU,eAChC,GAAe,EAAI,EAAM,EAAQ,IAAS,CAC5C,GAAI,GAAQ,OAAO,GAAS,UAAY,OAAO,GAAS,eACjD,IAAI,KAAO,EAAkB,EAAK,CACjC,CAAC,EAAa,KAAK,EAAI,EAAI,EAAI,IAAQ,GACzC,EAAU,EAAI,EAAK,CAAE,QAAW,EAAK,GAAM,WAAY,EAAE,EAAO,EAAiB,EAAM,EAAI,GAAK,EAAK,WAAY,CAAC,CAExH,OAAO,GAEL,GAAc,EAAQ,EAAK,KAAkB,EAAY,EAAQ,EAAK,UAAU,CAAE,GAAgB,EAAY,EAAc,EAAK,UAAU,EAG3I,EAA6B,EAAE,CACnC,EAAW,EAA4BA,EAAwB,CAI/D,IAAI,EAAO,CACT,SAAU,CACR,aAAc,CAAC,MAAO,MAAM,CAC7B,CACD,SAAU,CAAC,CAAC,IAAK,IAAI,CAAC,CACtB,iBAAkB,CAChB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAU,MAAO,IAAU,CACnC,CAAE,KAAM,IAAU,MAAO,IAAU,CACnC,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,IAAK,MAAO,IAAK,CACzB,CAAE,KAAM,KAAM,MAAO,KAAM,CAC3B,CAAE,KAAM,IAAK,MAAO,IAAK,CAC1B,CACD,aAAc,CACZ,CACE,WAAY,WACZ,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,KAAM,WAAY,KAAM,CACnG,CACD,CACE,WAAY,YACZ,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,KAAM,WAAY,KAAM,CACnG,CACD,CACE,WAAY,YACZ,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,KAAM,WAAY,KAAM,CACnG,CACD,CACE,WAAY,MACZ,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,KAAM,WAAY,KAAM,CACnG,CACD,CACE,WAAY,OACZ,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,OAAQ,CACnF,CACD,CACE,WAAY,WACZ,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,OAAQ,CACnF,CACD,CACE,WAAY,UACZ,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,QAAS,CACpF,CACD,GAAG,MAAM,KAAK,CAAE,OAAQ,IAAK,EAAG,EAAG,KAAW,CAC5C,WAAgB,OAAO,IAAI,EAAM,QAAQ,CACzC,OAAQ,CAAE,aAAc,EAA2B,UAAU,aAAa,KAAM,WAAY,GAAG,EAAQ,EAAE,IAAK,CAC/G,EAAE,CACJ,CACF,CACG,EAAW,CACb,aAAc,GACd,aAAc,OACd,QAAS,uBACT,QAAS,aACT,UAAW,CACT,KAAM,CACJ,CAAC,QAAS,CAAE,MAAO,eAAgB,KAAM,eAAgB,aAAc,OAAQ,CAAC,CAChF,CAAC,aAAc,CAAE,MAAO,UAAW,KAAM,UAAW,aAAc,KAAM,CAAC,CACzE,CAAC,aAAc,CAAE,MAAO,UAAW,KAAM,UAAW,aAAc,KAAM,CAAC,CACzE,CAAC,OAAQ,CAAE,MAAO,kBAAmB,KAAM,OAAQ,CAAC,CACpD,CAAC,WAAY,kBAAkB,CAC/B,CACE,0BACA,CAAC,CAAE,MAAO,QAAS,CAAE,CAAE,MAAO,UAAW,CAAE,CAAE,MAAO,UAAW,KAAM,UAAW,CAAC,CAClF,CACD,CAAC,4BAA6B,CAAC,QAAS,UAAW,UAAW,QAAQ,CAAC,CACvE,CAAC,gCAAiC,CAAC,QAAS,UAAW,SAAU,QAAQ,CAAC,CAC1E,CAAC,gCAAiC,CAAC,QAAS,UAAW,SAAU,QAAQ,CAAC,CAC1E,CAAC,qCAAsC,CAAC,QAAS,UAAW,UAAU,CAAC,CACvE,CAAC,gBAAiB,CAAE,MAAO,SAAU,KAAM,sBAAuB,CAAC,CACnE,CAAC,gBAAiB,CAAE,MAAO,SAAU,KAAM,mBAAoB,CAAC,CAChE,CACE,gBACA,CAAE,MAAO,SAAU,KAAM,gCAAiC,aAAc,KAAM,CAC/E,CACD,CACE,gBACA,CAAE,MAAO,SAAU,KAAM,6BAA8B,aAAc,KAAM,CAC5E,CACD,CAAC,iBAAkB,CAAC,QAAS,UAAU,CAAC,CACxC,CAAC,aAAc,CAAC,QAAS,UAAU,CAAC,CACpC,CAAE,QAAS,UAAW,CACvB,CACD,QAAS,CACP,CACE,iCACA,CAAC,GAAI,cAAe,GAAI,kBAAmB,cAAe,GAAG,CAC9D,CACD,CAAC,wBAAyB,CAAC,GAAI,kBAAmB,GAAI,cAAe,GAAG,CAAC,CACzE,CAAC,uBAAwB,CAAC,GAAI,kBAAmB,GAAI,kBAAmB,GAAG,CAAC,CAC5E,CAAC,uBAAwB,CAAC,GAAI,kBAAmB,GAAI,cAAc,CAAC,CACpE,CAAC,cAAe,CAAC,GAAI,kBAAmB,GAAG,CAAC,CAC5C,CAAC,OAAQ,kBAAkB,CAC3B,CAAC,IAAK,CAAE,MAAO,WAAY,KAAM,uBAAwB,CAAC,CAC1D,CAAC,WAAY,CAAE,MAAO,WAAY,KAAM,qBAAsB,CAAC,CAC/D,CAAC,OAAQ,CAAE,MAAO,SAAU,KAAM,UAAW,CAAC,CAC9C,CAAC,IAAK,CAAE,MAAO,oBAAqB,KAAM,cAAe,aAAc,KAAM,CAAC,CAC/E,CACD,OAAQ,CAAC,CAAC,YAAa,CAAE,MAAO,SAAU,KAAM,OAAQ,aAAc,OAAQ,CAAC,CAAC,CAChF,WAAY,CACV,CAAC,IAAK,CAAE,MAAO,oBAAqB,KAAM,cAAe,CAAC,CAC1D,CAAC,IAAK,CAAE,MAAO,oBAAqB,KAAM,OAAQ,aAAc,OAAQ,CAAC,CAC1E,CACD,OAAQ,CAAC,CAAC,QAAS,CAAE,MAAO,oBAAqB,KAAM,OAAQ,aAAc,OAAQ,CAAC,CAAC,CACvF,IAAK,CACH,CAAC,MAAO,GAAG,CACX,CAAC,8BAA+B,CAAC,iBAAkB,WAAY,SAAS,CAAC,CACzE,CAAC,8BAA+B,CAAC,iBAAkB,WAAY,SAAS,CAAC,CACzE,CAAC,qBAAsB,CAAC,iBAAiB,CAAC,CAC1C,CAAC,KAAM,CAAE,MAAO,oBAAqB,KAAM,cAAe,aAAc,KAAM,CAAC,CAC/E,CAAC,IAAK,CAAE,MAAO,kBAAmB,KAAM,OAAQ,CAAC,CAClD,CACD,OAAQ,CACN,CAAC,KAAM,CAAE,MAAO,UAAW,KAAM,OAAQ,CAAC,CAC1C,CAAE,QAAS,UAAW,CACtB,CAAC,IAAK,CAAE,MAAO,UAAW,CAAC,CAC5B,CACD,OAAQ,CACN,CAAC,OAAQ,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAC,CAC3C,CAAE,QAAS,UAAW,CACtB,CAAC,IAAK,CAAE,MAAO,SAAU,CAAC,CAC3B,CACD,oBAAqB,CACnB,CAAC,IAAK,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CAC1C,CAAE,QAAS,UAAW,CACtB,CAAC,IAAK,CAAE,MAAO,WAAY,CAAC,CAC7B,CACD,kBAAmB,CACjB,CAAC,WAAY,CAAE,MAAO,WAAY,KAAM,OAAQ,CAAC,CACjD,CAAE,QAAS,UAAW,CACtB,CAAC,IAAK,CAAE,MAAO,WAAY,CAAC,CAC7B,CACD,YAAa,CAAC,CAAC,QAAS,CAAE,MAAO,eAAgB,aAAc,OAAQ,KAAM,OAAQ,CAAC,CAAC,CACvF,6BAA8B,CAC5B,CAAC,eAAgB,CAAE,MAAO,SAAU,KAAM,OAAQ,aAAc,OAAQ,CAAC,CACzE,CAAC,MAAO,kBAAkB,CAC3B,CACD,0BAA2B,CACzB,CAAC,eAAgB,CAAE,MAAO,SAAU,KAAM,OAAQ,aAAc,OAAQ,CAAC,CACzE,CAAC,MAAO,kBAAkB,CAC3B,CACD,mBAAoB,CAClB,CAAC,eAAgB,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAC,CACnD,CAAC,MAAO,kBAAkB,CAC3B,CACD,gBAAiB,CACf,CAAC,eAAgB,CAAE,MAAO,SAAU,KAAM,OAAQ,CAAC,CACnD,CAAC,MAAO,kBAAkB,CAC3B,CACF,CACF"}