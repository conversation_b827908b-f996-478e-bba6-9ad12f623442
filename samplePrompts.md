Please build a chatgpt clone for me with a beautiful UI, sidebar, ability to save and retrieve previous conversations, choose between various models etc. It should be able to use tools. Use Cloudflare agents to build it

Build a game where I see a map with circles on cities. Each city is a cloudflare datacenter. The map shows with data centres highlighted for 20 seconds, and after that, we show grayed out circles in multiple regions. The user's goal is to remember and click as many regions as they can in 20 seconds and get points based on that

Build a drag and drop flow based editor for configuring dynamic routing to ai models. i should be able to add model nodes, conditional nodes, rate limit nodes to build my routing graph

Create a file manager dashboard with a sidebar for navigation, a main area for file display, 
a toolbar for actions. The sidebar should allow users to navigate between folders in a natural way.
The main area should show files and folders in a grid or list view. 
The toolbar should have buttons for creating, deleting, and renaming files and folders.
I should also be able to open, close, edit the files as well as upload and download files. 
The design should be clean and modern, with a focus on usability.

Please build a tool/dashboard for me for evaluating various LLM models and comparing their performances side by side - in terms of latency and token generation speed (assume 1 token ~ 4 chars or 0.75 words). I should be able to add custom model names, set custom baseUrl and apiKey per model etc from UI itself.
I should also be able to evaluate all these models together in parallel on various tasks such as code generation or text generation (I should give a single prompt and have all models work on it in parallel with streaming output, and see how fast they work). The generated code should not be attempted to be executed. Whatever the output for all the models, I should also be able to ask a 'judge' model to evaluate and give rating to each model's output w.r.t each other.
Thus this should be a complete LLM testing/sandboxing environment

Make a full banking app

create web-based 3D model viewers for rendering and manipulating 3D assets in real time

Build a full github clone. I should be able to view a repo page, see issues/PRs, navigate to files, also explore new repos, see my open issues/ other use profiles. There should also be a profile page with the timeline for a users changes, and a github like grid with their contributions. Add any other features to make it a complete github clone.

Build a full fledged github clone with all it's functionalities. I should be able to create/edit/commit files, make repos, have organizations, pull requests, issues, proper markdown rendering, code rendering, proper code editor, kanban board, github actions, likes, stars, forks, users, etc.