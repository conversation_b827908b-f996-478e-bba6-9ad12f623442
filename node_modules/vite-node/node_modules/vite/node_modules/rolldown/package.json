{"name": "rolldown", "version": "1.0.0-beta.37", "description": "Fast JavaScript/TypeScript bundler in Rust with Rollup-compatible API.", "type": "module", "homepage": "https://rolldown.rs/", "repository": {"type": "git", "url": "git+https://github.com/rolldown/rolldown.git", "directory": "packages/rolldown"}, "license": "MIT", "keywords": ["webpack", "parcel", "esbuild", "rollup", "bundler", "rolldown"], "files": ["bin", "cli", "dist", "!dist/*.node"], "bin": {"rolldown": "./bin/cli.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "exports": {".": "./dist/index.mjs", "./config": "./dist/config.mjs", "./experimental": "./dist/experimental-index.mjs", "./filter": "./dist/filter-index.mjs", "./parallelPlugin": "./dist/parallel-plugin.mjs", "./parseAst": "./dist/parse-ast-index.mjs", "./package.json": "./package.json"}, "imports": {"#parallel-plugin-worker": "./dist/parallel-plugin-worker.mjs"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "napi": {"binaryName": "rolldown-binding", "packageName": "@rolldown/binding", "targets": ["x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu", "x86_64-unknown-linux-musl", "x86_64-unknown-freebsd", "i686-pc-windows-msvc", "armv7-unknown-linux-gnueabihf", "aarch64-unknown-linux-gnu", "aarch64-apple-darwin", "aarch64-unknown-linux-musl", "aarch64-unknown-linux-ohos", "aarch64-pc-windows-msvc", "aarch64-linux-android", "wasm32-wasip1-threads"], "wasm": {"initialMemory": 16384, "browser": {"fs": true, "asyncInit": true}}, "dtsHeader": "type MaybePromise<T> = T | Promise<T>\ntype Nullable<T> = T | null | undefined\ntype VoidNullable<T = void> = T | null | undefined | void\nexport type BindingStringOrRegex = string | RegExp\n\n"}, "dependencies": {"@oxc-project/runtime": "=0.87.0", "@oxc-project/types": "=0.87.0", "ansis": "^4.0.0", "@rolldown/pluginutils": "1.0.0-beta.37"}, "devDependencies": {"@napi-rs/cli": "^3.1.2", "@napi-rs/wasm-runtime": "^1.0.0", "@oxc-node/cli": "^0.0.32", "@rollup/plugin-json": "^6.1.0", "@valibot/to-json-schema": "1.3.0", "buble": "^0.20.0", "consola": "^3.4.2", "emnapi": "^1.2.0", "execa": "^9.2.0", "glob": "^11.0.0", "oxc-parser": "=0.87.0", "pathe": "^2.0.3", "remeda": "^2.10.0", "rolldown-plugin-dts": "^0.16.0", "rollup": "^4.18.0", "signal-exit": "4.1.0", "source-map": "^0.7.4", "tsx": "^4.19.4", "typedoc": "^0.28.0", "typescript": "^5.7.3", "valibot": "1.1.0", "rolldown": "1.0.0-beta.37"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "optionalDependencies": {"@rolldown/binding-darwin-x64": "1.0.0-beta.37", "@rolldown/binding-win32-x64-msvc": "1.0.0-beta.37", "@rolldown/binding-linux-x64-gnu": "1.0.0-beta.37", "@rolldown/binding-linux-x64-musl": "1.0.0-beta.37", "@rolldown/binding-freebsd-x64": "1.0.0-beta.37", "@rolldown/binding-win32-ia32-msvc": "1.0.0-beta.37", "@rolldown/binding-linux-arm-gnueabihf": "1.0.0-beta.37", "@rolldown/binding-linux-arm64-gnu": "1.0.0-beta.37", "@rolldown/binding-darwin-arm64": "1.0.0-beta.37", "@rolldown/binding-linux-arm64-musl": "1.0.0-beta.37", "@rolldown/binding-openharmony-arm64": "1.0.0-beta.37", "@rolldown/binding-win32-arm64-msvc": "1.0.0-beta.37", "@rolldown/binding-android-arm64": "1.0.0-beta.37", "@rolldown/binding-wasm32-wasi": "1.0.0-beta.37"}, "scripts": {"# Scrips for binding #": "_", "artifacts": "napi artifacts --cwd ./src --package-json-path ../package.json -o=../artifacts --npm-dir ../npm", "build-binding": "oxnode ./build-binding.ts", "build-binding:release": "pnpm build-binding --release", "build-binding:profile": "pnpm build-binding --profile profile", "build-binding:wasi": "pnpm build-binding --target wasm32-wasip1-threads", "build-binding:wasi:release": "pnpm build-binding --profile release-wasi --target wasm32-wasip1-threads", "# Scrips for node #": "_", "build-node": "tsx -C dev ./build.ts", "build-types-check": "tsc -p ./tsconfig.check.json", "build-js-glue": "pnpm run --sequential '/^build-(node|types-check)$/'", "build-native:debug": "pnpm run --sequential '/^build-(binding|js-glue)$/'", "build-native:release": "pnpm run --sequential '/^build-(binding:release|js-glue)$/'", "build-native:profile": "pnpm run build-binding:profile && pnpm run build-js-glue", "build-native:memory-profile": "pnpm run build-binding:profile --features default_global_allocator && pnpm run build-js-glue", "build-browser:debug": "BROWSER_PKG=1 pnpm run --sequential '/^build-(binding|binding:wasi|node)$/'", "build-browser:release": "BROWSER_PKG=1 pnpm run --sequential '/^build-(binding|binding:wasi:release|node)$/'", "# Scrips for docs #": "_", "extract-options-doc": "typedoc"}}