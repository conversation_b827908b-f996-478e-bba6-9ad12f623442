{"name": "@oxc-project/runtime", "version": "0.87.0", "description": "Oxc's modular runtime helpers", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/oxc-project/oxc.git", "directory": "npm/runtime"}, "homepage": "https://oxc.rs", "exports": {"./helpers/OverloadYield": [{"node": "./src/helpers/OverloadYield.js", "import": "./src/helpers/esm/OverloadYield.js", "default": "./src/helpers/OverloadYield.js"}, "./src/helpers/OverloadYield.js"], "./helpers/esm/OverloadYield": "./src/helpers/esm/OverloadYield.js", "./helpers/applyDecoratedDescriptor": [{"node": "./src/helpers/applyDecoratedDescriptor.js", "import": "./src/helpers/esm/applyDecoratedDescriptor.js", "default": "./src/helpers/applyDecoratedDescriptor.js"}, "./src/helpers/applyDecoratedDescriptor.js"], "./helpers/esm/applyDecoratedDescriptor": "./src/helpers/esm/applyDecoratedDescriptor.js", "./helpers/applyDecs2311": [{"node": "./src/helpers/applyDecs2311.js", "import": "./src/helpers/esm/applyDecs2311.js", "default": "./src/helpers/applyDecs2311.js"}, "./src/helpers/applyDecs2311.js"], "./helpers/esm/applyDecs2311": "./src/helpers/esm/applyDecs2311.js", "./helpers/arrayLikeToArray": [{"node": "./src/helpers/arrayLikeToArray.js", "import": "./src/helpers/esm/arrayLikeToArray.js", "default": "./src/helpers/arrayLikeToArray.js"}, "./src/helpers/arrayLikeToArray.js"], "./helpers/esm/arrayLikeToArray": "./src/helpers/esm/arrayLikeToArray.js", "./helpers/arrayWithHoles": [{"node": "./src/helpers/arrayWithHoles.js", "import": "./src/helpers/esm/arrayWithHoles.js", "default": "./src/helpers/arrayWithHoles.js"}, "./src/helpers/arrayWithHoles.js"], "./helpers/esm/arrayWithHoles": "./src/helpers/esm/arrayWithHoles.js", "./helpers/arrayWithoutHoles": [{"node": "./src/helpers/arrayWithoutHoles.js", "import": "./src/helpers/esm/arrayWithoutHoles.js", "default": "./src/helpers/arrayWithoutHoles.js"}, "./src/helpers/arrayWithoutHoles.js"], "./helpers/esm/arrayWithoutHoles": "./src/helpers/esm/arrayWithoutHoles.js", "./helpers/assertClassBrand": [{"node": "./src/helpers/assertClassBrand.js", "import": "./src/helpers/esm/assertClassBrand.js", "default": "./src/helpers/assertClassBrand.js"}, "./src/helpers/assertClassBrand.js"], "./helpers/esm/assertClassBrand": "./src/helpers/esm/assertClassBrand.js", "./helpers/assertThisInitialized": [{"node": "./src/helpers/assertThisInitialized.js", "import": "./src/helpers/esm/assertThisInitialized.js", "default": "./src/helpers/assertThisInitialized.js"}, "./src/helpers/assertThisInitialized.js"], "./helpers/esm/assertThisInitialized": "./src/helpers/esm/assertThisInitialized.js", "./helpers/asyncGeneratorDelegate": [{"node": "./src/helpers/asyncGeneratorDelegate.js", "import": "./src/helpers/esm/asyncGeneratorDelegate.js", "default": "./src/helpers/asyncGeneratorDelegate.js"}, "./src/helpers/asyncGeneratorDelegate.js"], "./helpers/esm/asyncGeneratorDelegate": "./src/helpers/esm/asyncGeneratorDelegate.js", "./helpers/asyncIterator": [{"node": "./src/helpers/asyncIterator.js", "import": "./src/helpers/esm/asyncIterator.js", "default": "./src/helpers/asyncIterator.js"}, "./src/helpers/asyncIterator.js"], "./helpers/esm/asyncIterator": "./src/helpers/esm/asyncIterator.js", "./helpers/asyncToGenerator": [{"node": "./src/helpers/asyncToGenerator.js", "import": "./src/helpers/esm/asyncToGenerator.js", "default": "./src/helpers/asyncToGenerator.js"}, "./src/helpers/asyncToGenerator.js"], "./helpers/esm/asyncToGenerator": "./src/helpers/esm/asyncToGenerator.js", "./helpers/awaitAsyncGenerator": [{"node": "./src/helpers/awaitAsyncGenerator.js", "import": "./src/helpers/esm/awaitAsyncGenerator.js", "default": "./src/helpers/awaitAsyncGenerator.js"}, "./src/helpers/awaitAsyncGenerator.js"], "./helpers/esm/awaitAsyncGenerator": "./src/helpers/esm/awaitAsyncGenerator.js", "./helpers/callSuper": [{"node": "./src/helpers/callSuper.js", "import": "./src/helpers/esm/callSuper.js", "default": "./src/helpers/callSuper.js"}, "./src/helpers/callSuper.js"], "./helpers/esm/callSuper": "./src/helpers/esm/callSuper.js", "./helpers/checkInRHS": [{"node": "./src/helpers/checkInRHS.js", "import": "./src/helpers/esm/checkInRHS.js", "default": "./src/helpers/checkInRHS.js"}, "./src/helpers/checkInRHS.js"], "./helpers/esm/checkInRHS": "./src/helpers/esm/checkInRHS.js", "./helpers/checkPrivateRedeclaration": [{"node": "./src/helpers/checkPrivateRedeclaration.js", "import": "./src/helpers/esm/checkPrivateRedeclaration.js", "default": "./src/helpers/checkPrivateRedeclaration.js"}, "./src/helpers/checkPrivateRedeclaration.js"], "./helpers/esm/checkPrivateRedeclaration": "./src/helpers/esm/checkPrivateRedeclaration.js", "./helpers/classCallCheck": [{"node": "./src/helpers/classCallCheck.js", "import": "./src/helpers/esm/classCallCheck.js", "default": "./src/helpers/classCallCheck.js"}, "./src/helpers/classCallCheck.js"], "./helpers/esm/classCallCheck": "./src/helpers/esm/classCallCheck.js", "./helpers/classNameTDZError": [{"node": "./src/helpers/classNameTDZError.js", "import": "./src/helpers/esm/classNameTDZError.js", "default": "./src/helpers/classNameTDZError.js"}, "./src/helpers/classNameTDZError.js"], "./helpers/esm/classNameTDZError": "./src/helpers/esm/classNameTDZError.js", "./helpers/classPrivateFieldGet2": [{"node": "./src/helpers/classPrivateFieldGet2.js", "import": "./src/helpers/esm/classPrivateFieldGet2.js", "default": "./src/helpers/classPrivateFieldGet2.js"}, "./src/helpers/classPrivateFieldGet2.js"], "./helpers/esm/classPrivateFieldGet2": "./src/helpers/esm/classPrivateFieldGet2.js", "./helpers/classPrivateFieldInitSpec": [{"node": "./src/helpers/classPrivateFieldInitSpec.js", "import": "./src/helpers/esm/classPrivateFieldInitSpec.js", "default": "./src/helpers/classPrivateFieldInitSpec.js"}, "./src/helpers/classPrivateFieldInitSpec.js"], "./helpers/esm/classPrivateFieldInitSpec": "./src/helpers/esm/classPrivateFieldInitSpec.js", "./helpers/classPrivateFieldLooseBase": [{"node": "./src/helpers/classPrivateFieldLooseBase.js", "import": "./src/helpers/esm/classPrivateFieldLooseBase.js", "default": "./src/helpers/classPrivateFieldLooseBase.js"}, "./src/helpers/classPrivateFieldLooseBase.js"], "./helpers/esm/classPrivateFieldLooseBase": "./src/helpers/esm/classPrivateFieldLooseBase.js", "./helpers/classPrivateFieldLooseKey": [{"node": "./src/helpers/classPrivateFieldLooseKey.js", "import": "./src/helpers/esm/classPrivateFieldLooseKey.js", "default": "./src/helpers/classPrivateFieldLooseKey.js"}, "./src/helpers/classPrivateFieldLooseKey.js"], "./helpers/esm/classPrivateFieldLooseKey": "./src/helpers/esm/classPrivateFieldLooseKey.js", "./helpers/classPrivateFieldSet2": [{"node": "./src/helpers/classPrivateFieldSet2.js", "import": "./src/helpers/esm/classPrivateFieldSet2.js", "default": "./src/helpers/classPrivateFieldSet2.js"}, "./src/helpers/classPrivateFieldSet2.js"], "./helpers/esm/classPrivateFieldSet2": "./src/helpers/esm/classPrivateFieldSet2.js", "./helpers/classPrivateGetter": [{"node": "./src/helpers/classPrivateGetter.js", "import": "./src/helpers/esm/classPrivateGetter.js", "default": "./src/helpers/classPrivateGetter.js"}, "./src/helpers/classPrivateGetter.js"], "./helpers/esm/classPrivateGetter": "./src/helpers/esm/classPrivateGetter.js", "./helpers/classPrivateMethodInitSpec": [{"node": "./src/helpers/classPrivateMethodInitSpec.js", "import": "./src/helpers/esm/classPrivateMethodInitSpec.js", "default": "./src/helpers/classPrivateMethodInitSpec.js"}, "./src/helpers/classPrivateMethodInitSpec.js"], "./helpers/esm/classPrivateMethodInitSpec": "./src/helpers/esm/classPrivateMethodInitSpec.js", "./helpers/classPrivateSetter": [{"node": "./src/helpers/classPrivateSetter.js", "import": "./src/helpers/esm/classPrivateSetter.js", "default": "./src/helpers/classPrivateSetter.js"}, "./src/helpers/classPrivateSetter.js"], "./helpers/esm/classPrivateSetter": "./src/helpers/esm/classPrivateSetter.js", "./helpers/classStaticPrivateMethodGet": [{"node": "./src/helpers/classStaticPrivateMethodGet.js", "import": "./src/helpers/esm/classStaticPrivateMethodGet.js", "default": "./src/helpers/classStaticPrivateMethodGet.js"}, "./src/helpers/classStaticPrivateMethodGet.js"], "./helpers/esm/classStaticPrivateMethodGet": "./src/helpers/esm/classStaticPrivateMethodGet.js", "./helpers/construct": [{"node": "./src/helpers/construct.js", "import": "./src/helpers/esm/construct.js", "default": "./src/helpers/construct.js"}, "./src/helpers/construct.js"], "./helpers/esm/construct": "./src/helpers/esm/construct.js", "./helpers/createClass": [{"node": "./src/helpers/createClass.js", "import": "./src/helpers/esm/createClass.js", "default": "./src/helpers/createClass.js"}, "./src/helpers/createClass.js"], "./helpers/esm/createClass": "./src/helpers/esm/createClass.js", "./helpers/createForOfIteratorHelper": [{"node": "./src/helpers/createForOfIteratorHelper.js", "import": "./src/helpers/esm/createForOfIteratorHelper.js", "default": "./src/helpers/createForOfIteratorHelper.js"}, "./src/helpers/createForOfIteratorHelper.js"], "./helpers/esm/createForOfIteratorHelper": "./src/helpers/esm/createForOfIteratorHelper.js", "./helpers/createForOfIteratorHelperLoose": [{"node": "./src/helpers/createForOfIteratorHelperLoose.js", "import": "./src/helpers/esm/createForOfIteratorHelperLoose.js", "default": "./src/helpers/createForOfIteratorHelperLoose.js"}, "./src/helpers/createForOfIteratorHelperLoose.js"], "./helpers/esm/createForOfIteratorHelperLoose": "./src/helpers/esm/createForOfIteratorHelperLoose.js", "./helpers/createSuper": [{"node": "./src/helpers/createSuper.js", "import": "./src/helpers/esm/createSuper.js", "default": "./src/helpers/createSuper.js"}, "./src/helpers/createSuper.js"], "./helpers/esm/createSuper": "./src/helpers/esm/createSuper.js", "./helpers/decorate": [{"node": "./src/helpers/decorate.js", "import": "./src/helpers/esm/decorate.js", "default": "./src/helpers/decorate.js"}, "./src/helpers/decorate.js"], "./helpers/esm/decorate": "./src/helpers/esm/decorate.js", "./helpers/decorateMetadata": [{"node": "./src/helpers/decorateMetadata.js", "import": "./src/helpers/esm/decorateMetadata.js", "default": "./src/helpers/decorateMetadata.js"}, "./src/helpers/decorateMetadata.js"], "./helpers/esm/decorateMetadata": "./src/helpers/esm/decorateMetadata.js", "./helpers/decorateParam": [{"node": "./src/helpers/decorateParam.js", "import": "./src/helpers/esm/decorateParam.js", "default": "./src/helpers/decorateParam.js"}, "./src/helpers/decorateParam.js"], "./helpers/defaults": [{"node": "./src/helpers/defaults.js", "import": "./src/helpers/esm/defaults.js", "default": "./src/helpers/defaults.js"}, "./src/helpers/defaults.js"], "./helpers/esm/defaults": "./src/helpers/esm/defaults.js", "./helpers/defineAccessor": [{"node": "./src/helpers/defineAccessor.js", "import": "./src/helpers/esm/defineAccessor.js", "default": "./src/helpers/defineAccessor.js"}, "./src/helpers/defineAccessor.js"], "./helpers/esm/defineAccessor": "./src/helpers/esm/defineAccessor.js", "./helpers/defineProperty": [{"node": "./src/helpers/defineProperty.js", "import": "./src/helpers/esm/defineProperty.js", "default": "./src/helpers/defineProperty.js"}, "./src/helpers/defineProperty.js"], "./helpers/esm/defineProperty": "./src/helpers/esm/defineProperty.js", "./helpers/extends": [{"node": "./src/helpers/extends.js", "import": "./src/helpers/esm/extends.js", "default": "./src/helpers/extends.js"}, "./src/helpers/extends.js"], "./helpers/esm/extends": "./src/helpers/esm/extends.js", "./helpers/get": [{"node": "./src/helpers/get.js", "import": "./src/helpers/esm/get.js", "default": "./src/helpers/get.js"}, "./src/helpers/get.js"], "./helpers/esm/get": "./src/helpers/esm/get.js", "./helpers/getPrototypeOf": [{"node": "./src/helpers/getPrototypeOf.js", "import": "./src/helpers/esm/getPrototypeOf.js", "default": "./src/helpers/getPrototypeOf.js"}, "./src/helpers/getPrototypeOf.js"], "./helpers/esm/getPrototypeOf": "./src/helpers/esm/getPrototypeOf.js", "./helpers/identity": [{"node": "./src/helpers/identity.js", "import": "./src/helpers/esm/identity.js", "default": "./src/helpers/identity.js"}, "./src/helpers/identity.js"], "./helpers/esm/identity": "./src/helpers/esm/identity.js", "./helpers/importDeferProxy": [{"node": "./src/helpers/importDeferProxy.js", "import": "./src/helpers/esm/importDeferProxy.js", "default": "./src/helpers/importDeferProxy.js"}, "./src/helpers/importDeferProxy.js"], "./helpers/esm/importDeferProxy": "./src/helpers/esm/importDeferProxy.js", "./helpers/inherits": [{"node": "./src/helpers/inherits.js", "import": "./src/helpers/esm/inherits.js", "default": "./src/helpers/inherits.js"}, "./src/helpers/inherits.js"], "./helpers/esm/inherits": "./src/helpers/esm/inherits.js", "./helpers/inheritsLoose": [{"node": "./src/helpers/inheritsLoose.js", "import": "./src/helpers/esm/inheritsLoose.js", "default": "./src/helpers/inheritsLoose.js"}, "./src/helpers/inheritsLoose.js"], "./helpers/esm/inheritsLoose": "./src/helpers/esm/inheritsLoose.js", "./helpers/initializerDefineProperty": [{"node": "./src/helpers/initializerDefineProperty.js", "import": "./src/helpers/esm/initializerDefineProperty.js", "default": "./src/helpers/initializerDefineProperty.js"}, "./src/helpers/initializerDefineProperty.js"], "./helpers/esm/initializerDefineProperty": "./src/helpers/esm/initializerDefineProperty.js", "./helpers/initializerWarningHelper": [{"node": "./src/helpers/initializerWarningHelper.js", "import": "./src/helpers/esm/initializerWarningHelper.js", "default": "./src/helpers/initializerWarningHelper.js"}, "./src/helpers/initializerWarningHelper.js"], "./helpers/esm/initializerWarningHelper": "./src/helpers/esm/initializerWarningHelper.js", "./helpers/instanceof": [{"node": "./src/helpers/instanceof.js", "import": "./src/helpers/esm/instanceof.js", "default": "./src/helpers/instanceof.js"}, "./src/helpers/instanceof.js"], "./helpers/esm/instanceof": "./src/helpers/esm/instanceof.js", "./helpers/interopRequireDefault": [{"node": "./src/helpers/interopRequireDefault.js", "import": "./src/helpers/esm/interopRequireDefault.js", "default": "./src/helpers/interopRequireDefault.js"}, "./src/helpers/interopRequireDefault.js"], "./helpers/esm/interopRequireDefault": "./src/helpers/esm/interopRequireDefault.js", "./helpers/interopRequireWildcard": [{"node": "./src/helpers/interopRequireWildcard.js", "import": "./src/helpers/esm/interopRequireWildcard.js", "default": "./src/helpers/interopRequireWildcard.js"}, "./src/helpers/interopRequireWildcard.js"], "./helpers/esm/interopRequireWildcard": "./src/helpers/esm/interopRequireWildcard.js", "./helpers/isNativeFunction": [{"node": "./src/helpers/isNativeFunction.js", "import": "./src/helpers/esm/isNativeFunction.js", "default": "./src/helpers/isNativeFunction.js"}, "./src/helpers/isNativeFunction.js"], "./helpers/esm/isNativeFunction": "./src/helpers/esm/isNativeFunction.js", "./helpers/isNativeReflectConstruct": [{"node": "./src/helpers/isNativeReflectConstruct.js", "import": "./src/helpers/esm/isNativeReflectConstruct.js", "default": "./src/helpers/isNativeReflectConstruct.js"}, "./src/helpers/isNativeReflectConstruct.js"], "./helpers/esm/isNativeReflectConstruct": "./src/helpers/esm/isNativeReflectConstruct.js", "./helpers/iterableToArray": [{"node": "./src/helpers/iterableToArray.js", "import": "./src/helpers/esm/iterableToArray.js", "default": "./src/helpers/iterableToArray.js"}, "./src/helpers/iterableToArray.js"], "./helpers/esm/iterableToArray": "./src/helpers/esm/iterableToArray.js", "./helpers/iterableToArrayLimit": [{"node": "./src/helpers/iterableToArrayLimit.js", "import": "./src/helpers/esm/iterableToArrayLimit.js", "default": "./src/helpers/iterableToArrayLimit.js"}, "./src/helpers/iterableToArrayLimit.js"], "./helpers/esm/iterableToArrayLimit": "./src/helpers/esm/iterableToArrayLimit.js", "./helpers/jsx": [{"node": "./src/helpers/jsx.js", "import": "./src/helpers/esm/jsx.js", "default": "./src/helpers/jsx.js"}, "./src/helpers/jsx.js"], "./helpers/esm/jsx": "./src/helpers/esm/jsx.js", "./helpers/maybeArrayLike": [{"node": "./src/helpers/maybeArrayLike.js", "import": "./src/helpers/esm/maybeArrayLike.js", "default": "./src/helpers/maybeArrayLike.js"}, "./src/helpers/maybeArrayLike.js"], "./helpers/esm/maybeArrayLike": "./src/helpers/esm/maybeArrayLike.js", "./helpers/newArrowCheck": [{"node": "./src/helpers/newArrowCheck.js", "import": "./src/helpers/esm/newArrowCheck.js", "default": "./src/helpers/newArrowCheck.js"}, "./src/helpers/newArrowCheck.js"], "./helpers/esm/newArrowCheck": "./src/helpers/esm/newArrowCheck.js", "./helpers/nonIterableRest": [{"node": "./src/helpers/nonIterableRest.js", "import": "./src/helpers/esm/nonIterableRest.js", "default": "./src/helpers/nonIterableRest.js"}, "./src/helpers/nonIterableRest.js"], "./helpers/esm/nonIterableRest": "./src/helpers/esm/nonIterableRest.js", "./helpers/nonIterableSpread": [{"node": "./src/helpers/nonIterableSpread.js", "import": "./src/helpers/esm/nonIterableSpread.js", "default": "./src/helpers/nonIterableSpread.js"}, "./src/helpers/nonIterableSpread.js"], "./helpers/esm/nonIterableSpread": "./src/helpers/esm/nonIterableSpread.js", "./helpers/nullishReceiverError": [{"node": "./src/helpers/nullishReceiverError.js", "import": "./src/helpers/esm/nullishReceiverError.js", "default": "./src/helpers/nullishReceiverError.js"}, "./src/helpers/nullishReceiverError.js"], "./helpers/esm/nullishReceiverError": "./src/helpers/esm/nullishReceiverError.js", "./helpers/objectDestructuringEmpty": [{"node": "./src/helpers/objectDestructuringEmpty.js", "import": "./src/helpers/esm/objectDestructuringEmpty.js", "default": "./src/helpers/objectDestructuringEmpty.js"}, "./src/helpers/objectDestructuringEmpty.js"], "./helpers/esm/objectDestructuringEmpty": "./src/helpers/esm/objectDestructuringEmpty.js", "./helpers/objectSpread2": [{"node": "./src/helpers/objectSpread2.js", "import": "./src/helpers/esm/objectSpread2.js", "default": "./src/helpers/objectSpread2.js"}, "./src/helpers/objectSpread2.js"], "./helpers/esm/objectSpread2": "./src/helpers/esm/objectSpread2.js", "./helpers/objectWithoutProperties": [{"node": "./src/helpers/objectWithoutProperties.js", "import": "./src/helpers/esm/objectWithoutProperties.js", "default": "./src/helpers/objectWithoutProperties.js"}, "./src/helpers/objectWithoutProperties.js"], "./helpers/esm/objectWithoutProperties": "./src/helpers/esm/objectWithoutProperties.js", "./helpers/objectWithoutPropertiesLoose": [{"node": "./src/helpers/objectWithoutPropertiesLoose.js", "import": "./src/helpers/esm/objectWithoutPropertiesLoose.js", "default": "./src/helpers/objectWithoutPropertiesLoose.js"}, "./src/helpers/objectWithoutPropertiesLoose.js"], "./helpers/esm/objectWithoutPropertiesLoose": "./src/helpers/esm/objectWithoutPropertiesLoose.js", "./helpers/possibleConstructorReturn": [{"node": "./src/helpers/possibleConstructorReturn.js", "import": "./src/helpers/esm/possibleConstructorReturn.js", "default": "./src/helpers/possibleConstructorReturn.js"}, "./src/helpers/possibleConstructorReturn.js"], "./helpers/esm/possibleConstructorReturn": "./src/helpers/esm/possibleConstructorReturn.js", "./helpers/readOnlyError": [{"node": "./src/helpers/readOnlyError.js", "import": "./src/helpers/esm/readOnlyError.js", "default": "./src/helpers/readOnlyError.js"}, "./src/helpers/readOnlyError.js"], "./helpers/esm/readOnlyError": "./src/helpers/esm/readOnlyError.js", "./helpers/regeneratorRuntime": [{"node": "./src/helpers/regeneratorRuntime.js", "import": "./src/helpers/esm/regeneratorRuntime.js", "default": "./src/helpers/regeneratorRuntime.js"}, "./src/helpers/regeneratorRuntime.js"], "./helpers/esm/regeneratorRuntime": "./src/helpers/esm/regeneratorRuntime.js", "./helpers/set": [{"node": "./src/helpers/set.js", "import": "./src/helpers/esm/set.js", "default": "./src/helpers/set.js"}, "./src/helpers/set.js"], "./helpers/esm/set": "./src/helpers/esm/set.js", "./helpers/setFunctionName": [{"node": "./src/helpers/setFunctionName.js", "import": "./src/helpers/esm/setFunctionName.js", "default": "./src/helpers/setFunctionName.js"}, "./src/helpers/setFunctionName.js"], "./helpers/esm/setFunctionName": "./src/helpers/esm/setFunctionName.js", "./helpers/setPrototypeOf": [{"node": "./src/helpers/setPrototypeOf.js", "import": "./src/helpers/esm/setPrototypeOf.js", "default": "./src/helpers/setPrototypeOf.js"}, "./src/helpers/setPrototypeOf.js"], "./helpers/esm/setPrototypeOf": "./src/helpers/esm/setPrototypeOf.js", "./helpers/skipFirstGeneratorNext": [{"node": "./src/helpers/skipFirstGeneratorNext.js", "import": "./src/helpers/esm/skipFirstGeneratorNext.js", "default": "./src/helpers/skipFirstGeneratorNext.js"}, "./src/helpers/skipFirstGeneratorNext.js"], "./helpers/esm/skipFirstGeneratorNext": "./src/helpers/esm/skipFirstGeneratorNext.js", "./helpers/slicedToArray": [{"node": "./src/helpers/slicedToArray.js", "import": "./src/helpers/esm/slicedToArray.js", "default": "./src/helpers/slicedToArray.js"}, "./src/helpers/slicedToArray.js"], "./helpers/esm/slicedToArray": "./src/helpers/esm/slicedToArray.js", "./helpers/superPropBase": [{"node": "./src/helpers/superPropBase.js", "import": "./src/helpers/esm/superPropBase.js", "default": "./src/helpers/superPropBase.js"}, "./src/helpers/superPropBase.js"], "./helpers/esm/superPropBase": "./src/helpers/esm/superPropBase.js", "./helpers/superPropGet": [{"node": "./src/helpers/superPropGet.js", "import": "./src/helpers/esm/superPropGet.js", "default": "./src/helpers/superPropGet.js"}, "./src/helpers/superPropGet.js"], "./helpers/esm/superPropGet": "./src/helpers/esm/superPropGet.js", "./helpers/superPropSet": [{"node": "./src/helpers/superPropSet.js", "import": "./src/helpers/esm/superPropSet.js", "default": "./src/helpers/superPropSet.js"}, "./src/helpers/superPropSet.js"], "./helpers/esm/superPropSet": "./src/helpers/esm/superPropSet.js", "./helpers/taggedTemplateLiteral": [{"node": "./src/helpers/taggedTemplateLiteral.js", "import": "./src/helpers/esm/taggedTemplateLiteral.js", "default": "./src/helpers/taggedTemplateLiteral.js"}, "./src/helpers/taggedTemplateLiteral.js"], "./helpers/esm/taggedTemplateLiteral": "./src/helpers/esm/taggedTemplateLiteral.js", "./helpers/taggedTemplateLiteralLoose": [{"node": "./src/helpers/taggedTemplateLiteralLoose.js", "import": "./src/helpers/esm/taggedTemplateLiteralLoose.js", "default": "./src/helpers/taggedTemplateLiteralLoose.js"}, "./src/helpers/taggedTemplateLiteralLoose.js"], "./helpers/esm/taggedTemplateLiteralLoose": "./src/helpers/esm/taggedTemplateLiteralLoose.js", "./helpers/tdz": [{"node": "./src/helpers/tdz.js", "import": "./src/helpers/esm/tdz.js", "default": "./src/helpers/tdz.js"}, "./src/helpers/tdz.js"], "./helpers/esm/tdz": "./src/helpers/esm/tdz.js", "./helpers/temporalRef": [{"node": "./src/helpers/temporalRef.js", "import": "./src/helpers/esm/temporalRef.js", "default": "./src/helpers/temporalRef.js"}, "./src/helpers/temporalRef.js"], "./helpers/esm/temporalRef": "./src/helpers/esm/temporalRef.js", "./helpers/temporalUndefined": [{"node": "./src/helpers/temporalUndefined.js", "import": "./src/helpers/esm/temporalUndefined.js", "default": "./src/helpers/temporalUndefined.js"}, "./src/helpers/temporalUndefined.js"], "./helpers/esm/temporalUndefined": "./src/helpers/esm/temporalUndefined.js", "./helpers/toArray": [{"node": "./src/helpers/toArray.js", "import": "./src/helpers/esm/toArray.js", "default": "./src/helpers/toArray.js"}, "./src/helpers/toArray.js"], "./helpers/esm/toArray": "./src/helpers/esm/toArray.js", "./helpers/toConsumableArray": [{"node": "./src/helpers/toConsumableArray.js", "import": "./src/helpers/esm/toConsumableArray.js", "default": "./src/helpers/toConsumableArray.js"}, "./src/helpers/toConsumableArray.js"], "./helpers/esm/toConsumableArray": "./src/helpers/esm/toConsumableArray.js", "./helpers/toPrimitive": [{"node": "./src/helpers/toPrimitive.js", "import": "./src/helpers/esm/toPrimitive.js", "default": "./src/helpers/toPrimitive.js"}, "./src/helpers/toPrimitive.js"], "./helpers/esm/toPrimitive": "./src/helpers/esm/toPrimitive.js", "./helpers/toPropertyKey": [{"node": "./src/helpers/toPropertyKey.js", "import": "./src/helpers/esm/toPropertyKey.js", "default": "./src/helpers/toPropertyKey.js"}, "./src/helpers/toPropertyKey.js"], "./helpers/esm/toPropertyKey": "./src/helpers/esm/toPropertyKey.js", "./helpers/toSetter": [{"node": "./src/helpers/toSetter.js", "import": "./src/helpers/esm/toSetter.js", "default": "./src/helpers/toSetter.js"}, "./src/helpers/toSetter.js"], "./helpers/esm/toSetter": "./src/helpers/esm/toSetter.js", "./helpers/typeof": [{"node": "./src/helpers/typeof.js", "import": "./src/helpers/esm/typeof.js", "default": "./src/helpers/typeof.js"}, "./src/helpers/typeof.js"], "./helpers/esm/typeof": "./src/helpers/esm/typeof.js", "./helpers/unsupportedIterableToArray": [{"node": "./src/helpers/unsupportedIterableToArray.js", "import": "./src/helpers/esm/unsupportedIterableToArray.js", "default": "./src/helpers/unsupportedIterableToArray.js"}, "./src/helpers/unsupportedIterableToArray.js"], "./helpers/esm/unsupportedIterableToArray": "./src/helpers/esm/unsupportedIterableToArray.js", "./helpers/usingCtx": [{"node": "./src/helpers/usingCtx.js", "import": "./src/helpers/esm/usingCtx.js", "default": "./src/helpers/usingCtx.js"}, "./src/helpers/usingCtx.js"], "./helpers/esm/usingCtx": "./src/helpers/esm/usingCtx.js", "./helpers/wrapAsyncGenerator": [{"node": "./src/helpers/wrapAsyncGenerator.js", "import": "./src/helpers/esm/wrapAsyncGenerator.js", "default": "./src/helpers/wrapAsyncGenerator.js"}, "./src/helpers/wrapAsyncGenerator.js"], "./helpers/esm/wrapAsyncGenerator": "./src/helpers/esm/wrapAsyncGenerator.js", "./helpers/wrapNativeSuper": [{"node": "./src/helpers/wrapNativeSuper.js", "import": "./src/helpers/esm/wrapNativeSuper.js", "default": "./src/helpers/wrapNativeSuper.js"}, "./src/helpers/wrapNativeSuper.js"], "./helpers/esm/wrapNativeSuper": "./src/helpers/esm/wrapNativeSuper.js", "./helpers/wrapRegExp": [{"node": "./src/helpers/wrapRegExp.js", "import": "./src/helpers/esm/wrapRegExp.js", "default": "./src/helpers/wrapRegExp.js"}, "./src/helpers/wrapRegExp.js"], "./helpers/esm/wrapRegExp": "./src/helpers/esm/wrapRegExp.js", "./helpers/writeOnlyError": [{"node": "./src/helpers/writeOnlyError.js", "import": "./src/helpers/esm/writeOnlyError.js", "default": "./src/helpers/writeOnlyError.js"}, "./src/helpers/writeOnlyError.js"], "./helpers/esm/writeOnlyError": "./src/helpers/esm/writeOnlyError.js", "./helpers/AwaitValue": [{"node": "./src/helpers/AwaitValue.js", "import": "./src/helpers/esm/AwaitValue.js", "default": "./src/helpers/AwaitValue.js"}, "./src/helpers/AwaitValue.js"], "./helpers/esm/AwaitValue": "./src/helpers/esm/AwaitValue.js", "./helpers/applyDecs": [{"node": "./src/helpers/applyDecs.js", "import": "./src/helpers/esm/applyDecs.js", "default": "./src/helpers/applyDecs.js"}, "./src/helpers/applyDecs.js"], "./helpers/esm/applyDecs": "./src/helpers/esm/applyDecs.js", "./helpers/applyDecs2203": [{"node": "./src/helpers/applyDecs2203.js", "import": "./src/helpers/esm/applyDecs2203.js", "default": "./src/helpers/applyDecs2203.js"}, "./src/helpers/applyDecs2203.js"], "./helpers/esm/applyDecs2203": "./src/helpers/esm/applyDecs2203.js", "./helpers/applyDecs2203R": [{"node": "./src/helpers/applyDecs2203R.js", "import": "./src/helpers/esm/applyDecs2203R.js", "default": "./src/helpers/applyDecs2203R.js"}, "./src/helpers/applyDecs2203R.js"], "./helpers/esm/applyDecs2203R": "./src/helpers/esm/applyDecs2203R.js", "./helpers/applyDecs2301": [{"node": "./src/helpers/applyDecs2301.js", "import": "./src/helpers/esm/applyDecs2301.js", "default": "./src/helpers/applyDecs2301.js"}, "./src/helpers/applyDecs2301.js"], "./helpers/esm/applyDecs2301": "./src/helpers/esm/applyDecs2301.js", "./helpers/applyDecs2305": [{"node": "./src/helpers/applyDecs2305.js", "import": "./src/helpers/esm/applyDecs2305.js", "default": "./src/helpers/applyDecs2305.js"}, "./src/helpers/applyDecs2305.js"], "./helpers/esm/applyDecs2305": "./src/helpers/esm/applyDecs2305.js", "./helpers/classApplyDescriptorDestructureSet": [{"node": "./src/helpers/classApplyDescriptorDestructureSet.js", "import": "./src/helpers/esm/classApplyDescriptorDestructureSet.js", "default": "./src/helpers/classApplyDescriptorDestructureSet.js"}, "./src/helpers/classApplyDescriptorDestructureSet.js"], "./helpers/esm/classApplyDescriptorDestructureSet": "./src/helpers/esm/classApplyDescriptorDestructureSet.js", "./helpers/classApplyDescriptorGet": [{"node": "./src/helpers/classApplyDescriptorGet.js", "import": "./src/helpers/esm/classApplyDescriptorGet.js", "default": "./src/helpers/classApplyDescriptorGet.js"}, "./src/helpers/classApplyDescriptorGet.js"], "./helpers/esm/classApplyDescriptorGet": "./src/helpers/esm/classApplyDescriptorGet.js", "./helpers/classApplyDescriptorSet": [{"node": "./src/helpers/classApplyDescriptorSet.js", "import": "./src/helpers/esm/classApplyDescriptorSet.js", "default": "./src/helpers/classApplyDescriptorSet.js"}, "./src/helpers/classApplyDescriptorSet.js"], "./helpers/esm/classApplyDescriptorSet": "./src/helpers/esm/classApplyDescriptorSet.js", "./helpers/classCheckPrivateStaticAccess": [{"node": "./src/helpers/classCheckPrivateStaticAccess.js", "import": "./src/helpers/esm/classCheckPrivateStaticAccess.js", "default": "./src/helpers/classCheckPrivateStaticAccess.js"}, "./src/helpers/classCheckPrivateStaticAccess.js"], "./helpers/esm/classCheckPrivateStaticAccess": "./src/helpers/esm/classCheckPrivateStaticAccess.js", "./helpers/classCheckPrivateStaticFieldDescriptor": [{"node": "./src/helpers/classCheckPrivateStaticFieldDescriptor.js", "import": "./src/helpers/esm/classCheckPrivateStaticFieldDescriptor.js", "default": "./src/helpers/classCheckPrivateStaticFieldDescriptor.js"}, "./src/helpers/classCheckPrivateStaticFieldDescriptor.js"], "./helpers/esm/classCheckPrivateStaticFieldDescriptor": "./src/helpers/esm/classCheckPrivateStaticFieldDescriptor.js", "./helpers/classExtractFieldDescriptor": [{"node": "./src/helpers/classExtractFieldDescriptor.js", "import": "./src/helpers/esm/classExtractFieldDescriptor.js", "default": "./src/helpers/classExtractFieldDescriptor.js"}, "./src/helpers/classExtractFieldDescriptor.js"], "./helpers/esm/classExtractFieldDescriptor": "./src/helpers/esm/classExtractFieldDescriptor.js", "./helpers/classPrivateFieldDestructureSet": [{"node": "./src/helpers/classPrivateFieldDestructureSet.js", "import": "./src/helpers/esm/classPrivateFieldDestructureSet.js", "default": "./src/helpers/classPrivateFieldDestructureSet.js"}, "./src/helpers/classPrivateFieldDestructureSet.js"], "./helpers/esm/classPrivateFieldDestructureSet": "./src/helpers/esm/classPrivateFieldDestructureSet.js", "./helpers/classPrivateFieldGet": [{"node": "./src/helpers/classPrivateFieldGet.js", "import": "./src/helpers/esm/classPrivateFieldGet.js", "default": "./src/helpers/classPrivateFieldGet.js"}, "./src/helpers/classPrivateFieldGet.js"], "./helpers/esm/classPrivateFieldGet": "./src/helpers/esm/classPrivateFieldGet.js", "./helpers/classPrivateFieldSet": [{"node": "./src/helpers/classPrivateFieldSet.js", "import": "./src/helpers/esm/classPrivateFieldSet.js", "default": "./src/helpers/classPrivateFieldSet.js"}, "./src/helpers/classPrivateFieldSet.js"], "./helpers/esm/classPrivateFieldSet": "./src/helpers/esm/classPrivateFieldSet.js", "./helpers/classPrivateMethodGet": [{"node": "./src/helpers/classPrivateMethodGet.js", "import": "./src/helpers/esm/classPrivateMethodGet.js", "default": "./src/helpers/classPrivateMethodGet.js"}, "./src/helpers/classPrivateMethodGet.js"], "./helpers/esm/classPrivateMethodGet": "./src/helpers/esm/classPrivateMethodGet.js", "./helpers/classPrivateMethodSet": [{"node": "./src/helpers/classPrivateMethodSet.js", "import": "./src/helpers/esm/classPrivateMethodSet.js", "default": "./src/helpers/classPrivateMethodSet.js"}, "./src/helpers/classPrivateMethodSet.js"], "./helpers/esm/classPrivateMethodSet": "./src/helpers/esm/classPrivateMethodSet.js", "./helpers/classStaticPrivateFieldDestructureSet": [{"node": "./src/helpers/classStaticPrivateFieldDestructureSet.js", "import": "./src/helpers/esm/classStaticPrivateFieldDestructureSet.js", "default": "./src/helpers/classStaticPrivateFieldDestructureSet.js"}, "./src/helpers/classStaticPrivateFieldDestructureSet.js"], "./helpers/esm/classStaticPrivateFieldDestructureSet": "./src/helpers/esm/classStaticPrivateFieldDestructureSet.js", "./helpers/classStaticPrivateFieldSpecGet": [{"node": "./src/helpers/classStaticPrivateFieldSpecGet.js", "import": "./src/helpers/esm/classStaticPrivateFieldSpecGet.js", "default": "./src/helpers/classStaticPrivateFieldSpecGet.js"}, "./src/helpers/classStaticPrivateFieldSpecGet.js"], "./helpers/esm/classStaticPrivateFieldSpecGet": "./src/helpers/esm/classStaticPrivateFieldSpecGet.js", "./helpers/classStaticPrivateFieldSpecSet": [{"node": "./src/helpers/classStaticPrivateFieldSpecSet.js", "import": "./src/helpers/esm/classStaticPrivateFieldSpecSet.js", "default": "./src/helpers/classStaticPrivateFieldSpecSet.js"}, "./src/helpers/classStaticPrivateFieldSpecSet.js"], "./helpers/esm/classStaticPrivateFieldSpecSet": "./src/helpers/esm/classStaticPrivateFieldSpecSet.js", "./helpers/classStaticPrivateMethodSet": [{"node": "./src/helpers/classStaticPrivateMethodSet.js", "import": "./src/helpers/esm/classStaticPrivateMethodSet.js", "default": "./src/helpers/classStaticPrivateMethodSet.js"}, "./src/helpers/classStaticPrivateMethodSet.js"], "./helpers/esm/classStaticPrivateMethodSet": "./src/helpers/esm/classStaticPrivateMethodSet.js", "./helpers/defineEnumerableProperties": [{"node": "./src/helpers/defineEnumerableProperties.js", "import": "./src/helpers/esm/defineEnumerableProperties.js", "default": "./src/helpers/defineEnumerableProperties.js"}, "./src/helpers/defineEnumerableProperties.js"], "./helpers/esm/defineEnumerableProperties": "./src/helpers/esm/defineEnumerableProperties.js", "./helpers/dispose": [{"node": "./src/helpers/dispose.js", "import": "./src/helpers/esm/dispose.js", "default": "./src/helpers/dispose.js"}, "./src/helpers/dispose.js"], "./helpers/esm/dispose": "./src/helpers/esm/dispose.js", "./helpers/objectSpread": [{"node": "./src/helpers/objectSpread.js", "import": "./src/helpers/esm/objectSpread.js", "default": "./src/helpers/objectSpread.js"}, "./src/helpers/objectSpread.js"], "./helpers/esm/objectSpread": "./src/helpers/esm/objectSpread.js", "./helpers/using": [{"node": "./src/helpers/using.js", "import": "./src/helpers/esm/using.js", "default": "./src/helpers/using.js"}, "./src/helpers/using.js"], "./helpers/esm/using": "./src/helpers/esm/using.js", "./package": "./package.json", "./package.json": "./package.json", "./regenerator": "./regenerator/index.js", "./regenerator/*.js": "./regenerator/*.js", "./regenerator/": "./regenerator/"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}