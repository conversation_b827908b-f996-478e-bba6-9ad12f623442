import type * as Rolldown from 'rolldown'

export * from 'rolldown'

/** @deprecated use RolldownBuild instead */
export type RollupBuild = Rolldown.RolldownBuild

/** @deprecated use RolldownOptions instead */
export type RollupOptions = Rolldown.RolldownOptions

/** @deprecated use RolldownOutput instead */
export type RollupOutput = Rolldown.RolldownOutput

/** @deprecated use RolldownPlugin instead */
export type RollupPlugin = Rolldown.RolldownPlugin

/** @deprecated use RolldownPluginOption instead */
export type RollupPluginOption = Rolldown.RolldownPluginOption

/** @deprecated use RolldownWatcher instead */
export type RollupWatcher = Rolldown.RolldownWatcher

/** @deprecated use RollupWatcherEvent instead */
export type RollupWatcherEvent = Rolldown.RolldownWatcherEvent
