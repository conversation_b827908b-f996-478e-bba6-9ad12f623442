declare namespace _default {
    export let deleted: string;
    let _var: string;
    export { _var as var };
    export let err: string;
    export let kwd: string;
    export let num: string;
    let _class: string;
    export { _class as class };
    export let cmnt: string;
    export let insert: string;
    export let str: string;
    export let bool: string;
    export let type: string;
    export let oper: string;
    export let section: string;
    export let func: string;
}
export default _default;
//# sourceMappingURL=atom-dark.d.ts.map