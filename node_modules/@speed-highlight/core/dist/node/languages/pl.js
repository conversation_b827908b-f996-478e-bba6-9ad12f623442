var o=Object.defineProperty;var u=Object.getOwnPropertyDescriptor;var s=Object.getOwnPropertyNames;var l=Object.prototype.hasOwnProperty;var c=(t,e)=>{for(var n in e)o(t,n,{get:e[n],enumerable:!0})},d=(t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of s(e))!l.call(t,a)&&a!==n&&o(t,a,{get:()=>e[a],enumerable:!(r=u(e,a))||r.enumerable});return t};var i=t=>d(o({},"__esModule",{value:!0}),t);var p={};c(p,{default:()=>h});module.exports=i(p);var h=[{match:/#.*/g,sub:"todo"},{type:"str",match:/(["'])(\\[^]|(?!\1)[^])*\1?/g},{expand:"num"},{type:"kwd",match:/\b(any|break|continue|default|delete|die|do|else|elsif|eval|for|foreach|given|goto|if|last|local|my|next|our|package|print|redo|require|return|say|state|sub|switch|undef|unless|until|use|when|while|not|and|or|xor)\b/g},{type:"oper",match:/[-+*/%~!&<>|=?,]+/g},{type:"func",match:/[a-z_]+(?=\s*\()/g}];
