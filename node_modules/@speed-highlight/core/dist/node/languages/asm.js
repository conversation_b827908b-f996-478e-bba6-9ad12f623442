var p=Object.defineProperty;var d=Object.getOwnPropertyDescriptor;var g=Object.getOwnPropertyNames;var h=Object.prototype.hasOwnProperty;var n=(a,t)=>{for(var e in t)p(a,e,{get:t[e],enumerable:!0})},y=(a,t,e,c)=>{if(t&&typeof t=="object"||typeof t=="function")for(let m of g(t))!h.call(a,m)&&m!==e&&p(a,m,{get:()=>t[m],enumerable:!(c=d(t,m))||c.enumerable});return a};var u=a=>y(p({},"__esModule",{value:!0}),a);var b={};n(b,{default:()=>z});module.exports=u(b);var z=[{type:"cmnt",match:/(;|#).*/gm},{expand:"str"},{expand:"num"},{type:"num",match:/\$[\da-fA-F]*\b/g},{type:"kwd",match:/^[a-z]+\s+[a-z.]+\b/gm,sub:[{type:"func",match:/^[a-z]+/g}]},{type:"kwd",match:/^\t*[a-z][a-z\d]*\b/gm},{match:/%|\$/g,type:"oper"}];
