var e=Object.defineProperty;var h=Object.getOwnPropertyDescriptor;var g=Object.getOwnPropertyNames;var n=Object.prototype.hasOwnProperty;var u=(a,t)=>{for(var c in t)e(a,c,{get:t[c],enumerable:!0})},b=(a,t,c,p)=>{if(t&&typeof t=="object"||typeof t=="function")for(let m of g(t))!n.call(a,m)&&m!==c&&e(a,m,{get:()=>t[m],enumerable:!(p=h(t,m))||p.enumerable});return a};var y=a=>b(e({},"__esModule",{value:!0}),a);var d={};u(d,{default:()=>r});module.exports=y(d);var r=[{match:/\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{type:"kwd",match:/@\w+\b|\b(and|not|only|or)\b|\b[a-z-]+(?=[^{}]*{)/g},{type:"var",match:/\b[\w-]+(?=\s*:)|(::?|\.)[\w-]+(?=[^{}]*{)/g},{type:"func",match:/#[\w-]+(?=[^{}]*{)/g},{type:"num",match:/#[\da-f]{3,8}/g},{type:"num",match:/\d+(\.\d+)?(cm|mm|in|px|pt|pc|em|ex|ch|rem|vm|vh|vmin|vmax|%)?/g,sub:[{type:"var",match:/[a-z]+|%/g}]},{match:/url\([^)]*\)/g,sub:[{type:"func",match:/url(?=\()/g},{type:"str",match:/[^()]+/g}]},{type:"func",match:/\b[a-zA-Z]\w*(?=\s*\()/g},{type:"num",match:/\b[a-z-]+\b/g}];
