var E=Object.defineProperty;var a=Object.getOwnPropertyDescriptor;var c=Object.getOwnPropertyNames;var r=Object.prototype.hasOwnProperty;var m=(e,t)=>{for(var p in t)E(e,p,{get:t[p],enumerable:!0})},y=(e,t,p,I)=>{if(t&&typeof t=="object"||typeof t=="function")for(let b of c(t))!r.call(e,b)&&b!==p&&E(e,b,{get:()=>t[b],enumerable:!(I=a(t,b))||I.enumerable});return e};var G=e=>y(E({},"__esModule",{value:!0}),e);var g={};m(g,{default:()=>N,type:()=>X});module.exports=G(g);var N=[{type:"err",match:/\b(TODO|FIXME|DEBUG|OPTIMIZE|WARNING|XXX|BUG)\b/g},{type:"class",match:/\bIDEA\b/g},{type:"insert",match:/\b(CHANGED|FIX|CHANGE)\b/g},{type:"oper",match:/\bQUESTION\b/g}],X="cmnt";0&&(module.exports={type});
