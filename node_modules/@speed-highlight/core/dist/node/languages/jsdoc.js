var c=Object.defineProperty;var o=Object.getOwnPropertyDescriptor;var y=Object.getOwnPropertyNames;var b=Object.prototype.hasOwnProperty;var g=(e,t)=>{for(var a in t)c(e,a,{get:t[a],enumerable:!0})},h=(e,t,a,m)=>{if(t&&typeof t=="object"||typeof t=="function")for(let p of y(t))!b.call(e,p)&&p!==a&&c(e,p,{get:()=>t[p],enumerable:!(m=o(t,p))||m.enumerable});return e};var s=e=>h(c({},"__esModule",{value:!0}),e);var l={};g(l,{default:()=>E,type:()=>I});module.exports=s(l);var r=[{type:"err",match:/\b(TODO|FIXME|DEBUG|OPTIMIZE|WARNING|XXX|BUG)\b/g},{type:"class",match:/\bIDEA\b/g},{type:"insert",match:/\b(CHANGED|FIX|CHANGE)\b/g},{type:"oper",match:/\bQUESTION\b/g}];var E=[{type:"kwd",match:/@\w+/g},{type:"class",match:/{[\w\s|<>,.@\[\]]+}/g},{type:"var",match:/\[[\w\s="']+\]/g},...r],I="cmnt";0&&(module.exports={type});
