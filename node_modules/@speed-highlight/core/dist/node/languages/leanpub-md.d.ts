declare const _default: ({
    type: string;
    match: RegExp;
    sub?: undefined;
} | {
    match: RegExp;
    sub: (code: any) => {
        type: string;
        sub: {
            match: RegExp;
            sub: any;
        }[];
    };
    type?: undefined;
} | {
    type: string;
    match: RegExp;
    sub: ({
        type: string;
        match: RegExp;
        sub?: undefined;
    } | {
        match: RegExp;
        sub: (code: string) => ShjLanguage;
        type?: undefined;
    })[];
})[];
export default _default;
//# sourceMappingURL=leanpub-md.d.ts.map