var f=Object.defineProperty;var Vt=Object.getOwnPropertyDescriptor;var qt=Object.getOwnPropertyNames;var Qt=Object.prototype.hasOwnProperty;var F=e=>t=>{var s=e[t];if(s)return s();throw new Error("Module not found in bundle: "+t)};var a=(e,t)=>()=>(e&&(t=e(e=0)),t);var p=(e,t)=>{for(var s in t)f(e,s,{get:t[s],enumerable:!0})},Jt=(e,t,s,m)=>{if(t&&typeof t=="object"||typeof t=="function")for(let c of qt(t))!Qt.call(e,c)&&c!==s&&f(e,c,{get:()=>t[c],enumerable:!(m=Vt(t,c))||m.enumerable});return e};var te=e=>Jt(f({},"__esModule",{value:!0}),e);var $={};p($,{default:()=>ee});var ee,v=a(()=>{ee=[{type:"cmnt",match:/(;|#).*/gm},{expand:"str"},{expand:"num"},{type:"num",match:/\$[\da-fA-F]*\b/g},{type:"kwd",match:/^[a-z]+\s+[a-z.]+\b/gm,sub:[{type:"func",match:/^[a-z]+/g}]},{type:"kwd",match:/^\t*[a-z][a-z\d]*\b/gm},{match:/%|\$/g,type:"oper"}]});var G={};p(G,{default:()=>I});var B,I,N=a(()=>{B={type:"var",match:/\$\w+|\${[^}]*}|\$\([^)]*\)/g},I=[{sub:"todo",match:/#.*/g},{type:"str",match:/(["'])((?!\1)[^\r\n\\]|\\[^])*\1?/g,sub:[B]},{type:"oper",match:/(?<=\s|^)\.*\/[a-z/_.-]+/gi},{type:"kwd",match:/\s-[a-zA-Z]+|$<|[&|;]+|\b(unset|readonly|shift|export|if|fi|else|elif|while|do|done|for|until|case|esac|break|continue|exit|return|trap|wait|eval|exec|then|declare|enable|local|select|typeset|time|add|remove|install|update|delete)(?=\s|$)/g},{expand:"num"},{type:"func",match:/(?<=(^|\||\&\&|\;)\s*)[a-z_.-]+(?=\s|$)/gmi},{type:"bool",match:/(?<=\s|^)(true|false)(?=\s|$)/g},{type:"oper",match:/[=(){}<>!]+/g},{type:"var",match:/(?<=\s|^)[\w_]+(?=\s*=)/g},B]});var H={};p(H,{default:()=>ae});var ae,k=a(()=>{ae=[{match:/[^\[\->+.<\]\s].*/g,sub:"todo"},{type:"func",match:/\.+/g},{type:"kwd",match:/[<>]+/g},{type:"oper",match:/[+-]+/g}]});var _={};p(_,{default:()=>pe});var pe,z=a(()=>{pe=[{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{expand:"num"},{type:"kwd",match:/#\s*include (<.*>|".*")/g,sub:[{type:"str",match:/(<|").*/g}]},{match:/asm\s*{[^}]*}/g,sub:[{type:"kwd",match:/^asm/g},{match:/[^{}]*(?=}$)/g,sub:"asm"}]},{type:"kwd",match:/\*|&|#[a-z]+\b|\b(asm|auto|double|int|struct|break|else|long|switch|case|enum|register|typedef|char|extern|return|union|const|float|short|unsigned|continue|for|signed|void|default|goto|sizeof|volatile|do|if|static|while)\b/g},{type:"oper",match:/[/*+:?&|%^~=!,<>.^-]+/g},{type:"func",match:/[a-zA-Z_][\w_]*(?=\s*\()/g},{type:"class",match:/\b[A-Z][\w_]*\b/g}]});var Y={};p(Y,{default:()=>se});var se,Z=a(()=>{se=[{match:/\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{type:"kwd",match:/@\w+\b|\b(and|not|only|or)\b|\b[a-z-]+(?=[^{}]*{)/g},{type:"var",match:/\b[\w-]+(?=\s*:)|(::?|\.)[\w-]+(?=[^{}]*{)/g},{type:"func",match:/#[\w-]+(?=[^{}]*{)/g},{type:"num",match:/#[\da-f]{3,8}/g},{type:"num",match:/\d+(\.\d+)?(cm|mm|in|px|pt|pc|em|ex|ch|rem|vm|vh|vmin|vmax|%)?/g,sub:[{type:"var",match:/[a-z]+|%/g}]},{match:/url\([^)]*\)/g,sub:[{type:"func",match:/url(?=\()/g},{type:"str",match:/[^()]+/g}]},{type:"func",match:/\b[a-zA-Z]\w*(?=\s*\()/g},{type:"num",match:/\b[a-z-]+\b/g}]});var X={};p(X,{default:()=>ce});var ce,W=a(()=>{ce=[{expand:"strDouble"},{type:"oper",match:/,/g}]});var j={};p(j,{default:()=>A});var A,R=a(()=>{A=[{type:"deleted",match:/^[-<].*/gm},{type:"insert",match:/^[+>].*/gm},{type:"kwd",match:/!.*/gm},{type:"section",match:/^@@.*@@$|^\d.*|^([*-+])\1\1.*/gm}]});var K={};p(K,{default:()=>ne});var ne,V=a(()=>{N();ne=[{type:"kwd",match:/^(FROM|RUN|CMD|LABEL|MAINTAINER|EXPOSE|ENV|ADD|COPY|ENTRYPOINT|VOLUME|USER|WORKDIR|ARG|ONBUILD|STOPSIGNAL|HEALTHCHECK|SHELL)\b/gmi},...I]});var q={};p(q,{default:()=>me});var me,Q=a(()=>{R();me=[{match:/^#.*/gm,sub:"todo"},{expand:"str"},...A,{type:"func",match:/^(\$ )?git(\s.*)?$/gm},{type:"kwd",match:/^commit \w+$/gm}]});var J={};p(J,{default:()=>re});var re,tt=a(()=>{re=[{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{expand:"num"},{type:"kwd",match:/\*|&|\b(break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go|goto|if|import|interface|map|package|range|return|select|struct|switch|type|var)\b/g},{type:"func",match:/[a-zA-Z_][\w_]*(?=\s*\()/g},{type:"class",match:/\b[A-Z][\w_]*\b/g},{type:"oper",match:/[+\-*\/%&|^~=!<>.^-]+/g}]});var at={};p(at,{default:()=>O,name:()=>l,properties:()=>u,xmlElement:()=>E});var et,oe,l,u,E,O,L=a(()=>{et=":A-Z_a-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD",oe=et+"\\-\\.0-9\xB7\u0300-\u036F\u203F-\u2040",l=`[${et}][${oe}]*`,u=`\\s*(\\s+${l}\\s*(=\\s*([^"']\\S*|("|')(\\\\[^]|(?!\\4)[^])*\\4?)?)?\\s*)*`,E={match:RegExp(`<[/!?]?${l}${u}[/!?]?>`,"g"),sub:[{type:"var",match:RegExp(`^<[/!?]?${l}`,"g"),sub:[{type:"oper",match:/^<[\/!?]?/g}]},{type:"str",match:/=\s*([^"']\S*|("|')(\\[^]|(?!\2)[^])*\2?)/g,sub:[{type:"oper",match:/^=/g}]},{type:"oper",match:/[\/!?]?>/g},{type:"class",match:RegExp(l,"g")}]},O=[{match:/<!--((?!-->)[^])*-->/g,sub:"todo"},{type:"class",match:/<!\[CDATA\[[\s\S]*?\]\]>/gi},E,{type:"str",match:RegExp(`<\\?${l}([^?]|\\?[^?>])*\\?+>`,"g"),sub:[{type:"var",match:RegExp(`^<\\?${l}`,"g"),sub:[{type:"oper",match:/^<\?/g}]},{type:"oper",match:/\?+>$/g}]},{type:"var",match:/&(#x?)?[\da-z]{1,8};/gi}]});var pt={};p(pt,{default:()=>Ee});var Ee,st=a(()=>{L();Ee=[{type:"class",match:/<!DOCTYPE("[^"]*"|'[^']*'|[^"'>])*>/gi,sub:[{type:"str",match:/"[^"]*"|'[^']*'/g},{type:"oper",match:/^<!|>$/g},{type:"var",match:/DOCTYPE/gi}]},{match:RegExp(`<style${u}>((?!</style>)[^])*</style\\s*>`,"g"),sub:[{match:RegExp(`^<style${u}>`,"g"),sub:E.sub},{match:RegExp(`${E.match}|[^]*(?=</style\\s*>$)`,"g"),sub:"css"},E]},{match:RegExp(`<script${u}>((?!</script>)[^])*</script\\s*>`,"g"),sub:[{match:RegExp(`^<script${u}>`,"g"),sub:E.sub},{match:RegExp(`${E.match}|[^]*(?=</script\\s*>$)`,"g"),sub:"js"},E]},...O]});var le,h,d=a(()=>{le=[["bash",[/#!(\/usr)?\/bin\/bash/g,500],[/\b(if|elif|then|fi|echo)\b|\$/g,10]],["html",[/<\/?[a-z-]+[^\n>]*>/g,10],[/^\s+<!DOCTYPE\s+html/g,500]],["http",[/^(GET|HEAD|POST|PUT|DELETE|PATCH|HTTP)\b/g,500]],["js",[/\b(console|await|async|function|export|import|this|class|for|let|const|map|join|require)\b/g,10]],["ts",[/\b(console|await|async|function|export|import|this|class|for|let|const|map|join|require|implements|interface|namespace)\b/g,10]],["py",[/\b(def|print|class|and|or|lambda)\b/g,10]],["sql",[/\b(SELECT|INSERT|FROM)\b/g,50]],["pl",[/#!(\/usr)?\/bin\/perl/g,500],[/\b(use|print)\b|\$/g,10]],["lua",[/#!(\/usr)?\/bin\/lua/g,500]],["make",[/\b(ifneq|endif|if|elif|then|fi|echo|.PHONY|^[a-z]+ ?:$)\b|\$/gm,10]],["uri",[/https?:|mailto:|tel:|ftp:/g,30]],["css",[/^(@import|@page|@media|(\.|#)[a-z]+)/gm,20]],["diff",[/^[+><-]/gm,10],[/^@@ ?[-+,0-9 ]+ ?@@/gm,25]],["md",[/^(>|\t\*|\t\d+.)/gm,10],[/\[.*\](.*)/g,10]],["docker",[/^(FROM|ENTRYPOINT|RUN)/gm,500]],["xml",[/<\/?[a-z-]+[^\n>]*>/g,10],[/^<\?xml/g,500]],["c",[/#include\b|\bprintf\s+\(/g,100]],["rs",[/^\s+(use|fn|mut|match)\b/gm,100]],["go",[/\b(func|fmt|package)\b/g,100]],["java",[/^import\s+java/gm,500]],["asm",[/^(section|global main|extern|\t(call|mov|ret))/gm,100]],["css",[/^(@import|@page|@media|(\.|#)[a-z]+)/gm,20]],["json",[/\b(true|false|null|\{})\b|\"[^"]+\":/g,10]],["yaml",[/^(\s+)?[a-z][a-z0-9]*:/gmi,10]]],h=e=>{var t;return((t=le.map(([s,...m])=>[s,m.reduce((c,[r,n])=>c+[...e.matchAll(r)].length*n,0)]).filter(([s,m])=>m>20).sort((s,m)=>m[1]-s[1])[0])==null?void 0:t[0])||"plain"}});var ct={};p(ct,{default:()=>ue});var ue,nt=a(()=>{d();ue=[{type:"kwd",match:/^(GET|HEAD|POST|PUT|DELETE|CONNECT|OPTIONS|TRACE|PATCH|PRI|SEARCH)\b/gm},{expand:"str"},{type:"section",match:/\bHTTP\/[\d.]+\b/g},{expand:"num"},{type:"oper",match:/[,;:=]/g},{type:"var",match:/[a-zA-Z][\w-]*(?=:)/g},{match:/\n\n[^]*/g,sub:h}]});var mt={};p(mt,{default:()=>he});var he,rt=a(()=>{he=[{match:/(^[ \f\t\v]*)[#;].*/gm,sub:"todo"},{type:"str",match:/.*/g},{type:"var",match:/.*(?==)/g},{type:"section",match:/^\s*\[.+\]\s*$/gm},{type:"oper",match:/=/g}]});var ot={};p(ot,{default:()=>ie});var ie,Et=a(()=>{ie=[{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{expand:"num"},{type:"kwd",match:/\b(abstract|assert|boolean|break|byte|case|catch|char|class|continue|const|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|package|private|protected|public|requires|return|short|static|strictfp|super|switch|synchronized|this|throw|throws|transient|try|var|void|volatile|while)\b/g},{type:"oper",match:/[/*+:?&|%^~=!,<>.^-]+/g},{type:"func",match:/[a-zA-Z_][\w_]*(?=\s*\()/g},{type:"class",match:/\b[A-Z][\w_]*\b/g}]});var lt={};p(lt,{default:()=>x});var x,S=a(()=>{x=[{match:/\/\*\*((?!\*\/)[^])*(\*\/)?/g,sub:"jsdoc"},{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{match:/`((?!`)[^]|\\[^])*`?/g,sub:"js_template_literals"},{type:"kwd",match:/=>|\b(this|set|get|as|async|await|break|case|catch|class|const|constructor|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|if|implements|import|in|instanceof|interface|let|var|of|new|package|private|protected|public|return|static|super|switch|throw|throws|try|typeof|void|while|with|yield)\b/g},{match:/\/((?!\/)[^\r\n\\]|\\.)+\/[dgimsuy]*/g,sub:"regex"},{expand:"num"},{type:"num",match:/\b(NaN|null|undefined|[A-Z][A-Z_]*)\b/g},{type:"bool",match:/\b(true|false)\b/g},{type:"oper",match:/[/*+:?&|%^~=!,<>.^-]+/g},{type:"class",match:/\b[A-Z][\w_]*\b/g},{type:"func",match:/[a-zA-Z$_][\w$_]*(?=\s*((\?\.)?\s*\(|=\s*(\(?[\w,{}\[\])]+\)? =>|function\b)))/g}]});var ut={};p(ut,{default:()=>ge,type:()=>de});var ge,de,ht=a(()=>{ge=[{match:new class{exec(e){let t=this.lastIndex,s,m=c=>{for(;++t<e.length-2;)if(e[t]=="{")m();else if(e[t]=="}")return};for(;t<e.length;++t)if(e[t-1]!="\\"&&e[t]=="$"&&e[t+1]=="{")return s=t++,m(t),this.lastIndex=t+1,{index:s,0:e.slice(s,t+1)};return null}},sub:[{type:"kwd",match:/^\${|}$/g},{match:/(?!^\$|{)[^]+(?=}$)/g,sub:"js"}]}],de="str"});var it={};p(it,{default:()=>C,type:()=>be});var C,be,D=a(()=>{C=[{type:"err",match:/\b(TODO|FIXME|DEBUG|OPTIMIZE|WARNING|XXX|BUG)\b/g},{type:"class",match:/\bIDEA\b/g},{type:"insert",match:/\b(CHANGED|FIX|CHANGE)\b/g},{type:"oper",match:/\bQUESTION\b/g}],be="cmnt"});var gt={};p(gt,{default:()=>ye,type:()=>Te});var ye,Te,dt=a(()=>{D();ye=[{type:"kwd",match:/@\w+/g},{type:"class",match:/{[\w\s|<>,.@\[\]]+}/g},{type:"var",match:/\[[\w\s="']+\]/g},...C],Te="cmnt"});var bt={};p(bt,{default:()=>fe});var fe,yt=a(()=>{fe=[{type:"var",match:/("|')?[a-zA-Z]\w*\1(?=\s*:)/g},{expand:"str"},{expand:"num"},{type:"num",match:/\bnull\b/g},{type:"bool",match:/\b(true|false)\b/g}]});var Tt={};p(Tt,{default:()=>w});var w,U=a(()=>{d();w=[{type:"cmnt",match:/^>.*|(=|-)\1+/gm},{type:"class",match:/\*\*((?!\*\*).)*\*\*/g},{match:/```((?!```)[^])*\n```/g,sub:e=>({type:"kwd",sub:[{match:/\n[^]*(?=```)/g,sub:e.split(`
`)[0].slice(3)||h(e)}]})},{type:"str",match:/`[^`]*`/g},{type:"var",match:/~~((?!~~).)*~~/g},{type:"kwd",match:/_[^_]*_|\*[^*]*\*/g},{type:"kwd",match:/^\s*(\*|\d+\.)\s/gm},{type:"oper",match:/\[[^\]]*]/g},{type:"func",match:/\([^)]*\)/g}]});var ft={};p(ft,{default:()=>Ie});var Ie,It=a(()=>{U();d();Ie=[{type:"insert",match:/(leanpub-start-insert)((?!leanpub-end-insert)[^])*(leanpub-end-insert)?/g,sub:[{type:"insert",match:/leanpub-(start|end)-insert/g},{match:/(?!leanpub-start-insert)((?!leanpub-end-insert)[^])*/g,sub:h}]},{type:"deleted",match:/(leanpub-start-delete)((?!leanpub-end-delete)[^])*(leanpub-end-delete)?/g,sub:[{type:"deleted",match:/leanpub-(start|end)-delete/g},{match:/(?!leanpub-start-delete)((?!leanpub-end-delete)[^])*/g,sub:h}]},...w]});var Nt={};p(Nt,{default:()=>Ne});var Ne,At=a(()=>{Ne=[{type:"cmnt",match:/^#.*/gm},{expand:"strDouble"},{expand:"num"},{type:"err",match:/\b(err(or)?|[a-z_-]*exception|warn|warning|failed|ko|invalid|not ?found|alert|fatal)\b/gi},{type:"num",match:/\b(null|undefined)\b/gi},{type:"bool",match:/\b(false|true|yes|no)\b/gi},{type:"oper",match:/\.|,/g}]});var Rt={};p(Rt,{default:()=>Ae});var Ae,Ot=a(()=>{Ae=[{match:/^#!.*|--(\[(=*)\[((?!--\]\2\])[^])*--\]\2\]|.*)/g,sub:"todo"},{expand:"str"},{type:"kwd",match:/\b(and|break|do|else|elseif|end|for|function|if|in|local|not|or|repeat|return|then|until|while)\b/g},{type:"bool",match:/\b(true|false|nil)\b/g},{type:"oper",match:/[+*/%^#=~<>:,.-]+/g},{expand:"num"},{type:"func",match:/[a-z_]+(?=\s*[({])/g}]});var Lt={};p(Lt,{default:()=>Re});var Re,xt=a(()=>{Re=[{match:/^\s*#.*/gm,sub:"todo"},{expand:"str"},{type:"oper",match:/[${}()]+/g},{type:"class",match:/.PHONY:/gm},{type:"section",match:/^[\w.]+:/gm},{type:"kwd",match:/\b(ifneq|endif)\b/g},{expand:"num"},{type:"var",match:/[A-Z_]+(?=\s*=)/g},{match:/^.*$/gm,sub:"bash"}]});var St={};p(St,{default:()=>Oe});var Oe,Ct=a(()=>{Oe=[{match:/#.*/g,sub:"todo"},{type:"str",match:/(["'])(\\[^]|(?!\1)[^])*\1?/g},{expand:"num"},{type:"kwd",match:/\b(any|break|continue|default|delete|die|do|else|elsif|eval|for|foreach|given|goto|if|last|local|my|next|our|package|print|redo|require|return|say|state|sub|switch|undef|unless|until|use|when|while|not|and|or|xor)\b/g},{type:"oper",match:/[-+*/%~!&<>|=?,]+/g},{type:"func",match:/[a-z_]+(?=\s*\()/g}]});var Dt={};p(Dt,{default:()=>Le});var Le,wt=a(()=>{Le=[{expand:"strDouble"}]});var Ut={};p(Ut,{default:()=>xe});var xe,Pt=a(()=>{xe=[{match:/#.*/g,sub:"todo"},{match:/("""|''')(\\[^]|(?!\1)[^])*\1?/g,sub:"todo"},{type:"str",match:/f("|')(\\[^]|(?!\1).)*\1?|f((["'])\4\4)(\\[^]|(?!\3)[^])*\3?/gi,sub:[{type:"var",match:/{[^{}]*}/g,sub:[{match:/(?!^{)[^]*(?=}$)/g,sub:"py"}]}]},{expand:"str"},{type:"kwd",match:/\b(and|as|assert|break|class|continue|def|del|elif|else|except|finally|for|from|global|if|import|in|is|lambda|nonlocal|not|or|pass|raise|return|try|while|with|yield)\b/g},{type:"bool",match:/\b(False|True|None)\b/g},{expand:"num"},{type:"func",match:/[a-z_]+(?=\s*\()/g},{type:"oper",match:/[-/*+<>,=!&|^%]+/g},{type:"class",match:/\b[A-Z][\w_]*\b/g}]});var Ft={};p(Ft,{default:()=>Se,type:()=>Ce});var Se,Ce,Mt=a(()=>{Se=[{match:/^(?!\/).*/gm,sub:"todo"},{type:"num",match:/\[((?!\])[^\\]|\\.)*\]/g},{type:"kwd",match:/\||\^|\$|\\.|\w+($|\r|\n)/g},{type:"var",match:/\*|\+|\{\d+,\d+\}/g}],Ce="oper"});var $t={};p($t,{default:()=>De});var De,vt=a(()=>{De=[{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{expand:"num"},{type:"kwd",match:/\b(as|break|const|continue|crate|else|enum|extern|false|fn|for|if|impl|in|let|loop|match|mod|move|mut|pub|ref|return|self|Self|static|struct|super|trait|true|type|unsafe|use|where|while|async|await|dyn|abstract|become|box|do|final|macro|override|priv|typeof|unsized|virtual|yield|try)\b/g},{type:"oper",match:/[/*+:?&|%^~=!,<>.^-]+/g},{type:"class",match:/\b[A-Z][\w_]*\b/g},{type:"func",match:/[a-zA-Z_][\w_]*(?=\s*!?\s*\()/g}]});var Bt={};p(Bt,{default:()=>we});var we,Gt=a(()=>{we=[{match:/--.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{type:"func",match:/\b(AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/g},{type:"kwd",match:/\b(ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:_INSERT|COL)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|kwdS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:S|ING)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/g},{type:"num",match:/\.?\d[\d.oxa-fA-F-]*|\bNULL\b/g},{type:"bool",match:/\b(TRUE|FALSE)\b/g},{type:"oper",match:/[-+*\/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|IN|ILIKE|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/g},{type:"var",match:/@\S+/g}]});var Ht={};p(Ht,{default:()=>Ue});var Ue,kt=a(()=>{Ue=[{match:/#.*/g,sub:"todo"},{type:"str",match:/("""|''')((?!\1)[^]|\\[^])*\1?/g},{expand:"str"},{type:"section",match:/^\[.+\]\s*$/gm},{type:"num",match:/\b(inf|nan)\b|\d[\d:ZT.-]*/g},{expand:"num"},{type:"bool",match:/\b(true|false)\b/g},{type:"oper",match:/[+,.=-]/g},{type:"var",match:/\w+(?= \=)/g}]});var _t={};p(_t,{default:()=>Pe});var Pe,zt=a(()=>{S();Pe=[{type:"type",match:/:\s*(any|void|number|boolean|string|object|never|enum)\b/g},{type:"kwd",match:/\b(type|namespace|typedef|interface|public|private|protected|implements|declare|abstract|readonly)\b/g},...x]});var Yt={};p(Yt,{default:()=>Fe});var Fe,Zt=a(()=>{Fe=[{match:/^#.*/gm,sub:"todo"},{type:"class",match:/^\w+(?=:?)/gm},{type:"num",match:/:\d+/g},{type:"oper",match:/[:/&?]|\w+=/g},{type:"func",match:/[.\w]+@|#[\w]+$/gm},{type:"var",match:/\w+\.\w+(\.\w+)*/g}]});var Xt={};p(Xt,{default:()=>Me});var Me,Wt=a(()=>{Me=[{match:/#.*/g,sub:"todo"},{expand:"str"},{type:"str",match:/(>|\|)\r?\n((\s[^\n]*)?(\r?\n|$))*/g},{type:"type",match:/!![a-z]+/g},{type:"bool",match:/\b(Yes|No)\b/g},{type:"oper",match:/[+:-]/g},{expand:"num"},{type:"var",match:/[a-zA-Z]\w*(?=:)/g}]});var ke={};p(ke,{highlightAll:()=>Ge,highlightElement:()=>Kt,highlightText:()=>jt,loadLanguage:()=>He,tokenize:()=>P});module.exports=te(ke);var M={num:{type:"num",match:/(\.e?|\b)\d(e-|[\d.oxa-fA-F_])*(\.|\b)/g},str:{type:"str",match:/(["'])(\\[^]|(?!\1)[^\r\n\\])*\1?/g},strDouble:{type:"str",match:/"((?!")[^\r\n\\]|\\[^])*"?/g}};var $e=F({"./languages/asm.js":()=>Promise.resolve().then(()=>(v(),$)),"./languages/bash.js":()=>Promise.resolve().then(()=>(N(),G)),"./languages/bf.js":()=>Promise.resolve().then(()=>(k(),H)),"./languages/c.js":()=>Promise.resolve().then(()=>(z(),_)),"./languages/css.js":()=>Promise.resolve().then(()=>(Z(),Y)),"./languages/csv.js":()=>Promise.resolve().then(()=>(W(),X)),"./languages/diff.js":()=>Promise.resolve().then(()=>(R(),j)),"./languages/docker.js":()=>Promise.resolve().then(()=>(V(),K)),"./languages/git.js":()=>Promise.resolve().then(()=>(Q(),q)),"./languages/go.js":()=>Promise.resolve().then(()=>(tt(),J)),"./languages/html.js":()=>Promise.resolve().then(()=>(st(),pt)),"./languages/http.js":()=>Promise.resolve().then(()=>(nt(),ct)),"./languages/ini.js":()=>Promise.resolve().then(()=>(rt(),mt)),"./languages/java.js":()=>Promise.resolve().then(()=>(Et(),ot)),"./languages/js.js":()=>Promise.resolve().then(()=>(S(),lt)),"./languages/js_template_literals.js":()=>Promise.resolve().then(()=>(ht(),ut)),"./languages/jsdoc.js":()=>Promise.resolve().then(()=>(dt(),gt)),"./languages/json.js":()=>Promise.resolve().then(()=>(yt(),bt)),"./languages/leanpub-md.js":()=>Promise.resolve().then(()=>(It(),ft)),"./languages/log.js":()=>Promise.resolve().then(()=>(At(),Nt)),"./languages/lua.js":()=>Promise.resolve().then(()=>(Ot(),Rt)),"./languages/make.js":()=>Promise.resolve().then(()=>(xt(),Lt)),"./languages/md.js":()=>Promise.resolve().then(()=>(U(),Tt)),"./languages/pl.js":()=>Promise.resolve().then(()=>(Ct(),St)),"./languages/plain.js":()=>Promise.resolve().then(()=>(wt(),Dt)),"./languages/py.js":()=>Promise.resolve().then(()=>(Pt(),Ut)),"./languages/regex.js":()=>Promise.resolve().then(()=>(Mt(),Ft)),"./languages/rs.js":()=>Promise.resolve().then(()=>(vt(),$t)),"./languages/sql.js":()=>Promise.resolve().then(()=>(Gt(),Bt)),"./languages/todo.js":()=>Promise.resolve().then(()=>(D(),it)),"./languages/toml.js":()=>Promise.resolve().then(()=>(kt(),Ht)),"./languages/ts.js":()=>Promise.resolve().then(()=>(zt(),_t)),"./languages/uri.js":()=>Promise.resolve().then(()=>(Zt(),Yt)),"./languages/xml.js":()=>Promise.resolve().then(()=>(L(),at)),"./languages/yaml.js":()=>Promise.resolve().then(()=>(Wt(),Xt))});var b={},ve=(e="")=>{var t,s,m,c;return(c=(s=(t=e.replaceAll("&","&#38;")).replaceAll)==null?void 0:(m=s.call(t,"<","&lt;")).replaceAll)==null?void 0:c.call(m,">","&gt;")},Be=(e,t)=>t?`<span class="shj-syn-${t}">${e}</span>`:e;async function P(e,t,s){var m;try{let c,r,n={},y,o=[],i=0,T=typeof t=="string"?await((m=b[t])!=null?m:b[t]=$e(`./languages/${t}.js`)):t,g=[...typeof t=="string"?T.default:t.sub];for(;i<e.length;){for(n.index=null,c=g.length;c-- >0;){if(r=g[c].expand?M[g[c].expand]:g[c],o[c]===void 0||o[c].match.index<i){if(r.match.lastIndex=i,y=r.match.exec(e),y===null){g.splice(c,1),o.splice(c,1);continue}o[c]={match:y,lastIndex:r.match.lastIndex}}o[c].match[0]&&(o[c].match.index<=n.index||n.index===null)&&(n={part:r,index:o[c].match.index,match:o[c].match[0],end:o[c].lastIndex})}if(n.index===null)break;s(e.slice(i,n.index),T.type),i=n.end,n.part.sub?await P(n.match,typeof n.part.sub=="string"?n.part.sub:typeof n.part.sub=="function"?n.part.sub(n.match):n.part,s):s(n.match,n.part.type)}s(e.slice(i,e.length),T.type)}catch{s(e)}}async function jt(e,t,s=!0,m={}){let c="";return await P(e,t,(r,n)=>c+=Be(ve(r),n)),s?`<div><div class="shj-numbers">${"<div></div>".repeat(!m.hideLineNumbers&&e.split(`
`).length)}</div><div>${c}</div></div>`:c}async function Kt(e,t=(c=>(c=e.className.match(/shj-lang-([\w-]+)/))==null?void 0:c[1])(),s,m){let r=e.textContent;s!=null||(s=`${e.tagName=="CODE"?"in":r.split(`
`).length<2?"one":"multi"}line`),e.dataset.lang=t,e.className=`${[...e.classList].filter(n=>!n.startsWith("shj-")).join(" ")} shj-lang-${t} shj-${s}`,e.innerHTML=await jt(r,t,s=="multiline",m)}var Ge=async e=>Promise.all(Array.from(document.querySelectorAll('[class*="shj-lang-"]')).map(t=>Kt(t,void 0,void 0,e))),He=(e,t)=>{b[e]=t};0&&(module.exports={highlightAll,highlightElement,highlightText,loadLanguage,tokenize});
