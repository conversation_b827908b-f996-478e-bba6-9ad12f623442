declare namespace _default {
    namespace num {
        let type: string;
        let match: RegExp;
    }
    namespace str {
        let type_1: string;
        export { type_1 as type };
        let match_1: RegExp;
        export { match_1 as match };
    }
    namespace strDouble {
        let type_2: string;
        export { type_2 as type };
        let match_2: RegExp;
        export { match_2 as match };
    }
}
export default _default;
//# sourceMappingURL=common.d.ts.map