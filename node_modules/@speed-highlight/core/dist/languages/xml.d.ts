export let name: string;
export let properties: string;
export namespace xmlElement {
    let match: RegExp;
    let sub: ({
        type: string;
        match: RegExp;
        sub: {
            type: string;
            match: RegExp;
        }[];
    } | {
        type: string;
        match: RegExp;
        sub?: undefined;
    })[];
}
declare const _default: ({
    match: RegExp;
    sub: ({
        type: string;
        match: RegExp;
        sub: {
            type: string;
            match: RegExp;
        }[];
    } | {
        type: string;
        match: RegExp;
        sub?: undefined;
    })[];
} | {
    match: RegExp;
    sub: string;
    type?: undefined;
} | {
    type: string;
    match: RegExp;
    sub?: undefined;
})[];
export default _default;
//# sourceMappingURL=xml.d.ts.map