{"name": "vibesdk-production", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "DEV_MODE=true vite", "dev:worker": "DEV_MODE=true npm run build && wrangler dev --x-remote-bindings --port 5173", "dev:remote": "DEV_MODE=true npm run build && wrangler dev --remote --port 5173", "build": "tsc -b --incremental && vite build", "lint": "eslint .", "preview": "npm run build && vite preview", "deploy": "bun --env-file .prod.vars scripts/deploy.ts", "local": "wrangler dev --experimental-vectorize-bind-to-prod --local", "cf-typegen": "wrangler types --include-runtime false", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "db:generate": "drizzle-kit generate --config=drizzle.config.local.ts", "db:generate:remote": "drizzle-kit generate --config=drizzle.config.remote.ts", "db:migrate:local": "wrangler d1 migrations apply vibesdk-db --local", "db:migrate:remote": "CI=true wrangler d1 migrations apply vibesdk-db --remote", "db:push:local": "drizzle-kit push --config=drizzle.config.local.ts", "db:push:remote": "drizzle-kit push --config=drizzle.config.remote.ts", "db:studio": "drizzle-kit studio --config=drizzle.config.local.ts", "db:studio:remote": "drizzle-kit studio --config=drizzle.config.remote.ts", "db:setup": "bun run scripts/setup-database.ts", "db:drop": "drizzle-kit drop --config=drizzle.config.local.ts", "db:drop:remote": "drizzle-kit drop --config=drizzle.config.remote.ts", "db:introspect": "drizzle-kit introspect --config=drizzle.config.local.ts", "db:check": "drizzle-kit check --config=drizzle.config.local.ts", "db:up": "drizzle-kit up --config=drizzle.config.local.ts", "knip": "knip", "knip:fix": "knip --fix", "knip:production": "knip --production", "knip:dependencies": "knip --dependencies", "knip:exports": "knip --exports"}, "dependencies": {"@cloudflare/containers": "^0.0.25", "@cloudflare/sandbox": "0.1.3", "@noble/ciphers": "^1.3.0", "@octokit/rest": "^22.0.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@rolldown/binding-linux-x64-gnu": "^1.0.0-beta.9-commit.d91dfb5", "@sentry/cloudflare": "^10.11.0", "@sentry/react": "^10.13.0", "@sentry/vite-plugin": "^4.3.0", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "@typescript-eslint/typescript-estree": "^8.42.0", "@vitejs/plugin-react": "^5.0.2", "@vitejs/plugin-react-oxc": "^0.4.1", "agents": "^0.1.1", "chalk": "^5.6.2", "class-variance-authority": "^0.7.1", "cloudflare": "^4.5.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.2", "drizzle-orm": "^0.44.5", "embla-carousel-react": "^8.6.0", "esbuild": "^0.25.9", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "framer-motion": "^12.23.12", "hono": "^4.9.7", "html2canvas-pro": "^1.5.11", "input-otp": "^1.4.2", "jose": "^5.10.0", "jsonc-parser": "^3.3.1", "lucide-react": "^0.541.0", "monaco-editor": "^0.52.2", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "openai": "^5.19.1", "partysocket": "^1.1.5", "react": "^19.1.1", "react-day-picker": "^9.10.0", "react-dom": "^19.1.1", "react-feather": "^2.0.10", "react-hook-form": "^7.62.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.6", "react-router": "^7.8.2", "recharts": "^3.2.1", "rehype-external-links": "^3.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.7", "suffix-array": "^0.1.4", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "vite-plugin-svgr": "^4.5.0", "zod": "^3.25.76"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.13.2", "@cloudflare/vitest-pool-workers": "^0.8.70", "@eslint/js": "^9.35.0", "@types/node": "^22.9.0", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.13", "@types/jest": "^29.5.14", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react-swc": "^3.11.0", "drizzle-kit": "^0.31.4", "eslint": "^9.35.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "glob": "^11.0.3", "globals": "^16.3.0", "jest": "^29.7.0", "knip": "^5.63.1", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.13", "ts-jest": "^29.4.1", "tsx": "^4.20.5", "tw-animate-css": "^1.3.8", "typescript": "^5.9.2", "typescript-eslint": "^8.42.0", "vite": "npm:rolldown-vite@^7.1.11", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-node-polyfills": "^0.24.0", "vitest": "^3.2.4", "wrangler": "4.40.1"}, "prettier": {"singleQuote": true, "useTabs": true}, "overrides": {"vite": "npm:rolldown-vite@latest"}}