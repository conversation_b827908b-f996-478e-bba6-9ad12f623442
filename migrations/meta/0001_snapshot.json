{"version": "6", "dialect": "sqlite", "id": "ac0ed0af-c60e-4939-8a7f-d0ea8763f347", "prevId": "c41950c7-1162-4690-ae3f-072e69e53375", "tables": {"api_keys": {"name": "api_keys", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "key_hash": {"name": "key_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "key_preview": {"name": "key_preview", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "scopes": {"name": "scopes", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "last_used": {"name": "last_used", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "request_count": {"name": "request_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"api_keys_key_hash_unique": {"name": "api_keys_key_hash_unique", "columns": ["key_hash"], "isUnique": true}, "api_keys_user_id_idx": {"name": "api_keys_user_id_idx", "columns": ["user_id"], "isUnique": false}, "api_keys_key_hash_idx": {"name": "api_keys_key_hash_idx", "columns": ["key_hash"], "isUnique": false}, "api_keys_is_active_idx": {"name": "api_keys_is_active_idx", "columns": ["is_active"], "isUnique": false}, "api_keys_expires_at_idx": {"name": "api_keys_expires_at_idx", "columns": ["expires_at"], "isUnique": false}}, "foreignKeys": {"api_keys_user_id_users_id_fk": {"name": "api_keys_user_id_users_id_fk", "tableFrom": "api_keys", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "app_comments": {"name": "app_comments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "app_id": {"name": "app_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "parent_comment_id": {"name": "parent_comment_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_edited": {"name": "is_edited", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "is_deleted": {"name": "is_deleted", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"app_comments_app_idx": {"name": "app_comments_app_idx", "columns": ["app_id"], "isUnique": false}, "app_comments_user_idx": {"name": "app_comments_user_idx", "columns": ["user_id"], "isUnique": false}, "app_comments_parent_idx": {"name": "app_comments_parent_idx", "columns": ["parent_comment_id"], "isUnique": false}}, "foreignKeys": {"app_comments_app_id_apps_id_fk": {"name": "app_comments_app_id_apps_id_fk", "tableFrom": "app_comments", "tableTo": "apps", "columnsFrom": ["app_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "app_comments_user_id_users_id_fk": {"name": "app_comments_user_id_users_id_fk", "tableFrom": "app_comments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "app_likes": {"name": "app_likes", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "app_id": {"name": "app_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "reaction_type": {"name": "reaction_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'like'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"app_likes_app_user_idx": {"name": "app_likes_app_user_idx", "columns": ["app_id", "user_id"], "isUnique": true}, "app_likes_user_idx": {"name": "app_likes_user_idx", "columns": ["user_id"], "isUnique": false}}, "foreignKeys": {"app_likes_app_id_apps_id_fk": {"name": "app_likes_app_id_apps_id_fk", "tableFrom": "app_likes", "tableTo": "apps", "columnsFrom": ["app_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "app_likes_user_id_users_id_fk": {"name": "app_likes_user_id_users_id_fk", "tableFrom": "app_likes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "app_views": {"name": "app_views", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "app_id": {"name": "app_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "session_token": {"name": "session_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ip_address_hash": {"name": "ip_address_hash", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "referrer": {"name": "referrer", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "device_type": {"name": "device_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "viewed_at": {"name": "viewed_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "duration_seconds": {"name": "duration_seconds", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"app_views_app_idx": {"name": "app_views_app_idx", "columns": ["app_id"], "isUnique": false}, "app_views_user_idx": {"name": "app_views_user_idx", "columns": ["user_id"], "isUnique": false}, "app_views_viewed_at_idx": {"name": "app_views_viewed_at_idx", "columns": ["viewed_at"], "isUnique": false}}, "foreignKeys": {"app_views_app_id_apps_id_fk": {"name": "app_views_app_id_apps_id_fk", "tableFrom": "app_views", "tableTo": "apps", "columnsFrom": ["app_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "app_views_user_id_users_id_fk": {"name": "app_views_user_id_users_id_fk", "tableFrom": "app_views", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "apps": {"name": "apps", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "icon_url": {"name": "icon_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "original_prompt": {"name": "original_prompt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "final_prompt": {"name": "final_prompt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "framework": {"name": "framework", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "session_token": {"name": "session_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "visibility": {"name": "visibility", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'private'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'generating'"}, "deployment_url": {"name": "deployment_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "github_repository_url": {"name": "github_repository_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "github_repository_visibility": {"name": "github_repository_visibility", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_archived": {"name": "is_archived", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "is_featured": {"name": "is_featured", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 1}, "parent_app_id": {"name": "parent_app_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "screenshot_url": {"name": "screenshot_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "screenshot_captured_at": {"name": "screenshot_captured_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "last_deployed_at": {"name": "last_deployed_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"apps_user_idx": {"name": "apps_user_idx", "columns": ["user_id"], "isUnique": false}, "apps_status_idx": {"name": "apps_status_idx", "columns": ["status"], "isUnique": false}, "apps_visibility_idx": {"name": "apps_visibility_idx", "columns": ["visibility"], "isUnique": false}, "apps_session_token_idx": {"name": "apps_session_token_idx", "columns": ["session_token"], "isUnique": false}, "apps_parent_app_idx": {"name": "apps_parent_app_idx", "columns": ["parent_app_id"], "isUnique": false}, "apps_search_idx": {"name": "apps_search_idx", "columns": ["title", "description"], "isUnique": false}, "apps_framework_status_idx": {"name": "apps_framework_status_idx", "columns": ["framework", "status"], "isUnique": false}, "apps_visibility_status_idx": {"name": "apps_visibility_status_idx", "columns": ["visibility", "status"], "isUnique": false}, "apps_created_at_idx": {"name": "apps_created_at_idx", "columns": ["created_at"], "isUnique": false}, "apps_updated_at_idx": {"name": "apps_updated_at_idx", "columns": ["updated_at"], "isUnique": false}}, "foreignKeys": {"apps_user_id_users_id_fk": {"name": "apps_user_id_users_id_fk", "tableFrom": "apps", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "audit_logs": {"name": "audit_logs", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "entity_type": {"name": "entity_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "entity_id": {"name": "entity_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "old_values": {"name": "old_values", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "new_values": {"name": "new_values", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"audit_logs_user_idx": {"name": "audit_logs_user_idx", "columns": ["user_id"], "isUnique": false}, "audit_logs_entity_idx": {"name": "audit_logs_entity_idx", "columns": ["entity_type", "entity_id"], "isUnique": false}, "audit_logs_created_at_idx": {"name": "audit_logs_created_at_idx", "columns": ["created_at"], "isUnique": false}}, "foreignKeys": {"audit_logs_user_id_users_id_fk": {"name": "audit_logs_user_id_users_id_fk", "tableFrom": "audit_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "auth_attempts": {"name": "auth_attempts", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "attempt_type": {"name": "attempt_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "success": {"name": "success", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "attempted_at": {"name": "attempted_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"auth_attempts_lookup_idx": {"name": "auth_attempts_lookup_idx", "columns": ["identifier", "attempted_at"], "isUnique": false}, "auth_attempts_ip_idx": {"name": "auth_attempts_ip_idx", "columns": ["ip_address", "attempted_at"], "isUnique": false}, "auth_attempts_success_idx": {"name": "auth_attempts_success_idx", "columns": ["success", "attempted_at"], "isUnique": false}, "auth_attempts_type_idx": {"name": "auth_attempts_type_idx", "columns": ["attempt_type", "attempted_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "comment_likes": {"name": "comment_likes", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "comment_id": {"name": "comment_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "reaction_type": {"name": "reaction_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'like'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"comment_likes_comment_user_idx": {"name": "comment_likes_comment_user_idx", "columns": ["comment_id", "user_id"], "isUnique": true}, "comment_likes_user_idx": {"name": "comment_likes_user_idx", "columns": ["user_id"], "isUnique": false}, "comment_likes_comment_idx": {"name": "comment_likes_comment_idx", "columns": ["comment_id"], "isUnique": false}}, "foreignKeys": {"comment_likes_comment_id_app_comments_id_fk": {"name": "comment_likes_comment_id_app_comments_id_fk", "tableFrom": "comment_likes", "tableTo": "app_comments", "columnsFrom": ["comment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "comment_likes_user_id_users_id_fk": {"name": "comment_likes_user_id_users_id_fk", "tableFrom": "comment_likes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "email_verification_tokens": {"name": "email_verification_tokens", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token_hash": {"name": "token_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "used": {"name": "used", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"email_verification_tokens_token_hash_unique": {"name": "email_verification_tokens_token_hash_unique", "columns": ["token_hash"], "isUnique": true}, "email_verification_tokens_lookup_idx": {"name": "email_verification_tokens_lookup_idx", "columns": ["token_hash"], "isUnique": false}, "email_verification_tokens_expiry_idx": {"name": "email_verification_tokens_expiry_idx", "columns": ["expires_at"], "isUnique": false}}, "foreignKeys": {"email_verification_tokens_user_id_users_id_fk": {"name": "email_verification_tokens_user_id_users_id_fk", "tableFrom": "email_verification_tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "favorites": {"name": "favorites", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "app_id": {"name": "app_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"favorites_user_app_idx": {"name": "favorites_user_app_idx", "columns": ["user_id", "app_id"], "isUnique": true}, "favorites_user_idx": {"name": "favorites_user_idx", "columns": ["user_id"], "isUnique": false}, "favorites_app_idx": {"name": "favorites_app_idx", "columns": ["app_id"], "isUnique": false}}, "foreignKeys": {"favorites_user_id_users_id_fk": {"name": "favorites_user_id_users_id_fk", "tableFrom": "favorites", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "favorites_app_id_apps_id_fk": {"name": "favorites_app_id_apps_id_fk", "tableFrom": "favorites", "tableTo": "apps", "columnsFrom": ["app_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "oauth_states": {"name": "oauth_states", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "redirect_uri": {"name": "redirect_uri", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "scopes": {"name": "scopes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'[]'"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "code_verifier": {"name": "code_verifier", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "nonce": {"name": "nonce", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_used": {"name": "is_used", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}}, "indexes": {"oauth_states_state_unique": {"name": "oauth_states_state_unique", "columns": ["state"], "isUnique": true}, "oauth_states_state_idx": {"name": "oauth_states_state_idx", "columns": ["state"], "isUnique": true}, "oauth_states_expires_at_idx": {"name": "oauth_states_expires_at_idx", "columns": ["expires_at"], "isUnique": false}}, "foreignKeys": {"oauth_states_user_id_users_id_fk": {"name": "oauth_states_user_id_users_id_fk", "tableFrom": "oauth_states", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "password_reset_tokens": {"name": "password_reset_tokens", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token_hash": {"name": "token_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "used": {"name": "used", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"password_reset_tokens_token_hash_unique": {"name": "password_reset_tokens_token_hash_unique", "columns": ["token_hash"], "isUnique": true}, "password_reset_tokens_lookup_idx": {"name": "password_reset_tokens_lookup_idx", "columns": ["token_hash"], "isUnique": false}, "password_reset_tokens_expiry_idx": {"name": "password_reset_tokens_expiry_idx", "columns": ["expires_at"], "isUnique": false}}, "foreignKeys": {"password_reset_tokens_user_id_users_id_fk": {"name": "password_reset_tokens_user_id_users_id_fk", "tableFrom": "password_reset_tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sessions": {"name": "sessions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "device_info": {"name": "device_info", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_revoked": {"name": "is_revoked", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "revoked_at": {"name": "revoked_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "revoked_reason": {"name": "revoked_reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token_hash": {"name": "access_token_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "refresh_token_hash": {"name": "refresh_token_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "last_activity": {"name": "last_activity", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"sessions_user_id_idx": {"name": "sessions_user_id_idx", "columns": ["user_id"], "isUnique": false}, "sessions_expires_at_idx": {"name": "sessions_expires_at_idx", "columns": ["expires_at"], "isUnique": false}, "sessions_access_token_hash_idx": {"name": "sessions_access_token_hash_idx", "columns": ["access_token_hash"], "isUnique": false}, "sessions_refresh_token_hash_idx": {"name": "sessions_refresh_token_hash_idx", "columns": ["refresh_token_hash"], "isUnique": false}, "sessions_last_activity_idx": {"name": "sessions_last_activity_idx", "columns": ["last_activity"], "isUnique": false}, "sessions_is_revoked_idx": {"name": "sessions_is_revoked_idx", "columns": ["is_revoked"], "isUnique": false}}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "stars": {"name": "stars", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "app_id": {"name": "app_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "starred_at": {"name": "starred_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"stars_user_app_idx": {"name": "stars_user_app_idx", "columns": ["user_id", "app_id"], "isUnique": true}, "stars_user_idx": {"name": "stars_user_idx", "columns": ["user_id"], "isUnique": false}, "stars_app_idx": {"name": "stars_app_idx", "columns": ["app_id"], "isUnique": false}}, "foreignKeys": {"stars_user_id_users_id_fk": {"name": "stars_user_id_users_id_fk", "tableFrom": "stars", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "stars_app_id_apps_id_fk": {"name": "stars_app_id_apps_id_fk", "tableFrom": "stars", "tableTo": "apps", "columnsFrom": ["app_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "system_settings": {"name": "system_settings", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_by": {"name": "updated_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"system_settings_key_unique": {"name": "system_settings_key_unique", "columns": ["key"], "isUnique": true}, "system_settings_key_idx": {"name": "system_settings_key_idx", "columns": ["key"], "isUnique": true}}, "foreignKeys": {"system_settings_updated_by_users_id_fk": {"name": "system_settings_updated_by_users_id_fk", "tableFrom": "system_settings", "tableTo": "users", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_model_configs": {"name": "user_model_configs", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "agent_action_name": {"name": "agent_action_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "model_name": {"name": "model_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "max_tokens": {"name": "max_tokens", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "temperature": {"name": "temperature", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "reasoning_effort": {"name": "reasoning_effort", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "provider_override": {"name": "provider_override", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "fallback_model": {"name": "fallback_model", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"user_model_configs_user_agent_idx": {"name": "user_model_configs_user_agent_idx", "columns": ["user_id", "agent_action_name"], "isUnique": true}, "user_model_configs_user_idx": {"name": "user_model_configs_user_idx", "columns": ["user_id"], "isUnique": false}, "user_model_configs_is_active_idx": {"name": "user_model_configs_is_active_idx", "columns": ["is_active"], "isUnique": false}}, "foreignKeys": {"user_model_configs_user_id_users_id_fk": {"name": "user_model_configs_user_id_users_id_fk", "tableFrom": "user_model_configs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_model_providers": {"name": "user_model_providers", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "base_url": {"name": "base_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "secret_id": {"name": "secret_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"user_model_providers_user_name_idx": {"name": "user_model_providers_user_name_idx", "columns": ["user_id", "name"], "isUnique": true}, "user_model_providers_user_idx": {"name": "user_model_providers_user_idx", "columns": ["user_id"], "isUnique": false}, "user_model_providers_is_active_idx": {"name": "user_model_providers_is_active_idx", "columns": ["is_active"], "isUnique": false}}, "foreignKeys": {"user_model_providers_user_id_users_id_fk": {"name": "user_model_providers_user_id_users_id_fk", "tableFrom": "user_model_providers", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_model_providers_secret_id_user_secrets_id_fk": {"name": "user_model_providers_secret_id_user_secrets_id_fk", "tableFrom": "user_model_providers", "tableTo": "user_secrets", "columnsFrom": ["secret_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_secrets": {"name": "user_secrets", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "secret_type": {"name": "secret_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "encrypted_value": {"name": "encrypted_value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "key_preview": {"name": "key_preview", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_used": {"name": "last_used", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"user_secrets_user_idx": {"name": "user_secrets_user_idx", "columns": ["user_id"], "isUnique": false}, "user_secrets_provider_idx": {"name": "user_secrets_provider_idx", "columns": ["provider"], "isUnique": false}, "user_secrets_user_provider_idx": {"name": "user_secrets_user_provider_idx", "columns": ["user_id", "provider", "secret_type"], "isUnique": false}, "user_secrets_active_idx": {"name": "user_secrets_active_idx", "columns": ["is_active"], "isUnique": false}}, "foreignKeys": {"user_secrets_user_id_users_id_fk": {"name": "user_secrets_user_id_users_id_fk", "tableFrom": "user_secrets", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "failed_login_attempts": {"name": "failed_login_attempts", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "locked_until": {"name": "locked_until", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "password_changed_at": {"name": "password_changed_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "preferences": {"name": "preferences", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'{}'"}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'system'"}, "timezone": {"name": "timezone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'UTC'"}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "is_suspended": {"name": "is_suspended", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "last_active_at": {"name": "last_active_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}, "users_username_unique": {"name": "users_username_unique", "columns": ["username"], "isUnique": true}, "users_email_idx": {"name": "users_email_idx", "columns": ["email"], "isUnique": false}, "users_provider_unique_idx": {"name": "users_provider_unique_idx", "columns": ["provider", "provider_id"], "isUnique": true}, "users_username_idx": {"name": "users_username_idx", "columns": ["username"], "isUnique": false}, "users_failed_login_attempts_idx": {"name": "users_failed_login_attempts_idx", "columns": ["failed_login_attempts"], "isUnique": false}, "users_locked_until_idx": {"name": "users_locked_until_idx", "columns": ["locked_until"], "isUnique": false}, "users_is_active_idx": {"name": "users_is_active_idx", "columns": ["is_active"], "isUnique": false}, "users_last_active_at_idx": {"name": "users_last_active_at_idx", "columns": ["last_active_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "verification_otps": {"name": "verification_otps", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "otp": {"name": "otp", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "used": {"name": "used", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "used_at": {"name": "used_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"verification_otps_email_idx": {"name": "verification_otps_email_idx", "columns": ["email"], "isUnique": false}, "verification_otps_expires_at_idx": {"name": "verification_otps_expires_at_idx", "columns": ["expires_at"], "isUnique": false}, "verification_otps_used_idx": {"name": "verification_otps_used_idx", "columns": ["used"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}