# Cloudflare API Configuration (REQUIRED for deployment)
CLOUDFLARE_API_TOKEN=****************************************
CLOUDFLARE_ACCOUNT_ID=e1ef23a482d710064edcfdf77f1324c1

# AI Provider API Keys (at least one required)
ANTHROPIC_API_KEY=placeholder-add-your-key-here
OPENAI_API_KEY=placeholder-add-your-key-here
GOOGLE_AI_STUDIO_API_KEY=AIzaSyALhts-RGtOwOZa0eCWlcUOBz4rv4aMUJ0

# Security Configuration
JWT_SECRET=temp-jwt-secret-change-this-in-production
WEBHOOK_SECRET=temp-webhook-secret-change-this-in-production

# Optional: Custom Domain Configuration
# CUSTOM_DOMAIN=your-domain.com
# ENVIRONMENT=prod

# Optional: OAuth Configuration
# GOOGLE_CLIENT_ID=your-google-client-id
# GOOGLE_CLIENT_SECRET=your-google-client-secret
# GITHUB_CLIENT_ID=your-github-client-id
# GITHUB_CLIENT_SECRET=your-github-client-secret
